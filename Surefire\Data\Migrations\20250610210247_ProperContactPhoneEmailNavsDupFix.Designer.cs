﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Surefire.Data;

#nullable disable

namespace Surefire.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250610210247_ProperContactPhoneEmailNavsDupFix")]
    partial class ProperContactPhoneEmailNavsDupFix
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("Surefire.Data.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DesktopUsername")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("FirstName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("LastLogin")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastLookupClient")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastLookupPerson")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastName")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int?>("LastRenewalId")
                        .HasColumnType("int");

                    b.Property<int?>("LastRenewalMonth")
                        .HasColumnType("int");

                    b.Property<string>("LastRenewalPerson")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LastRenewalScreen")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("LastRenewalYear")
                        .HasColumnType("int");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("PictureUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Surefire.Domain.Accounting.Models.Settlement", b =>
                {
                    b.Property<int>("SettlementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SettlementId"));

                    b.Property<string>("AccountingBillingCompany")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AccountingCarrier")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("AccountingIssueDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("AccountingPaidOnDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("AccountingPaidToCarrierAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("AccountingPolicyNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("AccountingStatementDueDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("AccountingStatementNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BillType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("BrokerFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("CommissionAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("CommissionPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("DownPaymentAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("DownPaymentPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("FinanceAccountNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("FinanceAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("FinanceChargePercent")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("FinanceMonths")
                        .HasColumnType("int");

                    b.Property<decimal?>("FullGrandTotalPayment")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsFinanced")
                        .HasColumnType("bit");

                    b.Property<bool>("IsFullPayment")
                        .HasColumnType("bit");

                    b.Property<decimal?>("MinEarnedPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("PayAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PayAmountNeededToBind")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PayAmountNet")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("PayDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("PayDepositDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("PayDone")
                        .HasColumnType("bit");

                    b.Property<decimal?>("PayFees")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("PayNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PayType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("int");

                    b.Property<decimal?>("Premium")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("RenewalId")
                        .HasColumnType("int");

                    b.HasKey("SettlementId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("PolicyId");

                    b.HasIndex("RenewalId");

                    b.ToTable("Settlements");
                });

            modelBuilder.Entity("Surefire.Domain.Accounting.Models.SettlementItem", b =>
                {
                    b.Property<int>("SettlementItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SettlementItemId"));

                    b.Property<decimal?>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ItemCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SettlementId")
                        .HasColumnType("int");

                    b.HasKey("SettlementItemId");

                    b.HasIndex("SettlementId");

                    b.ToTable("SettlementItems");
                });

            modelBuilder.Entity("Surefire.Domain.Attachments.Models.Attachment", b =>
                {
                    b.Property<int>("AttachmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AttachmentId"));

                    b.Property<int?>("AttachmentGroupId")
                        .HasColumnType("int");

                    b.Property<int?>("CarrierId")
                        .HasColumnType("int");

                    b.Property<int?>("ClaimId")
                        .HasColumnType("int");

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateLastOpened")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FileFormat")
                        .HasColumnType("nvarchar(max)");

                    b.Property<double?>("FileSize")
                        .HasColumnType("float");

                    b.Property<int?>("FolderId")
                        .HasColumnType("int");

                    b.Property<string>("HashedFileName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsAcord")
                        .HasColumnType("bit");

                    b.Property<bool>("IsBinder")
                        .HasColumnType("bit");

                    b.Property<bool>("IsClientAccessible")
                        .HasColumnType("bit");

                    b.Property<bool>("IsEnclosure")
                        .HasColumnType("bit");

                    b.Property<bool>("IsEndorsement")
                        .HasColumnType("bit");

                    b.Property<bool>("IsInvoice")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPolicyCopy")
                        .HasColumnType("bit");

                    b.Property<bool>("IsProposal")
                        .HasColumnType("bit");

                    b.Property<bool>("IsQuote")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRefinedProposal")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSL2")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSupplemental")
                        .HasColumnType("bit");

                    b.Property<string>("LocalPath")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OriginalFileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("int");

                    b.Property<int?>("RenewalId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int?>("SubmissionId")
                        .HasColumnType("int");

                    b.Property<string>("UploadedById")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("AttachmentId");

                    b.HasIndex("AttachmentGroupId");

                    b.HasIndex("CarrierId");

                    b.HasIndex("ClaimId");

                    b.HasIndex("ClientId");

                    b.HasIndex("FolderId");

                    b.HasIndex("PolicyId");

                    b.HasIndex("RenewalId");

                    b.HasIndex("SubmissionId");

                    b.HasIndex("UploadedById");

                    b.ToTable("Attachments");
                });

            modelBuilder.Entity("Surefire.Domain.Attachments.Models.AttachmentGroup", b =>
                {
                    b.Property<int>("AttachmentGroupId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AttachmentGroupId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("AttachmentGroupId");

                    b.ToTable("AttachmentGroups");
                });

            modelBuilder.Entity("Surefire.Domain.Attachments.Models.Folder", b =>
                {
                    b.Property<int>("FolderId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FolderId"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("FolderId");

                    b.ToTable("Folders");
                });

            modelBuilder.Entity("Surefire.Domain.Carriers.Models.Carrier", b =>
                {
                    b.Property<int>("CarrierId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CarrierId"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<string>("AppetiteJson")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CarrierName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CarrierNickname")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IssuingCarrier")
                        .HasColumnType("bit");

                    b.Property<string>("LogoFilename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LookupCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LossRunsEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LossRunsNote")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LossRunsURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NewSubmissionEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("QuickLink")
                        .HasColumnType("bit");

                    b.Property<string>("QuotelinesJson")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QuotingWebsite")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ServicingEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ServicingWebsite")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("State")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StreetAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Website")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Wholesaler")
                        .HasColumnType("bit");

                    b.Property<string>("Zip")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("CarrierId");

                    b.HasIndex("AddressId");

                    b.HasIndex("CreatedById");

                    b.ToTable("Carriers");
                });

            modelBuilder.Entity("Surefire.Domain.Carriers.Models.Credential", b =>
                {
                    b.Property<int>("CredentialId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CredentialId"));

                    b.Property<int>("CarrierId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Password")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Username")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Website")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("CredentialId");

                    b.HasIndex("CarrierId");

                    b.HasIndex("CreatedById");

                    b.ToTable("Credentials");
                });

            modelBuilder.Entity("Surefire.Domain.Chat.CallTranscription", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("CallDuration")
                        .HasColumnType("int");

                    b.Property<string>("CallId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("CallStartTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FromPhoneNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsReviewed")
                        .HasColumnType("bit");

                    b.Property<string>("Language")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecordingId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ToPhoneNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TranscriptionText")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.ToTable("CallTranscriptions");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.BusinessDetails", b =>
                {
                    b.Property<int>("BusinessDetailsId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("BusinessDetailsId"));

                    b.Property<decimal?>("AnnualGrossSalesRevenueReceipts")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("AnnualPayrollHazardExposure")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool?>("BuildingLocationMonitoredSecurity")
                        .HasColumnType("bit");

                    b.Property<int?>("BuildingLocationNumberOfStories")
                        .HasColumnType("int");

                    b.Property<bool?>("BuildingLocationSprinklered")
                        .HasColumnType("bit");

                    b.Property<int?>("BuildingLocationSquareFootage")
                        .HasColumnType("int");

                    b.Property<int?>("BuildingLocationYearBuilt")
                        .HasColumnType("int");

                    b.Property<string>("BusinessIndustry")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("BusinessPersonalPropertyBPP")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("BusinessSpecialty")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("BusinessType")
                        .HasColumnType("int");

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateStarted")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("EstimatedAnnualPayroll0")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("EstimatedAnnualPayroll1")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("EstimatedAnnualPayroll2")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("EstimatedAnnualPayroll3")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("EstimatedAnnualPayroll4")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("EstimatedGrossSales0")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("EstimatedGrossSales1")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("EstimatedGrossSales2")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("EstimatedGrossSales3")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("EstimatedGrossSales4")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("EstimatedSubcontractingExpenses")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("FEIN")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("InsuranceHistory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LapseHistory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("LegalEntityType")
                        .HasColumnType("int");

                    b.Property<string>("LicenseNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("LicenseType")
                        .HasColumnType("int");

                    b.Property<string>("LongDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("NumClaims")
                        .HasColumnType("int");

                    b.Property<int?>("NumFullTimeEmployees")
                        .HasColumnType("int");

                    b.Property<int?>("NumPartTimeEmployees")
                        .HasColumnType("int");

                    b.Property<string>("PercentCommercial")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PercentExterior")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PercentInterior")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PercentNewConstruction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PercentPublic")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PercentRemodelRepair")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PercentResidential")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecordType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ShortDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("YearsExperience")
                        .HasColumnType("int");

                    b.HasKey("BusinessDetailsId");

                    b.HasIndex("ClientId");

                    b.ToTable("BusinessDetails");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.Client", b =>
                {
                    b.Property<int>("ClientId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ClientId"));

                    b.Property<int>("AddressId")
                        .HasColumnType("int");

                    b.Property<string>("CSRId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Comments")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateOpened")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LogoFilename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LookupCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PrimaryContactId")
                        .HasColumnType("int");

                    b.Property<string>("ProducerId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Website")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("eClientId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ClientId");

                    b.HasIndex("AddressId");

                    b.HasIndex("CSRId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("PrimaryContactId");

                    b.HasIndex("ProducerId");

                    b.ToTable("Clients");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.ClientNote", b =>
                {
                    b.Property<int>("ClientNoteId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ClientNoteId"));

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ClientNoteId");

                    b.HasIndex("ClientId");

                    b.ToTable("ClientNotes");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.GlobalNote", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AuthorId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AuthorName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<int>("EntityId")
                        .HasColumnType("int");

                    b.Property<int>("EntityType")
                        .HasColumnType("int");

                    b.Property<bool>("HasMarkdown")
                        .HasColumnType("bit");

                    b.Property<bool>("Pinned")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ReminderDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Tags")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Text")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("GlobalNotes");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.Lead", b =>
                {
                    b.Property<int>("LeadId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LeadId"));

                    b.Property<string>("Address")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("BindDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("CompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContactName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("LastOpened")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Operations")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ProductId")
                        .HasColumnType("int");

                    b.Property<int?>("Source")
                        .HasColumnType("int");

                    b.Property<int?>("Stage")
                        .HasColumnType("int");

                    b.Property<string>("State")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Website")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Zip")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("LeadId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ProductId");

                    b.ToTable("Leads");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.LeadNote", b =>
                {
                    b.Property<int>("LeadNoteId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LeadNoteId"));

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<int>("LeadId")
                        .HasColumnType("int");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("LeadNoteId");

                    b.HasIndex("LeadId");

                    b.ToTable("LeadNotes");
                });

            modelBuilder.Entity("Surefire.Domain.Contacts.Models.Contact", b =>
                {
                    b.Property<int>("ContactId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ContactId"));

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<bool>("Billing")
                        .HasColumnType("bit");

                    b.Property<int?>("CarrierId")
                        .HasColumnType("int");

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HeadshotFilename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsInactive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsStarred")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiddleName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PrimaryEmailId")
                        .HasColumnType("int");

                    b.Property<int?>("PrimaryPhoneId")
                        .HasColumnType("int");

                    b.Property<bool>("Representative")
                        .HasColumnType("bit");

                    b.Property<bool>("Service")
                        .HasColumnType("bit");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Underwriter")
                        .HasColumnType("bit");

                    b.HasKey("ContactId");

                    b.HasIndex("AddressId");

                    b.HasIndex("CarrierId");

                    b.HasIndex("ClientId");

                    b.HasIndex("PrimaryEmailId");

                    b.HasIndex("PrimaryPhoneId");

                    b.ToTable("Contacts");
                });

            modelBuilder.Entity("Surefire.Domain.Contacts.Models.EmailAddress", b =>
                {
                    b.Property<int>("EmailAddressId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EmailAddressId"));

                    b.Property<int?>("ContactId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("bit");

                    b.Property<string>("Label")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("EmailAddressId");

                    b.HasIndex("ContactId");

                    b.ToTable("EmailAddresses");
                });

            modelBuilder.Entity("Surefire.Domain.Contacts.Models.PhoneNumber", b =>
                {
                    b.Property<int>("PhoneNumberId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PhoneNumberId"));

                    b.Property<int?>("ContactId")
                        .HasColumnType("int");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Extension")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("bit");

                    b.Property<string>("Number")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("SMS")
                        .HasColumnType("bit");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("TypeOther")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("PhoneNumberId");

                    b.HasIndex("ContactId");

                    b.ToTable("PhoneNumbers");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.Certificate", b =>
                {
                    b.Property<int>("CertificateId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CertificateId"));

                    b.Property<bool?>("AttachGLAI")
                        .HasColumnType("bit");

                    b.Property<string>("AttachGLAIfilename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("AttachGLWOS")
                        .HasColumnType("bit");

                    b.Property<string>("AttachGLWOSfilename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("AttachPNC")
                        .HasColumnType("bit");

                    b.Property<bool?>("AttachWCWOS")
                        .HasColumnType("bit");

                    b.Property<string>("AttachWCWOSfilename")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("BlockAttachments")
                        .HasColumnType("bit");

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("HolderName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JSONData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JSONDataTemp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProjectName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("CertificateId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("Certificates");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.CertificateRequest", b =>
                {
                    b.Property<int>("CertificateRequestId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("CertificateRequestId"));

                    b.Property<string>("AdditionalInsured")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AssignedToId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("CertificateId")
                        .HasColumnType("int");

                    b.Property<string>("ClientCompany")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClientEmail")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("ClientName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClientPhone")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CoverageTypes")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("EmailCopyToClient")
                        .HasColumnType("bit");

                    b.Property<bool>("EmailToHolder")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ExternalRequestId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("HasProject")
                        .HasColumnType("bit");

                    b.Property<string>("HolderAddress")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HolderAttention")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HolderCity")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HolderEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HolderName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HolderState")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HolderZip")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ImportedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsImported")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastOpened")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PrimaryNonContributary")
                        .HasColumnType("bit");

                    b.Property<string>("ProjectAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ProjectId")
                        .HasColumnType("int");

                    b.Property<string>("ProjectName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProjectNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("RequestDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("WaiverSubrogationGL")
                        .HasColumnType("bit");

                    b.Property<bool>("WaiverSubrogationWC")
                        .HasColumnType("bit");

                    b.HasKey("CertificateRequestId");

                    b.HasIndex("AssignedToId");

                    b.HasIndex("CertificateId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.ToTable("CertificateRequests");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.EmailTemplate", b =>
                {
                    b.Property<int>("EmailTemplateId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("EmailTemplateId"));

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("CustomFunction")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("NeedsContact")
                        .HasColumnType("bit");

                    b.Property<bool>("NeedsDownPayment")
                        .HasColumnType("bit");

                    b.Property<bool>("NeedsPayment")
                        .HasColumnType("bit");

                    b.Property<bool>("NeedsPolicy")
                        .HasColumnType("bit");

                    b.Property<bool>("NeedsProduct")
                        .HasColumnType("bit");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("EmailTemplateId");

                    b.ToTable("EmailTemplates");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.FormDoc", b =>
                {
                    b.Property<int>("FormDocId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FormDocId"));

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FormPdfId")
                        .HasColumnType("int");

                    b.Property<string>("JSONData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("LeadId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("int");

                    b.Property<int?>("RenewalId")
                        .HasColumnType("int");

                    b.Property<int?>("SubmissionId")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("FormDocId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FormPdfId");

                    b.HasIndex("LeadId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PolicyId");

                    b.HasIndex("RenewalId");

                    b.HasIndex("SubmissionId");

                    b.ToTable("FormDocs");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.FormDocRevision", b =>
                {
                    b.Property<int>("FormDocRevisionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FormDocRevisionId"));

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<int>("FormDocId")
                        .HasColumnType("int");

                    b.Property<string>("JSONData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RevisionName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("FormDocRevisionId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FormDocId");

                    b.HasIndex("ModifiedById");

                    b.ToTable("FormDocRevisions");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.FormPdf", b =>
                {
                    b.Property<int>("FormPdfId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FormPdfId"));

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Filepath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("JSONFields")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("FormPdfId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("FormPdf");
                });

            modelBuilder.Entity("Surefire.Domain.Home.Models.DailyTask", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AssignedToId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool>("Completed")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Highlighted")
                        .HasColumnType("bit");

                    b.Property<int?>("Order")
                        .HasColumnType("int");

                    b.Property<string>("TaskName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("AssignedToId");

                    b.ToTable("DailyTasks");
                });

            modelBuilder.Entity("Surefire.Domain.Logs.Log", b =>
                {
                    b.Property<int>("LogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LogId"));

                    b.Property<string>("EntityId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EntityType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Exception")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("LogLevel")
                        .HasColumnType("int");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Source")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LogId");

                    b.HasIndex("UserId");

                    b.ToTable("Logs");
                });

            modelBuilder.Entity("Surefire.Domain.OpenAI.OpenAIPrompt", b =>
                {
                    b.Property<int>("OpenAIPromptId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpenAIPromptId"));

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<string>("note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("prompt")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("OpenAIPromptId");

                    b.ToTable("OpenAIPrompt");
                });

            modelBuilder.Entity("Surefire.Domain.OpenAI.OpenAIResponse", b =>
                {
                    b.Property<int>("OpenAIResponseId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("OpenAIResponseId"));

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<string>("promptId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("response")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("OpenAIResponseId");

                    b.ToTable("OpenAIResponse");
                });

            modelBuilder.Entity("Surefire.Domain.Plugins.Plugin", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("BaseUri")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClientId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClientSecret")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DeveloperName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GrantType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HashId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Jwt")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PluginWebsite")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RedirectUri")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Scope")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ShortDescription")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TokenUri")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Plugins");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.AutoCoverage", b =>
                {
                    b.Property<int>("AutoCoverageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AutoCoverageId"));

                    b.Property<bool?>("AdditionalAttachments")
                        .HasColumnType("bit");

                    b.Property<int?>("AdditionalAttachmentsAttachmentAttachmentId")
                        .HasColumnType("int");

                    b.Property<int?>("AdditionalCoverageLimit")
                        .HasColumnType("int");

                    b.Property<string>("AdditionalCoverageName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("AdditionalInsured")
                        .HasColumnType("bit");

                    b.Property<int?>("AdditionalInsuredAttachmentAttachmentId")
                        .HasColumnType("int");

                    b.Property<int?>("BodilyInjuryPerAccident")
                        .HasColumnType("int");

                    b.Property<int?>("BodilyInjuryPerPerson")
                        .HasColumnType("int");

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<int?>("CombinedLimit")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("ForAny")
                        .HasColumnType("bit");

                    b.Property<bool?>("ForHired")
                        .HasColumnType("bit");

                    b.Property<bool?>("ForNonOwned")
                        .HasColumnType("bit");

                    b.Property<bool?>("ForOwned")
                        .HasColumnType("bit");

                    b.Property<bool?>("ForScheduled")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("int");

                    b.Property<int?>("PropertyDamage")
                        .HasColumnType("int");

                    b.Property<bool?>("WaiverOfSub")
                        .HasColumnType("bit");

                    b.Property<int?>("WaiverOfSubAttachmentAttachmentId")
                        .HasColumnType("int");

                    b.HasKey("AutoCoverageId");

                    b.HasIndex("AdditionalAttachmentsAttachmentAttachmentId");

                    b.HasIndex("AdditionalInsuredAttachmentAttachmentId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PolicyId")
                        .IsUnique()
                        .HasFilter("[PolicyId] IS NOT NULL");

                    b.HasIndex("WaiverOfSubAttachmentAttachmentId");

                    b.ToTable("AutoCoverages");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Claim", b =>
                {
                    b.Property<int>("ClaimId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ClaimId"));

                    b.Property<decimal>("AmountPaid")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ClaimNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateOfLoss")
                        .HasColumnType("datetime2");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("int");

                    b.HasKey("ClaimId");

                    b.HasIndex("PolicyId");

                    b.ToTable("Claims");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.GeneralLiabilityCoverage", b =>
                {
                    b.Property<int>("GeneralLiabilityCoverageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("GeneralLiabilityCoverageId"));

                    b.Property<bool?>("AdditionalAttachments")
                        .HasColumnType("bit");

                    b.Property<int?>("AdditionalAttachmentsAttachmentAttachmentId")
                        .HasColumnType("int");

                    b.Property<int?>("AdditionalCoverageLimit")
                        .HasColumnType("int");

                    b.Property<string>("AdditionalCoverageName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("AdditionalInsured")
                        .HasColumnType("bit");

                    b.Property<int?>("AdditionalInsuredAttachmentAttachmentId")
                        .HasColumnType("int");

                    b.Property<string>("AdditionalInsuredFormNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AggregateAppliesPer")
                        .HasColumnType("int");

                    b.Property<bool?>("ClaimsMade")
                        .HasColumnType("bit");

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("DamageToPremises")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<int?>("EachOccurrence")
                        .HasColumnType("int");

                    b.Property<int?>("GeneralAggregate")
                        .HasColumnType("int");

                    b.Property<int?>("MedicalExpenses")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool?>("Occurence")
                        .HasColumnType("bit");

                    b.Property<int?>("PersonalInjury")
                        .HasColumnType("int");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("int");

                    b.Property<decimal?>("Premium")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool?>("PrimaryWording")
                        .HasColumnType("bit");

                    b.Property<int?>("ProductsAggregate")
                        .HasColumnType("int");

                    b.Property<bool?>("WaiverOfSub")
                        .HasColumnType("bit");

                    b.Property<int?>("WaiverOfSubAttachmentAttachmentId")
                        .HasColumnType("int");

                    b.HasKey("GeneralLiabilityCoverageId");

                    b.HasIndex("AdditionalAttachmentsAttachmentAttachmentId");

                    b.HasIndex("AdditionalInsuredAttachmentAttachmentId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PolicyId")
                        .IsUnique()
                        .HasFilter("[PolicyId] IS NOT NULL");

                    b.HasIndex("WaiverOfSubAttachmentAttachmentId");

                    b.ToTable("GeneralLiabilityCoverages");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Loss", b =>
                {
                    b.Property<int>("LossId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LossId"));

                    b.Property<decimal?>("AmountPaid")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("AmountReserved")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime?>("DateClaimSubmitted")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateOccurred")
                        .HasColumnType("datetime2");

                    b.Property<string>("LongDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("Open")
                        .HasColumnType("bit");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("int");

                    b.Property<string>("ShortDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("Subgrogated")
                        .HasColumnType("bit");

                    b.Property<string>("UserModifiedId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LossId");

                    b.HasIndex("PolicyId");

                    b.HasIndex("UserModifiedId");

                    b.ToTable("Losses");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Policy", b =>
                {
                    b.Property<int>("PolicyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PolicyId"));

                    b.Property<int?>("ApplicationId")
                        .HasColumnType("int");

                    b.Property<string>("CSRId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("CarrierId")
                        .HasColumnType("int");

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("EffectiveDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ExpirationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PolicyNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Premium")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ProducerId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("ProductId")
                        .HasColumnType("int");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("WholesalerId")
                        .HasColumnType("int");

                    b.Property<string>("ePolicyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ePolicyLineId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("eType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("eTypeCode")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("PolicyId");

                    b.HasIndex("ApplicationId");

                    b.HasIndex("CSRId");

                    b.HasIndex("CarrierId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ProducerId");

                    b.HasIndex("ProductId");

                    b.HasIndex("WholesalerId");

                    b.ToTable("Policies");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.PropertyCoverage", b =>
                {
                    b.Property<int>("PropertyCoverageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PropertyCoverageId"));

                    b.Property<int?>("BusinessPersonalProperty")
                        .HasColumnType("int");

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<int?>("Equipment")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("int");

                    b.HasKey("PropertyCoverageId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PolicyId")
                        .IsUnique()
                        .HasFilter("[PolicyId] IS NOT NULL");

                    b.ToTable("PropertyCoverage");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.RatingBasis", b =>
                {
                    b.Property<int>("RatingBasisId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RatingBasisId"));

                    b.Property<decimal?>("BaseRate")
                        .HasColumnType("decimal(7,4)");

                    b.Property<string>("Basis")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClassCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClassDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Exposure")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("LocationId")
                        .HasColumnType("int");

                    b.Property<decimal?>("NetRate")
                        .HasColumnType("decimal(7,4)");

                    b.Property<decimal?>("Payroll")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("int");

                    b.Property<decimal?>("Premium")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("ProductId")
                        .HasColumnType("int");

                    b.Property<string>("UserModifiedId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("RatingBasisId");

                    b.HasIndex("LocationId");

                    b.HasIndex("PolicyId");

                    b.HasIndex("ProductId");

                    b.HasIndex("UserModifiedId");

                    b.ToTable("RatingBases");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.UmbrellaCoverage", b =>
                {
                    b.Property<int>("UmbrellaCoverageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UmbrellaCoverageId"));

                    b.Property<bool?>("ClaimsMade")
                        .HasColumnType("bit");

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DeductibleRetentionAmount")
                        .HasColumnType("int");

                    b.Property<int?>("EachOccurrence")
                        .HasColumnType("int");

                    b.Property<int?>("GeneralAggregate")
                        .HasColumnType("int");

                    b.Property<bool?>("HasDeductible")
                        .HasColumnType("bit");

                    b.Property<bool?>("HasRetention")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsExcess")
                        .HasColumnType("bit");

                    b.Property<bool?>("IsUmbrella")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool?>("Occurrence")
                        .HasColumnType("bit");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("int");

                    b.HasKey("UmbrellaCoverageId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PolicyId")
                        .IsUnique()
                        .HasFilter("[PolicyId] IS NOT NULL");

                    b.ToTable("UmbrellaCoverage");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.WorkCompCoverage", b =>
                {
                    b.Property<int>("WorkCompCoverageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("WorkCompCoverageId"));

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DiseaseEachEmployee")
                        .HasColumnType("int");

                    b.Property<int?>("DiseasePolicyLimit")
                        .HasColumnType("int");

                    b.Property<int?>("EachAccident")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool?>("OwnersOfficersExcluded")
                        .HasColumnType("bit");

                    b.Property<bool?>("PerOther")
                        .HasColumnType("bit");

                    b.Property<bool?>("PerStatute")
                        .HasColumnType("bit");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("int");

                    b.Property<bool?>("WaiverOfSub")
                        .HasColumnType("bit");

                    b.Property<int?>("WaiverOfSubAttachmentAttachmentId")
                        .HasColumnType("int");

                    b.HasKey("WorkCompCoverageId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PolicyId")
                        .IsUnique()
                        .HasFilter("[PolicyId] IS NOT NULL");

                    b.HasIndex("WaiverOfSubAttachmentAttachmentId");

                    b.ToTable("WorkCompCoverages");
                });

            modelBuilder.Entity("Surefire.Domain.Proposals.Proposal", b =>
                {
                    b.Property<int>("ProposalId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProposalId"));

                    b.Property<string>("ApprovedJson")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("AttachmentGroupId")
                        .HasColumnType("int");

                    b.Property<int?>("AttachmentId")
                        .HasColumnType("int");

                    b.Property<int?>("BillType")
                        .HasColumnType("int");

                    b.Property<int?>("BrokerFee")
                        .HasColumnType("int");

                    b.Property<string>("CleanedJson")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("CleanedJsonAttachmentId")
                        .HasColumnType("int");

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DocuSignOptions")
                        .HasColumnType("int");

                    b.Property<int?>("DownPaymentPercent")
                        .HasColumnType("int");

                    b.Property<string>("ExtractPages")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IncludeD1")
                        .HasColumnType("bit");

                    b.Property<bool>("IncludeD2")
                        .HasColumnType("bit");

                    b.Property<bool>("IncludeSL2")
                        .HasColumnType("bit");

                    b.Property<string>("IncludeSL2BusinessDesc")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IncludeSL2Code")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("bit");

                    b.Property<bool>("IsFinanced")
                        .HasColumnType("bit");

                    b.Property<int?>("MinimumEarnedPercent")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("int");

                    b.Property<decimal?>("PurePremium")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("RawJson")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RawJsonAttachmentId")
                        .HasColumnType("int");

                    b.Property<int?>("RenewalId")
                        .HasColumnType("int");

                    b.Property<bool>("SendASAP")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("SendDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("SendInstructions")
                        .HasColumnType("int");

                    b.Property<string>("SpecialInstructions")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<decimal?>("TotalCost")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalDeposit")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("TotalTaxesAndFees")
                        .HasColumnType("decimal(18,2)");

                    b.HasKey("ProposalId");

                    b.HasIndex("AttachmentGroupId");

                    b.HasIndex("AttachmentId");

                    b.HasIndex("CleanedJsonAttachmentId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PolicyId");

                    b.HasIndex("RawJsonAttachmentId");

                    b.HasIndex("RenewalId");

                    b.ToTable("Proposals");
                });

            modelBuilder.Entity("Surefire.Domain.Proposals.ProposalFeeItem", b =>
                {
                    b.Property<int>("ProposalFeeItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProposalFeeItemId"));

                    b.Property<decimal?>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ProposalId")
                        .HasColumnType("int");

                    b.HasKey("ProposalFeeItemId");

                    b.HasIndex("ProposalId");

                    b.ToTable("ProposalFeeItems");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.MasterSubTask", b =>
                {
                    b.Property<int>("MasterSubTaskId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MasterSubTaskId"));

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("OrderNumber")
                        .HasColumnType("int");

                    b.Property<int>("TaskMasterId")
                        .HasColumnType("int");

                    b.Property<string>("TaskName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("MasterSubTaskId");

                    b.HasIndex("TaskMasterId");

                    b.ToTable("MasterSubTasks");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.Renewal", b =>
                {
                    b.Property<int>("RenewalId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RenewalId"));

                    b.Property<string>("AssignedToId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("BillType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("CarrierId")
                        .HasColumnType("int");

                    b.Property<int>("ClientId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("ExpiringPolicyNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal?>("ExpiringPremium")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("int");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<DateTime>("RenewalDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("RenewalStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("WholesalerId")
                        .HasColumnType("int");

                    b.HasKey("RenewalId");

                    b.HasIndex("AssignedToId");

                    b.HasIndex("CarrierId");

                    b.HasIndex("ClientId");

                    b.HasIndex("PolicyId");

                    b.HasIndex("ProductId");

                    b.HasIndex("WholesalerId");

                    b.ToTable("Renewals");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.RenewalNote", b =>
                {
                    b.Property<int>("RenewalNoteId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RenewalNoteId"));

                    b.Property<string>("CreatedById")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NoteType")
                        .HasColumnType("int");

                    b.Property<int>("RenewalId")
                        .HasColumnType("int");

                    b.Property<int?>("SubmissionId")
                        .HasColumnType("int");

                    b.Property<int?>("TrackTaskId")
                        .HasColumnType("int");

                    b.HasKey("RenewalNoteId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("RenewalId");

                    b.HasIndex("SubmissionId");

                    b.HasIndex("TrackTaskId");

                    b.ToTable("RenewalNotes");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.Submission", b =>
                {
                    b.Property<int>("SubmissionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SubmissionId"));

                    b.Property<int?>("CarrierId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateDeleted")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<int?>("LeadId")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("Premium")
                        .HasColumnType("int");

                    b.Property<int?>("PrimaryCarrierContactId")
                        .HasColumnType("int");

                    b.Property<int?>("PrimaryWholesalerContactId")
                        .HasColumnType("int");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int?>("RenewalId")
                        .HasColumnType("int");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StatusInt")
                        .HasColumnType("int");

                    b.Property<DateTime>("SubmissionDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("SubmissionStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("WholesalerId")
                        .HasColumnType("int");

                    b.HasKey("SubmissionId");

                    b.HasIndex("CarrierId");

                    b.HasIndex("LeadId");

                    b.HasIndex("ProductId");

                    b.HasIndex("RenewalId");

                    b.HasIndex("WholesalerId");

                    b.ToTable("Submissions");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.SubmissionTask", b =>
                {
                    b.Property<int>("SubmissionTaskId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SubmissionTaskId"));

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DueDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("bit");

                    b.Property<int>("SubmissionId")
                        .HasColumnType("int");

                    b.Property<string>("TaskName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("SubmissionTaskId");

                    b.HasIndex("SubmissionId");

                    b.ToTable("SubmissionTasks");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TaskGroup", b =>
                {
                    b.Property<int>("TaskGroupId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TaskGroupId"));

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("TaskGroupId");

                    b.ToTable("TaskGroups");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TaskGroupTaskMaster", b =>
                {
                    b.Property<int>("TaskGroupId")
                        .HasColumnType("int");

                    b.Property<int>("TaskMasterId")
                        .HasColumnType("int");

                    b.Property<int>("OrderNumber")
                        .HasColumnType("int");

                    b.HasKey("TaskGroupId", "TaskMasterId");

                    b.HasIndex("TaskMasterId");

                    b.ToTable("TaskGroupTaskMasters");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TaskMaster", b =>
                {
                    b.Property<int>("TaskMasterId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("TaskMasterId"));

                    b.Property<int?>("DaysBeforeExpiration")
                        .HasColumnType("int");

                    b.Property<string>("DefaultAssignedToId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ForType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Important")
                        .HasColumnType("bit");

                    b.Property<string>("TaskName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("TaskMasterId");

                    b.HasIndex("DefaultAssignedToId");

                    b.ToTable("TaskMasters");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TaskMasterSubTask", b =>
                {
                    b.Property<int>("ParentTaskMasterId")
                        .HasColumnType("int");

                    b.Property<int>("SubTaskMasterId")
                        .HasColumnType("int");

                    b.Property<int>("OrderNumber")
                        .HasColumnType("int");

                    b.HasKey("ParentTaskMasterId", "SubTaskMasterId");

                    b.HasIndex("SubTaskMasterId");

                    b.ToTable("TaskMasterSubTasks");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TrackTask", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AssignedToId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<bool>("Completed")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DailyCheckOff")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("GoalDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Hidden")
                        .HasColumnType("bit");

                    b.Property<bool>("Highlighted")
                        .HasColumnType("bit");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("OrderNumber")
                        .HasColumnType("int");

                    b.Property<int?>("ParentTaskId")
                        .HasColumnType("int");

                    b.Property<int>("RenewalId")
                        .HasColumnType("int");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TaskName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("AssignedToId");

                    b.HasIndex("ParentTaskId");

                    b.HasIndex("RenewalId");

                    b.ToTable("TrackTasks");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Address", b =>
                {
                    b.Property<int>("AddressId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AddressId"));

                    b.Property<string>("AddressLine1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AddressLine2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Country")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PostalCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("State")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("AddressId");

                    b.ToTable("Address");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Application", b =>
                {
                    b.Property<int>("ApplicationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ApplicationId"));

                    b.Property<DateTime>("ApplicationDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ApplicationId");

                    b.ToTable("Application");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Driver", b =>
                {
                    b.Property<int>("DriverId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("DriverId"));

                    b.Property<DateTime>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsPrimaryDriver")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("LicenseExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LicenseNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("int");

                    b.HasKey("DriverId");

                    b.HasIndex("PolicyId");

                    b.ToTable("Drivers");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.EntityAssociation", b =>
                {
                    b.Property<int>("AssociationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AssociationId"));

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<int>("EntityId1")
                        .HasColumnType("int");

                    b.Property<int>("EntityId2")
                        .HasColumnType("int");

                    b.Property<string>("EntityType1")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("EntityType2")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RelationshipDescription")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("AssociationId");

                    b.ToTable("EntityAssociations");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.FireSearchResultViewModel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AddressType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DataType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Parent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Primary")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("FireSearchResultViewModel");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Location", b =>
                {
                    b.Property<int>("LocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LocationId"));

                    b.Property<int>("AddressId")
                        .HasColumnType("int");

                    b.Property<string>("BuildingName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("BuildingTotalSquareFootage")
                        .HasColumnType("int");

                    b.Property<int?>("ClientId")
                        .HasColumnType("int");

                    b.Property<decimal?>("GrossSales")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int?>("NumFullTimeEmployees")
                        .HasColumnType("int");

                    b.Property<int?>("NumPartTimeEmployees")
                        .HasColumnType("int");

                    b.Property<int?>("NumStories")
                        .HasColumnType("int");

                    b.Property<int?>("OccupiedSquareFootage")
                        .HasColumnType("int");

                    b.Property<bool?>("Owner")
                        .HasColumnType("bit");

                    b.Property<string>("SquareFootage")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool?>("Tenant")
                        .HasColumnType("bit");

                    b.Property<string>("YearBuilt")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("LocationId");

                    b.HasIndex("AddressId");

                    b.HasIndex("ClientId");

                    b.ToTable("Locations");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Product", b =>
                {
                    b.Property<int>("ProductId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProductId"));

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LineCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LineName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LineNickname")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ProductId");

                    b.ToTable("Products");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Settings", b =>
                {
                    b.Property<int>("SettingsId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("SettingsId"));

                    b.Property<string>("AgentLlm")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AzureBlobConnectionString")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AzureBlobContainerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DbConnectionString")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DbType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("DisablePlugins")
                        .HasColumnType("bit");

                    b.Property<string>("FileServerMappedPath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FileStore")
                        .HasColumnType("int");

                    b.Property<string>("OpenAiApiKey")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PayLinkStringTemplate")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("SettingsId");

                    b.ToTable("Settings");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Vehicle", b =>
                {
                    b.Property<int>("VehicleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("VehicleId"));

                    b.Property<string>("CountryOfRegistration")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Make")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Model")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("int");

                    b.Property<DateTime>("RegistrationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("VIN")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Year")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("VehicleId");

                    b.HasIndex("PolicyId");

                    b.ToTable("Vehicles");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Data.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Surefire.Domain.Accounting.Models.Settlement", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithMany("Settlements")
                        .HasForeignKey("PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Renewals.Models.Renewal", "Renewal")
                        .WithMany("Settlements")
                        .HasForeignKey("RenewalId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedBy");

                    b.Navigation("Policy");

                    b.Navigation("Renewal");
                });

            modelBuilder.Entity("Surefire.Domain.Accounting.Models.SettlementItem", b =>
                {
                    b.HasOne("Surefire.Domain.Accounting.Models.Settlement", "Settlement")
                        .WithMany("SettlementItems")
                        .HasForeignKey("SettlementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Settlement");
                });

            modelBuilder.Entity("Surefire.Domain.Attachments.Models.Attachment", b =>
                {
                    b.HasOne("Surefire.Domain.Attachments.Models.AttachmentGroup", "AttachmentGroup")
                        .WithMany("Attachments")
                        .HasForeignKey("AttachmentGroupId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Carrier")
                        .WithMany("Attachments")
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Claim", null)
                        .WithMany("Attachments")
                        .HasForeignKey("ClaimId");

                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany("Attachments")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Surefire.Domain.Attachments.Models.Folder", "Folder")
                        .WithMany("Attachments")
                        .HasForeignKey("FolderId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithMany("Attachments")
                        .HasForeignKey("PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Renewals.Models.Renewal", "Renewal")
                        .WithMany("Attachments")
                        .HasForeignKey("RenewalId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Renewals.Models.Submission", "Submission")
                        .WithMany("Attachments")
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "UploadedBy")
                        .WithMany()
                        .HasForeignKey("UploadedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AttachmentGroup");

                    b.Navigation("Carrier");

                    b.Navigation("Client");

                    b.Navigation("Folder");

                    b.Navigation("Policy");

                    b.Navigation("Renewal");

                    b.Navigation("Submission");

                    b.Navigation("UploadedBy");
                });

            modelBuilder.Entity("Surefire.Domain.Carriers.Models.Carrier", b =>
                {
                    b.HasOne("Surefire.Domain.Shared.Models.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId");

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Address");

                    b.Navigation("CreatedBy");
                });

            modelBuilder.Entity("Surefire.Domain.Carriers.Models.Credential", b =>
                {
                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Carrier")
                        .WithMany("Credentials")
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.Navigation("Carrier");

                    b.Navigation("CreatedBy");
                });

            modelBuilder.Entity("Surefire.Domain.Chat.CallTranscription", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", null)
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.SetNull);
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.BusinessDetails", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.Client", b =>
                {
                    b.HasOne("Surefire.Domain.Shared.Models.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Data.ApplicationUser", "CSR")
                        .WithMany()
                        .HasForeignKey("CSRId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Contacts.Models.Contact", "PrimaryContact")
                        .WithMany()
                        .HasForeignKey("PrimaryContactId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Surefire.Data.ApplicationUser", "Producer")
                        .WithMany()
                        .HasForeignKey("ProducerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Address");

                    b.Navigation("CSR");

                    b.Navigation("CreatedBy");

                    b.Navigation("PrimaryContact");

                    b.Navigation("Producer");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.ClientNote", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany("ClientNotes")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Client");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.Lead", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Shared.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.LeadNote", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Lead", "Lead")
                        .WithMany("LeadNotes")
                        .HasForeignKey("LeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Lead");
                });

            modelBuilder.Entity("Surefire.Domain.Contacts.Models.Contact", b =>
                {
                    b.HasOne("Surefire.Domain.Shared.Models.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId");

                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Carrier")
                        .WithMany("Contacts")
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany("Contacts")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Surefire.Domain.Contacts.Models.EmailAddress", "PrimaryEmail")
                        .WithMany()
                        .HasForeignKey("PrimaryEmailId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Contacts.Models.PhoneNumber", "PrimaryPhone")
                        .WithMany()
                        .HasForeignKey("PrimaryPhoneId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Address");

                    b.Navigation("Carrier");

                    b.Navigation("Client");

                    b.Navigation("PrimaryEmail");

                    b.Navigation("PrimaryPhone");
                });

            modelBuilder.Entity("Surefire.Domain.Contacts.Models.EmailAddress", b =>
                {
                    b.HasOne("Surefire.Domain.Contacts.Models.Contact", "Contact")
                        .WithMany("EmailAddresses")
                        .HasForeignKey("ContactId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Contact");
                });

            modelBuilder.Entity("Surefire.Domain.Contacts.Models.PhoneNumber", b =>
                {
                    b.HasOne("Surefire.Domain.Contacts.Models.Contact", "Contact")
                        .WithMany("PhoneNumbers")
                        .HasForeignKey("ContactId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Contact");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.Certificate", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany("Certificates")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.CertificateRequest", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Forms.Models.Certificate", "Certificate")
                        .WithMany()
                        .HasForeignKey("CertificateId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AssignedTo");

                    b.Navigation("Certificate");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.FormDoc", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany("FormDocs")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Forms.Models.FormPdf", "FormPdf")
                        .WithMany()
                        .HasForeignKey("FormPdfId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Clients.Models.Lead", "Lead")
                        .WithMany("FormDocs")
                        .HasForeignKey("LeadId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithMany()
                        .HasForeignKey("PolicyId");

                    b.HasOne("Surefire.Domain.Renewals.Models.Renewal", "Renewal")
                        .WithMany()
                        .HasForeignKey("RenewalId");

                    b.HasOne("Surefire.Domain.Renewals.Models.Submission", "Submission")
                        .WithMany()
                        .HasForeignKey("SubmissionId");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("FormPdf");

                    b.Navigation("Lead");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Policy");

                    b.Navigation("Renewal");

                    b.Navigation("Submission");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.FormDocRevision", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Forms.Models.FormDoc", "FormDoc")
                        .WithMany("FormDocRevisions")
                        .HasForeignKey("FormDocId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedBy");

                    b.Navigation("FormDoc");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.FormPdf", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("Surefire.Domain.Home.Models.DailyTask", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("AssignedTo");
                });

            modelBuilder.Entity("Surefire.Domain.Logs.Log", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.AutoCoverage", b =>
                {
                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "AdditionalAttachmentsAttachment")
                        .WithMany()
                        .HasForeignKey("AdditionalAttachmentsAttachmentAttachmentId");

                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "AdditionalInsuredAttachment")
                        .WithMany()
                        .HasForeignKey("AdditionalInsuredAttachmentAttachmentId");

                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithOne("AutoCoverage")
                        .HasForeignKey("Surefire.Domain.Policies.Models.AutoCoverage", "PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "WaiverOfSubAttachment")
                        .WithMany()
                        .HasForeignKey("WaiverOfSubAttachmentAttachmentId");

                    b.Navigation("AdditionalAttachmentsAttachment");

                    b.Navigation("AdditionalInsuredAttachment");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Policy");

                    b.Navigation("WaiverOfSubAttachment");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Claim", b =>
                {
                    b.HasOne("Surefire.Domain.Policies.Models.Policy", null)
                        .WithMany("Claims")
                        .HasForeignKey("PolicyId");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.GeneralLiabilityCoverage", b =>
                {
                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "AdditionalAttachmentsAttachment")
                        .WithMany()
                        .HasForeignKey("AdditionalAttachmentsAttachmentAttachmentId");

                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "AdditionalInsuredAttachment")
                        .WithMany()
                        .HasForeignKey("AdditionalInsuredAttachmentAttachmentId");

                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithOne("GeneralLiabilityCoverage")
                        .HasForeignKey("Surefire.Domain.Policies.Models.GeneralLiabilityCoverage", "PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "WaiverOfSubAttachment")
                        .WithMany()
                        .HasForeignKey("WaiverOfSubAttachmentAttachmentId");

                    b.Navigation("AdditionalAttachmentsAttachment");

                    b.Navigation("AdditionalInsuredAttachment");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Policy");

                    b.Navigation("WaiverOfSubAttachment");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Loss", b =>
                {
                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithMany("Losses")
                        .HasForeignKey("PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "UserModified")
                        .WithMany()
                        .HasForeignKey("UserModifiedId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Policy");

                    b.Navigation("UserModified");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Policy", b =>
                {
                    b.HasOne("Surefire.Domain.Shared.Models.Application", "Application")
                        .WithMany()
                        .HasForeignKey("ApplicationId");

                    b.HasOne("Surefire.Data.ApplicationUser", "CSR")
                        .WithMany()
                        .HasForeignKey("CSRId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Carrier")
                        .WithMany()
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany("Policies")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "Producer")
                        .WithMany()
                        .HasForeignKey("ProducerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Shared.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Wholesaler")
                        .WithMany()
                        .HasForeignKey("WholesalerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Application");

                    b.Navigation("CSR");

                    b.Navigation("Carrier");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("Producer");

                    b.Navigation("Product");

                    b.Navigation("Wholesaler");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.PropertyCoverage", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithOne("PropertyCoverage")
                        .HasForeignKey("Surefire.Domain.Policies.Models.PropertyCoverage", "PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Policy");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.RatingBasis", b =>
                {
                    b.HasOne("Surefire.Domain.Shared.Models.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithMany("RatingBases")
                        .HasForeignKey("PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Shared.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "UserModified")
                        .WithMany()
                        .HasForeignKey("UserModifiedId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Location");

                    b.Navigation("Policy");

                    b.Navigation("Product");

                    b.Navigation("UserModified");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.UmbrellaCoverage", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithOne("UmbrellaCoverage")
                        .HasForeignKey("Surefire.Domain.Policies.Models.UmbrellaCoverage", "PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Policy");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.WorkCompCoverage", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithOne("WorkCompCoverage")
                        .HasForeignKey("Surefire.Domain.Policies.Models.WorkCompCoverage", "PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "WaiverOfSubAttachment")
                        .WithMany()
                        .HasForeignKey("WaiverOfSubAttachmentAttachmentId");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Policy");

                    b.Navigation("WaiverOfSubAttachment");
                });

            modelBuilder.Entity("Surefire.Domain.Proposals.Proposal", b =>
                {
                    b.HasOne("Surefire.Domain.Attachments.Models.AttachmentGroup", "AttachmentGroup")
                        .WithMany()
                        .HasForeignKey("AttachmentGroupId");

                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "Attachment")
                        .WithMany()
                        .HasForeignKey("AttachmentId");

                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "CleanedJsonAttachment")
                        .WithMany()
                        .HasForeignKey("CleanedJsonAttachmentId");

                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId");

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithMany()
                        .HasForeignKey("PolicyId");

                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "RawJsonAttachment")
                        .WithMany()
                        .HasForeignKey("RawJsonAttachmentId");

                    b.HasOne("Surefire.Domain.Renewals.Models.Renewal", "Renewal")
                        .WithMany()
                        .HasForeignKey("RenewalId");

                    b.Navigation("Attachment");

                    b.Navigation("AttachmentGroup");

                    b.Navigation("CleanedJsonAttachment");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Policy");

                    b.Navigation("RawJsonAttachment");

                    b.Navigation("Renewal");
                });

            modelBuilder.Entity("Surefire.Domain.Proposals.ProposalFeeItem", b =>
                {
                    b.HasOne("Surefire.Domain.Proposals.Proposal", "Proposal")
                        .WithMany("ProposalFeeItems")
                        .HasForeignKey("ProposalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Proposal");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.MasterSubTask", b =>
                {
                    b.HasOne("Surefire.Domain.Renewals.Models.TaskMaster", "TaskMaster")
                        .WithMany("MasterSubTasks")
                        .HasForeignKey("TaskMasterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TaskMaster");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.Renewal", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Carrier")
                        .WithMany()
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithMany("Renewals")
                        .HasForeignKey("PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Shared.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Wholesaler")
                        .WithMany()
                        .HasForeignKey("WholesalerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AssignedTo");

                    b.Navigation("Carrier");

                    b.Navigation("Client");

                    b.Navigation("Policy");

                    b.Navigation("Product");

                    b.Navigation("Wholesaler");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.RenewalNote", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Renewals.Models.Renewal", "Renewal")
                        .WithMany()
                        .HasForeignKey("RenewalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Renewals.Models.Submission", "Submission")
                        .WithMany()
                        .HasForeignKey("SubmissionId");

                    b.HasOne("Surefire.Domain.Renewals.Models.TrackTask", "TrackTask")
                        .WithMany()
                        .HasForeignKey("TrackTaskId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedBy");

                    b.Navigation("Renewal");

                    b.Navigation("Submission");

                    b.Navigation("TrackTask");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.Submission", b =>
                {
                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Carrier")
                        .WithMany()
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Clients.Models.Lead", "Lead")
                        .WithMany("Submissions")
                        .HasForeignKey("LeadId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Shared.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Renewals.Models.Renewal", "Renewal")
                        .WithMany("Submissions")
                        .HasForeignKey("RenewalId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Wholesaler")
                        .WithMany()
                        .HasForeignKey("WholesalerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Carrier");

                    b.Navigation("Lead");

                    b.Navigation("Product");

                    b.Navigation("Renewal");

                    b.Navigation("Wholesaler");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.SubmissionTask", b =>
                {
                    b.HasOne("Surefire.Domain.Renewals.Models.Submission", "Submission")
                        .WithMany("SubmissionTasks")
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Submission");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TaskGroupTaskMaster", b =>
                {
                    b.HasOne("Surefire.Domain.Renewals.Models.TaskGroup", "TaskGroup")
                        .WithMany("TaskGroupTaskMasters")
                        .HasForeignKey("TaskGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Renewals.Models.TaskMaster", "TaskMaster")
                        .WithMany("TaskGroupTaskMasters")
                        .HasForeignKey("TaskMasterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TaskGroup");

                    b.Navigation("TaskMaster");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TaskMaster", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "DefaultAssignedTo")
                        .WithMany()
                        .HasForeignKey("DefaultAssignedToId");

                    b.Navigation("DefaultAssignedTo");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TaskMasterSubTask", b =>
                {
                    b.HasOne("Surefire.Domain.Renewals.Models.TaskMaster", "ParentTaskMaster")
                        .WithMany("SubTaskLinks")
                        .HasForeignKey("ParentTaskMasterId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Renewals.Models.TaskMaster", "SubTaskMaster")
                        .WithMany("ParentLinks")
                        .HasForeignKey("SubTaskMasterId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ParentTaskMaster");

                    b.Navigation("SubTaskMaster");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TrackTask", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Renewals.Models.TrackTask", "ParentTask")
                        .WithMany("Subtasks")
                        .HasForeignKey("ParentTaskId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Renewals.Models.Renewal", "Renewal")
                        .WithMany("TrackTasks")
                        .HasForeignKey("RenewalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssignedTo");

                    b.Navigation("ParentTask");

                    b.Navigation("Renewal");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Driver", b =>
                {
                    b.HasOne("Surefire.Domain.Policies.Models.Policy", null)
                        .WithMany("Drivers")
                        .HasForeignKey("PolicyId");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Location", b =>
                {
                    b.HasOne("Surefire.Domain.Shared.Models.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Clients.Models.Client", null)
                        .WithMany("Locations")
                        .HasForeignKey("ClientId");

                    b.Navigation("Address");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Vehicle", b =>
                {
                    b.HasOne("Surefire.Domain.Policies.Models.Policy", null)
                        .WithMany("Vehicles")
                        .HasForeignKey("PolicyId");
                });

            modelBuilder.Entity("Surefire.Domain.Accounting.Models.Settlement", b =>
                {
                    b.Navigation("SettlementItems");
                });

            modelBuilder.Entity("Surefire.Domain.Attachments.Models.AttachmentGroup", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("Surefire.Domain.Attachments.Models.Folder", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("Surefire.Domain.Carriers.Models.Carrier", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Contacts");

                    b.Navigation("Credentials");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.Client", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Certificates");

                    b.Navigation("ClientNotes");

                    b.Navigation("Contacts");

                    b.Navigation("FormDocs");

                    b.Navigation("Locations");

                    b.Navigation("Policies");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.Lead", b =>
                {
                    b.Navigation("FormDocs");

                    b.Navigation("LeadNotes");

                    b.Navigation("Submissions");
                });

            modelBuilder.Entity("Surefire.Domain.Contacts.Models.Contact", b =>
                {
                    b.Navigation("EmailAddresses");

                    b.Navigation("PhoneNumbers");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.FormDoc", b =>
                {
                    b.Navigation("FormDocRevisions");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Claim", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Policy", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("AutoCoverage");

                    b.Navigation("Claims");

                    b.Navigation("Drivers");

                    b.Navigation("GeneralLiabilityCoverage");

                    b.Navigation("Losses");

                    b.Navigation("PropertyCoverage");

                    b.Navigation("RatingBases");

                    b.Navigation("Renewals");

                    b.Navigation("Settlements");

                    b.Navigation("UmbrellaCoverage");

                    b.Navigation("Vehicles");

                    b.Navigation("WorkCompCoverage");
                });

            modelBuilder.Entity("Surefire.Domain.Proposals.Proposal", b =>
                {
                    b.Navigation("ProposalFeeItems");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.Renewal", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Settlements");

                    b.Navigation("Submissions");

                    b.Navigation("TrackTasks");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.Submission", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("SubmissionTasks");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TaskGroup", b =>
                {
                    b.Navigation("TaskGroupTaskMasters");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TaskMaster", b =>
                {
                    b.Navigation("MasterSubTasks");

                    b.Navigation("ParentLinks");

                    b.Navigation("SubTaskLinks");

                    b.Navigation("TaskGroupTaskMasters");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TrackTask", b =>
                {
                    b.Navigation("Subtasks");
                });
#pragma warning restore 612, 618
        }
    }
}
