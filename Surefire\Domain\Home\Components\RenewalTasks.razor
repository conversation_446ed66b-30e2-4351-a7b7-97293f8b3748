﻿@using Surefire.Domain.Renewals.ViewModels
@using Surefire.Domain.Renewals.Services
@using Syncfusion.Blazor.Notifications
@inject RenewalService RenewalService
@inject HomeService HomeService
@inject Surefire.Domain.Clients.Services.ClientStateService ClientStateService
@inject Surefire.Domain.Shared.Services.StateService StateService

<div class="sectiontitletab background-@bgColor">@tabTitle</div>
<div class="home-box background-@bgColor">
    <table id="tasktable" cellspacing="0" class="ttable">
        <thead class="tbg background-@bgColor">
            <tr>
                <th>Policy</th>
                <th width="40%">Client</th>
                <th>Task</th>
                <th>Priority</th>
            </tr>
        </thead>
        <tbody class="tbody">
            @if (tasks == null || isLoading)
            {
            
                for (var i = 0; i < 8; i++)
                {
                <tr class="trow">
                    <td colspan="4"><SfSkeleton Shape=SkeletonType.Rectangle Width="100%" Height="20px" CssClass="e-customize" Visible="true"></SfSkeleton></td>
                    </tr>
                }

            }
            else if (!tasks.Any())
            {
                <tr>
                    <td colspan="4"><div class="nothingfound">Nothing found here.</div></td>
                </tr>
            }
            else
            {
                @foreach (var task in tasks)
                {
                    <tr class="trow @(task.Highlighted ? "highlighted" : "")">
                        <td width="85"><span class="hprod hprodbg-@bgColor">@StringHelper.GetSafeSubstring(task.PolicyProduct, 0, 3)</span><span class="hexp">@task.RenewalDate.ToString("MM/dd")</span></td>
                        <td class="tcname ellipsis"><a class="sf-link" @onclick="() => HandleClientClick(task.ClientId)" @onclick:preventDefault="true" style="cursor: pointer;">@task.ClientName</a> </td>
                        <td class="ttname ellipsis"><a class="rentask-link" @onclick="() => HandleRenewalClick(task.RenewalId)">@task.TaskName</a></td>
                        <td width="100" class="ttpri ellipsis">@((MarkupString)task.Priority)</td>
                    </tr>
                }
            }
        </tbody>
    </table>
</div>

@code {
    [Parameter]
    public string tabTitle { get; set; } = "";

    [Parameter]
    public string bgColor { get; set; } = "default";

    [Parameter]
    public string taskType { get; set; } = "tasks"; // "tasks" or "subtasks"

    private List<HomePageTasksViewModel> tasks = new();
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadTaskData();
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadTaskData();
    }

    private async Task LoadTaskData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            if (taskType.ToLower() == "subtasks")
            {
                tasks = await HomeService.GetHomePageSubTasksAsync();
            }
            else
            {
                tasks = await HomeService.GetHomePageTasksAsync();
            }
        }
        catch (Exception ex)
        {
            tasks = new();
            // Optionally log the error
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task HandleClientClick(int clientId)
    {
        // Set the client state service to remember which client was selected
        ClientStateService.SelectedClientId = clientId;
        ClientStateService.ActiveTab = "tab-1";
        await ClientStateService.SaveStateAsync();
        
        Navigation.NavigateTo($"/Clients/{clientId}");
    }

    protected async Task HandleRenewalClick(int renewalId)
    {
        // Set the state service to remember which renewal was selected
        StateService.HtmlRenewalId = renewalId;
        StateService.HtmlView = "details";
        StateService.HtmlTab = "tab-1";
        
        var ren = await RenewalService.GetRenewalByIdAsync(renewalId);
        Navigation.NavigateTo($"/Renewals/Details/{renewalId}");
    }
}