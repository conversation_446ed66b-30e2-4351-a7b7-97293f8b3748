{"Form_CompletionDate_A": "", "Producer_FullName_A": "", "Producer_MailingAddress_LineOne_A": "", "Producer_MailingAddress_LineTwo_A": "", "Producer_MailingAddress_CityName_A": "", "Producer_MailingAddress_StateOrProvinceCode_A": "", "Producer_MailingAddress_PostalCode_A": "", "Producer_ContactPerson_FullName_A": "", "Producer_CustomerServiceRepresentative_FullName_A": "", "Producer_ContactPerson_PhoneNumber_A": "", "Producer_ContactPerson_CellPhoneNumber_A": "", "Producer_FaxNumber_A": "", "Producer_ContactPerson_EmailAddress_A": "", "Insurer_ProducerIdentifier_A": "", "Insurer_SubProducerIdentifier_A": "", "Producer_CustomerIdentifier_A": "", "Insurer_FullName_A": "", "Insurer_Underwriter_FullName_A": "", "NamedInsured_FullName_A": "", "NamedInsured_Primary_PhoneNumber_A": "", "NamedInsured_Secondary_PhoneNumber_A": "", "NamedInsured_MailingAddress_LineOne_A": "", "NamedInsured_MailingAddress_LineTwo_A": "", "NamedInsured_MailingAddress_CityName_A": "", "NamedInsured_MailingAddress_StateOrProvinceCode_A": "", "NamedInsured_MailingAddress_PostalCode_A": "", "NamedInsured_InBusinessYearCount_A": "", "NamedInsured_SICCode_A": "", "NamedInsured_NAICSCode_A": "", "NamedInsured_Primary_WebsiteAddress_A": "", "NamedInsured_Primary_EmailAddress_A": "", "NamedInsured_LegalEntity_SoleProprietorIndicator_A": "Off", "NamedInsured_LegalEntity_PartnershipIndicator_A": "Off", "NamedInsured_LegalEntity_CorporationIndicator_A": "Off", "NamedInsured_LegalEntity_SubchapterSCorporationIndicator_A": "Off", "NamedInsured_LegalEntity_LimitedLiabilityCorporationIndicator_A": "Off", "NamedInsured_LegalEntity_JointVentureIndicator_A": "Off", "NamedInsured_LegalEntity_TrustIndicator_A": "Off", "NamedInsured_LegalEntity_OtherIndicator_A": "Off", "NamedInsured_LegalEntity_OtherDescription_A": "", "NamedInsured_LegalEntity_UnincorporatedAssociationIndicator_A": "Off", "NamedInsured_CreditBureauName_A": "", "NamedInsured_CreditBureauIdentifier_A": "", "NamedInsured_TaxIdentifier_A": "", "NamedInsured_NCCIRiskIdentifier_A": "", "NamedInsured_RatingBureauIdentifier_A": "", "Policy_Status_QuoteIndicator_A": "Off", "Policy_Status_BoundIndicator_A": "Off", "Binder_EffectiveDate_A": "", "Policy_Status_AssignedRiskIndicator_A": "Off", "Policy_Status_IssueIndicator_A": "Off", "Policy_Payment_ProducerBillIndicator_A": "Off", "Policy_Payment_DirectBillIndicator_A": "Off", "Policy_Payment_AnnualIndicator_A": "Off", "Policy_Payment_SemiAnnualIndicator_A": "Off", "Policy_Payment_QuarterlyIndicator_A": "Off", "Policy_Payment_OtherIndicator_A": "Off", "Policy_Payment_PaymentScheduleCode_A": "", "Policy_Payment_DownPaymentPercent_A": "", "Policy_Audit_AtExpirationIndicator_A": "Off", "Policy_Audit_SemiAnnualIndicator_A": "Off", "Policy_Audit_QuarterlyIndicator_A": "Off", "Policy_Audit_MonthlyIndicator_A": "Off", "Policy_Audit_OtherIndicator_A": "Off", "Policy_Audit_FrequencyCode_A": "", "Location_ProducerIdentifier_A": "", "Location_HighestFloorCount_A": "", "Location_PhysicalAddress_LineOne_A": "", "Location_PhysicalAddress_LineTwo_A": "", "Location_PhysicalAddress_CityName_A": "", "Location_PhysicalAddress_CountyName_A": "", "Location_PhysicalAddress_StateOrProvinceCode_A": "", "Location_PhysicalAddress_PostalCode_A": "", "Location_ProducerIdentifier_B": "", "Location_HighestFloorCount_B": "", "Location_PhysicalAddress_LineOne_B": "", "Location_PhysicalAddress_LineTwo_B": "", "Location_PhysicalAddress_CityName_B": "", "Location_PhysicalAddress_CountyName_B": "", "Location_PhysicalAddress_StateOrProvinceCode_B": "", "Location_PhysicalAddress_PostalCode_B": "", "Location_ProducerIdentifier_C": "", "Location_HighestFloorCount_C": "", "Location_PhysicalAddress_LineOne_C": "", "Location_PhysicalAddress_LineTwo_C": "", "Location_PhysicalAddress_CityName_C": "", "Location_PhysicalAddress_CountyName_C": "", "Location_PhysicalAddress_StateOrProvinceCode_C": "", "Location_PhysicalAddress_PostalCode_C": "", "Policy_EffectiveDate_A": "", "Policy_ExpirationDate_A": "", "Policy_RatingEffectiveDate_A": "", "Policy_NormalAnniversaryRatingDate_A": "", "Policy_ParticipatingIndicator_A": "Off", "Policy_NonParticipatingIndicator_A": "Off", "Policy_RetrospectiveRatingPlan_A": "", "WorkersCompensation_PartOne_StateOrProvinceCode_A": "", "WorkersCompensation_PartOne_StateOrProvinceCode_B": "", "WorkersCompensation_PartOne_StateOrProvinceCode_C": "", "WorkersCompensation_PartOne_StateOrProvinceCode_D": "", "WorkersCompensation_PartOne_StateOrProvinceCode_E": "", "WorkersCompensation_PartOne_StateOrProvinceCode_F": "", "WorkersCompensation_PartOne_StateOrProvinceCode_G": "", "WorkersCompensation_PartOne_StateOrProvinceCode_H": "", "WorkersCompensation_PartOne_StateOrProvinceCode_I": "", "WorkersCompensation_PartOne_StateOrProvinceCode_J": "", "WorkersCompensationEmployersLiability_EmployersLiability_EachAccidentLimitAmount_A": "", "WorkersCompensationEmployersLiability_EmployersLiability_DiseasePolicyLimitAmount_A": "", "WorkersCompensationEmployersLiability_EmployersLiability_DiseaseEachEmployeeLimitAmount_A": "", "WorkersCompensation_PartThree_StateOrProvinceCode_A": "", "WorkersCompensation_PartThree_StateOrProvinceCode_B": "", "WorkersCompensation_PartThree_StateOrProvinceCode_C": "", "WorkersCompensation_PartThree_StateOrProvinceCode_D": "", "WorkersCompensation_PartThree_StateOrProvinceCode_E": "", "WorkersCompensation_PartThree_StateOrProvinceCode_F": "", "WorkersCompensation_PartThree_StateOrProvinceCode_G": "", "WorkersCompensation_PartThree_StateOrProvinceCode_H": "", "WorkersCompensation_PartThree_StateOrProvinceCode_I": "", "WorkersCompensation_PartThree_StateOrProvinceCode_J": "", "WorkersCompensation_DeductibleType_MedicalIndicator_A": "Off", "WorkersCompensation_DeductibleType_IndemnityIndicator_A": "Off", "WorkersCompensation_DeductibleType_OtherIndicator_A": "Off", "WorkersCompensation_DeductibleType_OtherDescription_A": "", "WorkersCompensation_DeductibleAmount_A": "", "WorkersCompensation_Coverage_USLHIndicator_A": "Off", "WorkersCompensation_Coverage_VoluntaryCompensationIndicator_A": "Off", "WorkersCompensation_Coverage_ForeignCoverageIndicator_A": "Off", "WorkersCompensation_Coverage_ManagedCareOptionIndicator_A": "Off", "WorkersCompensation_Coverage_OtherIndicator_A": "Off", "WorkersCompensation_Coverage_OtherDescription_A": "", "WorkersCompensation_Coverage_OtherIndicator_B": "Off", "WorkersCompensation_Coverage_OtherDescription_B": "", "WorkersCompensation_DividendOrSafetyPlan_A": "", "WorkersCompensation_AdditionalCompanyInformation_A": "", "WorkersCompensation_AdditionalCoverageEndorsementDescription_A": "", "WorkersCompensationLineOfBusiness_TotalEstimatedAnnualPremiumAllStatesAmount_A": "", "WorkersCompensationLineOfBusiness_TotalMinimumPremiumAllStatesAmount_A": "", "WorkersCompensationLineOfBusiness_TotalDepositPremiumAllStatesAmount_A": "", "NamedInsured_InspectionContact_FullName_A": "", "NamedInsured_InspectionContact_PhoneNumber_A": "", "NamedInsured_InspectionContact_CellPhoneNumber_A": "", "NamedInsured_InspectionContact_EmailAddress_A": "", "NamedInsured_AccountingContact_FullName_A": "", "NamedInsured_AccountingContact_PhoneNumber_A": "", "NamedInsured_AccountingContact_CellPhoneNumber_A": "", "NamedInsured_AccountingContact_EmailAddress_A": "", "NamedInsured_ClaimContact_FullName_A": "", "NamedInsured_ClaimContact_PhoneNumber_A": "", "NamedInsured_ClaimContact_CellPhoneNumber_A": "", "NamedInsured_ClaimContact_EmailAddress_A": "", "WorkersCompensation_Individual_StateOrProvinceCode_A": "", "WorkersCompensation_Individual_LocationProducerIdentifier_A": "", "WorkersCompensation_Individual_FullName_A": "", "WorkersCompensation_Individual_BirthDate_A": "", "WorkersCompensation_Individual_TitleRelationshipCode_A": "", "WorkersCompensation_Individual_OwnershipPercent_A": "", "WorkersCompensation_Individual_DutiesDescription_A": "", "WorkersCompensation_Individual_IncludedExcludedCode_A": "", "WorkersCompensation_Individual_RatingClassificationCode_A": "", "WorkersCompensation_Individual_RemunerationAmount_A": "", "WorkersCompensation_Individual_StateOrProvinceCode_B": "", "WorkersCompensation_Individual_LocationProducerIdentifier_B": "", "WorkersCompensation_Individual_FullName_B": "", "WorkersCompensation_Individual_BirthDate_B": "", "WorkersCompensation_Individual_TitleRelationshipCode_B": "", "WorkersCompensation_Individual_OwnershipPercent_B": "", "WorkersCompensation_Individual_DutiesDescription_B": "", "WorkersCompensation_Individual_IncludedExcludedCode_B": "", "WorkersCompensation_Individual_RatingClassificationCode_B": "", "WorkersCompensation_Individual_RemunerationAmount_B": "", "WorkersCompensation_Individual_StateOrProvinceCode_C": "", "WorkersCompensation_Individual_LocationProducerIdentifier_C": "", "WorkersCompensation_Individual_FullName_C": "", "WorkersCompensation_Individual_BirthDate_C": "", "WorkersCompensation_Individual_TitleRelationshipCode_C": "", "WorkersCompensation_Individual_OwnershipPercent_C": "", "WorkersCompensation_Individual_DutiesDescription_C": "", "WorkersCompensation_Individual_IncludedExcludedCode_C": "", "WorkersCompensation_Individual_RatingClassificationCode_C": "", "WorkersCompensation_Individual_RemunerationAmount_C": "", "WorkersCompensation_Individual_StateOrProvinceCode_D": "", "WorkersCompensation_Individual_LocationProducerIdentifier_D": "", "WorkersCompensation_Individual_FullName_D": "", "WorkersCompensation_Individual_BirthDate_D": "", "WorkersCompensation_Individual_TitleRelationshipCode_D": "", "WorkersCompensation_Individual_OwnershipPercent_D": "", "WorkersCompensation_Individual_DutiesDescription_D": "", "WorkersCompensation_Individual_IncludedExcludedCode_D": "", "WorkersCompensation_Individual_RatingClassificationCode_D": "", "WorkersCompensation_Individual_RemunerationAmount_D": "", "WorkersCompensation_RateState_PageNumber_A": "", "WorkersCompensation_RateState_TotalPageNumber_A": "", "WorkersCompensation_RateState_StateOrProvinceName_A": "", "WorkersCompensation_RateClass_LocationProducerIdentifier_A": "", "WorkersCompensation_RateClass_ClassificationCode_A": "", "WorkersCompensation_RateClass_DescriptionCode_A": "", "WorkersCompensation_RateClass_DutiesDescription_A": "", "WorkersCompensation_RateClass_FullTimeEmployeeCount_A": "", "WorkersCompensation_RateClass_PartTimeEmployeeCount_A": "", "WorkersCompensation_RateClass_SICCode_A": "", "WorkersCompensation_RateClass_NAICSCode_A": "", "WorkersCompensation_RateClass_RemunerationAmount_A": "", "WorkersCompensation_RateClass_Rate_A": "", "WorkersCompensation_RateClass_EstimatedManualPremiumAmount_A": "", "WorkersCompensation_RateClass_LocationProducerIdentifier_B": "", "WorkersCompensation_RateClass_ClassificationCode_B": "", "WorkersCompensation_RateClass_DescriptionCode_B": "", "WorkersCompensation_RateClass_DutiesDescription_B": "", "WorkersCompensation_RateClass_FullTimeEmployeeCount_B": "", "WorkersCompensation_RateClass_PartTimeEmployeeCount_B": "", "WorkersCompensation_RateClass_SICCode_B": "", "WorkersCompensation_RateClass_NAICSCode_B": "", "WorkersCompensation_RateClass_RemunerationAmount_B": "", "WorkersCompensation_RateClass_Rate_B": "", "WorkersCompensation_RateClass_EstimatedManualPremiumAmount_B": "", "WorkersCompensation_RateClass_LocationProducerIdentifier_C": "", "WorkersCompensation_RateClass_ClassificationCode_C": "", "WorkersCompensation_RateClass_DescriptionCode_C": "", "WorkersCompensation_RateClass_DutiesDescription_C": "", "WorkersCompensation_RateClass_FullTimeEmployeeCount_C": "", "WorkersCompensation_RateClass_PartTimeEmployeeCount_C": "", "WorkersCompensation_RateClass_SICCode_C": "", "WorkersCompensation_RateClass_NAICSCode_C": "", "WorkersCompensation_RateClass_RemunerationAmount_C": "", "WorkersCompensation_RateClass_Rate_C": "", "WorkersCompensation_RateClass_EstimatedManualPremiumAmount_C": "", "WorkersCompensation_RateClass_LocationProducerIdentifier_D": "", "WorkersCompensation_RateClass_ClassificationCode_D": "", "WorkersCompensation_RateClass_DescriptionCode_D": "", "WorkersCompensation_RateClass_DutiesDescription_D": "", "WorkersCompensation_RateClass_FullTimeEmployeeCount_D": "", "WorkersCompensation_RateClass_PartTimeEmployeeCount_D": "", "WorkersCompensation_RateClass_SICCode_D": "", "WorkersCompensation_RateClass_NAICSCode_D": "", "WorkersCompensation_RateClass_RemunerationAmount_D": "", "WorkersCompensation_RateClass_Rate_D": "", "WorkersCompensation_RateClass_EstimatedManualPremiumAmount_D": "", "WorkersCompensation_RateClass_LocationProducerIdentifier_E": "", "WorkersCompensation_RateClass_ClassificationCode_E": "", "WorkersCompensation_RateClass_DescriptionCode_E": "", "WorkersCompensation_RateClass_DutiesDescription_E": "", "WorkersCompensation_RateClass_FullTimeEmployeeCount_E": "", "WorkersCompensation_RateClass_PartTimeEmployeeCount_E": "", "WorkersCompensation_RateClass_SICCode_E": "", "WorkersCompensation_RateClass_NAICSCode_E": "", "WorkersCompensation_RateClass_RemunerationAmount_E": "", "WorkersCompensation_RateClass_Rate_E": "", "WorkersCompensation_RateClass_EstimatedManualPremiumAmount_E": "", "WorkersCompensation_RateClass_LocationProducerIdentifier_F": "", "WorkersCompensation_RateClass_ClassificationCode_F": "", "WorkersCompensation_RateClass_DescriptionCode_F": "", "WorkersCompensation_RateClass_DutiesDescription_F": "", "WorkersCompensation_RateClass_FullTimeEmployeeCount_F": "", "WorkersCompensation_RateClass_PartTimeEmployeeCount_F": "", "WorkersCompensation_RateClass_SICCode_F": "", "WorkersCompensation_RateClass_NAICSCode_F": "", "WorkersCompensation_RateClass_RemunerationAmount_F": "", "WorkersCompensation_RateClass_Rate_F": "", "WorkersCompensation_RateClass_EstimatedManualPremiumAmount_F": "", "WorkersCompensation_RateClass_LocationProducerIdentifier_G": "", "WorkersCompensation_RateClass_ClassificationCode_G": "", "WorkersCompensation_RateClass_DescriptionCode_G": "", "WorkersCompensation_RateClass_DutiesDescription_G": "", "WorkersCompensation_RateClass_FullTimeEmployeeCount_G": "", "WorkersCompensation_RateClass_PartTimeEmployeeCount_G": "", "WorkersCompensation_RateClass_SICCode_G": "", "WorkersCompensation_RateClass_NAICSCode_G": "", "WorkersCompensation_RateClass_RemunerationAmount_G": "", "WorkersCompensation_RateClass_Rate_G": "", "WorkersCompensation_RateClass_EstimatedManualPremiumAmount_G": "", "WorkersCompensation_RateClass_LocationProducerIdentifier_H": "", "WorkersCompensation_RateClass_ClassificationCode_H": "", "WorkersCompensation_RateClass_DescriptionCode_H": "", "WorkersCompensation_RateClass_DutiesDescription_H": "", "WorkersCompensation_RateClass_FullTimeEmployeeCount_H": "", "WorkersCompensation_RateClass_PartTimeEmployeeCount_H": "", "WorkersCompensation_RateClass_SICCode_H": "", "WorkersCompensation_RateClass_NAICSCode_H": "", "WorkersCompensation_RateClass_RemunerationAmount_H": "", "WorkersCompensation_RateClass_Rate_H": "", "WorkersCompensation_RateClass_EstimatedManualPremiumAmount_H": "", "WorkersCompensation_RateClass_LocationProducerIdentifier_I": "", "WorkersCompensation_RateClass_ClassificationCode_I": "", "WorkersCompensation_RateClass_DescriptionCode_I": "", "WorkersCompensation_RateClass_DutiesDescription_I": "", "WorkersCompensation_RateClass_FullTimeEmployeeCount_I": "", "WorkersCompensation_RateClass_PartTimeEmployeeCount_I": "", "WorkersCompensation_RateClass_SICCode_I": "", "WorkersCompensation_RateClass_NAICSCode_I": "", "WorkersCompensation_RateClass_RemunerationAmount_I": "", "WorkersCompensation_RateClass_Rate_I": "", "WorkersCompensation_RateClass_EstimatedManualPremiumAmount_I": "", "WorkersCompensation_RateClass_LocationProducerIdentifier_J": "", "WorkersCompensation_RateClass_ClassificationCode_J": "", "WorkersCompensation_RateClass_DescriptionCode_J": "", "WorkersCompensation_RateClass_DutiesDescription_J": "", "WorkersCompensation_RateClass_FullTimeEmployeeCount_J": "", "WorkersCompensation_RateClass_PartTimeEmployeeCount_J": "", "WorkersCompensation_RateClass_SICCode_J": "", "WorkersCompensation_RateClass_NAICSCode_J": "", "WorkersCompensation_RateClass_RemunerationAmount_J": "", "WorkersCompensation_RateClass_Rate_J": "", "WorkersCompensation_RateClass_EstimatedManualPremiumAmount_J": "", "WorkersCompensation_RateClass_LocationProducerIdentifier_K": "", "WorkersCompensation_RateClass_ClassificationCode_K": "", "WorkersCompensation_RateClass_DescriptionCode_K": "", "WorkersCompensation_RateClass_DutiesDescription_K": "", "WorkersCompensation_RateClass_FullTimeEmployeeCount_K": "", "WorkersCompensation_RateClass_PartTimeEmployeeCount_K": "", "WorkersCompensation_RateClass_SICCode_K": "", "WorkersCompensation_RateClass_NAICSCode_K": "", "WorkersCompensation_RateClass_RemunerationAmount_K": "", "WorkersCompensation_RateClass_Rate_K": "", "WorkersCompensation_RateClass_EstimatedManualPremiumAmount_K": "", "WorkersCompensation_RateClass_LocationProducerIdentifier_L": "", "WorkersCompensation_RateClass_ClassificationCode_L": "", "WorkersCompensation_RateClass_DescriptionCode_L": "", "WorkersCompensation_RateClass_DutiesDescription_L": "", "WorkersCompensation_RateClass_FullTimeEmployeeCount_L": "", "WorkersCompensation_RateClass_PartTimeEmployeeCount_L": "", "WorkersCompensation_RateClass_SICCode_L": "", "WorkersCompensation_RateClass_NAICSCode_L": "", "WorkersCompensation_RateClass_RemunerationAmount_L": "", "WorkersCompensation_RateClass_Rate_L": "", "WorkersCompensation_RateClass_EstimatedManualPremiumAmount_L": "", "WorkersCompensation_RateClass_LocationProducerIdentifier_M": "", "WorkersCompensation_RateClass_ClassificationCode_M": "", "WorkersCompensation_RateClass_DescriptionCode_M": "", "WorkersCompensation_RateClass_DutiesDescription_M": "", "WorkersCompensation_RateClass_FullTimeEmployeeCount_M": "", "WorkersCompensation_RateClass_PartTimeEmployeeCount_M": "", "WorkersCompensation_RateClass_SICCode_M": "", "WorkersCompensation_RateClass_NAICSCode_M": "", "WorkersCompensation_RateClass_RemunerationAmount_M": "", "WorkersCompensation_RateClass_Rate_M": "", "WorkersCompensation_RateClass_EstimatedManualPremiumAmount_M": "", "WorkersCompensation_RateClass_LocationProducerIdentifier_N": "", "WorkersCompensation_RateClass_ClassificationCode_N": "", "WorkersCompensation_RateClass_DescriptionCode_N": "", "WorkersCompensation_RateClass_DutiesDescription_N": "", "WorkersCompensation_RateClass_FullTimeEmployeeCount_N": "", "WorkersCompensation_RateClass_PartTimeEmployeeCount_N": "", "WorkersCompensation_RateClass_SICCode_N": "", "WorkersCompensation_RateClass_NAICSCode_N": "", "WorkersCompensation_RateClass_RemunerationAmount_N": "", "WorkersCompensation_RateClass_Rate_N": "", "WorkersCompensation_RateClass_EstimatedManualPremiumAmount_N": "", "WorkersCompensation_RateState_StateOrProvinceName_A1": "", "WorkersCompensationStateCoverage_TotalFactoredPremiumAmount_A": "", "WorkersCompensationStateCoverage_IncreasedLimits_ModificationFactor_A": "", "WorkersCompensationStateCoverage_IncreasedLimits_FactoredPremiumAmount_A": "", "WorkersCompensationStateCoverage_Deductible_ModificationFactor_A": "", "WorkersCompensationStateCoverage_Deductible_FactoredPremiumAmount_A": "", "WorkersCompensationStateCoverage_ExperienceOrMerit_ModificationFactor_A": "", "WorkersCompensationStateCoverage_ExperienceOrMerit_FactoredPremiumAmount_A": "", "WorkersCompensationStateCoverage_Terrorism_PremiumAmount_A": "", "WorkersCompensationStateCoverage_Catastrophe_PremiumAmount_A": "", "WorkersCompensationStateCoverage_AssignedRiskSurcharge_ModificationFactor_A": "", "WorkersCompensationStateCoverage_AssignedRiskSurcharge_FactoredPremiumAmount_A": "", "WorkersCompensationStateCoverage_AssignedRiskAdditionalPremium_ModificationFactor_A": "", "WorkersCompensationStateCoverage_AssignedRiskAdditionalPremium_FactoredPremiumAmount_A": "", "WorkersCompensationStateCoverage_Other_CoverageDescription_A": "", "WorkersCompensationStateCoverage_Other_ModificationFactor_A": "", "WorkersCompensationStateCoverage_Other_FactoredPremiumAmount_A": "", "WorkersCompensationStateCoverage_ScheduleRating_ModificationFactor_A": "", "WorkersCompensationStateCoverage_ScheduleRating_FactoredPremiumAmount_A": "", "WorkersCompensationStateCoverage_ContractingClassPremiumAdjustmentProgram_ModificationFactor_A": "", "WorkersCompensationStateCoverage_ContractingClassPremiumAdjustmentProgram_FactoredPremiumAmount_A": "", "WorkersCompensationStateCoverage_StandardPremium_ModificationFactor_A": "", "WorkersCompensationStateCoverage_StandardPremium_FactoredPremiumAmount_A": "", "WorkersCompensationStateCoverage_PremiumDiscount_ModificationFactor_A": "", "WorkersCompensationStateCoverage_PremiumDiscount_FactoredPremiumAmount_A": "", "WorkersCompensationStateCoverage_ExpenseConstant_PremiumAmount_A": "", "WorkersCompensationStateCoverage_TaxesFeeAssessment_PremiumAmount_A": "", "WorkersCompensationStateCoverage_Other_CoverageDescription_B": "", "WorkersCompensationStateCoverage_Other_ModificationFactor_B": "", "WorkersCompensationStateCoverage_Other_FactoredPremiumAmount_B": "", "WorkersCompensation_RateState_TotalEstimatedAnnualPremiumAmount_A": "", "WorkersCompensation_RateState_MinimumPremiumAmount_A": "", "WorkersCompensation_RateState_DepositPremiumAmount_A": "", "WorkersCompensation_RateState_RemarkText_A": "", "WorkersCompensationLineOfBusiness_Attachment_LossRunIndicator_A": "Off", "PriorCoverage_EffectiveYear_A": "", "PriorCoverage_InsurerFullName_A": "", "PriorCoverage_PolicyNumberIdentifier_A": "", "PriorCoverage_TotalPremiumAmount_A": "", "PriorCoverage_ModificationFactor_A": "", "LossHistory_ClaimCount_A": "", "LossHistory_PaidAmount_A": "", "LossHistory_ReservedAmount_A": "", "PriorCoverage_EffectiveYear_B": "", "PriorCoverage_InsurerFullName_B": "", "PriorCoverage_PolicyNumberIdentifier_B": "", "PriorCoverage_TotalPremiumAmount_B": "", "PriorCoverage_ModificationFactor_B": "", "LossHistory_ClaimCount_B": "", "LossHistory_PaidAmount_B": "", "LossHistory_ReservedAmount_B": "", "PriorCoverage_EffectiveYear_C": "", "PriorCoverage_InsurerFullName_C": "", "PriorCoverage_PolicyNumberIdentifier_C": "", "PriorCoverage_TotalPremiumAmount_C": "", "PriorCoverage_ModificationFactor_C": "", "LossHistory_ClaimCount_C": "", "LossHistory_PaidAmount_C": "", "LossHistory_ReservedAmount_C": "", "PriorCoverage_EffectiveYear_D": "", "PriorCoverage_InsurerFullName_D": "", "PriorCoverage_PolicyNumberIdentifier_D": "", "PriorCoverage_TotalPremiumAmount_D": "", "PriorCoverage_ModificationFactor_D": "", "LossHistory_ClaimCount_D": "", "LossHistory_PaidAmount_D": "", "LossHistory_ReservedAmount_D": "", "PriorCoverage_EffectiveYear_E": "", "PriorCoverage_InsurerFullName_E": "", "PriorCoverage_PolicyNumberIdentifier_E": "", "PriorCoverage_TotalPremiumAmount_E": "", "PriorCoverage_ModificationFactor_E": "", "LossHistory_ClaimCount_E": "", "LossHistory_PaidAmount_E": "", "LossHistory_ReservedAmount_E": "", "CommercialPolicy_OperationsDescription_A": "", "WorkersCompensationLineOfBusiness_Question_ABECode_A": "", "WorkersCompensationLineOfBusiness_ApplicantOwnLeaseAircraftOrWatercraftExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_ACDCode_A": "", "WorkersCompensationLineOfBusiness_PastPresentDiscontinuedOperationsHazardousMaterialExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_AADCode_A": "", "WorkersCompensationLineOfBusiness_WorkPerformedUndergroundOrAboveFifteenFeetExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_KARCode_A": "", "WorkersCompensationLineOfBusiness_WorkPerformedOnVesselsDocksBridgesExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_KASCode_A": "", "WorkersCompensationLineOfBusiness_ApplicantEngagedAnyOtherTypeOfBusinessExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_KATCode_A": "", "WorkersCompensationLineOfBusiness_SubcontractorsUsedExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_KAUCode_A": "", "WorkersCompensationLineOfBusiness_AnyWorkSubletWithoutCertificatesOfInsuranceExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_ABCCode_A": "", "WorkersCompensationLineOfBusiness_WrittenSafetyProgramInOperationExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_ABICode_A": "", "WorkersCompensationLineOfBusiness_AnyGroupTransportationProvidedExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_AAECode_A": "", "WorkersCompensationLineOfBusiness_AnyEmployeesUnderSixteenOrOverSixtyYearsExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_KAVCode_A": "", "WorkersCompensationLineOfBusiness_AnySeasonalEmployeesExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_AAFCode_A": "", "WorkersCompensationLineOfBusiness_VolunteerOrDonatedLabourExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_ABJCode_A": "", "WorkersCompensationLineOfBusiness_EmployeesWithPhysicalHandicapsExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_ABHCode_A": "", "WorkersCompensationLineOfBusiness_EmployeesTravelOutOfStateExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_AABCode_A": "", "WorkersCompensationLineOfBusiness_AthleticTeamsSponsoredExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_ACBCode_A": "", "WorkersCompensationLineOfBusiness_PhysicalsRequiredAfterOffersOfEmploymentAreMadeExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_ABACode_A": "", "WorkersCompensationLineOfBusiness_OtherInsuranceWithThisInsurerExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_AAICode_A": "", "WorkersCompensationLineOfBusiness_PriorCoverageDeclinedCancelledNonRenewedLastThreeYearsExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_ABFCode_A": "", "WorkersCompensationLineOfBusiness_EmployeeHealthPlansProvidedExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_KAWCode_A": "", "WorkersCompensationLineOfBusiness_AnyEmployeesPerformWorkForOtherBusinessesExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_AAGCode_A": "", "WorkersCompensationLineOfBusiness_LeaseEmployeesToOrFromOtherEmployersExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_ABGCode_A": "", "WorkersCompensationLineOfBusiness_Question_AtHomeEmployeeCount_A": "", "WorkersCompensationLineOfBusiness_EmployeesPredominantlyWorkAtHomeExplanation_A": "", "WorkersCompensationLineOfBusiness_Question_KAXCode_A": "", "WorkersCompensationLineOfBusiness_TaxLiensOrBankruptcyWithinLastFiveYearsExplanation_W": "", "WorkersCompensationLineOfBusiness_Question_KAYCode_A": "", "WorkersCompensationLineOfBusiness_UndisputedUnpaidWorkersCompensationPremiumDueExplanation_A": "", "Policy_InformationPracticesNoticeIndicator_A": "Off", "NamedInsured_Initials_A": "", "NamedInsured_Signature_A": "", "NamedInsured_SignatureDate_A": "", "Producer_AuthorizedRepresentative_Signature_A": "", "Producer_NationalIdentifier_A": ""}