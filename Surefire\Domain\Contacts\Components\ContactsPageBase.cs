// Domain/Contacts/Components/ContactsPageBase.cs
using Microsoft.AspNetCore.Components;
using Surefire.Domain.Contacts.Services;
using Surefire.Domain.Shared.Services;

namespace Surefire.Domain.Contacts.Components
{
    public abstract class ContactsPageBase : AppComponentBase
    {
        [Inject] protected ContactService ContactService { get; set; } = default!;
        [Inject] protected NavigationManager Navigation { get; set; } = default!;
        [CascadingParameter] public Action<string> UpdateHeader { get; set; }

        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();
            UpdateHeader?.Invoke("Contacts");
        }
    }
}