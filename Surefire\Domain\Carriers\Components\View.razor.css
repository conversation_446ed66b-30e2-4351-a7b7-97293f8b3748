﻿.page-content {
    padding-left: 17px;
    padding-top: 10px;
}

.sf-header {
    display: flex;
    justify-content: normal;
    flex-direction: row;
    /*background: linear-gradient(to bottom, rgba(249,249,249,1) 0%,rgba(234,234,234,1) 100%);*/
    background-color: #ffffff7e;
    padding-top: 12px;
    padding-bottom: 8px;
    padding-left: 15px;
    padding-right: 15px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.client-header {
    font-family: "montserrat", sans-serif;
    font-weight: 800;
    font-style: normal;
    font-size: 2.5em;
    flex: 2;
    line-height: 30px;
}

    .client-header h1 {
        font-family: "montserrat", sans-serif;
        font-weight: 800;
        font-style: normal;
        font-size: 2.5em;
        margin-bottom: 20px;
    }

.client-header-2 {
    flex: 1;
}

.client-header-3 {
    flex: 1;
    line-height: 20px;
    text-align: right;
    align-content: center;
}

.client-address {
    font-family: "montserrat", sans-serif;
    font-weight: 200;
    font-style: normal;
    font-size: .5em;
}

.m-h3 {
    font-family: "montserrat", sans-serif;
    font-weight: 100;
    font-style: normal;
    font-size: 2em;
    color: #a7a7a7;
    margin-bottom: 10px;
}

.carrier-contacts {
    font-size: .8em;
    width: 100%;
}

.logo-box {
    flex: 0 0 auto;
}

    .logo-box img {
        max-height: 70px !important;
    }

.sfc-top {
    font-family: "montserrat", sans-serif;
    background-color: #ffffff7e;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    z-index: 300;
    padding-bottom: 10px;
}

.sfc-header-col-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 200px;
    max-width: 50%;
    margin-left: 16px;
    flex: 1;
}

.sfc-header-col-phone {
    margin-left: auto;
}

.client-phone {
    float: right;
}

.client-phonenumber {
    font-weight: 300;
    font-style: normal;
    font-size: 2.5em;
    position: relative;
    top: -10px;
    left: -5px;
    color: #8d8d8d;
}

.div-section {
    padding: 10px;
    border-left: 5px solid #ccc;
    background-color: #ffffff43;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    border-top-right-radius: 8px;
    box-shadow:0px 3px 8px #ccc;
}

.mf-flextwocolumn-col {
    padding: 0px !important;
}

.txt-section {
    position: relative;
    top: 8px;
}

.txt-sm {
    font-size: .7em;
    position: relative;
    top: 2px;
}

.spc {
    margin-top: 20px;
}
.products-checklist {
    display: flex;
    flex-direction: column;
    gap: 0px;
    max-height: 300px;
    overflow-y: auto;
}

.product-item {
    display: flex;
    align-items: center;
    padding: 4px 0;
}

.specialty-indicator {
    margin-left: 8px;
    color: #ffa500;
    font-weight: bold;
}
.products-checklist-compact {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2px;
    max-height: 250px;
    overflow-y: auto;
    font-size: 11px;
    line-height: 1.2;
}

.product-item-compact {
    margin: 0;
    padding: 1px 2px;
}

.product-label {
    font-size: 11px;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 140px;
    display: inline-block;
}

.specialty-indicator-compact {
    margin-left: 3px;
    color: #ffa500;
    font-size: 10px;
}

.compact-checkbox .e-checkbox-wrapper {
    font-size: 11px !important;
    line-height: 1.2 !important;
    margin: 0 !important;
    padding: 0 !important;
}

.compact-checkbox .e-frame {
    width: 12px !important;
    height: 12px !important;
    margin-right: 4px !important;
}

.compact-checkbox .e-label {
    font-size: 11px !important;
    line-height: 1.2 !important;
    padding: 0 !important;
    margin: 0 !important;
}