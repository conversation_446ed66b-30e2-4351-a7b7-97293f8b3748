﻿namespace Surefire.Domain.Agents.Models
{
    public class AgentContext
    {
        public string UserId { get; set; }
        public IDictionary<string, object> Data { get; set; } = new Dictionary<string, object>();
        // Extend with properties as needed
    }

    public class AgentResult
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public object? Payload { get; set; }
    }

    /// <summary>
    /// Result of agent matching operation
    /// </summary>
    public class AgentMatchResult
    {
        public TaskAgentDefinition? Agent { get; set; }
        public double Confidence { get; set; }
    }

    /// <summary>
    /// Result of SQL validation
    /// </summary>
    public class SqlValidationResult
    {
        /// <summary>
        /// Whether the SQL is valid and safe to execute
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// List of validation errors
        /// </summary>
        public List<string> Errors { get; set; } = new();

        /// <summary>
        /// List of validation warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new();

        /// <summary>
        /// Whether the query is read-only
        /// </summary>
        public bool IsReadOnly { get; set; }

        /// <summary>
        /// Whether the query is safe to execute
        /// </summary>
        public bool IsSafeToExecute { get; set; }

        /// <summary>
        /// Estimated execution complexity (Low, Medium, High)
        /// </summary>
        public string Complexity { get; set; } = "Unknown";
    }
}
