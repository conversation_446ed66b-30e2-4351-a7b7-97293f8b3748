@namespace Surefire.Components.Layout
@implements IDisposable
@using Microsoft.FluentUI.AspNetCore.Components
@using Surefire.Domain.Shared.Services
@using Surefire.Domain.Renewals.Services
@using Surefire.Domain.Renewals.Models
@inject NavigationManager NavigationManager
@inject SharedService SharedService
@inject RenewalService RenewalService
@inject Surefire.Domain.Clients.Services.ClientStateService ClientStateService
@inject StateService _stateService
@inject IJSRuntime JSRuntime


<!-- Rollover NavTips -->
@foreach (var item in navItems)
{
    <div class="ro-container ro-@(item.Name.ToLower()) @(item.RolloverClass)">
        <span class="ro-mi-name">@item.Name</span>
    </div>
}

<!-- Dynamic Navbar Items -->
<div class="sidebar">
    @foreach (var item in navItems)
    {
        <div class="sfnav @(GetNavItemClass(item.Href))" @onmouseenter="@(e => NavRollOver("over", item.Name))" @onmouseleave="@(e => NavRollOver("out", item.Name))" @onclick="@(e => NavTo(item.Href))">
            <div class="nb-blueline@(GetNavItemClass(item.Href))"></div>
            <NavLink Id="@($"btn{item.Name}")" class="nav-link" href="@item.Href" Match="NavLinkMatch.Prefix">
                @if (item.UsesPngIcon)
                {
                    if (GetNavItemClass(item.Href) == "nb-active")
                    {
                        <img src="@item.ActivePngPath" alt="@item.Name" class="nav-icon" />
                    }
                    else
                    {
                        var roClass = "inactive-icon-" + item.RolloverClass;
                        <img src="@item.InactivePngPath" alt="@item.Name" class="nav-icon @roClass" />
                    }
                }
                else
                {
                    if (GetNavItemClass(item.Href) == "nb-active")
                    {
                        <FluentIcon Value="@(item.ActiveIcon())" />
                    }
                    else
                    {
                        var roClass = "inactive-icon-" + item.RolloverClass;
                        <FluentIcon Value="@(item.InactiveIcon())" Class="@roClass" />
                    }
                }
            </NavLink>
        </div>
        <br />
        @if (item.Name == "Renewals")
        {
            <text><hr><br /></text>
        }
    }
</div>

<style>
    :root .nav-link {
        padding: 0px !important;
        margin-left: 11px;
        margin-bottom: 2px;
    }
    
    .nav-icon {
        width: 24px;
        height: 24px;
    }
</style>
@code {
    private string? currentUrl;
    private List<NavItem> navItems;

    public string AgentsRolloverClass { get; set; } = "ro-off";
    public string ClientsRolloverClass { get; set; } = "ro-off";
    public string PoliciesRolloverClass { get; set; } = "ro-off";
    public string LocationsRolloverClass { get; set; } = "ro-off";
    public string ContactsRolloverClass { get; set; } = "ro-off";
    public string CarriersRolloverClass { get; set; } = "ro-off";
    public string RenewalsRolloverClass { get; set; } = "ro-off";
    public string LeadsRolloverClass { get; set; } = "ro-off";
    public string HomeRolloverClass { get; set; } = "ro-off";

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;

        navItems = new List<NavItem>
        {
            new NavItem("Home", "Home", () => new Icons.Color.Size24.Home(), () => new Icons.Color.Size24.Home()) 
            { 
                UsesPngIcon = true,
                ActivePngPath = "img/icons/home-sm-over.png",
                InactivePngPath = "img/icons/home-sm.png"
            },
            new NavItem("Clients", "Clients", () => new Icons.Color.Size24.Library(), () => new Icons.Color.Size24.Library())
            { 
                UsesPngIcon = true,
                ActivePngPath = "img/icons/clients-sm-over.png",
                InactivePngPath = "img/icons/clients-sm.png"
            },
            new NavItem("Renewals", "Renewals", () => new Icons.Color.Size24.History(), () => new Icons.Color.Size24.History())
            { 
                UsesPngIcon = true,
                ActivePngPath = "img/icons/renewals-sm-over.png",
                InactivePngPath = "img/icons/renewals-sm.png"
            },
            new NavItem("Leads", "Leads", () => new Icons.Filled.Size24.TargetArrow(), () => new Icons.Regular.Size24.TargetArrow()),
            new NavItem("Carriers", "Carriers", () => new Icons.Filled.Size24.BuildingRetailShield(), () => new Icons.Regular.Size24.BuildingRetailShield()),
            new NavItem("Contacts", "Contacts", () => new Icons.Filled.Size24.People(), () => new Icons.Regular.Size24.People()),
            new NavItem("Locations", "Locations", () => new Icons.Filled.Size24.Location(), () => new Icons.Regular.Size24.Location()),
            new NavItem("Policies", "Policies", () => new Icons.Filled.Size24.DocumentMultiple(), () => new Icons.Regular.Size24.DocumentMultiple()),
            new NavItem("Agents", "Agents", () => new Icons.Filled.Size24.Bot(), () => new Icons.Regular.Size24.Bot())
        };
    }
    private void NavRollOver(string whichway, string whatItem)
    {
        string rollclass = whichway == "over" ? "ro-on" : "ro-off";

        var navItem = navItems.FirstOrDefault(item => item.Name == whatItem);
        if (navItem != null)
        {
            navItem.RolloverClass = rollclass;
        }
    }
    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        StateHasChanged();
    }
    private string GetNavItemClass(string href)
    {
        if (string.IsNullOrEmpty(currentUrl) && href == "/")
        {
            return "";
        }
        else
        {
            if (currentUrl != null)
            {
                if (currentUrl.Contains(href, StringComparison.OrdinalIgnoreCase) || (currentUrl == "" && href == "Home"))
                {
                    return "nb-active";
                }
                else
                {
                    return "nb-inactive";
                }
            }
            else
            {
                return "nb-inactive";
            }
        }
    }
    private async Task NavTo(string whereto)
    {
        await TriggerFireshelfAnimation(whereto);

        if(whereto == "Clients")
        {
            var clientId = ClientStateService.SelectedClientId;
            ClientStateService.ActiveTab = "tab-1";
            NavigationManager.NavigateTo($"/Clients/{clientId}");
        }
        else if (whereto == "Renewals")
        { 
            if (!string.IsNullOrEmpty(currentUrl) && (currentUrl.Contains("/Renewals/Details/", StringComparison.OrdinalIgnoreCase) || currentUrl.Contains("Renewals/Details/", StringComparison.OrdinalIgnoreCase))) 
            {
                // Already on renewals details page so navigate to the renewals list
                NavigationManager.NavigateTo("/Renewals");
            }
            else if (_stateService.HtmlView == "details" && _stateService.HtmlRenewalId != 0)
            {
                // User was on renewals details page prior to this, so return to that page
                NavigationManager.NavigateTo($"/Renewals/Details/{_stateService.HtmlRenewalId}");  
            }
            else
            {
                // Go to the renewals calendar list
                NavigationManager.NavigateTo("/Renewals");
            }
        }
        else if (whereto == "Contacts")
        {
            if (_stateService.ContactId > 0)
            {
                NavigationManager.NavigateTo($"/Contacts/{_stateService.ContactId}");
            }
            else
            {
                NavigationManager.NavigateTo("/Contacts");
            }
        }
        else
        {
            NavigationManager.NavigateTo(whereto);
        }
    }
    private async Task TriggerFireshelfAnimation(string section)
    {
        try
        {
            if (section == "Home" || section == "Clients" || section == "Renewals")
            {
                await JSRuntime.InvokeVoidAsync("PlayTopBarVideoAsync", section);
            }
            else
            {
                // Show blank fireshelf
                await JSRuntime.InvokeVoidAsync("PlayTopBarVideoAsyncOther");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error playing top bar animation: {ex.Message}");
        }
    }
    private class NavItem
    // Define a class to represent navigation items
    {
        public string Name { get; set; }
        public string Href { get; set; }
        public Func<Icon> ActiveIcon { get; set; }
        public Func<Icon> InactiveIcon { get; set; }
        public string RolloverClass { get; set; } = "ro-off";
        public bool UsesPngIcon { get; set; } = false;
        public string ActivePngPath { get; set; } = "";
        public string InactivePngPath { get; set; } = "";

        public NavItem(string name, string href, Func<Icon> activeIcon, Func<Icon> inactiveIcon)
        {
            Name = name;
            Href = href;
            ActiveIcon = activeIcon;
            InactiveIcon = inactiveIcon;
        }
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}