using Surefire.Domain.Agents.Models;
using Surefire.Domain.Agents.Services;
using Surefire.Domain.Agents.TaskAgents;

namespace Surefire.Domain.Agents.Interfaces
{
    /// <summary>
    /// Main orchestration service for task agent execution
    /// Coordinates between AI chat, action buttons, parameter extraction, and agent execution
    /// </summary>
    public interface ITaskAgentOrchestrator
    {
        /// <summary>
        /// Process a natural language request from AI chat
        /// Handles intent detection, parameter extraction, clarification, and execution
        /// </summary>
        /// <param name="userInput">User's natural language input</param>
        /// <param name="userId">ID of the user making the request</param>
        /// <param name="sessionId">Session ID for conversation context</param>
        /// <param name="context">Additional context from the current page/state</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result indicating if agent was found, parameters validated, and execution result</returns>
        Task<TaskAgentIntentResult> ProcessChatRequestAsync(
            string userInput,
            string userId,
            string sessionId,
            Dictionary<string, object> context = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Execute an agent from an action button with pre-filled parameters
        /// </summary>
        /// <param name="agentId">ID of the agent to execute</param>
        /// <param name="parameters">Pre-filled parameters from the button context</param>
        /// <param name="userId">ID of the user clicking the button</param>
        /// <param name="pageContext">Context from the current page</param>
        /// <param name="skipConfirmation">Whether to skip confirmation if normally required</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Execution result</returns>
        Task<TaskAgentResult> ExecuteFromButtonAsync(
            string agentId,
            Dictionary<string, object> parameters,
            string userId,
            Dictionary<string, object> pageContext = null,
            bool skipConfirmation = false,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Continue execution after user provides clarification for missing parameters
        /// </summary>
        /// <param name="agentId">ID of the agent to execute</param>
        /// <param name="clarificationInput">User's clarification input</param>
        /// <param name="existingParameters">Parameters already validated</param>
        /// <param name="userId">User ID</param>
        /// <param name="sessionId">Session ID</param>
        /// <param name="context">Additional context</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Updated intent result, possibly ready for execution</returns>
        Task<TaskAgentIntentResult> ContinueWithClarificationAsync(
            string agentId,
            string clarificationInput,
            Dictionary<string, object> existingParameters,
            string userId,
            string sessionId,
            Dictionary<string, object> context = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Execute an agent that has been fully validated and is ready for execution
        /// </summary>
        /// <param name="agentId">ID of the agent to execute</param>
        /// <param name="validatedParameters">Fully validated parameters</param>
        /// <param name="executionContext">Context (AI chat or action button)</param>
        /// <param name="userId">User ID</param>
        /// <param name="sessionId">Session ID (optional)</param>
        /// <param name="pageContext">Page context (optional)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Execution result</returns>
        Task<TaskAgentResult> ExecuteValidatedAgentAsync(
            string agentId,
            Dictionary<string, object> validatedParameters,
            AgentExecutionContext executionContext,
            string userId,
            string sessionId = null,
            Dictionary<string, object> pageContext = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Get available agents for a specific context (for building action buttons)
        /// </summary>
        /// <param name="context">Execution context</param>
        /// <param name="category">Optional category filter</param>
        /// <returns>Available agents for the context</returns>
        IEnumerable<TaskAgentDefinition> GetAvailableAgents(
            AgentExecutionContext context,
            string category = null);

        /// <summary>
        /// Generate action button configuration for a specific agent
        /// </summary>
        /// <param name="agentId">Agent ID</param>
        /// <param name="pageContext">Current page context that provides parameters</param>
        /// <returns>Button configuration with pre-filled parameters</returns>
        Task<ActionButtonConfig?> GenerateActionButtonConfigAsync(
            string agentId,
            Dictionary<string, object> pageContext);

        /// <summary>
        /// Execute agent from AI chat input (backward compatibility method)
        /// </summary>
        /// <param name="userInput">User's natural language input</param>
        /// <param name="userId">ID of the user making the request</param>
        /// <param name="context">Additional context from the current page/state</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Execution result</returns>
        Task<TaskAgentResult> ExecuteFromChatAsync(
            string userInput,
            string userId,
            Dictionary<string, object>? context = null,
            CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// Configuration for an action button
    /// </summary>
    public class ActionButtonConfig
    {
        /// <summary>
        /// Agent ID to execute
        /// </summary>
        public string AgentId { get; set; } = string.Empty;

        /// <summary>
        /// Display text for the button
        /// </summary>
        public string ButtonText { get; set; } = string.Empty;

        /// <summary>
        /// Description/tooltip for the button
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// CSS classes for button styling
        /// </summary>
        public string CssClasses { get; set; } = string.Empty;

        /// <summary>
        /// Icon to display on the button
        /// </summary>
        public string? Icon { get; set; }

        /// <summary>
        /// Parameters that will be automatically filled from page context
        /// </summary>
        public Dictionary<string, object> PreFilledParameters { get; set; } = new();

        /// <summary>
        /// Whether all required parameters are available
        /// </summary>
        public bool IsReady { get; set; }

        /// <summary>
        /// List of missing parameters (if not ready)
        /// </summary>
        public List<string> MissingParameters { get; set; } = new();

        /// <summary>
        /// Whether this button should show a confirmation dialog
        /// </summary>
        public bool ShowConfirmation { get; set; }

        /// <summary>
        /// Confirmation message to show
        /// </summary>
        public string? ConfirmationMessage { get; set; }
    }
} 