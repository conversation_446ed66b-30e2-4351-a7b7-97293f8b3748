﻿.incomplete-container {
    background-color: #dfdfdf;
    color:#707070;
}

.policylistsmall-container {
}

.sectiontitletab {
    font-family: "montserrat", sans-serif;
    font-size: 1.2em;
    padding-top: 5px;
    padding-bottom: 3px;
    /*border-top-left-radius: 15px;
    border-top-right-radius: 15px;*/
    background: linear-gradient(to right, #8b8b8b 0%, #aaaaaa 87%, #bfbfbf 94%, #fff 100%);
    text-shadow: 1px 1px 3px #2b2b2b;
    color: #e6e6e6;
    text-align: center;
}
.prodmeme {
    background-color: #8b8b8b;
    color: #e0e0e0;
    text-shadow: 0px 0px 2px #000;
    padding-left: 3px;
    padding-top: 1px;
    padding-bottom: 1px;
    padding-right: 3px;
    font-size: .9em;
    font-weight: bold;
    min-width: 90px;
    font-family: "mono45-headline", monospace;
    font-weight: 500;
    font-style: normal;
    letter-spacing: 1.5px;
    letter-spacing: 1.5px;
    border-right: 1px solid #00000047;
    box-shadow: 5px 0px 10px #909090c1;
}
#incomplete-table {
    padding-top: 10px;
    border-right: 5px solid #aaaaaa;
    text-align: left;
    min-width: 250px;
    width: 100%;
    margin: 0;
    padding: 0;
    box-shadow: inset 0px 5px 7px #a1a1a1;
}

.ttable {
    text-align: left;
    width: 100%;
    margin: 0;
    padding: 0;
    border-collapse: collapse;
    font-size: 1em;
}

tr .trow2 {
    display: block;
    margin-top: 0px !important;
    padding: 0px;
}
.trow2 td {
    padding:0px;
}
.tcname {
    width: 200px;
    height: 10px;
    font-size: .9em;
}

.prodinc {
    background-color: #3d3d3d;
    color: #bebebe;
    padding: 2px 5px;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    font-size: .9em;
    font-weight: bold;
    min-width: 90px;
    font-family: "mono45-headline", monospace;
    font-weight: 500;
    font-style: normal;
}
.inco {
    font-size: .75em;
    font-weight: bold;
}
.intd {
    width: 75px;
}
.infill a:hover {
    cursor:pointer;
    text-decoration:underline;
    text-decoration:dashed !important;
}
.ttpri {
    height: 10px;
    font-size: .7em;
}
.hprod2 {
    color: #d0d0d0;
    padding-left: 3px;
    padding-top: 1px;
    padding-bottom: 1px;
    padding-right: 3px;
    font-size: .9em;
    font-weight: bold;
    min-width: 90px;
    font-family: "mono45-headline", monospace;
    font-weight: 500;
    font-style: normal;
    letter-spacing: 1.5px;
}

.hexp2 {
    color: #686868;
    padding-left: 3px;
    padding-right: 4px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    font-size: .9em;
    font-family: "mono45-headline", monospace;
    font-weight: 500;
    font-style: normal;
}
.rentask-link {
    color:#3d3d3d !important;
}

.rentask-link:hover {
    color: #006bb7 !important;
}