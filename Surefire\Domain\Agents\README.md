# Surefire Task Agent Framework

A comprehensive framework for creating and managing automated task agents that can be triggered from AI chat or action buttons throughout the Surefire application.

## Overview

The Task Agent Framework enables two primary use cases:

1. **AI Chat Integration** - Users can request actions in natural language ("Send loss run requests for Acme Corp work comp for last 5 years")
2. **Action Buttons** - Pre-configured buttons placed in key areas of the site that execute agents with context-aware parameters

## Architecture

### Core Components

- **Models** (`Models/TaskAgentModels.cs`) - Data structures for agent definitions, requests, responses, and parameter handling
- **Interfaces** - Contracts for task agents, registry, parameter extraction, and orchestration
- **Services** - Implementations for agent registry, parameter extraction, and entity extraction
- **Task Agents** - Individual agent implementations for specific business processes

### Key Interfaces

- `ITaskAgent` - Base interface all task agents must implement
- `ITaskAgentRegistry` - Service for registering and discovering agents
- `IParameterExtractionService` - Service for extracting parameters from user input
- `IEntityExtractionService` - Service for entity extraction using embeddings (placeholder)
- `ITaskAgentOrchestrator` - Main orchestration service

## Creating a New Task Agent

### Step 1: Implement ITaskAgent

Create a new class that implements `ITaskAgent`:

```csharp
public class MyCustomAgent : ITaskAgent
{
    // Required: Agent definition with metadata
    public TaskAgentDefinition Definition => new()
    {
        AgentId = "my_custom_agent",
        Name = "My Custom Agent",
        Description = "Description of what this agent does",
        Category = "Category Name",
        TriggerPhrases = new List<string> { "trigger phrase 1", "trigger phrase 2" },
        Parameters = new List<AgentParameterDefinition>
        {
            // Define required and optional parameters
        },
        OutcomeType = AgentOutcomeType.Message, // or Navigation, Download, etc.
        SupportsAIChat = true,
        SupportsActionButton = true,
        RequiresConfirmation = false
    };

    // Required: Execute the agent
    public async Task<TaskAgentResult> ExecuteAsync(TaskAgentRequest request, CancellationToken cancellationToken = default)
    {
        // Your business logic here
    }

    // Required: Validate parameters
    public async Task<AgentParameterValidation> ValidateParametersAsync(Dictionary<string, object> parameters)
    {
        // Parameter validation logic
    }

    // Required: Preview for confirmations
    public async Task<string> GetExecutionPreviewAsync(Dictionary<string, object> parameters)
    {
        // Return human-readable preview of what will happen
    }
}
```

### Step 2: Define Parameters

Each parameter should specify:

```csharp
new AgentParameterDefinition
{
    Name = "parameter_name",
    Description = "Human-readable description",
    ParameterType = typeof(string), // or int, decimal, DateTime, etc.
    IsRequired = true,
    UseEntityExtraction = true, // For client names, carriers, policy types
    EntityType = "ClientName", // "ClientName", "CarrierName", "PolicyType"
    ExtractionPrompt = "AI prompt for extracting this parameter",
    ClarificationQuestion = "Question to ask user if parameter is missing",
    ValidValues = new List<string> { "option1", "option2" }, // For enum-like parameters
    DefaultValue = "default_value" // Optional default
}
```

### Step 3: Register the Agent

Register your agent with the system:

```csharp
// In your DI container configuration
services.AddTransient<ITaskAgent, MyCustomAgent>();

// The registry will automatically discover and register all ITaskAgent implementations
```

## Outcome Types

Agents can produce different types of outcomes:

### Message
Simple text response to the user:
```csharp
return new TaskAgentResult
{
    Success = true,
    OutcomeType = AgentOutcomeType.Message,
    Message = "Task completed successfully"
};
```

### Navigation
Navigate to a specific page with pre-filled data:
```csharp
return new TaskAgentResult
{
    Success = true,
    OutcomeType = AgentOutcomeType.Navigation,
    Message = "Opening payment form",
    NavigationInfo = new AgentNavigationInfo
    {
        NavigationUrl = "/payments/create",
        PrePopulatedFields = new Dictionary<string, object>
        {
            ["ClientName"] = "Acme Corp",
            ["Amount"] = 400.00m
        }
    }
};
```

### Download
Provide downloadable files:
```csharp
return new TaskAgentResult
{
    Success = true,
    OutcomeType = AgentOutcomeType.Download,
    Message = "Report generated",
    DownloadUrls = new List<string> { "/downloads/report.pdf" }
};
```

### Data
Return structured data for display:
```csharp
return new TaskAgentResult
{
    Success = true,
    OutcomeType = AgentOutcomeType.Data,
    Message = "Search completed",
    Data = new { Results = searchResults, Count = 25 }
};
```

## Entity Extraction

The framework supports entity extraction for common insurance entities:

### Client Names
```csharp
new AgentParameterDefinition
{
    Name = "client_name",
    UseEntityExtraction = true,
    EntityType = "ClientName"
}
```

### Carrier Names
```csharp
new AgentParameterDefinition
{
    Name = "carrier_name",
    UseEntityExtraction = true,
    EntityType = "CarrierName"
}
```

### Policy Types
```csharp
new AgentParameterDefinition
{
    Name = "policy_type",
    UseEntityExtraction = true,
    EntityType = "PolicyType"
}
```

**Note**: Entity extraction currently uses a placeholder service. This will be replaced with the embedding-based system for production use.

## Action Buttons

### Creating Action Buttons

Action buttons can be placed anywhere in the application. They automatically extract context from the current page and execute agents.

#### Razor Component Example

```razor
@inject ITaskAgentOrchestrator AgentOrchestrator

<div class="agent-buttons">
    @foreach (var buttonConfig in _availableButtons)
    {
        <button class="btn @buttonConfig.CssClasses" 
                disabled="@(!buttonConfig.IsReady)"
                @onclick="() => ExecuteAgent(buttonConfig)">
            <i class="@buttonConfig.Icon"></i>
            @buttonConfig.ButtonText
        </button>
    }
</div>

@code {
    private List<ActionButtonConfig> _availableButtons = new();

    protected override async Task OnInitializedAsync()
    {
        // Get context from current page
        var pageContext = GetCurrentPageContext();
        
        // Get available agents for action buttons
        var agents = AgentOrchestrator.GetAvailableAgents(AgentExecutionContext.ActionButton);
        
        // Generate button configurations
        foreach (var agent in agents)
        {
            var buttonConfig = await AgentOrchestrator.GenerateActionButtonConfigAsync(agent.AgentId, pageContext);
            if (buttonConfig != null)
            {
                _availableButtons.Add(buttonConfig);
            }
        }
    }

    private async Task ExecuteAgent(ActionButtonConfig buttonConfig)
    {
        var result = await AgentOrchestrator.ExecuteFromButtonAsync(
            buttonConfig.AgentId,
            buttonConfig.PreFilledParameters,
            getCurrentUserId(),
            GetCurrentPageContext()
        );
        
        // Handle result (show message, navigate, etc.)
        await HandleAgentResult(result);
    }

    private Dictionary<string, object> GetCurrentPageContext()
    {
        // Extract context from current page
        return new Dictionary<string, object>
        {
            ["client_id"] = CurrentClientId,
            ["client_name"] = CurrentClientName,
            ["policy_id"] = CurrentPolicyId,
            ["page_type"] = "client_detail"
        };
    }
}
```

#### Context-Aware Parameters

Buttons automatically fill parameters from page context:

```csharp
// On a client detail page, this context is available:
var pageContext = new Dictionary<string, object>
{
    ["client_id"] = 123,
    ["client_name"] = "Acme Corp",
    ["page_type"] = "client_detail"
};

// The Loss Run agent would automatically get client_name = "Acme Corp"
// Only policy_type and time_period would need clarification
```

### Button Styling

Action buttons support different styles based on context:

```csharp
new ActionButtonConfig
{
    ButtonText = "Request Loss Runs",
    CssClasses = "btn-primary",
    Icon = "fas fa-file-alt",
    Description = "Send loss run requests for this client"
}
```

## AI Chat Integration

### Natural Language Processing

The framework handles natural language input through the orchestrator:

```csharp
var result = await AgentOrchestrator.ProcessChatRequestAsync(
    "Send loss run requests for Acme Corp work comp for last 5 years",
    userId,
    sessionId,
    currentPageContext
);
```

### Parameter Clarification

When parameters are missing or ambiguous, the system generates clarification questions:

```
User: "Send a payment link for $400"
System: "I need some clarification:
1. Which client should receive the payment link?
2. What is this payment for? (e.g., 'Work Comp Broker Fee', 'Deductible Payment')"
```

### Continuing Conversations

Users can provide clarification and continue:

```csharp
var result = await AgentOrchestrator.ContinueWithClarificationAsync(
    agentId,
    "Acme Corp for their broker fee",
    existingParameters,
    userId,
    sessionId
);
```

## Error Handling

### Parameter Validation Errors

```csharp
return new TaskAgentResult
{
    Success = false,
    OutcomeType = AgentOutcomeType.Error,
    ErrorMessage = "Invalid parameter: Amount must be greater than 0"
};
```

### Execution Errors

```csharp
try
{
    // Agent logic
}
catch (Exception ex)
{
    return new TaskAgentResult
    {
        Success = false,
        OutcomeType = AgentOutcomeType.Error,
        ErrorMessage = $"Failed to execute: {ex.Message}",
        ExecutionTime = stopwatch.Elapsed
    };
}
```

## Best Practices

### 1. Agent Design
- Use clear, descriptive trigger phrases
- Define comprehensive parameter validation
- Provide helpful clarification questions
- Include execution previews for confirmation

### 2. Parameter Extraction
- Use entity extraction for common entities (clients, carriers, policies)
- Provide clear extraction prompts for AI processing
- Set reasonable defaults for optional parameters
- Validate parameter types and ranges

### 3. Error Handling
- Always wrap agent execution in try-catch blocks
- Provide user-friendly error messages
- Log errors for debugging
- Return appropriate outcome types

### 4. Performance
- Keep agent execution time reasonable (< 30 seconds)
- Use async/await properly
- Implement cancellation token support
- Consider background processing for long-running tasks

### 5. Security
- Validate all user input
- Check user permissions for agent execution
- Sanitize parameters before database operations
- Use parameterized queries

## Configuration

### Dependency Injection

```csharp
// Program.cs or Startup.cs
services.AddScoped<ITaskAgentRegistry, TaskAgentRegistry>();
services.AddScoped<IParameterExtractionService, ParameterExtractionService>();
services.AddScoped<IEntityExtractionService, PlaceholderEntityExtractionService>();
services.AddScoped<ITaskAgentOrchestrator, TaskAgentOrchestrator>();

// Register all task agents
services.AddTransient<ITaskAgent, LossRunRequestAgent>();
services.AddTransient<ITaskAgent, PaymentLinkAgent>();
services.AddTransient<ITaskAgent, CertificateRequestAgent>();
```

### Environment-Specific Settings

```json
{
  "TaskAgents": {
    "EnableEntityExtraction": true,
    "DefaultTimeout": 30,
    "RequireConfirmationForAll": false,
    "LogExecutions": true
  }
}
```

## Testing

### Unit Testing Agents

```csharp
[Test]
public async Task LossRunRequestAgent_ValidParameters_ReturnsSuccess()
{
    // Arrange
    var agent = new LossRunRequestAgent(logger);
    var parameters = new Dictionary<string, object>
    {
        ["client_name"] = "Acme Corp",
        ["policy_type"] = "Workers Compensation",
        ["time_period"] = 5
    };

    var request = new TaskAgentRequest
    {
        AgentId = "loss_run_request",
        Parameters = parameters,
        UserId = "test_user"
    };

    // Act
    var result = await agent.ExecuteAsync(request);

    // Assert
    Assert.IsTrue(result.Success);
    Assert.AreEqual(AgentOutcomeType.Message, result.OutcomeType);
}
```

### Integration Testing

```csharp
[Test]
public async Task AgentOrchestrator_ProcessChatRequest_HandlesParameterExtraction()
{
    // Test full flow from natural language to agent execution
    var input = "Send loss run requests for Acme Corp work comp for last 5 years";
    var result = await orchestrator.ProcessChatRequestAsync(input, "user123", "session456");
    
    Assert.IsTrue(result.ReadyForExecution);
    Assert.IsNotNull(result.ParameterValidation);
}
```

## Examples

See the following example implementations:

- `LossRunRequestAgent` - Demonstrates message outcome with email integration
- `PaymentLinkAgent` - Demonstrates navigation outcome with form pre-population

## Future Enhancements

1. **Embedding-based Entity Extraction** - Replace placeholder with vectorized search
2. **Agent Chaining** - Allow agents to trigger other agents
3. **Scheduling** - Support for delayed and recurring agent execution
4. **Approval Workflows** - Multi-step approval processes for sensitive operations
5. **Agent Analytics** - Track usage, performance, and success rates

## Support

For questions or issues with the Task Agent Framework, please contact the development team or create an issue in the project repository.

# Vector Store Implementation Guide

## 🔑 Critical Requirements for All Vector Store Implementations

### 1. **ID Format Requirements**
Different vector stores have different ID requirements:
- **Qdrant**: Integer IDs or UUIDs only (no string prefixes like "client:123")
- **Pinecone**: String IDs up to 512 characters
- **Weaviate**: UUID format preferred

**Solution**: Use integer offsets or GUIDs
```csharp
// Integer approach with entity offsets
var clientId = (originalId + 1000000).ToString();     // Clients: 1000000+
var productId = (originalId + 2000000).ToString();    // Products: 2000000+
var carrierId = (originalId + 3000000).ToString();    // Carriers: 3000000+

// OR UUID approach
var id = Guid.NewGuid().ToString();
```

### 2. **Metadata Type Safety**
Vector stores often serialize/deserialize metadata as JSON, causing type conversion issues.

**Solution**: Robust type conversion helpers
```csharp
static string? GetMetadataString(IDictionary<string, object> metadata, string key)
{
    return metadata.TryGetValue(key, out var value) ? value?.ToString() : null;
}

static int GetMetadataInt(IDictionary<string, object> metadata, string key)
{
    if (!metadata.TryGetValue(key, out var value)) return 0;
    
    return value switch
    {
        int i => i,
        long l => (int)l,
        string s when int.TryParse(s, out var parsed) => parsed,
        JsonElement je when je.ValueKind == JsonValueKind.Number => je.GetInt32(),
        _ => 0
    };
}
```

### 3. **Rate Limiting & Resilience**
Embedding APIs have rate limits and can fail.

**Solution**: Built-in delays and error handling
```csharp
foreach (var item in items)
{
    try
    {
        // Validate input
        var text = item.Text?.Trim() ?? "";
        if (string.IsNullOrWhiteSpace(text) || text.Length < minLength)
        {
            _logger.LogWarning("Skipping {Entity} {Id}: insufficient text", entityType, item.Id);
            continue;
        }

        // Process with error handling
        var vector = await _embeddingService.GenerateEmbeddingAsync(text, cancellationToken);
        await _vectorStore.UpsertAsync(id, vector, metadata, cancellationToken);
        
        // Rate limiting
        await Task.Delay(50, cancellationToken);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Failed to process {Entity} {Id}", entityType, item.Id);
        // Continue processing other items
    }
}
```

### 4. **Collection Management**
Always ensure collections exist before operations.

**Solution**: Auto-creation with proper configuration
```csharp
public async Task EnsureCollectionExistsAsync(CancellationToken cancellationToken = default)
{
    if (_collectionChecked) return;

    try
    {
        // Check if exists
        var checkResponse = await CheckCollectionExists(cancellationToken);
        if (checkResponse)
        {
            _collectionChecked = true;
            return;
        }

        // Create with proper vector dimensions
        await CreateCollection(1536, "Cosine", cancellationToken); // Match embedding model
        _collectionChecked = true;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Failed to ensure collection {Collection} exists", _collectionName);
        throw;
    }
}
```

### 5. **Error Diagnostics**
Provide detailed error information for debugging.

**Solution**: Enhanced error logging
```csharp
if (!response.IsSuccessStatusCode)
{
    var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
    _logger.LogError("{Operation} failed with status {StatusCode}: {ErrorContent}", 
                     operationName, response.StatusCode, errorContent);
    throw new HttpRequestException($"{operationName} failed with status {response.StatusCode}: {errorContent}");
}
```

## 🛠️ Implementation Checklist

When implementing a new vector store:

- [ ] **ID Format**: Verify compatible ID format (integer/UUID/string)
- [ ] **Metadata Handling**: Implement type-safe metadata extraction
- [ ] **Rate Limiting**: Add delays between API calls
- [ ] **Error Handling**: Continue processing on individual failures
- [ ] **Collection Management**: Auto-create collections with proper config
- [ ] **Input Validation**: Skip empty/short text inputs
- [ ] **Logging**: Detailed error and debug information
- [ ] **Null Safety**: Handle null values in all metadata fields
- [ ] **Vector Dimensions**: Match embedding model output (1536 for OpenAI text-embedding-3-small)
- [ ] **Distance Metric**: Use appropriate similarity function (Cosine recommended)

## 🔄 Testing Pattern

Always test with this sequence:
1. **Configuration Check**: Verify API keys, URLs, collection names
2. **Connection Test**: Basic connectivity to vector store
3. **Collection Creation**: Auto-creation functionality
4. **Single Upsert**: Test one vector insert
5. **Batch Upsert**: Test rate limiting and error handling
6. **Search Test**: Verify retrieval and metadata extraction
7. **Type Safety**: Test with various metadata types (string, int, JsonElement)

## 📊 Monitoring Recommendations

Track these metrics for production:
- **Embedding API calls per minute** (rate limiting)
- **Failed upsert percentage** (data quality)
- **Average processing time per entity** (performance)
- **Collection size growth** (storage costs)
- **Search latency** (user experience)

---

*This guide is based on lessons learned from implementing Qdrant integration and should be applied to all future vector store implementations.* 