using System;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.Extensions.Caching.Memory;

namespace Surefire.Domain.DocuSign
{
    public class DocuSignService : IDocuSignService
    {
        private readonly HttpClient _httpClient;
        private readonly IDocuSignConfigService _configService;
        private readonly ILogger<DocuSignService> _logger;
        private readonly IMemoryCache _cache;
        
        // Cache keys
        private const string ACCESS_TOKEN_CACHE_KEY = "DocuSign_AccessToken";
        private const string RSA_CACHE_KEY = "DocuSign_RSA";
        private const string ENVELOPES_CACHE_KEY = "DocuSign_RecentEnvelopes";
        
        // Cache durations
        private static readonly TimeSpan ACCESS_TOKEN_CACHE_DURATION = TimeSpan.FromMinutes(55); // Slightly under an hour
        private static readonly TimeSpan RSA_CACHE_DURATION = TimeSpan.FromHours(24); // RSA object can be cached longer
        private static readonly TimeSpan ENVELOPES_CACHE_DURATION = TimeSpan.FromMinutes(55); // Slightly under an hour

        public DocuSignService(HttpClient httpClient, IDocuSignConfigService configService, ILogger<DocuSignService> logger, IMemoryCache cache)
        {
            _httpClient = httpClient;
            _configService = configService;
            _logger = logger;
            _cache = cache;
        }

        public async Task<string> GetAccessTokenAsync()
        {
            // Try to get the token from cache first
            if (_cache.TryGetValue(ACCESS_TOKEN_CACHE_KEY, out string cachedToken))
            {
                return cachedToken;
            }
            
            var config = _configService.GetDocuSignConfig();
            
            var jwt = GenerateJwtToken();
            
            // Use account.docusign.com for production or account-d.docusign.com for demo
            string authServer = _configService.IsProduction() ? "account.docusign.com" : "account.docusign.com";
            
            var url = $"https://{authServer}/oauth/token";
            
            var request = new HttpRequestMessage(HttpMethod.Post, url)
            {
                Content = new FormUrlEncodedContent(new Dictionary<string, string>
                {
                    { "grant_type", "urn:ietf:params:oauth:grant-type:jwt-bearer" },
                    { "assertion", jwt }
                })
            };

            var response = await _httpClient.SendAsync(request);
            var content = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                try
                {
                    var errorJson = JsonDocument.Parse(content);
                    if (errorJson.RootElement.TryGetProperty("error", out var errorCode) &&
                        errorJson.RootElement.TryGetProperty("error_description", out var errorDescription))
                    {
                        var errorMessage = $"DocuSign API error: {errorCode.GetString()} - {errorDescription.GetString()}";
                        throw new Exception(errorMessage);
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning("Failed to parse error response as JSON: {Message}", ex.Message);
                }
                throw new Exception($"DocuSign API error ({response.StatusCode}): {content}");
            }

            var json = JsonDocument.Parse(content);
            var accessToken = json.RootElement.GetProperty("access_token").GetString();
            
            // Cache the token
            _cache.Set(ACCESS_TOKEN_CACHE_KEY, accessToken, ACCESS_TOKEN_CACHE_DURATION);
            
            return accessToken;
        }

        public async Task<string> GetRecentEnvelopesAsync(string accessToken)
        {
            // Try to get the envelopes from cache first
            if (_cache.TryGetValue(ENVELOPES_CACHE_KEY, out string cachedEnvelopes))
            {
                return cachedEnvelopes;
            }
            
            var config = _configService.GetDocuSignConfig();
            
            string baseUrl = _configService.IsProduction() ? "na3.docusign.net" : "na3.docusign.net";
            
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            var url = $"https://{baseUrl}/restapi/v2.1/accounts/{config.AccountId}/envelopes?from_date=2024-01-01";
            
            var response = await _httpClient.GetAsync(url);
            var content = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                try
                {
                    var errorJson = JsonDocument.Parse(content);
                    if (errorJson.RootElement.TryGetProperty("errorCode", out var errorCode) &&
                        errorJson.RootElement.TryGetProperty("message", out var errorMessage))
                    {
                        var error = $"DocuSign API error: {errorCode.GetString()} - {errorMessage.GetString()}";
                        _logger.LogError(error);
                        throw new Exception(error);
                    }
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning("Failed to parse error response as JSON: {Message}", ex.Message);
                }
                
                throw new Exception($"DocuSign API error ({response.StatusCode}): {content}");
            }

            // Cache the envelopes
            _cache.Set(ENVELOPES_CACHE_KEY, content, ENVELOPES_CACHE_DURATION);
            
            return content;
        }

        public async Task<byte[]> DownloadDocumentPdfAsync(string accessToken, string envelopeId)
        {
            var config = _configService.GetDocuSignConfig();
            
            string baseUrl = _configService.IsProduction() ? "na3.docusign.net" : "na3.docusign.net";            
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            
            // DocuSign API endpoint to get the combined PDF for all documents in the envelope
            var url = $"https://{baseUrl}/restapi/v2.1/accounts/{config.AccountId}/envelopes/{envelopeId}/documents/combined";
            
            try
            {
                var response = await _httpClient.GetAsync(url);
                
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("DocuSign API error. Status: {StatusCode}, Content: {Content}", 
                        response.StatusCode, errorContent);
                    throw new Exception($"Failed to download document: {response.StatusCode} - {errorContent}");
                }
                
                // Read the PDF content as bytes
                var pdfBytes = await response.Content.ReadAsByteArrayAsync();
                
                return pdfBytes;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading document PDF");
                throw new Exception($"Error downloading document PDF: {ex.Message}", ex);
            }
        }

        private string GenerateJwtToken()
        {
            var config = _configService.GetDocuSignConfig();
            
            if (!_cache.TryGetValue(RSA_CACHE_KEY, out RSA rsa))
            {
                if (string.IsNullOrEmpty(config.PrivateKeyPath))
                {
                    var error = "Private key path is not configured. Please set DOCUSIGN_PRIVATE_KEY_PATH environment variable or DocuSign:PrivateKeyPath in appsettings.json.";
                    _logger.LogError(error);
                    throw new Exception(error);
                }
                
                if (!File.Exists(config.PrivateKeyPath))
                {
                    var error = $"Private key file not found at path: {config.PrivateKeyPath}";
                    _logger.LogError(error);
                    throw new Exception(error);
                }
                
                var privateKey = File.ReadAllText(config.PrivateKeyPath);
                rsa = RSA.Create();
                
                try
                {
                    rsa.ImportFromPem(privateKey.ToCharArray());
                    
                    _cache.Set(RSA_CACHE_KEY, rsa, RSA_CACHE_DURATION);
                }
                catch (Exception ex)
                {
                    var error = $"Failed to import private key: {ex.Message}. Make sure the key is in PEM format with correct BEGIN/END PRIVATE KEY markers.";
                    _logger.LogError(error);
                    throw new Exception(error);
                }
            }

            var credentials = new SigningCredentials(new RsaSecurityKey(rsa), SecurityAlgorithms.RsaSha256);
            var handler = new JwtSecurityTokenHandler();

            string audience = _configService.IsProduction() ? "account.docusign.com" : "account.docusign.com";
            
            var descriptor = new SecurityTokenDescriptor
            {
                Issuer = config.IntegratorKey,
                Subject = new ClaimsIdentity(new[]
                {
                    new Claim("sub", config.UserId),
                    new Claim("scope", "signature impersonation")
                }),
                Audience = audience,
                Expires = DateTime.UtcNow.AddHours(1),
                SigningCredentials = credentials
            };

            var token = handler.CreateToken(descriptor);
            var jwt = handler.WriteToken(token);
            
            return jwt;
        }
    }

    // Your DocuSignConfig with all the properties needed for JWT
    public class DocuSignConfig
    {
        /// <summary>
        /// DocuSign API key (usually the same as IntegratorKey)
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;
        
        /// <summary>
        /// DocuSign account ID - found in the DocuSign admin panel
        /// </summary>
        public string AccountId { get; set; } = string.Empty;
        
        /// <summary>
        /// DocuSign user ID for the user being impersonated with JWT
        /// </summary>
        public string UserId { get; set; } = string.Empty;
        
        /// <summary>
        /// DocuSign integrator key (client ID) from developer dashboard
        /// </summary>
        public string IntegratorKey { get; set; } = string.Empty;
        
        /// <summary>
        /// Base URL for DocuSign API calls
        /// </summary>
        public string BaseUrl { get; set; } = string.Empty;
        
        /// <summary>
        /// DocuSign authentication server
        /// </summary>
        public string AuthServer { get; set; } = string.Empty;
        
        /// <summary>
        /// Path to the RSA private key file for JWT authentication
        /// </summary>
        public string PrivateKeyPath { get; set; } = string.Empty;
        
        /// <summary>
        /// Creates a clone of this configuration
        /// </summary>
        public DocuSignConfig Clone()
        {
            return new DocuSignConfig
            {
                ApiKey = this.ApiKey,
                AccountId = this.AccountId,
                UserId = this.UserId,
                IntegratorKey = this.IntegratorKey,
                BaseUrl = this.BaseUrl,
                AuthServer = this.AuthServer,
                PrivateKeyPath = this.PrivateKeyPath
            };
        }
    }
}
