﻿@page "/Policies/Details/{PolicyId:int}"
@using Surefire.Domain.Policies.Models
@using Surefire.Domain.Policies.Services
@using Surefire.Components.Policies.Coverages
@using Microsoft.FluentUI.AspNetCore.Components
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Inputs
@using Surefire.Domain.Forms.Components
@inject PolicyService PolicyService
@inject NavigationManager Navigation

<_toolbar PolicyId="@PolicyId" PageName="Details" />

@if (Policy is null || Policy.Product is null)
{
    <div class="loading-state">
        <FluentProgressRing />
        <span class="loading-text">Loading policy details...</span>
    </div>
}
else
{
    <FluentSplitter>
        <Panel1>
            <div class="page-content policy-details-container">
                <!-- Policy Header -->
                <div class="policy-header">
                    <FluentIcon Value="@(new Icons.Regular.Size48.ShieldTask())" Class="icon" Color="Color.Custom" CustomColor="#fff" />
                    <div class="policy-title">
                        <h1 class="policy-product-name">@Policy.Product.LineName</h1>
                        <div class="policy-client-name" @onclick="GoToClient">
                            @Policy.Client.Name
                        </div>
                    </div>
                    <div class="policy-carriers">
                        @if (Policy.Carrier != null)
                        {
                            <div class="carrier-info">
                                <span class="carrier-label">Carrier: </span>
                                <span class="carrier-name" @onclick="() => GoToCarrier(Policy.Carrier.CarrierId)">
                                    @Policy.Carrier.CarrierName
                                </span>
                            </div>
                        }
                        @if (Policy.Wholesaler != null)
                        {
                            <div class="carrier-info">
                                <span class="carrier-label">Wholesaler</span>
                                <span class="carrier-name" @onclick="() => GoToCarrier(Policy.Wholesaler.CarrierId)">
                                    @Policy.Wholesaler.CarrierName
                                </span>
                            </div>
                        }
                    </div>
                    <div class="status-indicator status-active"></div>
                </div>

                <!-- Basic Policy Information -->
                <div class="policy-basic-info">
                    <div class="section-title">
                        <FluentIcon Value="@(new Icons.Regular.Size20.DocumentEdit())" />
                        Policy Information
                    </div>

                    <div class="field-grid">
                        <div class="field-item">
                            <label class="field-label">Policy Number</label>
                            <div class="field-value">
                                <input type="text"
                                       class="form-input top-field"
                                       value="@Policy.PolicyNumber"
                                       @onchange="@(async (ChangeEventArgs e) => { Policy.PolicyNumber = e.Value?.ToString(); await UpdatePolicyAsync(Policy); })"
                                       placeholder="Enter policy number" />
                            </div>
                        </div>

                        <div class="field-item">
                            <label class="field-label">Premium</label>
                            <div class="field-value">
                                <div class="currency-input-container">
                                    <input type="number"
                                           class="form-input currency-input top-field"
                                           value="@Policy.Premium"
                                           @onchange="@(async (ChangeEventArgs e) => { if (decimal.TryParse(e.Value?.ToString(), out var premium)) { Policy.Premium = premium; await UpdatePolicyAsync(Policy); } })"
                                           step="100"
                                           min="0"
                                           placeholder="Enter premium amount" />
                                </div>
                            </div>
                        </div>

                        <div class="field-item">
                            <label class="field-label">Effective Date</label>
                            <div class="field-value">
                                <div class="date-display-container">
                                    <div class="date-display-text">
                                        @(Policy.EffectiveDate.ToString("MMMM dd, yyyy") ?? "Not set")
                                    </div>
                                    <div class="cal-width">
                                        <SfDatePicker TValue="DateTime"
                                                      Value="Policy.EffectiveDate"
                                                      ValueChanged="@(async (DateTime newDate) => { Policy.EffectiveDate = newDate; await OnEffectiveDateChanged(); })"
                                                      ShowClearButton="false"
                                                      CssClass="date-picker-icon-only"
                                                      FloatLabelType="FloatLabelType.Never" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="field-item">
                            <label class="field-label">Expiration Date</label>
                            <div class="field-value">
                                <div class="date-display-container">
                                    <div class="date-display-text">
                                        @(Policy.ExpirationDate.ToString("MMMM dd, yyyy") ?? "Not set")
                                    </div>
                                    <div class="cal-width">
                                        <SfDatePicker TValue="DateTime"
                                                      Value="Policy.ExpirationDate"
                                                      ValueChanged="@(async (DateTime newDate) => { Policy.ExpirationDate = newDate; await OnExpirationDateChanged(); })"
                                                      ShowClearButton="false"
                                                      CssClass="date-picker-icon-only"
                                                      FloatLabelType="FloatLabelType.Never" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Coverage Sections -->
                @if (Policy.Product.ProductId == 3)
                {
                    <div class="coverage-section">
                        <GeneralLiabilityCoverages PolicyId="@PolicyId" />
                    </div>
                }

                @if (Policy.Product.ProductId == 2)
                {
                    <div class="coverage-section">
                        <WorkCompCoverages PolicyId="@PolicyId" />
                    </div>
                }

                @if (Policy.Product.ProductId == 4)
                {
                    <div class="coverage-section">
                        <AutoCoverages PolicyId="@PolicyId" />
                    </div>
                }

                <!-- Policy Metadata -->
                <div class="policy-metadata">
                    Policy ID: @Policy.PolicyId | ePolicy ID: @Policy.ePolicyId | ePolicy Line ID: @Policy.ePolicyLineId
                </div>
            </div>
        </Panel1>
        <Panel2>
            <PolicyViewer FormDocId="157" />
        </Panel2>
    </FluentSplitter>
}

@code {
    [Parameter]
    public int PolicyId { get; set; }

    private Policy Policy { get; set; }
    [CascadingParameter] public Action<string> UpdateHeader { get; set; }

    protected override async Task OnInitializedAsync()
    {
        UpdateHeader?.Invoke("Policies");
        Policy = await PolicyService.GetPolicyByIdAsync(PolicyId);
    }

    public void GoToClient()
    {
        Navigation.NavigateTo($"/Clients/{Policy.Client.ClientId}");
    }

    public void GoToCarrier(int carrierId)
    {
        Navigation.NavigateTo($"/Carriers/{carrierId}");
    }

    private async Task UpdatePolicyAsync(Policy policy)
    {
        await PolicyService.UpdatePolicyContextModelAsync(policy);
    }

    private async Task OnEffectiveDateChanged()
    {
        await UpdatePolicyAsync(Policy);
        StateHasChanged();
    }

    private async Task OnExpirationDateChanged()
    {
        await UpdatePolicyAsync(Policy);
        StateHasChanged();
    }
}
