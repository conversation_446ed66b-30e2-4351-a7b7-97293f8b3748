using Surefire.Domain.Agents.Models;

namespace Surefire.Domain.Agents.Interfaces
{
    /// <summary>
    /// Interface for handling navigation requests within the application
    /// </summary>
    public interface INavigationAgent
    {
        /// <summary>
        /// Processes a navigation request and returns the appropriate URL and state changes
        /// </summary>
        /// <param name="request">The navigation request containing the user's intent</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Navigation response with URL and state information</returns>
        Task<NavigationResponse> ProcessNavigationRequestAsync(NavigationRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Determines if a given input is a navigation request
        /// </summary>
        /// <param name="input">User input to analyze</param>
        /// <returns>True if the input appears to be a navigation request</returns>
        bool IsNavigationRequest(string input);
    }
} 