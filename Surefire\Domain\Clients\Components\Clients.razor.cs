using Microsoft.AspNetCore.Components;
using Microsoft.FluentUI.AspNetCore.Components;
using Microsoft.JSInterop;
using Surefire.Domain.Agents.Interfaces;
using Surefire.Domain.Agents.Models;
using Surefire.Domain.Clients.Models;
using Surefire.Domain.Clients.Services;
using Surefire.Domain.Contacts.Models;
using Surefire.Domain.Ember;
using Surefire.Domain.Forms.Models;
using Surefire.Domain.Forms.Services;
using Surefire.Domain.Graph.Components;
using Surefire.Domain.Logs;
using Surefire.Domain.Plugins;
using Surefire.Domain.Policies.Models;
using Surefire.Domain.Renewals.Services;
using Surefire.Domain.Shared.Components;
using Surefire.Domain.Shared.Helpers;
using Surefire.Domain.Shared.Services;
using System.Data;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;

namespace Surefire.Domain.Clients.Components
{
    public class ClientsBase : ComponentBase, IDisposable
    {
        [Parameter, SupplyParameterFromQuery] public int LoadClientId { get; set; }
        [Parameter] public bool showCreatePolicy { get; set; } = false;
        [CascadingParameter] public Action<string>? UpdateHeader { get; set; }
        [Inject] protected StateService? _stateService { get; set; }
        [Inject] protected FormService? FormService { get; set; }
        [Inject] protected ClientService? ClientService { get; set; }
        [Inject] protected NavigationManager NavigationManager { get; set; }
        [Inject] protected EmberService? EmberService { get; set; }
        [Inject] protected PluginManager? _plugin { get; set; }
        [Inject] protected ILoggingService? _logs { get; set; }
        [Inject] protected IOpenAIAgent? OpenAIAgent { get; set; }
        [Inject] protected IJSRuntime? JS { get; set; }
        [Inject] protected ClientStateService? ClientStateService { get; set; }
        [Inject] protected RenewalService? RenewalService { get; set; }
        
        // Clients
        protected Surefire.Domain.Clients.Models.Client? selectedClient;
        protected List<ClientListItem> clients = new();
        protected List<ClientListItem> filteredClients = new();
        // Related
        protected List<Policy> currentPolicies = new();
        protected List<Policy> pastPolicies = new();
        protected List<Contact> contactList = new();
        protected List<string> phoneNumbers = new();
        protected List<Contact> loadedContacts = new();
        protected List<FormPdf> allFormPdfs = new();
        // UI
        protected bool isLoading = false;
        protected bool showNotes = true;
        protected bool showProposalCleaner = false;
        protected bool utilityLoading = false;
        protected string dynamicClass = "sf-quicklist-close";
        protected string searchTerm = string.Empty;
        protected string utilityStatus = "";
        protected int currentProposalId;
        protected FluentTabs? tabInterface;
        private CancellationTokenSource? _dataSyncCts;
        private CancellationTokenSource? _clientListCts;
        protected RecentClientEmails recentEmailsComponent;
        protected RecentPhoneCallsList RecentPhoneCallsListComponent;

        
        // Dialogs
        protected bool IsAiSummaryDialogOpen { get; set; } = false;
        protected bool IsGeneratingAiSummary { get; set; } = false;
        protected string AiSummary { get; set; } = string.Empty;
        protected string AiSummaryTitle { get; set; } = "Client Brief";
        protected string AiSummaryLoadingMessage { get; set; } = "Generating client summary...";

        // Transcription confirmation dialog
        protected bool IsTranscriptionConfirmDialogOpen { get; set; } = false;
        protected int UntranscribedCallsCount { get; set; } = 0;


        // - - - - - OnInit - - - - - -   -     -        -     /
        protected override async Task OnInitializedAsync()
        {
            UpdateHeader?.Invoke("Clients");
            _stateService.LoadClientFromSearch = LoadClientFromSearchBar;
            _stateService.OnClientUpdated += HandleClientUpdate;

            // Load client-specific state from local storage
            if (ClientStateService != null)
            {
                await ClientStateService.LoadStateAsync();
                if (!string.IsNullOrEmpty(ClientStateService.SearchTerm))
                {
                    searchTerm = ClientStateService.SearchTerm;
                }
            }

            // Load cached data or fetch from services if cache is invalid/empty
            await LoadCachedOrFreshData();

            //Check if database is empty
            if (clients.Count == 0)
            {
                NavigationManager.NavigateTo($"/Clients/Create", false);
            }
            // Always prefer URL parameter if present and valid
            if (LoadClientId > 0)
            {
                await LoadClient(LoadClientId);
            }
            else
            {
                // fallback: load last selected client from state or default
                await LoadClient(ClientStateService?.SelectedClientId ?? clients.FirstOrDefault()?.ClientId ?? 0);
            }
        }
        protected async Task ShowProposalCleaner((int clientId, int proposalId) args)
        {
            currentProposalId = args.proposalId;
            showProposalCleaner = true;
            StateHasChanged();
        }
        private async Task LoadCachedOrFreshData()
        {
            var tasks = new List<Task>();

            // Check and load FormPdfs
            if (ClientStateService != null && ClientStateService.IsFormPdfsCacheValid())
            {
                Console.WriteLine($"[CACHE] Using cached FormPdfs ({ClientStateService.AllFormPdfs?.Count ?? 0} items)");
                allFormPdfs = ClientStateService.AllFormPdfs ?? new List<FormPdf>();
            }
            else
            {
                var formPdfsTask = Task.Run(async () =>
                {
                    Console.WriteLine("[CACHE] Loading fresh FormPdfs from service");
                    var pdfs = await FormService?.GetAllFormPdfs();
                    allFormPdfs = pdfs ?? new List<FormPdf>();
                    
                    // Cache the result
                    if (ClientStateService != null)
                    {
                        ClientStateService.SetFormPdfsCache(allFormPdfs);
                        Console.WriteLine($"[CACHE] Cached {allFormPdfs.Count} FormPdfs");
                    }
                });
                tasks.Add(formPdfsTask);
            }

            // Check and load Clients
            if (ClientStateService != null && ClientStateService.IsClientsCacheValid())
            {
                Console.WriteLine($"[CACHE] Using cached Clients ({ClientStateService.AllClients?.Count ?? 0} items)");
                clients = ClientStateService.AllClients ?? new List<ClientListItem>();
            }
            else
            {
                var clientsTask = Task.Run(async () =>
                {
                    Console.WriteLine("[CACHE] Loading fresh Clients from service");
                    var clientList = await ClientService?.GetClientListAsync();
                    clients = clientList ?? new List<ClientListItem>();
                    
                    // Cache the result
                    if (ClientStateService != null)
                    {
                        ClientStateService.SetClientsCache(clients);
                        Console.WriteLine($"[CACHE] Cached {clients.Count} Clients");
                    }
                });
                tasks.Add(clientsTask);
            }

            // Wait for any remaining tasks to complete
            if (tasks.Any())
            {
                await Task.WhenAll(tasks);
            }

            // Update filtered clients list
            filteredClients = clients.Take(50).ToList();

            // Save the updated cache to local storage
            if (ClientStateService != null)
            {
                await ClientStateService.SaveStateAsync();
            }
        }
        private async Task HandleClientUpdate(int clientId)
        {
            if (clientId == selectedClient?.ClientId)
            {
                await ReloadSelectedClient();
                await InvokeAsync(StateHasChanged);
            }
        }

        // - - - - - Main Methods - - - - - -   -   -     -    /
        private async Task LoadClient(int loadClientId)
        {
            Console.WriteLine($"[DEBUG] Loading client from service for ClientId {loadClientId}, showing spinner.");
            isLoading = true;
            StateHasChanged();

            try
            {
                selectedClient = await ClientService.GetClientById(loadClientId) ??
                 await ClientService.GetClientById(clients.FirstOrDefault()?.ClientId ?? 0);

                // Always update SelectedClientId when loading a client
                if (selectedClient != null && ClientStateService != null)
                {
                    ClientStateService.SelectedClientId = selectedClient.ClientId;
                }

                // Create the contact list
                contactList = selectedClient?.Contacts?.ToList() ?? new List<Contact>();

                // Sort the policies to current and past lists
                var today = DateTime.Today;
                currentPolicies = selectedClient?.Policies
                    .Where(p => p.EffectiveDate <= p.ExpirationDate && p.ExpirationDate >= today)
                    .OrderByDescending(p => p.EffectiveDate)
                    .ToList() ?? new List<Policy>();

                pastPolicies = selectedClient?.Policies
                    .Where(p => p.ExpirationDate < today)
                    .OrderByDescending(p => p.EffectiveDate)
                    .ToList() ?? new List<Policy>();

                searchTerm = string.Empty;
                phoneNumbers = GetClientAndContactPhoneNumbers(selectedClient);

                // Save only the small state to ClientStateService
                if (ClientStateService != null)
                {
                    await ClientStateService.SaveStateAsync();
                }
            }
            catch (Exception ex)
            {
                await _logs.LogAsync(LogLevel.Error, ex.ToString(), "LoadClient in Clients.razor");
                Console.Error.WriteLine($"Error loading client: {ex.Message}");
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }

            // Initiate policy sync and import
            await RunDataSync(false);
        }
        private async Task FilterClients()
        {
            if (string.IsNullOrEmpty(searchTerm))
            {
                //Making new filter list for clients
                filteredClients = clients.Take(40).ToList();
            }
            else
            {
                //Filtering clients
                filteredClients = clients
                    .Where(client => client.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                    .Take(20)
                    .ToList();
            }
        }
        protected async Task LoadClientClickHandler(int clientId)
        {
            ExpandDetails(0);
            searchTerm = string.Empty;
            if (ClientStateService != null)
            {
                ClientStateService.ActiveTab = "tab-1";
                ClientStateService.SelectedClientId = clientId;
            }
            await tabInterface.GoToTabAsync("tab-1");
            NavigationManager.NavigateTo($"/Clients/{clientId}", false);
            await LoadClient(clientId);
        }
        public async Task LoadClientFromSearchBar(int newClientId)
        {
            if (newClientId != selectedClient?.ClientId)
            {
                ExpandDetails(0);
                searchTerm = string.Empty;
                if (ClientStateService != null)
                {
                    ClientStateService.ActiveTab = "tab-1";
                    ClientStateService.SelectedClientId = newClientId;
                }
                NavigationManager.NavigateTo($"/Clients/{newClientId}", false);
                await LoadClient(newClientId);
            }
        }
        public List<string> GetClientAndContactPhoneNumbers(Surefire.Domain.Clients.Models.Client? selectedClient)
        {
            var phoneNumbers = new List<string>();

            // Add client's legacy phone number if it exists
            if (selectedClient != null && !string.IsNullOrEmpty(selectedClient.PhoneNumber))
            {
                phoneNumbers.Add(selectedClient.PhoneNumber);
            }

            // Add contact phone numbers
            if (selectedClient?.Contacts != null)
            {
                foreach (var contact in selectedClient.Contacts)
                {
                    // Add primary phone if it exists
                    if (contact.PrimaryPhone != null)
                    {
                        phoneNumbers.Add(contact.PrimaryPhone.Number);
                    }

                    // Add all other phone numbers
                    if (contact.PhoneNumbers != null)
                    {
                        phoneNumbers.AddRange(
                            contact.PhoneNumbers
                                .Where(p => !p.IsPrimary) // Skip primary as it's already added
                                .Select(p => p.Number)
                        );
                    }
                }
            }

            return phoneNumbers.Where(phone => !string.IsNullOrEmpty(phone)).Distinct().ToList();
        }

        // - - - - - User Interface - - - - - -   -   -     -   /
        protected void SetShowCreatePolicy()
        {
            _ = tabInterface.GoToTabAsync("tab-2");
            showCreatePolicy = true;
        }
        protected void SetHideCreatePolicy()
        {
            showCreatePolicy = false;
        }
        protected void ExpandDetails(int? forceIt = null)
        {
            if (forceIt == 1)
            {
                dynamicClass = "sf-quicklist";
            }
            else if (forceIt == 0)
            {
                dynamicClass = "sf-quicklist-close";
            }
            else
            {
                dynamicClass = dynamicClass == "sf-quicklist" ? "sf-quicklist-close" : "sf-quicklist";
            }
        }
        protected void UpdatePolicyLists()
        {
            var today = DateTime.Today;

            // Get the updated policies from the client
            var allPolicies = selectedClient?.Policies ?? new List<Policy>();

            // Update currentPolicies and pastPolicies
            currentPolicies = allPolicies
                .Where(p => p.EffectiveDate <= p.ExpirationDate && p.ExpirationDate >= today)
                .OrderByDescending(p => p.EffectiveDate)
                .ToList();

            pastPolicies = allPolicies
                .Where(p => p.ExpirationDate < today)
                .OrderByDescending(p => p.EffectiveDate)
                .ToList();
        }
        protected void OnInputChanged(Microsoft.AspNetCore.Components.ChangeEventArgs e)
        {
            searchTerm = e.Value?.ToString() ?? string.Empty;
            _clientListCts?.Cancel(); // Cancel any previous task
            _clientListCts = new CancellationTokenSource();
            var cancellationToken = _clientListCts.Token;

            _ = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(150, cancellationToken);

                    await InvokeAsync(async () =>
                    {
                        await FilterClients();
                        StateHasChanged();
                    });
                }
                catch (TaskCanceledException)
                {
                    // Task was canceled, safe to ignore
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error in OnInputChanged: {ex.Message}");
                }
            }, cancellationToken);
        }
        protected async Task OnActiveTabChanged()
        {
            if (ClientStateService != null)
            {
                await ClientStateService.SaveStateAsync();
            }
        }
        public void showNotesToggle()
        {
            showNotes = !showNotes;
            StateHasChanged();
        }

        // - - - - - Outlook Interop - - - - - -   -   -     -  /
        public async Task OutlookSearchForThisPolicy(string policyNumber)
        {
            var policySearchList = StringHelper.GeneratePolicyVariations(policyNumber);
            await EmberService.RunEmberFunction("OutlookSearch_Policy", policySearchList);
        }
        public async Task OutlookSearchBroad()
        {
            List<string> emailAddresses = GetAllEmailAddresses();
            await EmberService.RunEmberFunction("OutlookSearch_EmailBroad", emailAddresses);
        }
        public async Task OutlookSearchStrict()
        {
            List<string> emailAddresses = GetAllEmailAddresses();
            await EmberService.RunEmberFunction("OutlookSearch_EmailStrictToFrom", emailAddresses);
        }
        public async Task OutlookSearchSmart()
        {
            var policySearchList = StringHelper.GenerateCompanyNameVariations(selectedClient.Name);
            await EmberService.RunEmberFunction("OutlookSearch_SmartSearch", policySearchList);
        }
        public List<string> GetAllEmailAddresses()
        {
            var emailAddresses = new List<string>();

            // Add client's legacy email if it exists
            if (!string.IsNullOrEmpty(selectedClient?.Email))
            {
                emailAddresses.Add(selectedClient.Email);
            }

            // Add all contact email addresses
            if (selectedClient?.Contacts != null)
            {
                foreach (var contact in selectedClient.Contacts)
                {
                    // Add primary email if it exists
                    if (contact.PrimaryEmail != null)
                    {
                        emailAddresses.Add(contact.PrimaryEmail.Email);
                    }

                    // Add all other email addresses
                    if (contact.EmailAddresses != null)
                    {
                        emailAddresses.AddRange(
                            contact.EmailAddresses
                                .Where(e => !e.IsPrimary) // Skip primary as it's already added
                                .Select(e => e.Email)
                        );
                    }
                }
            }

            return emailAddresses.Where(email => !string.IsNullOrEmpty(email)).Distinct().ToList();
        }
        public async Task OutlookNewEmail(string toEmail = null, string subject = null, string body = null)
        {
            if (toEmail != null && subject != null && body != null)
            {
                var myParams = new List<string> { toEmail, subject, body };
                await EmberService.RunEmberFunction("OutlookEmail_CreateNew", myParams);
            }
        }

        // - - - - - Plugins  - - - - - -   -     -        -    /
        protected async Task ForceImportPolicies()
        {
            Console.WriteLine("Forcing policy import...");
            await RunDataSync(true);
        }
        protected async Task RunDataSync(bool forceUpdate)
        {
            _dataSyncCts?.Cancel(); // Cancel any previous task
            _dataSyncCts = new CancellationTokenSource();
            var cancellationToken = _dataSyncCts.Token;

            try
            {
                utilityStatus = "Syncing Policies...";
                utilityLoading = true;

                var plugin = _plugin.GetPluginByName("Applied Epic Cloud API");
                if (plugin == null || !plugin.IsActive || _stateService.DisablePlugins)
                {
                    Console.Error.WriteLine("DataSync plugin not found or is inactive.");
                    utilityStatus = "Sync plugin not available.";
                    utilityLoading = false;
                    return;
                }

                _stateService.UpdateStatus(utilityStatus, true);
                if (selectedClient == null)
                {
                    NavigationManager.NavigateTo("/");
                }
                // Execute the DataSync method on the plugin
                var response = await plugin.ExecuteAsync("DataSync", new object[] { selectedClient.ClientId, forceUpdate }, cancellationToken);

                if (response?.success == true && response.cleanup == true)
                {
                    await ReloadSelectedClient();
                }
            }
            catch (OperationCanceledException)
            {
                Console.Error.WriteLine("DataSync operation canceled.");
            }
            catch (Exception ex)
            {
                await _logs.LogAsync(LogLevel.Error, ex.Message, "Clients.razor - RunDataSync");
                Console.Error.WriteLine($"Error running DataSync plugins: {ex.Message}");
                _stateService.UpdateStatus($"Error running DataSync plugins: {ex.Message}", false);
                return;
            }
            finally
            {
                // Update the last opened date
                await ClientService.UpdateLastOpenedAsync(selectedClient.ClientId, DateTime.Now);
                selectedClient.DateOpened = DateTime.Now;
                filteredClients = clients.OrderByDescending(c => c.DateOpened).Take(40).ToList();

                UpdatePolicyLists();

                utilityStatus = "";
                utilityLoading = false;
                await InvokeAsync(StateHasChanged);
            }
        }
        protected async Task UtilImportContacts()
        {
            utilityStatus = "Grabbing contacts from DataSync API...";
            utilityLoading = true;
            _stateService.UpdateStatus(utilityStatus, true);
            try
            {
                Console.WriteLine("Looking for DataSync Plugins...");

                //Looks for and runs any plugins with a GetContacts type and  method
                //var responses = await _stateService.RunPluginMethodAsync<IDataSyncPlugin>(async plugin => await plugin.GetContacts(selectedClient.eClientId));
                var responses = await _stateService.RunPluginMethodAsync("GetContacts", new object[] { selectedClient.eClientId }, CancellationToken.None);

                if (responses.FirstOrDefault() != null)
                {
                    if (responses.FirstOrDefault()?.success == true && responses.FirstOrDefault()?.contacts.Count > 0)
                    {
                        foreach (var contactItem in responses.FirstOrDefault()?.contacts)
                        {
                            loadedContacts.Add(contactItem);
                        }
                    }
                    utilityStatus = responses.FirstOrDefault()?.message;
                }

            }
            catch (Exception ex)
            {
                await _logs.LogAsync(LogLevel.Error, ex.Message, "Clients.razor - GetContacts");
                Console.Error.WriteLine($"Error running GetContacts plugins: {ex.Message}");
                utilityStatus = "Error running GetContacts plugins...";
                utilityLoading = false;
            }
            utilityLoading = false;
            _stateService.UpdateStatus(utilityStatus, false);
        }

        // - - - - - Utilities - - - - - -   -     -        -    /
        protected async Task CreateNewForm(int formPdfId)
        {
            try
            {
                int newFormDocId = await FormService.CreateFormDoc(formPdfId, selectedClient.ClientId);
                NavigationManager.NavigateTo($"/Forms/Editor/{newFormDocId}");
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error creating new form: {ex.Message}");
            }
        }
        protected async Task AddLoadedContactsToClient()
        {
            if (selectedClient != null && loadedContacts.Any())
            {
                try
                {
                    await ClientService.AddContactsToClientAsync(selectedClient.ClientId, loadedContacts.ToList());
                    loadedContacts.Clear();
                    contactList.Clear();
                    await LoadClient(selectedClient.ClientId);
                    await tabInterface.GoToTabAsync("tab-1");
                    NavigationManager.NavigateTo($"/Clients/{selectedClient.ClientId}", false);
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Error adding contacts: {ex.Message}");
                }
            }
        }
        private async Task ReloadSelectedClient()
        {
            Console.WriteLine("Reloading selected client and all related data");
            try
            {
                // Re-fetch the client from the database with all related data
                selectedClient = await ClientService.GetClientById(selectedClient.ClientId);

                // Update contact list
                contactList = selectedClient?.Contacts?.ToList() ?? new List<Contact>();

                // Update phone numbers
                phoneNumbers = GetClientAndContactPhoneNumbers(selectedClient);

                // Update policy lists
                var today = DateTime.Today;
                currentPolicies = selectedClient?.Policies
                    .Where(p => p.EffectiveDate <= p.ExpirationDate && p.ExpirationDate >= today)
                    .OrderByDescending(p => p.EffectiveDate)
                    .ToList() ?? new List<Policy>();

                pastPolicies = selectedClient?.Policies
                    .Where(p => p.ExpirationDate < today)
                    .OrderByDescending(p => p.EffectiveDate)
                    .ToList() ?? new List<Policy>();

                // Update client list if needed
                var updatedClient = clients.FirstOrDefault(c => c.ClientId == selectedClient.ClientId);
                if (updatedClient != null)
                {
                    updatedClient.DateOpened = selectedClient.DateOpened;
                    filteredClients = clients.OrderByDescending(c => c.DateOpened).Take(40).ToList();
                    
                    // Also update the cached client list if it exists
                    if (ClientStateService != null && ClientStateService.AllClients != null)
                    {
                        var cachedClient = ClientStateService.AllClients.FirstOrDefault(c => c.ClientId == selectedClient.ClientId);
                        if (cachedClient != null)
                        {
                            cachedClient.DateOpened = selectedClient.DateOpened;
                            // Save the updated cache
                            await ClientStateService.SaveStateAsync();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error updating selected client data: {ex.Message}");
            }
        }
        protected async void HandleAttachmentAdded()
        {
            Console.WriteLine("Triggering parent refresh after attachment added...");
        }

        // - - - - - AI Summaries - - - - - -   -   -    -      /
        protected async Task GetAllClientData()
        {
            if (selectedClient == null)
            {
                Console.WriteLine("No client selected");
                return;
            }
            if (ClientService == null)
            {
                Console.WriteLine("ClientService not available.");
                return;
            }
            if (OpenAIAgent == null)
            {
                Console.WriteLine("OpenAIAgent not available.");
                await _logs?.LogAsync(LogLevel.Error, "OpenAIAgent not injected or available.", "Clients.GetAllClientData");
                return;
            }

            try
            {
                Console.WriteLine("\n=== Gathering Client Data ===");
                object? clientData = null; // Use object? to allow for potential nulls
                string jsonData = "{}"; // Default to empty JSON object

                // Get all the data (keep existing logic)
                var businessDetails = await ClientService.GetBusinessDetailsByClientId(selectedClient.ClientId);
                var globalNotes = await ClientService.GetGlobalNotesByEntityAsync(EntityType.Client, selectedClient.ClientId);
                var allContacts = selectedClient.Contacts.ToList();
                var allPolicies = selectedClient.Policies.ToList();
                var allEmails = GetAllEmailAddresses();
                var allPhoneNumbers = GetClientAndContactPhoneNumbers(selectedClient);

                // Create the data structure (keep existing logic)
                clientData = new // Assign to the variable
                {
                    Client = new
                    {
                        selectedClient.ClientId,
                        selectedClient.Name,
                        selectedClient.PhoneNumber,
                        selectedClient.Email,
                        selectedClient.Address,
                        selectedClient.DateOpened,
                        selectedClient.CreatedDate,
                        ClientNotes = selectedClient.ClientNotes?.Select(cn => new { cn.Note, cn.DateCreated }).ToList(), // Select specific fields if needed
                        selectedClient.LookupCode,
                        selectedClient.eClientId
                    },
                    BusinessDetails = businessDetails != null ? new
                    {
                        businessDetails.ShortDescription,
                        businessDetails.LongDescription,
                        businessDetails.BusinessIndustry,
                        businessDetails.BusinessSpecialty,
                        businessDetails.BusinessType,
                        businessDetails.LegalEntityType,
                        businessDetails.AnnualGrossSalesRevenueReceipts,
                        businessDetails.AnnualPayrollHazardExposure,
                        businessDetails.NumFullTimeEmployees,
                        businessDetails.NumPartTimeEmployees,
                        businessDetails.DateStarted,
                        businessDetails.YearsExperience,
                        businessDetails.LicenseType // Added based on prompt
                    } : null,
                    Contacts = allContacts.Select(c => new
                    {
                        c.ContactId,
                        c.FullName,
                        c.Title,
                        Phone = c.PrimaryPhone?.Number ?? c.PhoneNumbers?.FirstOrDefault(p => p.Type == PhoneType.Office)?.Number,
                        Mobile = c.PhoneNumbers?.FirstOrDefault(p => p.Type == PhoneType.Mobile)?.Number,
                        Email = c.PrimaryEmail?.Email ?? c.EmailAddresses?.FirstOrDefault()?.Email,
                        c.Notes,
                        c.IsStarred,
                        IsPrimary = c.ContactId == selectedClient.PrimaryContactId,
                        PhoneNumbers = c.PhoneNumbers?.Select(p => new
                        {
                            p.Number,
                            p.Type,
                            p.IsPrimary
                        }).ToList(),
                        EmailAddresses = c.EmailAddresses?.Select(e => new
                        {
                            e.Email,
                            e.Label,
                            e.IsPrimary
                        }).ToList()
                    }).ToList(),
                    Policies = allPolicies.Select(p => new
                    {
                        p.PolicyId,
                        p.PolicyNumber,
                        p.Carrier?.CarrierName, // Get Carrier Name if available
                        p.Product?.LineName, // Get Product Name if available
                        p.EffectiveDate,
                        p.ExpirationDate,
                        p.Premium,
                        p.Status, // Ensure Status enum/string is meaningful
                        // Include Vehicle count or details if needed
                        VehicleCount = p.Vehicles?.Count ?? 0,
                        p.Notes
                    }).ToList(),
                    GlobalNotes = globalNotes?.Select(n => new
                    {
                        n.Text,
                        n.Tags,
                        n.CreatedAt,
                        n.AuthorName,
                        n.Pinned
                    }).ToList(),
                };

                // Serialize with indentation for readability (keep existing logic)
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    ReferenceHandler = ReferenceHandler.Preserve,
                };

                jsonData = JsonSerializer.Serialize(clientData, options);

                // Log to console (keep existing logic)
                Console.WriteLine("\n=== Complete Client Data (JSON Payload) ===");
                Console.WriteLine(jsonData);
                Console.WriteLine("\n=== End of Client Data (JSON Payload) ===");

                // Log summary (keep existing logic)
                Console.WriteLine($"\nData Summary:");
                Console.WriteLine($"- Client: selectedClient.Name (ID: selectedClient.Id)");
                Console.WriteLine($"- Business Details: {(businessDetails != null ? "Available" : "Not available")}");
                Console.WriteLine($"- Contacts: allContacts.Count");
                Console.WriteLine($"- Policies: allPolicies.Count");
                Console.WriteLine($"- Global Notes: {globalNotes?.Count ?? 0}");


                // --- *** NEW: Call OpenAI *** ---
                Console.WriteLine("\n=== Requesting AI Client Brief ===");
                isLoading = true; // Optionally show loading indicator
                StateHasChanged();

                // Define the prompt provided by the user
                string basePrompt = """
                You're a commercial-insurance-savvy AI assistant.
                You've just been handed a JSON file with everything we know about a client - who they are, what they do, who works there, what kind of coverage they have, notes we've taken over time, and more.
                Your job?
                Write a quick, conversational summary to get a fellow agent up to speed. Pretend you're talking to an associate who just asked, "Can you bring me up to speed with this client?"
                Focus on:
                Who they are and what they do
                Who the main people are
                What kind of insurance they've got (and what they don't)
                What's important to keep in mind (based on notes, renewals, etc.)
                Be natural, like you're chatting over coffee - but keep it professional and don't skip the important stuff.
                You'll get a chunk of JSON with fields like Client, Contacts, Policies, Notes, etc.
                Just read the data and speak human.
                Format your response for legibility with simple html tags like strong, p, ul, li and br.
                Don't include a heading or greeting.
                Ready? Here�s the JSON:
                """; // Raw string literal ends here"""; // Raw string literal ends here

                // Define the prompt provided by the user
                string basePrompt2 = """
                You are an AI assistant tasked with generating a concise, natural-language client summary for an insurance agency. Your purpose is to brief a colleague who is unfamiliar with a specific client account.

                **Input Context:**

                You will receive JSON data representing the client's information, including details about the client entity, business specifics, contacts, policies, and notes. Use this data to generate your summary.

                **Task:**

                Generate a "Client Brief" summary based *only* on the provided JSON data.

                **Instructions:**

                1.  **Format:** Use simple HTML tags for structure:
                    *   A main heading `<strong>Client Brief: [Client Name]</strong>` (extract Client Name from the JSON).
                    *   The main summary within `<p>` tags. Use `<strong>` tags to highlight key terms like business type, license type, key personnel roles (like primary contact or owner), or significant warnings found in notes.
                    *   A "Key Points" section using an unordered list (`<ul>` and `<li>`).
                2.  **Tone:** Write conversationally and informally, as if you were verbally briefing a colleague in the office.
                3.  **Content - Main Brief (Derive from JSON):**
                    *   **Identify the client:** Use `Client.Name`, `BusinessDetails.BusinessType`, `BusinessDetails.ShortDescription`, `BusinessDetails.BusinessSpecialty`, and `BusinessDetails.LicenseType`.
                    *   **History & Experience:** Mention years in business (use `BusinessDetails.YearsExperience` or calculate from `BusinessDetails.DateStarted`). Use `Client.Address` for location context if available.
                    *   **Key People:** Identify the primary contact (find contact where `IsPrimary` might be true, or use `Client.PrimaryContactId` if available, then look up in `Contacts`). Mention any `Contacts` marked as `Owner`. Look for relationship insights in `Client.ClientNotes.Note` or `Contacts.Notes` or `GlobalNotes.Text`.
                    *   **Operational Notes:** Extract crucial operational characteristics, warnings, or quirks noted in `Client.ClientNotes.Note` or `GlobalNotes.Text`.
                    *   **Account Size & Structure:** Count active policies (where `Policies.Status` indicates active, e.g., "Active", "Renewed" - adapt based on actual status values). Sum `Policies.Premium` for active policies. Mention `BusinessDetails.LegalEntityType` and count contacts where `Owner` is true.
                    *   **Key Figures:** Use `BusinessDetails.NumFullTimeEmployees`/`NumPartTimeEmployees` and sum `Policies.VehicleCount` across relevant policies (e.g., Auto).
                    *   **Significant Events:** Look for notes in `GlobalNotes` or `ClientNotes` indicating recent significant events. Check `Policies` for recently expired or non-renewed policies.
                4.  **Content - Key Points (Synthesize from JSON):**
                    *   Focus on **actionable insights** from `GlobalNotes` or `ClientNotes`. Are there recent high-priority notes (e.g., `Pinned` notes)? Are there notes indicating client concerns or needs?
                    *   Highlight potential **opportunities** suggested by notes (e.g., "Client asked about cyber liability" in a note) or policy data (e.g., only has GL, potential for Workers Comp). Mention if key policies are expiring soon (`Policies.ExpirationDate`).
                    *   Note any communication flags from notes (e.g., "Client prefers email", "Difficult to reach by phone").
                5.  **Prioritization:** Emphasize information that helps the colleague quickly understand *who* the client is, *how* to interact with them (based on notes), the *value* of the account (premium, policy count), and any *immediate* actions or considerations (expiring policies, urgent notes).
                6.  **Insurance Specifics:** Frame the information relevantly for an insurance agent.

                **Input JSON Data:**
                ```json
                """; // Raw string literal ends here

                string fullPrompt = basePrompt + jsonData + "\n```"; // Combine base prompt and the actual JSON data

                // Send to OpenAI service using new OpenAIAgent
                var aiRequest = new UnifiedRequest
                {
                    Input = fullPrompt,
                    UserId = "client-analysis",
                    SessionId = $"client-{selectedClient.ClientId}",
                    Context = new Dictionary<string, object>
                    {
                        ["clientId"] = selectedClient.ClientId,
                        ["operation"] = "client_brief_generation"
                    }
                };

                var aiResponse = await OpenAIAgent.ProcessRequestAsync(aiRequest, default, IntentType.GeneralAI);
                string? aiSummary = aiResponse.Success ? aiResponse.Response : null;

                Console.WriteLine("\n=== AI Client Brief Response ===");
                if (!string.IsNullOrWhiteSpace(aiSummary))
                {
                    Console.WriteLine(aiSummary);
                }
                else
                {
                    await _logs?.LogAsync(LogLevel.Warning, "AI Client Brief generation returned null or empty.", "Clients.GetAllClientData");
                }

                // Store the AI summary response
                if (!string.IsNullOrEmpty(aiSummary))
                {
                    AiSummary = aiSummary.Replace("```html", "").Replace("```", "").Trim();
                }
                IsGeneratingAiSummary = false;
                await InvokeAsync(StateHasChanged);
            }
            catch (JsonException jsonEx)
            {
                Console.WriteLine($"Error serializing client data: {jsonEx.Message}");
                await _logs?.LogAsync(LogLevel.Error, $"JSON Serialization Error in GetAllClientData: {jsonEx.Message}", "Clients.GetAllClientData");
            }
            catch (Exception ex) // Catch potential errors during data gathering or AI call
            {
                Console.WriteLine($"Error during client data processing or AI call: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                await _logs?.LogAsync(LogLevel.Error, $"Error in GetAllClientData: {ex.Message} \nStackTrace: {ex.StackTrace}", "Clients.GetAllClientData");
                utilityStatus = "Error generating client brief."; // Update UI feedback if needed
                IsGeneratingAiSummary = false;
                await InvokeAsync(StateHasChanged);
            }
            finally
            {
                isLoading = false; // Ensure loading indicator is turned off
                await InvokeAsync(StateHasChanged); // Update the UI
            }
        }
        protected async Task ShowAiSummaryDialog()
        {
            Console.WriteLine("Show AI Summary Dialog");
            AiSummaryTitle = "Client Brief";
            AiSummaryLoadingMessage = "Generating client summary...";
            IsAiSummaryDialogOpen = true;
            IsGeneratingAiSummary = true;
            StateHasChanged();

            await GetAllClientData();
        }
        protected async Task ShowAiRecentActivitySummaryDialog()
        {
            // First check if there are un-transcribed calls
            if (RecentPhoneCallsListComponent != null)
            {
                UntranscribedCallsCount = await RecentPhoneCallsListComponent.GetUntranscribedCallsCountAsync();
                
                if (UntranscribedCallsCount > 0)
                {
                    // Show custom dialog instead of system confirm
                    IsTranscriptionConfirmDialogOpen = true;
                    StateHasChanged();
                    return; // Exit early, will continue after user choice
                }
            }
            
            // If no un-transcribed calls or user already made choice, proceed with AI summary
            await GenerateAiRecentActivitySummary();
        }
        
        /// <summary>
        /// Handles user choice to transcribe calls first
        /// </summary>
        protected async Task OnTranscribeCallsFirst()
        {
            IsTranscriptionConfirmDialogOpen = false;
            StateHasChanged();
            
            await TranscribeAllUntranscribedCalls();
            
            // After transcription is complete, generate the AI summary
            await GenerateAiRecentActivitySummary();
        }
        
        /// <summary>
        /// Handles user choice to proceed with available data
        /// </summary>
        protected async Task OnProceedWithAvailableData()
        {
            IsTranscriptionConfirmDialogOpen = false;
            StateHasChanged();
            
            // Generate AI summary with currently available data
            await GenerateAiRecentActivitySummary();
        }
        
        /// <summary>
        /// Generates the AI recent activity summary
        /// </summary>
        private async Task GenerateAiRecentActivitySummary()
        {
            AiSummaryTitle = "Recent Activity Summary";
            AiSummaryLoadingMessage = "Analyzing recent activities...";
            IsAiSummaryDialogOpen = true;
            IsGeneratingAiSummary = true;
            AiSummary = string.Empty;
            StateHasChanged();

            try
            {
                // Create the prompt for OpenAI
                System.Text.StringBuilder promptBuilder = new System.Text.StringBuilder();

                promptBuilder.AppendLine($"General AI Query! You're a commercial-insurance-savvy AI assistant. \n You've just been handed the most recent emails and call transcriptions along with a list of recent renewals for a client by the name of '{selectedClient?.Name}'.");
                promptBuilder.AppendLine($"Your job?\n Write a quick, conversational summary to bring a fellow agent up to speed on what's going on with the client.");
                promptBuilder.AppendLine($"You'll be given some basic details about the client, but focus on boiling down the key issues and action items between the broker, underwriters, staff and client.");
                promptBuilder.AppendLine($"Be careful when interpreting time phrases like \"today,\" \"next week,\" or \"by Friday.\" Treat {DateTime.Today:MMMM d, yyyy} as \"today\" and look at dates in emails, of phone calls or for renewals relative to today's date before you summarize.");
                promptBuilder.AppendLine($"Format your response for legibility with simple html tags like strong, p, ul, li and br.");

                // Add business context if available
                if (selectedClient != null)
                {
                    // Fetch business details through service
                    BusinessDetails businessDetails = null;
                    try 
                    {
                        businessDetails = await ClientService.GetBusinessDetailsByClientId(selectedClient.ClientId);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error fetching business details: {ex.Message}");
                    }

                    promptBuilder.AppendLine("\nCLIENT BASIC DETAILS:");
                    promptBuilder.AppendLine($"- Client Name: {selectedClient.Name}");

                    if (businessDetails != null)
                    {
                        promptBuilder.AppendLine($"- Industry: {businessDetails.BusinessIndustry ?? "Unknown"}");
                        promptBuilder.AppendLine($"- Business Type: {businessDetails.BusinessType?.ToString() ?? "Unknown"}");
                        if (!string.IsNullOrEmpty(businessDetails.ShortDescription))
                        {
                            promptBuilder.AppendLine($"- Business Summary: {businessDetails.ShortDescription}");
                        }
                    }
                }

                // ADD RECENT EMAILS -----------------------//
                List<string> recentEmailTexts = new List<string>();
                if (recentEmailsComponent != null)
                {
                    recentEmailTexts = recentEmailsComponent.GetTopEmailTexts();
                    Console.WriteLine($"Fetched {recentEmailTexts.Count} recent emails for recent summary");
                }
                if (recentEmailTexts.Any())
                {
                    promptBuilder.AppendLine("\nRECENT EMAILS:");
                    for (int i = 0; i < recentEmailTexts.Count; i++)
                    {
                        promptBuilder.AppendLine($"{i+1}. {recentEmailTexts[i]}");
                    }
                }
                else
                {
                    promptBuilder.AppendLine("\nNo recent emails found.");
                }

                // ADD RECENT CALL TRANSCRIPTIONS --------- //
                List<string> recentTranscriptions = new List<string>();
                if (RecentPhoneCallsListComponent != null)
                {
                    recentTranscriptions = await RecentPhoneCallsListComponent.GetTopCallTranscriptionsAsync();
                    Console.WriteLine($"Fetched {recentTranscriptions.Count} recent phone calls for recent summary");
                }
                if (recentTranscriptions.Any())
                {
                    promptBuilder.AppendLine("\nRECENT CALL TRANSCRIPTIONS:");
                    for (int i = 0; i < recentTranscriptions.Count; i++)
                    {
                        promptBuilder.AppendLine($"{i+1}. {recentTranscriptions[i]}");
                    }
                }

                // ADD RECENT RENEWALS ---------------------//
                List<string> recentRenewals = new List<string>();
                if (RenewalService != null && selectedClient != null)
                {
                    try
                    {
                        recentRenewals = await RenewalService.GetRenewalStringsForAIAsync(selectedClient.ClientId);
                        Console.WriteLine($"Fetched {recentRenewals.Count} recent renewals for recent summary");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error fetching recent renewals: {ex.Message}");
                    }
                }
                
                if (recentRenewals.Any())
                {
                    promptBuilder.AppendLine("\nRECENT RENEWALS:");
                    for (int i = 0; i < recentRenewals.Count; i++)
                    {
                        promptBuilder.AppendLine($"{i+1}. {recentRenewals[i]}");
                    }
                }
                else
                {
                    promptBuilder.AppendLine("\nNo recent renewals found.");
                }


                string prompt = promptBuilder.ToString();

                // Call OpenAI service using OpenAIAgent
                if (OpenAIAgent != null)
                {
                    var aiRequest = new UnifiedRequest
                    {
                        Input = prompt,
                        UserId = "client-activity-analysis",
                        SessionId = $"client-{selectedClient?.ClientId}",
                        Context = new Dictionary<string, object>
                        {
                            ["clientId"] = selectedClient?.ClientId ?? 0,
                            ["operation"] = "recent_activity_summary"
                        }
                    };

                    var aiResponse = await OpenAIAgent.ProcessRequestAsync(aiRequest, default, IntentType.GeneralAI);
                    string? aiSummary = aiResponse.Success ? aiResponse.Response : null;

                    if (!string.IsNullOrEmpty(aiSummary))
                    {
                        // Check if the response is already in HTML format, otherwise wrap it
                        if (!aiSummary.Trim().StartsWith("<") && !aiSummary.Contains("<p>"))
                        {
                            System.Text.StringBuilder htmlResponse = new System.Text.StringBuilder();
                            htmlResponse.AppendLine("<h3>Recent Activity Summary for " + selectedClient?.Name + "</h3>");

                            // Split paragraphs and format as HTML
                            var paragraphs = aiSummary.Split("\n\n", StringSplitOptions.RemoveEmptyEntries);
                            foreach (var paragraph in paragraphs)
                            {
                                htmlResponse.AppendLine($"<p>{paragraph.Replace("\n", "<br/>")}</p>");
                            }

                            AiSummary = htmlResponse.ToString();
                        }
                        else
                        {
                            // Response is already in HTML format
                            AiSummary = aiSummary;
                        }
                    }
                    else
                    {
                        // Fallback if AI response fails
                        System.Text.StringBuilder summaryBuilder = new System.Text.StringBuilder();
                        summaryBuilder.AppendLine("<h3>Recent Activity Summary for " + selectedClient?.Name + "</h3>");

                        if (recentEmailTexts.Any())
                        {
                            summaryBuilder.AppendLine("<h4>Recent Email Communications:</h4>");
                            summaryBuilder.AppendLine("<ul>");
                            foreach (var emailText in recentEmailTexts)
                            {
                                summaryBuilder.AppendLine($"<li>{emailText}</li>");
                            }
                            summaryBuilder.AppendLine("</ul>");
                        }
                        else
                        {
                            summaryBuilder.AppendLine("<p>No recent emails found.</p>");
                        }

                        if (recentTranscriptions.Any())
                        {
                            summaryBuilder.AppendLine("<h4>Recent Call Transcriptions:</h4>");
                            summaryBuilder.AppendLine("<ul>");
                            foreach (var transcript in recentTranscriptions)
                            {
                                summaryBuilder.AppendLine($"<li>{transcript}</li>");
                            }
                            summaryBuilder.AppendLine("</ul>");
                        }
                        else
                        {
                            summaryBuilder.AppendLine("<p>No recent call transcriptions found.</p>");
                        }

                        summaryBuilder.AppendLine("<p class='text-warning'>AI-generated insights could not be produced at this time.</p>");

                        AiSummary = summaryBuilder.ToString();
                    }
                }
                else
                {
                    AiSummary = "<p class='text-danger'>OpenAI Agent not available.</p>";
                }
            }
            catch (Exception ex)
            {
                AiSummary = $"<p class='text-danger'>Error generating recent activity summary: {ex.Message}</p>";
            }
            finally
            {
                IsGeneratingAiSummary = false;
                StateHasChanged();
            }
        }
        
        /// <summary>
        /// Transcribes all un-transcribed calls for the current client
        /// </summary>
        private async Task TranscribeAllUntranscribedCalls()
        {
            if (RecentPhoneCallsListComponent == null) return;
            
            try
            {
                // Get list of un-transcribed call IDs
                var untranscribedCallIds = await RecentPhoneCallsListComponent.GetUntranscribedCallIdsAsync();
                
                if (!untranscribedCallIds.Any())
                {
                    return; // No calls to transcribe, just return silently
                }
                
                int completedCount = 0;
                int totalCount = untranscribedCallIds.Count;
                var failedCalls = new List<string>();
                
                // Transcribe each call
                foreach (var callId in untranscribedCallIds)
                {
                    try
                    {
                        // Find the call log for this call ID
                        var callLog = RecentPhoneCallsListComponent.GetCallLogById(callId);
                        if (callLog?.recording != null && !string.IsNullOrEmpty(callLog.recording.id))
                        {
                            // Use the existing TranscribeRecording method from RecentPhoneCallsListComponent
                            await RecentPhoneCallsListComponent.TranscribeRecordingAsync(callId, callLog.recording.id, callLog.recording.contentUri);
                            completedCount++;
                            
                            // Update progress every few calls
                            if (completedCount % 3 == 0 || completedCount == totalCount)
                            {
                                Console.WriteLine($"Transcribed {completedCount} of {totalCount} calls...");
                            }
                        }
                        else
                        {
                            failedCalls.Add(callId);
                            Console.WriteLine($"Call {callId} has no recording available");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error transcribing call {callId}: {ex.Message}");
                        failedCalls.Add(callId);
                        // Continue with other calls even if one fails
                    }
                }
                
                // Log completion to console only
                Console.WriteLine($"Completed transcription of {completedCount} out of {totalCount} calls.");
                if (failedCalls.Any())
                {
                    Console.WriteLine($"Failed to transcribe {failedCalls.Count} calls due to errors or missing recordings.");
                }
                
                // Refresh the component to show updated transcriptions
                await RecentPhoneCallsListComponent.RefreshTopCallTranscriptions();
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in TranscribeAllUntranscribedCalls: {ex.Message}");
                // Don't show error dialog - just log to console
            }
        }
        protected void CloseAiSummaryDialog()
        {
            IsAiSummaryDialogOpen = false;
            StateHasChanged();
        }
        protected async Task CopyAiSummary()
        {
            if (string.IsNullOrEmpty(AiSummary)) return;
            
            // Strip HTML tags for clipboard
            string plainText = Regex.Replace(AiSummary, "<.*?>", string.Empty);
            await JS.InvokeVoidAsync("navigator.clipboard.writeText", plainText);
        }

        // - - - - - Misc - - - -  - -   -      -        -     /
        public void Dispose()
        {
            _dataSyncCts?.Cancel();
            _clientListCts?.Cancel();
            _dataSyncCts?.Dispose();
            _clientListCts?.Dispose();
            if (_stateService != null)
            {
                _stateService.OnClientUpdated -= HandleClientUpdate;
            }
        }
    }
}
