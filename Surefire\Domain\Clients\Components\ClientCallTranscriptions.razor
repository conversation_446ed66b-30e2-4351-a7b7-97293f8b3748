@page "/Clients/{ClientId:int}/transcriptions"
@using Surefire.Domain.Chat
@using Surefire.Domain.Chat.Services
@using Surefire.Domain.Contacts.Services
@using Surefire.Domain.Shared.Services
@using Surefire.Domain.Shared.Helpers
@using Surefire.Domain.Clients.Services
@using Microsoft.EntityFrameworkCore
@using Surefire.Data
@using Surefire.Domain.OpenAI.Simple
@inject StateService _stateService
@inject TranscriptionService _transcriptionService
@inject ContactService _contactService
@inject ClientService _clientService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject IDbContextFactory<ApplicationDbContext> _dbFactory
@inject IOpenAISimpleService _openAiService

<PageTitle>Client Call Transcriptions</PageTitle>

<div class="sf-layout">
    <div class="sf-header">
        <h1>Client Call Transcriptions</h1>
        @if (clientName != null)
        {
            <h2>@clientName</h2>
        }
        <FluentButton Appearance="Appearance.Accent" 
                    OnClick="@(() => NavigationManager.NavigateTo($"/Clients/{ClientId}"))" 
                    IconStart="@(new Icons.Regular.Size20.ArrowLeft())">
            Back to Client
        </FluentButton>
    </div>

    @if (isLoading)
    {
        <div class="loading-container">
            <FluentProgressRing Width="50px" Color="#1b8ce3" />
            <p>Loading transcriptions...</p>
        </div>
    }
    else if (transcriptions == null || transcriptions.Count == 0)
    {
        <div class="empty-state">
            <FluentIcon Value="@(new Icons.Regular.Size48.SoundWaveCircle())" CustomColor="#b7b7b7" Color="Color.Custom" />
            <p>No call transcriptions found for this client.</p>
            <p>Go to the call history section to transcribe call recordings.</p>
        </div>
    }
    else
    {
        <div class="sf-content">
            <div class="filters">
                <FluentSearch @bind-Value="searchTerm" Placeholder="Search transcriptions..." @oninput="SearchTranscriptions" />
                
                <FluentSelect TOption="string" @bind-Value="dateFilter" Label="Date Range">
                    <FluentOption Value="all">All Dates</FluentOption>
                    <FluentOption Value="today">Today</FluentOption>
                    <FluentOption Value="week">Last 7 Days</FluentOption>
                    <FluentOption Value="month">Last 30 Days</FluentOption>
                </FluentSelect>
            </div>
            
            <div class="transcription-list">
                @foreach (var transcription in filteredTranscriptions)
                {
                    <FluentCard Class="transcription-card">
                        <div class="card-header">
                            <div class="phone-info">
                                <FluentIcon Value="@(new Icons.Regular.Size20.Phone())" />
                                <span>@StringHelper.FormatPhoneNumber(transcription.PhoneNumber)</span>
                            </div>
                            
                            <div class="date-info">
                                <FluentIcon Value="@(new Icons.Regular.Size20.Calendar())" />
                                <span>@(transcription.CallStartTime?.ToString("MM/dd/yyyy hh:mm tt") ?? "Unknown Date")</span>
                            </div>
                            
                            @if (transcription.CallDuration.HasValue)
                            {
                                <div class="duration-info">
                                    <FluentIcon Value="@(new Icons.Regular.Size20.Timer())" />
                                    <span>@StringHelper.FormatSeconds(transcription.CallDuration.Value)</span>
                                </div>
                            }
                        </div>
                        
                        <div class="transcription-text">
                            @GetPreviewText(transcription.TranscriptionText)
                        </div>
                        
                        <div class="card-actions">
                            <FluentButton Appearance="Appearance.Lightweight" 
                                         IconStart="@(new Icons.Regular.Size20.TextDescription())" 
                                         OnClick="@(() => ViewTranscription(transcription))">
                                View Full Transcription
                            </FluentButton>
                            
                            <FluentButton Appearance="Appearance.Lightweight" 
                                         IconStart="@(new Icons.Regular.Size20.Bot())" 
                                         OnClick="@(() => GenerateAndShowAiSummary(transcription))">
                                AI Summary
                            </FluentButton>
                            
                            <FluentButton Appearance="Appearance.Lightweight" 
                                         IconStart="@(new Icons.Regular.Size20.Copy())" 
                                         OnClick="@(() => CopyToClipboard(transcription.TranscriptionText))">
                                Copy Text
                            </FluentButton>
                            
                            <FluentButton Appearance="Appearance.Lightweight" 
                                         IconStart="@(new Icons.Regular.Size20.Delete())" 
                                         OnClick="@(() => DeleteTranscription(transcription))">
                                Delete
                            </FluentButton>
                        </div>
                    </FluentCard>
                }
            </div>
        </div>
    }
</div>

@if (showTranscriptionDialog)
{
    <FluentDialog @bind-Visible="@showTranscriptionDialog" Title="Call Transcription" TrapFocus="true" Modal="true">
        <FluentDialogBody>
            <div class="dialog-content">
                <div class="dialog-header">
                    <div>
                        <strong>Phone:</strong> @StringHelper.FormatPhoneNumber(selectedTranscription?.PhoneNumber ?? "")
                    </div>
                    <div>
                        <strong>Date:</strong> @(selectedTranscription?.CallStartTime?.ToString("MM/dd/yyyy hh:mm tt") ?? "Unknown")
                    </div>
                    @if (selectedTranscription?.CallDuration.HasValue == true)
                    {
                        <div>
                            <strong>Duration:</strong> @StringHelper.FormatSeconds(selectedTranscription.CallDuration.Value)
                        </div>
                    }
                </div>
                
                <div class="transcription-full-text">
                    @selectedTranscription?.TranscriptionText
                </div>
            </div>
        </FluentDialogBody>
        <FluentDialogFooter>
            <FluentButton Appearance="Appearance.Accent" OnClick="@(() => CopyToClipboard(selectedTranscription?.TranscriptionText ?? ""))" IconStart="@(new Icons.Regular.Size20.Copy())">
                Copy Text
            </FluentButton>
            <FluentButton Appearance="Appearance.Accent" OnClick="CloseTranscriptionDialog">Close</FluentButton>
        </FluentDialogFooter>
    </FluentDialog>
}

@if (showAiSummaryDialog)
{
    <FluentDialog @bind-Visible="@showAiSummaryDialog" Title="AI Call Summary" TrapFocus="true" Modal="true">
        <FluentDialogBody>
            <div class="dialog-content">
                <div class="dialog-header">
                    <div>
                        <strong>Phone:</strong> @StringHelper.FormatPhoneNumber(selectedTranscription?.PhoneNumber ?? "")
                    </div>
                    <div>
                        <strong>Date:</strong> @(selectedTranscription?.CallStartTime?.ToString("MM/dd/yyyy hh:mm tt") ?? "Unknown")
                    </div>
                </div>
                
                @if (isGeneratingAiSummary)
                {
                    <div class="ai-summary-section">
                        <div class="loading-container">
                            <FluentProgressRing Width="30px" Color="#1b8ce3" />
                            <p>Generating AI summary...</p>
                        </div>
                    </div>
                }
                else if (!string.IsNullOrEmpty(aiSummary))
                {
                    <div class="ai-summary-section">
                        <div class="ai-summary-header">
                            <FluentIcon Value="@(new Icons.Regular.Size24.Bot())" CustomColor="#1b8ce3" Color="Color.Custom" />
                            <h4>AI Call Summary</h4>
                        </div>
                        <div class="ai-summary-content">
                            @((MarkupString)aiSummary)
                        </div>
                    </div>
                }
            </div>
        </FluentDialogBody>
        <FluentDialogFooter>
            @if (isGeneratingAiSummary)
            {
                <FluentButton Appearance="Appearance.Accent" Disabled="true">Close</FluentButton>
            }
            else
            {
                <FluentButton Appearance="Appearance.Accent" OnClick="@(() => CopyToClipboard(aiSummary))" IconStart="@(new Icons.Regular.Size20.Copy())">
                    Copy Summary
                </FluentButton>
                <FluentButton Appearance="Appearance.Accent" OnClick="CloseAiSummaryDialog">Close</FluentButton>
            }
        </FluentDialogFooter>
    </FluentDialog>
}

@code {
    [Parameter] public int ClientId { get; set; }
    
    private List<CallTranscription> transcriptions = new();
    private List<CallTranscription> filteredTranscriptions = new();
    private bool isLoading = true;
    private string clientName;
    private string searchTerm = "";
    private string dateFilter = "all";
    private bool showTranscriptionDialog = false;
    private CallTranscription selectedTranscription;
    private bool isGeneratingAiSummary = false;
    private string aiSummary = "";
    private bool showAiSummaryDialog = false;
    
    protected override async Task OnInitializedAsync()
    {
        await LoadClientInfo();
        await LoadTranscriptions();
    }
    
    private async Task LoadClientInfo()
    {
        try
        {
            var client = await _clientService.GetClientById(ClientId);
            if (client != null)
            {
                clientName = client.Name;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading client info: {ex.Message}");
            clientName = $"Client #{ClientId}";
        }
    }
    
    private async Task LoadTranscriptions()
    {
        isLoading = true;
        try
        {
            // Get client phone numbers
            var clientPhoneNumbers = await GetClientPhoneNumbersAsync();
            
            // Use the improved method that matches by phone numbers
            transcriptions = await _transcriptionService.GetTranscriptionsForClientByPhoneNumbersAsync(ClientId, clientPhoneNumbers);
            ApplyFilters();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading transcriptions: {ex.Message}");
            transcriptions = new List<CallTranscription>();
            filteredTranscriptions = new List<CallTranscription>();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
    
    private async Task<List<string>> GetClientPhoneNumbersAsync()
    {
        var phoneNumbers = new List<string>();
        
        try
        {
            using var context = await _dbFactory.CreateDbContextAsync();
            
            //Get Client from ClientId
            var client = await context.Clients
                .Where(c => c.ClientId == ClientId)
                .FirstOrDefaultAsync();
            if (client != null)
            {
                phoneNumbers.Add(client.PhoneNumber);
            }

            // Get phone numbers from contacts
            var contactPhones = await context.PhoneNumbers
                .Where(p => p.Contact.ClientId == ClientId)
                .Select(p => p.Number)
                .ToListAsync();
            phoneNumbers.AddRange(contactPhones);
            
            // Normalize all phone numbers
            return phoneNumbers.Select(NormalizePhoneNumber).Where(p => !string.IsNullOrEmpty(p)).ToList();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error getting client phone numbers: {ex.Message}");
            return phoneNumbers;
        }
    }
    
    private string NormalizePhoneNumber(string phoneNumber)
    {
        if (string.IsNullOrEmpty(phoneNumber))
            return string.Empty;
            
        // Remove all non-digit characters
        var digitsOnly = new string(phoneNumber.Where(char.IsDigit).ToArray());
        
        // Ensure 10-digit US numbers have country code
        if (digitsOnly.Length == 10)
            return "1" + digitsOnly;
            
        // If it already has country code (11 digits starting with 1)
        if (digitsOnly.Length == 11 && digitsOnly.StartsWith("1"))
            return digitsOnly;
            
        // Otherwise return as is
        return digitsOnly;
    }
    
    private void SearchTranscriptions(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? "";
        ApplyFilters();
    }
    
    private void ApplyFilters()
    {
        // Start with all transcriptions
        var result = transcriptions;
        
        // Apply search filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            result = result
                .Where(t => (t.TranscriptionText?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false) ||
                           (t.PhoneNumber?.Contains(searchTerm) ?? false))
                .ToList();
        }
        
        // Apply date filter
        DateTime cutoffDate = DateTime.Now;
        
        switch (dateFilter)
        {
            case "today":
                cutoffDate = DateTime.Today;
                result = result.Where(t => t.CallStartTime?.Date >= cutoffDate.Date).ToList();
                break;
            case "week":
                cutoffDate = DateTime.Now.AddDays(-7);
                result = result.Where(t => t.CallStartTime >= cutoffDate).ToList();
                break;
            case "month":
                cutoffDate = DateTime.Now.AddDays(-30);
                result = result.Where(t => t.CallStartTime >= cutoffDate).ToList();
                break;
        }
        
        // Always sort by date (newest first)
        filteredTranscriptions = result
            .OrderByDescending(t => t.CallStartTime ?? DateTime.MinValue)
            .ToList();
        
        StateHasChanged();
    }
    
    private string GetPreviewText(string text)
    {
        if (string.IsNullOrEmpty(text))
            return "No transcription available";
            
        return StringHelper.TruncateText(text, 200);
    }
    
    private void ViewTranscription(CallTranscription transcription)
    {
        selectedTranscription = transcription;
        aiSummary = "";  // Reset AI summary when viewing a new transcription
        showTranscriptionDialog = true;
        StateHasChanged();
    }
    
    private async Task CopyToClipboard(string text)
    {
        if (!string.IsNullOrEmpty(text))
        {
            await JSRuntime.InvokeVoidAsync("navigator.clipboard.writeText", text);
            await JSRuntime.InvokeVoidAsync("alert", "Transcription copied to clipboard");
        }
    }
    
    private async Task DeleteTranscription(CallTranscription transcription)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this transcription? This action cannot be undone."))
        {
            try
            {
                // Delete the transcription from the database
                using var context = await _dbFactory.CreateDbContextAsync();
                var dbTranscription = await context.CallTranscriptions
                    .FirstOrDefaultAsync(t => t.Id == transcription.Id);
                    
                if (dbTranscription != null)
                {
                    context.CallTranscriptions.Remove(dbTranscription);
                    await context.SaveChangesAsync();
                    
                    // Remove from local collections
                    transcriptions.Remove(transcription);
                    filteredTranscriptions.Remove(transcription);
                    StateHasChanged();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting transcription: {ex.Message}");
                await JSRuntime.InvokeVoidAsync("alert", $"Error deleting transcription: {ex.Message}");
            }
        }
    }
    
    private void CloseTranscriptionDialog()
    {
        showTranscriptionDialog = false;
        selectedTranscription = null;
        StateHasChanged();
    }
    
    private async Task GenerateAndShowAiSummary(CallTranscription transcription)
    {
        selectedTranscription = transcription;
        aiSummary = "";
        showAiSummaryDialog = true;
        await GenerateAiSummary();
    }
    
    private void CloseAiSummaryDialog()
    {
        showAiSummaryDialog = false;
        StateHasChanged();
    }
    
    private async Task GenerateAiSummary()
    {
        if (selectedTranscription == null || string.IsNullOrEmpty(selectedTranscription.TranscriptionText))
        {
            return;
        }
        
        isGeneratingAiSummary = true;
        StateHasChanged();
        
        try
        {
            // Create prompt for OpenAI
            string prompt = $@"The following is a transcription of a phone call between an insurance agent and a client. 
Please summarize the key points of this conversation in 3-5 bullet points. Format your response as HTML with <ul> and <li> tags.
Focus on identifying:
- The main purpose of the call
- Any specific insurance policies or coverages discussed
- Any action items or next steps mentioned
- Any questions or concerns from the client

Here is the transcription:
{selectedTranscription.TranscriptionText}";
            
            // Call OpenAI for summary
            var summaryText = await _openAiService.GenerateResponseAsync(prompt, "gpt-4o-mini");
            
            if (!string.IsNullOrEmpty(summaryText))
            {
                // Ensure the response has HTML formatting
                if (!summaryText.Contains("<ul>"))
                {
                    summaryText = $"<ul><li>{summaryText.Replace("\n", "</li><li>")}</li></ul>";
                    summaryText = summaryText.Replace("<li></li>", "");
                }
                
                aiSummary = summaryText;
            }
            else
            {
                aiSummary = "<p>Unable to generate summary. Please try again later.</p>";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error generating AI summary: {ex.Message}");
            aiSummary = "<p>Error generating summary. Please try again later.</p>";
        }
        finally
        {
            isGeneratingAiSummary = false;
            StateHasChanged();
        }
    }
}

<style>
    .sf-layout {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 20px;
    }
    
    .sf-header {
        margin-bottom: 20px;
    }
    
    .sf-header h1 {
        margin-bottom: 5px;
    }
    
    .sf-header h2 {
        margin-top: 0;
        color: #666;
        font-weight: normal;
    }
    
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 300px;
    }
    
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 300px;
        text-align: center;
        color: #666;
    }
    
    .filters {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .transcription-list {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }
    
    .transcription-card {
        padding: 16px;
    }
    
    .card-header {
        display: flex;
        gap: 16px;
        margin-bottom: 12px;
        font-size: 14px;
        color: #666;
    }
    
    .phone-info, .date-info, .duration-info {
        display: flex;
        align-items: center;
        gap: 6px;
    }
    
    .transcription-text {
        margin-bottom: 16px;
        white-space: pre-wrap;
        line-height: 1.4;
    }
    
    .card-actions {
        display: flex;
        gap: 8px;
    }
    
    .dialog-content {
        max-height: 70vh;
        overflow-y: auto;
    }
    
    .dialog-header {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #eee;
    }
    
    .transcription-full-text {
        white-space: pre-wrap;
        line-height: 1.5;
    }
    
    .ai-summary-section {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #1b8ce3;
    }
    
    .ai-summary-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 10px;
    }
    
    .ai-summary-header h4 {
        margin: 0;
        color: #1b8ce3;
    }
    
    .ai-summary-content {
        line-height: 1.5;
    }
    
    .ai-summary-content ul {
        margin: 0;
        padding-left: 20px;
    }
    
    .ai-summary-content li {
        margin-bottom: 5px;
    }
</style> 