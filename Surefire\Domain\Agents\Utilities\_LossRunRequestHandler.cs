//using Surefire.Domain.Carriers.Models;
//using Surefire.Domain.Policies.Models;
//using Surefire.Domain.Clients.Models;
//using Surefire.Data;
//using Microsoft.EntityFrameworkCore;
//using Surefire.Domain.Ember;

//namespace Surefire.Domain.Agents.Handlers
//{
//    public class LossRunRequestHandler
//    {
//        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
//        private readonly EmberService _emberService;

//        public LossRunRequestHandler(IDbContextFactory<ApplicationDbContext> dbContextFactory, EmberService emberService)
//        {
//            _dbContextFactory = dbContextFactory;
//            _emberService = emberService;
//        }

//        // 3. Identify carriers and their loss-run request emails
//        public async Task<Dictionary<Carrier, string>> GetCarriersAndEmailsAsync(IEnumerable<Policy> policies)
//        {
//            using var context = _dbContextFactory.CreateDbContext();
            
//            // Get unique carrier IDs from the policies
//            var carrierIds = policies
//                .Where(p => p.CarrierId.HasValue)
//                .Select(p => p.CarrierId.Value)
//                .Distinct()
//                .ToList();

//            // Fetch carriers with their loss run emails
//            var carriersWithEmails = await context.Carriers
//                .Where(c => carrierIds.Contains(c.CarrierId) && !string.IsNullOrEmpty(c.LossRunsEmail))
//                .ToDictionaryAsync(c => c, c => c.LossRunsEmail);

//            return carriersWithEmails;
//        }

//        // 4. Draft and send emails to carriers missing runs
//        public async Task<List<EmailResult>> DraftAndSendLossRunRequestsAsync(Client client, IEnumerable<Policy> policies, Dictionary<Carrier, string> carrierEmails)
//        {
//            var emailResults = new List<EmailResult>();
            
//            // Group policies by carrier name (similar to LossRunsRequester.razor logic)
//            var groupedPolicies = policies
//                .GroupBy(p => GetCarrierName(p))
//                .ToDictionary(g => g.Key, g => g.ToList());

//            foreach (var carrierGroup in groupedPolicies)
//            {
//                var carrierName = carrierGroup.Key;
//                var carrierPolicies = carrierGroup.Value;
                
//                // Get the loss runs email for this carrier group
//                var lossRunsEmail = GetLossRunsEmail(carrierPolicies);
                
//                var emailResult = new EmailResult
//                {
//                    CarrierName = carrierName,
//                    Email = lossRunsEmail
//                };

//                if (!string.IsNullOrEmpty(lossRunsEmail))
//                {
//                    try
//                    {
//                        // Send email using the same logic as LossRunsRequester.razor
//                        await SendEmailForCarrier(client, carrierName, carrierPolicies, lossRunsEmail);
//                        emailResult.Sent = true;
//                    }
//                    catch (Exception ex)
//                    {
//                        emailResult.Sent = false;
//                        emailResult.Error = ex.Message;
//                    }
//                }
//                else
//                {
//                    emailResult.Sent = false;
//                    emailResult.Error = "No loss runs email available for this carrier";
//                }

//                emailResults.Add(emailResult);
//            }

//            return emailResults;
//        }

//        // 5. Provide fallback guidance
//        public async Task<string> GetFallbackGuidanceAsync(Dictionary<Carrier, string> carrierEmails)
//        {
//            using var context = _dbContextFactory.CreateDbContext();
            
//            // Get all carriers that don't have emails but might have URLs or notes
//            var carrierIds = carrierEmails.Keys.Select(c => c.CarrierId).ToList();
            
//            var carriersWithoutEmails = await context.Carriers
//                .Where(c => !carrierIds.Contains(c.CarrierId) && 
//                           (!string.IsNullOrEmpty(c.LossRunsURL) || !string.IsNullOrEmpty(c.LossRunsNote)))
//                .ToListAsync();

//            if (!carriersWithoutEmails.Any())
//            {
//                return "No additional guidance available for carriers without email addresses.";
//            }

//            var guidance = "For carriers without email addresses, please use the following alternatives:\n\n";
            
//            foreach (var carrier in carriersWithoutEmails)
//            {
//                guidance += $"**{carrier.CarrierName}:**\n";
                
//                if (!string.IsNullOrEmpty(carrier.LossRunsURL))
//                {
//                    guidance += $"- Loss Runs Portal: {carrier.LossRunsURL}\n";
//                }
                
//                if (!string.IsNullOrEmpty(carrier.LossRunsNote))
//                {
//                    guidance += $"- Instructions: {carrier.LossRunsNote}\n";
//                }
                
//                guidance += "\n";
//            }

//            return guidance;
//        }

//        private string GetCarrierName(Policy policy)
//        {
//            // Same logic as LossRunsRequester.razor
//            const string unknownCarrierName = "Unknown Carrier";

//            try
//            {
//                // Check if Wholesaler exists and is marked as Wholesaler
//                if (policy.Wholesaler != null && policy.Wholesaler.Wholesaler)
//                {
//                    return policy.Wholesaler.CarrierName;
//                }
                
//                // Check if Carrier exists and is marked as Carrier (or IssuingCarrier)
//                if (policy.Carrier != null && (policy.Carrier.IssuingCarrier || !policy.Carrier.Wholesaler))
//                {
//                    return policy.Carrier.CarrierName;
//                }
                
//                // If none of the above, return the placeholder
//                return unknownCarrierName;
//            }
//            catch
//            {
//                // In case of any error accessing properties, return the placeholder
//                return unknownCarrierName;
//            }
//        }

//        private string GetLossRunsEmail(List<Policy> policies)
//        {
//            // Same logic as LossRunsRequester.razor
//            foreach (var policy in policies)
//            {
//                // Check wholesaler first if it exists and was used for grouping
//                if (policy.Wholesaler != null && 
//                    policy.Wholesaler.Wholesaler && 
//                    !string.IsNullOrEmpty(policy.Wholesaler.LossRunsEmail))
//                {
//                    return policy.Wholesaler.LossRunsEmail;
//                }
                
//                // Then check carrier if it exists and was used for grouping
//                if (policy.Carrier != null && 
//                    (policy.Carrier.IssuingCarrier || !policy.Carrier.Wholesaler) &&
//                    !string.IsNullOrEmpty(policy.Carrier.LossRunsEmail))
//                {
//                    return policy.Carrier.LossRunsEmail;
//                }
//            }
            
//            // If no email found, return empty string
//            return string.Empty;
//        }

//        private async Task SendEmailForCarrier(Client client, string carrierName, List<Policy> policies, string toEmail)
//        {
//            if (policies == null || !policies.Any())
//            {
//                throw new ArgumentException("No policies provided for email generation");
//            }

//            if (string.IsNullOrEmpty(toEmail))
//            {
//                throw new ArgumentException("No email address provided");
//            }

//            try
//            {
//                // Get a representative policy to use in the subject
//                var firstPolicy = policies.First();
                
//                // Get the product line name from the first policy
//                var productLine = firstPolicy.Product?.LineName ?? "Insurance";
                
//                // Create subject with client name
//                string subject = $"Loss Run Request - {client.Name} - {productLine} ({firstPolicy.PolicyNumber})";
                
//                // Build email body with client name
//                string body = $"<p>Please send us the current valued loss runs for <strong>{client.Name}</strong>'s {productLine} policies from {carrierName}:</p>";
                
//                // Add policy details to body
//                body += "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
//                body += "<tr style='background-color: #f2f2f2;'><th>Policy Number</th><th>Effective Date</th><th>Expiration Date</th><th>Carrier</th><th>Wholesaler</th></tr>";
                
//                foreach (var policy in policies)
//                {
//                    string carrierValue = policy.Carrier?.CarrierName ?? "N/A";
//                    string wholesalerValue = policy.Wholesaler?.CarrierName ?? "N/A";
                    
//                    body += $"<tr><td>{policy.PolicyNumber}</td><td>{policy.EffectiveDate.ToShortDateString()}</td><td>{policy.ExpirationDate.ToShortDateString()}</td><td>{carrierValue}</td><td>{wholesalerValue}</td></tr>";
//                }
                
//                body += "</table>";
//                body += "<p>Thank you for your assistance.</p>";
                
//                // Create the email using Ember service
//                var parameters = new List<string> { toEmail, subject, body };
                
//                // Log the attempt
//                Console.WriteLine($"[LossRunRequestHandler] Attempting to create email for {carrierName} to {toEmail}");
//                Console.WriteLine($"[LossRunRequestHandler] Subject: {subject}");
                
//                await _emberService.RunEmberFunction("OutlookEmail_CreateNew", parameters);
                
//                // Log success
//                Console.WriteLine($"[LossRunRequestHandler] Successfully called Ember service for email creation");
//            }
//            catch (Exception ex)
//            {
//                Console.WriteLine($"[LossRunRequestHandler] Error in SendEmailForCarrier: {ex.Message}");
//                Console.WriteLine($"[LossRunRequestHandler] Stack trace: {ex.StackTrace}");
//                throw; // Re-throw to be caught by the calling method
//            }
//        }
//    }

//    public class EmailResult
//    {
//        public string CarrierName { get; set; }
//        public string Email { get; set; }
//        public bool Sent { get; set; }
//        public string? Error { get; set; }
//    }
//}
