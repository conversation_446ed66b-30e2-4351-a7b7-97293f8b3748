using Surefire.Domain.Agents.Models;

namespace Surefire.Domain.Agents.Interfaces
{
    /// <summary>
    /// Base interface for all task agents in the Surefire system
    /// Task agents are automated actions that can be triggered from AI chat or action buttons
    /// </summary>
    public interface ITaskAgent
    {
        /// <summary>
        /// Metadata definition of this agent including parameters, triggers, and capabilities
        /// </summary>
        TaskAgentDefinition Definition { get; }

        /// <summary>
        /// Execute the agent with validated parameters
        /// </summary>
        /// <param name="request">The agent execution request with parameters and context</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Result of the agent execution</returns>
        Task<TaskAgentResult> ExecuteAsync(TaskAgentRequest request, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validate that this agent can execute with the provided parameters
        /// </summary>
        /// <param name="parameters">Parameters to validate</param>
        /// <returns>Validation result with any missing or invalid parameters</returns>
        Task<AgentParameterValidation> ValidateParametersAsync(Dictionary<string, object> parameters);

        /// <summary>
        /// Get a preview/description of what this agent would do with the given parameters
        /// Useful for confirmation prompts
        /// </summary>
        /// <param name="parameters">Parameters for the action</param>
        /// <returns>Human-readable description of the action</returns>
        Task<string> GetExecutionPreviewAsync(Dictionary<string, object> parameters);
    }
} 