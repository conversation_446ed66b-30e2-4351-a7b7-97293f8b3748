using System.Data;
using Surefire.Data;
using Surefire.Domain.Policies.Models;
using Microsoft.EntityFrameworkCore;
using Surefire.Domain.Carriers.Models;
using Surefire.Domain.Shared.Models;
using Surefire.Domain.Shared.Services;


namespace Surefire.Domain.Policies.Services
{
    public class PolicyService
    {
        private readonly ApplicationDbContext _context;
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly StateService _stateService;

        public PolicyService(ApplicationDbContext context, IDbContextFactory<ApplicationDbContext> dbContextFactory, StateService stateService)
        {
            _context = context;
            _dbContextFactory = dbContextFactory;
            _stateService = stateService;
        }

        // POLICY [GET] -----------------------------------------------------//
        // Get policies for a client and line of business within the last N years
        public async Task<List<Policy>> GetPoliciesForClientAndLineAsync(int clientId, int productId, int years)
        {
            var cutoffDate = DateTime.UtcNow.AddYears(-years);
            return await _context.Policies
                .Include(p => p.Product)
                .Include(p => p.Carrier)
                .Where(p => p.ClientId == clientId
                            && p.Product != null
                            && p.Product.ProductId == productId
                            && p.EffectiveDate >= cutoffDate)
                .OrderByDescending(p => p.EffectiveDate)
                .ToListAsync();
        }

        // Lookup product by name with smart matching
        public async Task<Product?> GetProductByNameAsync(string productName)
        {
            if (string.IsNullOrWhiteSpace(productName)) return null;
            using var context = _dbContextFactory.CreateDbContext();
            var allProducts = await context.Products.ToListAsync();

            // Helper method to calculate similarity score
            int CalculateSimilarityScore(Product product, string input)
            {
                var inputLower = input.ToLower();
                var lineName = product.LineName.ToLower();
                var lineNickname = product.LineNickname.ToLower();
                var lineCode = product.LineCode.ToLower();

                // Exact match gets highest score
                if (lineName == inputLower || lineNickname == inputLower || lineCode == inputLower)
                    return 1000;

                // Calculate character overlap for each field
                int lineNameScore = CalculateCharacterOverlap(lineName, inputLower);
                int lineNicknameScore = CalculateCharacterOverlap(lineNickname, inputLower);
                int lineCodeScore = CalculateCharacterOverlap(lineCode, inputLower);

                // Return the highest score among the three fields
                return Math.Max(lineNameScore, Math.Max(lineNicknameScore, lineCodeScore));
            }

            // Helper method to calculate character overlap
            int CalculateCharacterOverlap(string text, string input)
            {
                if (text.Contains(input)) return 500 + input.Length; // Contains match gets high score
                if (text.StartsWith(input)) return 400 + input.Length; // Starts with gets good score
                if (input.StartsWith(text)) return 300 + text.Length; // Input starts with text

                // Count common characters
                var textChars = text.ToCharArray().GroupBy(c => c).ToDictionary(g => g.Key, g => g.Count());
                var inputChars = input.ToCharArray().GroupBy(c => c).ToDictionary(g => g.Key, g => g.Count());
                
                int commonChars = 0;
                foreach (var kvp in inputChars)
                {
                    if (textChars.ContainsKey(kvp.Key))
                    {
                        commonChars += Math.Min(kvp.Value, textChars[kvp.Key]);
                    }
                }

                // Score based on percentage of common characters
                return (commonChars * 100) / Math.Max(input.Length, 1);
            }

            // Calculate scores for all products and find the best match
            var productScores = allProducts
                .Select(p => new { Product = p, Score = CalculateSimilarityScore(p, productName) })
                .Where(ps => ps.Score > 50) // Only consider products with reasonable similarity
                .OrderByDescending(ps => ps.Score)
                .ThenBy(ps => ps.Product.LineName.Length) // Prefer shorter names as tiebreaker
                .ToList();

            // Return the best match, or null if no good matches found
            return productScores.FirstOrDefault()?.Product;
        }

        public async Task<Policy> GetPolicyByIdAsync(int policyId)
        {
            var policy = await _context.Policies
                .Include(p => p.GeneralLiabilityCoverage)
                    .ThenInclude(glc => glc.AdditionalInsuredAttachment)
                .Include(p => p.GeneralLiabilityCoverage)
                    .ThenInclude(glc => glc.WaiverOfSubAttachment)
                .Include(p => p.WorkCompCoverage)
                .Include(p => p.AutoCoverage)
                .Include(p => p.PropertyCoverage)
                .Include(p => p.UmbrellaCoverage)
                .Include(p => p.Product)
                .Include(p => p.Client)
                .Include(p => p.Carrier)
                .Include(p => p.Wholesaler)
                .Include(p => p.RatingBases)
                .FirstOrDefaultAsync(p => p.PolicyId == policyId);

            if (policy == null) return null;

            //Add WorkComp to WC
            if (policy.Product.ProductId == 2 && policy.WorkCompCoverage == null)
            {
                var workCompCoverage = new WorkCompCoverage
                {
                    PolicyId = policy.PolicyId,
                };
                _context.WorkCompCoverages.Add(workCompCoverage);
                policy.WorkCompCoverage = workCompCoverage; //
            }

            //Add Liability to GL
            if (policy.Product.ProductId == 3 && policy.GeneralLiabilityCoverage == null)
            {
                var generalLiabilityCoverage = new GeneralLiabilityCoverage
                {
                    PolicyId = policy.PolicyId,
                };
                _context.GeneralLiabilityCoverages.Add(generalLiabilityCoverage);
                policy.GeneralLiabilityCoverage = generalLiabilityCoverage;
            }

            //Add Liability Coverage to BOP
            if (policy.Product.ProductId == 6 && policy.GeneralLiabilityCoverage == null)
            {
                var generalLiabilityCoverage = new GeneralLiabilityCoverage
                {
                    PolicyId = policy.PolicyId,
                };
                _context.GeneralLiabilityCoverages.Add(generalLiabilityCoverage);
                policy.GeneralLiabilityCoverage = generalLiabilityCoverage;
            }

            //Add Property Coverage to BOP
            if (policy.Product.ProductId == 6 && policy.PropertyCoverage == null)
            {
                var propertyCoverage = new PropertyCoverage
                {
                    PolicyId = policy.PolicyId,
                };
                _context.PropertyCoverage.Add(propertyCoverage);
                policy.PropertyCoverage = propertyCoverage;
            }

            //Add Property Coverage to Property
            if (policy.Product.ProductId == 14 && policy.PropertyCoverage == null)
            {
                var propertyCoverage = new PropertyCoverage
                {
                    PolicyId = policy.PolicyId,
                };
                _context.PropertyCoverage.Add(propertyCoverage);
                policy.PropertyCoverage = propertyCoverage;
            }

            //Add Auto COverage to Auto
            if (policy.Product.ProductId == 4 && policy.AutoCoverage == null)
            {
                var autoCoverage = new AutoCoverage
                {
                    PolicyId = policy.PolicyId,
                };
                _context.AutoCoverages.Add(autoCoverage);
                policy.AutoCoverage = autoCoverage;
            }

            //Add Umbrella Coverage to Umbrella
            if (policy.Product.ProductId == 7 && policy.UmbrellaCoverage == null)
            {
                var umbrellaCoverage = new UmbrellaCoverage
                {
                    PolicyId = policy.PolicyId,
                };
                _context.UmbrellaCoverage.Add(umbrellaCoverage);
                policy.UmbrellaCoverage = umbrellaCoverage;
            }

            await _context.SaveChangesAsync();

            return policy;
        }
        public IQueryable<Policy> GetAllPolicies()
        {
            return _context.Policies
                .Include(p => p.Carrier)
                .Include(p => p.Wholesaler)
                .Include(p => p.Client)
                .Include(p => p.Product)
                .Select(p => new Policy
                {
                    PolicyId = p.PolicyId,
                    PolicyNumber = p.PolicyNumber,
                    EffectiveDate = p.EffectiveDate,
                    ExpirationDate = p.ExpirationDate,
                    Premium = p.Premium,
                    Product = p.Product ?? new Product { LineNickname = "N/A" },  // Handle null Product
                    Carrier = p.Carrier ?? new Carrier { CarrierName = "N/A" },    // Handle null Carrier
                    Wholesaler = p.Wholesaler ?? new Carrier { CarrierName = "N/A" }, // Handle null Wholesaler
                    Client = p.Client
                })
                .AsQueryable();
        }
        public async Task<List<Policy>> GetCurrentPoliciesByClientIdAsync(int clientId)
        {
            var today = DateTime.UtcNow.Date;

            var policies = await _context.Policies
                .Include(p => p.Carrier)
                .Include(p => p.Wholesaler)
                .Include(p => p.Product)
                .Include(p => p.GeneralLiabilityCoverage)
                    .ThenInclude(glc => glc.AdditionalInsuredAttachment)
                .Include(p => p.GeneralLiabilityCoverage)
                    .ThenInclude(glc => glc.WaiverOfSubAttachment)
                .Include(p => p.UmbrellaCoverage)
                .Include(p => p.WorkCompCoverage)
                    .ThenInclude(glc => glc.WaiverOfSubAttachment)
                .Include(p => p.PropertyCoverage)
                .Include(p => p.AutoCoverage)
                .Where(p => p.ClientId == clientId && p.EffectiveDate <= today && p.ExpirationDate >= today)
                .ToListAsync();

            return policies;
        }
        public async Task<List<Policy>> GetCurrentPoliciesByCarrierIdAsync(int carrierId)
        {
            var today = DateTime.UtcNow.Date;

            var policies = await _context.Policies
                .Include(p => p.Carrier)
                .Include(p => p.Wholesaler)
                .Include(p => p.Product)
                .Include(p => p.Client)
                .Where(p => (p.CarrierId == carrierId || p.WholesalerId == carrierId) && p.EffectiveDate <= today && p.ExpirationDate >= today)
                .OrderBy(p => p.ExpirationDate)
                .ToListAsync();

            return policies;
        }

        // POLICY [CRUD] ----------------------------------------------------//
        public async Task<int> CreatePolicyAsync(Policy policy, int clientId)
        {
            var clientExists = await _context.Clients.AnyAsync(c => c.ClientId == clientId);
            if (!clientExists)
            {
                throw new ArgumentException("Invalid ClientId. The specified client does not exist.");
            }

            var currentUser = _stateService.CurrentUser;
            Policy newPolicy = new Policy
            {
                ClientId = clientId,
                CSR = currentUser,
                CreatedBy = currentUser,
                DateCreated = DateTime.UtcNow,
                DateModified = DateTime.UtcNow,
                ProductId = policy.ProductId,
                PolicyNumber = policy.PolicyNumber,
                Premium = policy.Premium,
                Notes = policy.Notes,
                Status = policy.Status,
                EffectiveDate = policy.EffectiveDate,
                ExpirationDate = policy.ExpirationDate,
                CarrierId = policy.CarrierId,
                WholesalerId = policy.CarrierId
            };
            _context.Policies.Add(newPolicy);
            await _context.SaveChangesAsync();
            return newPolicy.PolicyId;
        }
        public async Task<int> CreatePolicyForClientAsync(PolicyCreate policy, int clientId)
        {
            using var context = _dbContextFactory.CreateDbContext();
            var clientExists = await context.Clients.AnyAsync(c => c.ClientId == clientId);
            if (!clientExists)
            {
                throw new ArgumentException("Invalid ClientId. The specified client does not exist.");
            }

            var currentUser = _stateService.CurrentUser;
            context.Attach(currentUser);
            Policy newPolicy = new Policy
            {
                ClientId = clientId,
                CSR = currentUser,
                CreatedBy = currentUser,
                DateCreated = DateTime.UtcNow,
                DateModified = DateTime.UtcNow,
                ProductId = policy.ProductId,
                PolicyNumber = policy.PolicyNumber,
                Premium = policy.Premium ?? 0,
                EffectiveDate = policy.EffectiveDate,
                ExpirationDate = policy.ExpirationDate,
                CarrierId = policy.CarrierId,
                WholesalerId = policy.WholesalerId
            };
            context.Policies.Add(newPolicy);
            await context.SaveChangesAsync();
            return newPolicy.PolicyId;
        }
        public async Task UpdatePolicyContextModelAsync(Policy policy)
        {
            using var context = _dbContextFactory.CreateDbContext();
            context.Entry(policy).State = EntityState.Modified;
            await context.SaveChangesAsync();
        }

        // RATING BASIS -----------------------------------------------------//
        public async Task DeleteRatingBasisAsync(int ratingBasisId)
        {
            using var context = _dbContextFactory.CreateDbContext();
            var ratingBasis = await context.RatingBases.FindAsync(ratingBasisId);
            if (ratingBasis == null) throw new KeyNotFoundException("Rating Basis not found");

            context.RatingBases.Remove(ratingBasis);
            await context.SaveChangesAsync();
        }
        public async Task<RatingBasis> AddBlankRatingBasisAsync(int policyId)
        {
            using var context = _dbContextFactory.CreateDbContext();
            var newRatingBasis = new RatingBasis
            {
                PolicyId = policyId,
            };

            context.RatingBases.Add(newRatingBasis);
            await context.SaveChangesAsync();

            return newRatingBasis;
        }

        // WORK COMP RATING BASIS ----------------------------------------//
        public async Task<WorkCompRatingBasis> UpsertWorkCompRatingBasisAsync(WorkCompRatingBasis workCompRatingBasis)
        {
            using var context = _dbContextFactory.CreateDbContext();
            // Check if a rating basis with this ClassCode already exists for this policy
            var existingRatingBasis = await context.WorkCompRatingBases
                .FirstOrDefaultAsync(rb => rb.PolicyId == workCompRatingBasis.PolicyId && 
                                          rb.ClassCode == workCompRatingBasis.ClassCode);

            if (existingRatingBasis != null)
            {
                // Update existing record
                existingRatingBasis.LocationNumberNote = workCompRatingBasis.LocationNumberNote;
                existingRatingBasis.LocationState = workCompRatingBasis.LocationState;
                existingRatingBasis.ClassDescription = workCompRatingBasis.ClassDescription;
                existingRatingBasis.PartTimeEmployees = workCompRatingBasis.PartTimeEmployees;
                existingRatingBasis.FullTimeEmployees = workCompRatingBasis.FullTimeEmployees;
                existingRatingBasis.BaseRate = workCompRatingBasis.BaseRate;
                existingRatingBasis.Premium = workCompRatingBasis.Premium;
                existingRatingBasis.Payroll = workCompRatingBasis.Payroll;
                existingRatingBasis.DateModified = DateTime.UtcNow;

                context.WorkCompRatingBases.Update(existingRatingBasis);
                await context.SaveChangesAsync();
                
                return existingRatingBasis;
            }
            else
            {
                // Create new record
                workCompRatingBasis.DateCreated = DateTime.UtcNow;
                workCompRatingBasis.DateModified = DateTime.UtcNow;
                
                context.WorkCompRatingBases.Add(workCompRatingBasis);
                await context.SaveChangesAsync();
                
                return workCompRatingBasis;
            }
        }

        // WORK COMP COVERAGE ----------------------------------------//
        public async Task<WorkCompCoverage> UpsertWorkCompCoverageAsync(WorkCompCoverage workCompCoverage)
        {
            using var context = _dbContextFactory.CreateDbContext();
            // Check if a coverage record already exists for this policy
            var existingCoverage = await context.WorkCompCoverages
                .FirstOrDefaultAsync(wcc => wcc.PolicyId == workCompCoverage.PolicyId);

            if (existingCoverage != null)
            {
                // Update existing record
                existingCoverage.EachAccident = workCompCoverage.EachAccident;
                existingCoverage.DiseaseEachEmployee = workCompCoverage.DiseaseEachEmployee;
                existingCoverage.DiseasePolicyLimit = workCompCoverage.DiseasePolicyLimit;
                existingCoverage.DateModified = DateTime.UtcNow;

                context.WorkCompCoverages.Update(existingCoverage);
                await context.SaveChangesAsync();
                
                return existingCoverage;
            }
            else
            {
                // Create new record
                workCompCoverage.DateCreated = DateTime.UtcNow;
                workCompCoverage.DateModified = DateTime.UtcNow;
                
                context.WorkCompCoverages.Add(workCompCoverage);
                await context.SaveChangesAsync();
                
                return workCompCoverage;
            }
        }

        // Get WorkCompCoverage by PolicyId
        public async Task<WorkCompCoverage?> GetWorkCompCoverageByPolicyIdAsync(int policyId)
        {
            using var context = _dbContextFactory.CreateDbContext();
            return await context.WorkCompCoverages
                .Include(wcc => wcc.WaiverOfSubAttachment)
                .FirstOrDefaultAsync(wcc => wcc.PolicyId == policyId);
        }

        // Get WorkCompRatingBasis collection by PolicyId
        public async Task<List<WorkCompRatingBasis>> GetWorkCompRatingBasesByPolicyIdAsync(int policyId)
        {
            using var context = _dbContextFactory.CreateDbContext();
            return await context.WorkCompRatingBases
                .Where(wcrb => wcrb.PolicyId == policyId)
                .ToListAsync();
        }

        // AUTO COVERAGE ----------------------------------------//
        public async Task<AutoCoverage> UpsertAutoCoverageAsync(AutoCoverage autoCoverage)
        {
            using var context = _dbContextFactory.CreateDbContext();
            // Check if a coverage record already exists for this policy
            var existingCoverage = await context.AutoCoverages
                .FirstOrDefaultAsync(ac => ac.PolicyId == autoCoverage.PolicyId);

            if (existingCoverage != null)
            {
                // Update existing record
                existingCoverage.CombinedLimit = autoCoverage.CombinedLimit;
                existingCoverage.BodilyInjuryPerPerson = autoCoverage.BodilyInjuryPerPerson;
                existingCoverage.BodilyInjuryPerAccident = autoCoverage.BodilyInjuryPerAccident;
                existingCoverage.PropertyDamage = autoCoverage.PropertyDamage;
                existingCoverage.ForAny = autoCoverage.ForAny;
                existingCoverage.ForOwned = autoCoverage.ForOwned;
                existingCoverage.ForHired = autoCoverage.ForHired;
                existingCoverage.ForScheduled = autoCoverage.ForScheduled;
                existingCoverage.ForNonOwned = autoCoverage.ForNonOwned;
                existingCoverage.DateModified = DateTime.UtcNow;

                context.AutoCoverages.Update(existingCoverage);
                await context.SaveChangesAsync();
                
                return existingCoverage;
            }
            else
            {
                // Create new record
                autoCoverage.DateCreated = DateTime.UtcNow;
                autoCoverage.DateModified = DateTime.UtcNow;
                
                context.AutoCoverages.Add(autoCoverage);
                await context.SaveChangesAsync();
                
                return autoCoverage;
            }
        }

        // Get AutoCoverage by PolicyId
        public async Task<AutoCoverage?> GetAutoCoverageByPolicyIdAsync(int policyId)
        {
            using var context = _dbContextFactory.CreateDbContext();
            return await context.AutoCoverages
                .Include(ac => ac.AdditionalInsuredAttachment)
                .Include(ac => ac.WaiverOfSubAttachment)
                .Include(ac => ac.AdditionalAttachmentsAttachment)
                .FirstOrDefaultAsync(ac => ac.PolicyId == policyId);
        }

        // DRIVERS ----------------------------------------//
        public async Task<Driver> UpsertDriverAsync(Driver driver)
        {
            using var context = _dbContextFactory.CreateDbContext();
            // Check if a driver with this DriverNumberNote already exists for this policy
            var existingDriver = await context.Drivers
                .FirstOrDefaultAsync(d => d.PolicyId == driver.PolicyId && 
                                         d.DriverNumberNote == driver.DriverNumberNote);

            if (existingDriver != null)
            {
                // Update existing record
                existingDriver.FullName = driver.FullName;
                existingDriver.DateOfBirth = driver.DateOfBirth;
                existingDriver.LicenseNumber = driver.LicenseNumber;
                existingDriver.LicenseState = driver.LicenseState;
                existingDriver.Gender = driver.Gender;
                existingDriver.Married = driver.Married;
                existingDriver.LicenseExpiryDate = driver.LicenseExpiryDate;
                existingDriver.DateOfHire = driver.DateOfHire;
                existingDriver.IsPrimaryDriver = driver.IsPrimaryDriver;

                context.Drivers.Update(existingDriver);
                await context.SaveChangesAsync();
                
                return existingDriver;
            }
            else
            {
                // Create new record
                context.Drivers.Add(driver);
                await context.SaveChangesAsync();
                
                return driver;
            }
        }

        // Get Drivers by PolicyId
        public async Task<List<Driver>> GetDriversByPolicyIdAsync(int policyId)
        {
            using var context = _dbContextFactory.CreateDbContext();
            return await context.Drivers
                .Where(d => d.PolicyId == policyId)
                .ToListAsync();
        }

        // VEHICLES ----------------------------------------//
        public async Task<Vehicle> UpsertVehicleAsync(Vehicle vehicle)
        {
            using var context = _dbContextFactory.CreateDbContext();
            // Check if a vehicle with this VehicleNumberNote already exists for this policy
            var existingVehicle = await context.Vehicles
                .FirstOrDefaultAsync(v => v.PolicyId == vehicle.PolicyId && 
                                         v.VehicleNumberNote == vehicle.VehicleNumberNote);

            if (existingVehicle != null)
            {
                // Update existing record
                existingVehicle.Year = vehicle.Year;
                existingVehicle.Make = vehicle.Make;
                existingVehicle.Model = vehicle.Model;
                existingVehicle.VIN = vehicle.VIN;
                existingVehicle.LicensePlate = vehicle.LicensePlate;
                existingVehicle.RegistrationDate = vehicle.RegistrationDate;
                existingVehicle.GaragedAddress = vehicle.GaragedAddress;
                existingVehicle.GaragedCity = vehicle.GaragedCity;
                existingVehicle.GaragedState = vehicle.GaragedState;
                existingVehicle.GaragedPostalCode = vehicle.GaragedPostalCode;
                existingVehicle.CountryOfRegistration = vehicle.CountryOfRegistration;

                context.Vehicles.Update(existingVehicle);
                await context.SaveChangesAsync();
                
                return existingVehicle;
            }
            else
            {
                // Create new record
                context.Vehicles.Add(vehicle);
                await context.SaveChangesAsync();
                
                return vehicle;
            }
        }

        // Get Vehicles by PolicyId
        public async Task<List<Vehicle>> GetVehiclesByPolicyIdAsync(int policyId)
        {
            using var context = _dbContextFactory.CreateDbContext();
            return await context.Vehicles
                .Where(v => v.PolicyId == policyId)
                .ToListAsync();
        }

        // GENERAL LIABILITY COVERAGE ----------------------------------------//
        public async Task<GeneralLiabilityCoverage> UpsertGeneralLiabilityCoverageAsync(GeneralLiabilityCoverage glCoverage)
        {
            using var context = _dbContextFactory.CreateDbContext();
            // Check if a coverage record already exists for this policy
            var existingCoverage = await context.GeneralLiabilityCoverages
                .FirstOrDefaultAsync(glc => glc.PolicyId == glCoverage.PolicyId);

            if (existingCoverage != null)
            {
                // Update existing record
                existingCoverage.EachOccurrence = glCoverage.EachOccurrence;
                existingCoverage.DamageToPremises = glCoverage.DamageToPremises;
                existingCoverage.MedicalExpenses = glCoverage.MedicalExpenses;
                existingCoverage.PersonalInjury = glCoverage.PersonalInjury;
                existingCoverage.GeneralAggregate = glCoverage.GeneralAggregate;
                existingCoverage.ProductsAggregate = glCoverage.ProductsAggregate;
                existingCoverage.AdditionalCoverageName = glCoverage.AdditionalCoverageName;
                existingCoverage.AdditionalCoverageLimit = glCoverage.AdditionalCoverageLimit;
                existingCoverage.Premium = glCoverage.Premium;
                existingCoverage.ClaimsMade = glCoverage.ClaimsMade;
                existingCoverage.Occurence = glCoverage.Occurence;
                existingCoverage.AggregateAppliesPer = glCoverage.AggregateAppliesPer;
                existingCoverage.DateModified = DateTime.UtcNow;

                context.GeneralLiabilityCoverages.Update(existingCoverage);
                await context.SaveChangesAsync();
                
                return existingCoverage;
            }
            else
            {
                // Create new record
                glCoverage.DateCreated = DateTime.UtcNow;
                glCoverage.DateModified = DateTime.UtcNow;
                
                context.GeneralLiabilityCoverages.Add(glCoverage);
                await context.SaveChangesAsync();
                
                return glCoverage;
            }
        }

        // Get GeneralLiabilityCoverage by PolicyId
        public async Task<GeneralLiabilityCoverage?> GetGeneralLiabilityCoverageByPolicyIdAsync(int policyId)
        {
            using var context = _dbContextFactory.CreateDbContext();
            return await context.GeneralLiabilityCoverages
                .Include(glc => glc.AdditionalInsuredAttachment)
                .Include(glc => glc.WaiverOfSubAttachment)
                .Include(glc => glc.AdditionalAttachmentsAttachment)
                .FirstOrDefaultAsync(glc => glc.PolicyId == policyId);
        }

        // RATING BASIS ----------------------------------------//
        public async Task<RatingBasis> UpsertRatingBasisAsync(RatingBasis ratingBasis)
        {
            using var context = _dbContextFactory.CreateDbContext();
            // Check if a rating basis with this ClassCode already exists for this policy
            var existingRatingBasis = await context.RatingBases
                .FirstOrDefaultAsync(rb => rb.PolicyId == ratingBasis.PolicyId && 
                                          rb.ClassCode == ratingBasis.ClassCode);

            if (existingRatingBasis != null)
            {
                // Update existing record
                existingRatingBasis.ClassDescription = ratingBasis.ClassDescription;
                existingRatingBasis.BaseRate = ratingBasis.BaseRate;
                existingRatingBasis.NetRate = ratingBasis.NetRate;
                existingRatingBasis.Premium = ratingBasis.Premium;
                existingRatingBasis.Payroll = ratingBasis.Payroll;
                existingRatingBasis.Basis = ratingBasis.Basis;
                existingRatingBasis.Exposure = ratingBasis.Exposure;
                existingRatingBasis.DateModified = DateTime.UtcNow;

                context.RatingBases.Update(existingRatingBasis);
                await context.SaveChangesAsync();
                
                return existingRatingBasis;
            }
            else
            {
                // Create new record
                ratingBasis.DateCreated = DateTime.UtcNow;
                ratingBasis.DateModified = DateTime.UtcNow;
                
                context.RatingBases.Add(ratingBasis);
                await context.SaveChangesAsync();
                
                return ratingBasis;
            }
        }

        // Get RatingBasis collection by PolicyId
        public async Task<List<RatingBasis>> GetRatingBasesByPolicyIdAsync(int policyId)
        {
            using var context = _dbContextFactory.CreateDbContext();
            return await context.RatingBases
                .Where(rb => rb.PolicyId == policyId)
                .ToListAsync();
        }

        public async Task<List<Policy>> GetPolicyLineHistoryAsync(Policy currentPolicy, int years = 5)
        {
            using var context = _dbContextFactory.CreateDbContext();
            if (currentPolicy == null || currentPolicy.Product == null)
            {
                return new List<Policy>();
            }

            var cutoffDate = DateTime.UtcNow.AddYears(-years);
            var clientId = currentPolicy.ClientId;
            var productName = currentPolicy.Product.LineName;

            var policies = await context.Policies
                .Include(p => p.Product)
                .Include(p => p.Carrier)
                .Where(p => p.ClientId == clientId && 
                            p.Product.LineName == productName && 
                            p.EffectiveDate >= cutoffDate &&
                            p.PolicyId != currentPolicy.PolicyId)
                .OrderByDescending(p => p.EffectiveDate)
                .ToListAsync();

            return policies;
        }

        public string FormatPolicyLineHistory(List<Policy> policies)
        {
            if (policies == null || !policies.Any())
            {
                return "No previous policy history found.";
            }

            var formattedHistory = new System.Text.StringBuilder();
            formattedHistory.AppendLine("<ul style=\"list-style-type: disc; margin-left: 20px;\">");

            foreach (var policy in policies)
            {
                formattedHistory.AppendLine($"<li><strong>{policy.PolicyNumber}</strong> - " +
                    $"{policy.Carrier?.CarrierName} - " +
                    $"Effective: {policy.EffectiveDate.ToShortDateString()} to {policy.ExpirationDate.ToShortDateString()} - " +
                    $"Premium: ${policy.Premium:N2}</li>");
            }

            formattedHistory.AppendLine("</ul>");
            return formattedHistory.ToString();
        }
    }
}
