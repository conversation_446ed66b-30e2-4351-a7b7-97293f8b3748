﻿@page "/Policies/Products"
@using Microsoft.EntityFrameworkCore
@using Surefire.Domain.Policies.Models
@inject SharedService SharedService
@inject IDialogService DialogService

<_toolbar OnMenuAction="HandleToolbarAction" PageName="Products" />

<div class="page-content-datagrid">
    <div class="fluent-data-grid">
        @if (FilteredProducts != null)
        {
            <FluentDataGrid Items="@FilteredProducts" ResizableColumns="true" Pagination="@pagination" ShowHover="true" OnRowClick="@((FluentDataGridRow<Product> row) => HandleRowClick(row))">
                <PropertyColumn Property="@(p => p.ProductId)" Title="Product Id" Width="60px" Sortable="true" />
                <PropertyColumn Property="@(p => p.LineName)" Title="Line Name" Width="200px" Sortable="true" />
                <PropertyColumn Property="@(p => p.LineNickname)" Title="Line Nickname" Sortable="true" />
                <PropertyColumn Property="@(p => p.LineCode)" Title="Line Code" Sortable="true" />
                <PropertyColumn Property="@(p => p.Description)" Title="Description" Sortable="true" />
            </FluentDataGrid>
            <div class="fluent-data-grid__bottombar">
                <div class="fluent-data-grid__pagination">
                    <FluentPaginator State="@pagination" />
                </div>

                <div class="fluent-data-grid__search">
                    <FluentSearch Placeholder="Search..." @bind-Value="SearchTerm" @oninput="HandleSearchInput" />
                </div>
            </div>
        }
    </div>
</div>

<div>
    <FluentButton Appearance="Appearance.Accent" OnClick="ShowAddDialog">Add Product</FluentButton>
</div>

@code {
    public IQueryable<Product> ProductData { get; set; }
    public IQueryable<Product> FilteredProducts { get; set; }
    public int ProductId { get; set; }
    public string SelectedProductName { get; set; } = "";
    public string SearchTerm { get; set; } = string.Empty;
    PaginationState pagination = new PaginationState { ItemsPerPage = 18 };
    private System.Timers.Timer debounceTimer;
    bool SelectFromEntireRow = true;


    protected override async Task OnInitializedAsync()
    {
        var myProducts = await SharedService.GetAllProductsAsync();
        var myProductList = myProducts.ToList();
        ProductData = myProductList.AsQueryable(); //needs to be quertyable
        FilteredProducts = ProductData;
        debounceTimer = new System.Timers.Timer(300); // Debounce for 300ms
        debounceTimer.AutoReset = false; // Prevent multiple triggers
        debounceTimer.Elapsed += async (sender, e) =>
        {
            await InvokeAsync(ApplySearchFilter); // Apply the search filter after the debounce period
        };
    }

    private void HandleSearchInput(Microsoft.AspNetCore.Components.ChangeEventArgs e)
    {
        if (e.Value is string searchTerm)
        {
            SearchTerm = searchTerm;
            debounceTimer.Stop();
            debounceTimer.Start(); // Start the debounce timer
        }
    }

    private void ApplySearchFilter()
    {
        FilteredProducts = string.IsNullOrWhiteSpace(SearchTerm)
        ? ProductData
        : ProductData.Where(p =>
              EF.Functions.Like(p.LineName.ToLower(), $"%{SearchTerm.ToLower()}%")
           || EF.Functions.Like(p.LineNickname.ToLower(), $"%{SearchTerm.ToLower()}%")
           || EF.Functions.Like(p.LineCode.ToLower(), $"%{SearchTerm.ToLower()}%")
           || EF.Functions.Like(p.Description.ToLower(), $"%{SearchTerm.ToLower()}%"));

        InvokeAsync(StateHasChanged); // Trigger UI refresh after filtering
    }

    private void HandleRowDoubleClick(FluentDataGridRow<Product> row)
    {
        if (row != null && row.Item != null)
        {
            var selectedProduct = row.Item;
            ProductId = selectedProduct.ProductId;
            Navigation.NavigateTo($"/Products/Details/{ProductId}");
        }
    }

    private void HandleRowClick(FluentDataGridRow<Product> row)
    {
        if (row != null && row.Item != null)
        {
            var selectedProduct = row.Item;
            row.Class = "selected-row";
            SelectedProductName = row.Item.LineName + ": ";
            ProductId = selectedProduct.ProductId;
            //StateHasChanged();
            EditProduct(row.Item);
        }
    }
    private async Task HandleToolbarAction(string action)
    {
        if (action == "Product")
        {
            await ShowAddDialog();
        }
    }
    private async Task ShowAddDialog()
    {
        var product = new Product();
        await OpenProductDialog(product, "Add Product");
    }
    private async Task EditProduct(Product product)
    {
        var productCopy = new Product
            {
                ProductId = product.ProductId,
                LineName = product.LineName,
                LineNickname = product.LineNickname,
                LineCode = product.LineCode,
                Description = product.Description
            };

        await OpenProductDialog(productCopy, "Edit Product");
    }

    private async Task OpenProductDialog(Product product, string title)
    {
        var dialog = await DialogService.ShowDialogAsync<_dialogProducts>(product, new DialogParameters
            {
                Title = title,
                Width = "400px",
                PreventDismissOnOverlayClick = true
            });

        var result = await dialog.Result;
        if (!result.Cancelled && result.Data != null)
        {
            var updatedProduct = (Product)result.Data;
            if (updatedProduct.ProductId == 0)
            {
                await SharedService.CreateProductAsync(updatedProduct);
            }
            else
            {
                await SharedService.UpdateProductAsync(updatedProduct.ProductId, updatedProduct.LineName, updatedProduct.LineNickname, updatedProduct.LineCode, updatedProduct.Description);
            }

            // Refresh the data after the update
            var refreshedProducts = await SharedService.GetAllProductsAsync();
            var refreshedProductsList = refreshedProducts.ToList();
            ProductData = refreshedProductsList.AsQueryable();
            ApplySearchFilter();
        }
    }
}
