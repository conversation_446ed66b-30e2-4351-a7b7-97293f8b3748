using Surefire.Domain.Agents.Models;

namespace Surefire.Domain.Agents.Interfaces
{
    /// <summary>
    /// Interface for the advanced OpenAI agent using Semantic Kernel
    /// </summary>
    public interface IOpenAIAgent
    {
        /// <summary>
        /// Process a unified request using AI capabilities
        /// </summary>
        /// <param name="request">The unified request containing user input and context</param>
        /// <param name="cancellationToken">Cancellation token for the operation</param>
        /// <param name="manualIntent">Optional manual intent to bypass automatic intent classification</param>
        /// <returns>Unified response with AI-generated content and metadata</returns>
        Task<UnifiedResponse> ProcessRequestAsync(UnifiedRequest request, CancellationToken cancellationToken = default, IntentType? manualIntent = null);

        /// <summary>
        /// Classify the intent of user input
        /// </summary>
        Task<IntentClassificationResult> ClassifyIntentAsync(string input, Dictionary<string, object> context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Stream responses in real-time with optional manual intent
        /// </summary>
        /// <param name="request">The unified request containing user input and context</param>
        /// <param name="onChunk">Callback function to handle streaming response chunks</param>
        /// <param name="cancellationToken">Cancellation token for the operation</param>
        /// <param name="manualIntent">Optional manual intent to bypass automatic intent classification</param>
        /// <returns>Unified response with AI-generated content and metadata</returns>
        Task<UnifiedResponse> ProcessRequestStreamingAsync(
            UnifiedRequest request,
            Func<StreamingResponseChunk, Task> onChunk,
            CancellationToken cancellationToken = default,
            IntentType? manualIntent = null);

        /// <summary>
        /// Get suggested follow-up actions
        /// </summary>
        Task<List<string>> GetSuggestionsAsync(string input, Dictionary<string, object> context, CancellationToken cancellationToken = default);

        /// <summary>
        /// Validate user input for safety
        /// </summary>
        bool ValidateInput(string input);

        /// <summary>
        /// Get health status of the AI agent
        /// </summary>
        Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default);
    }
} 