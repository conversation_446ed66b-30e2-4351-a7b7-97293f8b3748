﻿using Surefire.Domain.Clients.Models; // Assuming BusinessDetails and Enums are here
using System;
using System.Globalization;
using System.Linq;
using System.Text.Json;

namespace Surefire.Domain.Policies.Services
{
    public class PolicyExtractorService
    {
        // Maps properties from a "BusinessDetails" root object in the JSON
        public void MapSpecificJsonProperties(JsonElement jsonElement, BusinessDetails businessDetails)
        {
            if (businessDetails == null)
            {
                Console.WriteLine("Error: BusinessDetails object is null in MapSpecificJsonProperties.");
                return;
            }

            if (jsonElement.TryGetProperty("BusinessDetails", out var businessDetailsElement))
            {
                businessDetails.FEIN = GetStringProperty(businessDetailsElement, "FEIN");
                businessDetails.ShortDescription = GetStringProperty(businessDetailsElement, "ShortDescription");
                businessDetails.LongDescription = GetStringProperty(businessDetailsElement, "LongDescription");
                businessDetails.BusinessIndustry = GetStringProperty(businessDetailsElement, "BusinessIndustry");
                businessDetails.BusinessSpecialty = GetStringProperty(businessDetailsElement, "BusinessSpecialty");
                businessDetails.AnnualGrossSalesRevenueReceipts = GetDecimalProperty(businessDetailsElement, "AnnualGrossSalesRevenueReceipts");
                businessDetails.BusinessPersonalPropertyBPP = GetDecimalProperty(businessDetailsElement, "BusinessPersonalPropertyBPP");
                businessDetails.AnnualPayrollHazardExposure = GetDecimalProperty(businessDetailsElement, "AnnualPayrollHazardExposure");
                businessDetails.DateStarted = GetDateTimeProperty(businessDetailsElement, "DateStarted");
                businessDetails.YearsExperience = GetIntProperty(businessDetailsElement, "YearsExperience");
                businessDetails.LicenseNumber = GetStringProperty(businessDetailsElement, "LicenseNumber");

                if (businessDetailsElement.TryGetProperty("BuildingLocation", out var buildingLocationElement))
                {
                    businessDetails.BuildingLocationYearBuilt = GetIntProperty(buildingLocationElement, "YearBuilt");
                    businessDetails.BuildingLocationSquareFootage = GetIntProperty(buildingLocationElement, "SquareFootage");
                    businessDetails.BuildingLocationNumberOfStories = GetIntProperty(buildingLocationElement, "NumberOfStories");
                    // Add Sprinklered and MonitoredSecurity if they are in this JSON structure
                    businessDetails.BuildingLocationSprinklered = GetBooleanProperty(buildingLocationElement, "Sprinklered");
                    businessDetails.BuildingLocationMonitoredSecurity = GetBooleanProperty(buildingLocationElement, "MonitoredSecurity");
                }

                businessDetails.NumPartTimeEmployees = GetIntProperty(businessDetailsElement, "NumPartTimeEmployees");
                businessDetails.NumFullTimeEmployees = GetIntProperty(businessDetailsElement, "NumFullTimeEmployees");
                // Add other enum properties if they exist in this specific JSON structure
                businessDetails.LegalEntityType = GetEnumProperty<LegalEntityType>(businessDetailsElement, "LegalEntityType");
                businessDetails.BusinessType = GetEnumProperty<BusinessType>(businessDetailsElement, "BusinessType");
                businessDetails.LicenseType = GetEnumProperty<LicenseType>(businessDetailsElement, "LicenseType");
            }
            else
            {
                Console.WriteLine("Warning: 'BusinessDetails' property not found in JSON for MapSpecificJsonProperties.");
            }
        }

        // Maps properties from a flatter JSON structure (Lacy's schema)
        public void MapLacyJsonProperties(JsonElement jsonRootElement, BusinessDetails businessDetails)
        {
            if (businessDetails == null)
            {
                Console.WriteLine("Error: BusinessDetails object is null in MapLacyJsonProperties. Cannot map data.");
                return;
            }

            Console.WriteLine("Mapping properties based on Lacy schema...");

            businessDetails.FEIN = GetStringProperty(jsonRootElement, "FEIN");
            businessDetails.LegalEntityType = GetEnumProperty<LegalEntityType>(jsonRootElement, "LegalEntityType");
            businessDetails.ShortDescription = GetStringProperty(jsonRootElement, "ShortDescription");
            businessDetails.LongDescription = GetStringProperty(jsonRootElement, "LongDescription");
            businessDetails.BusinessIndustry = GetStringProperty(jsonRootElement, "BusinessIndustry");
            businessDetails.BusinessSpecialty = GetStringProperty(jsonRootElement, "BusinessSpecialty");
            businessDetails.BusinessType = GetEnumProperty<BusinessType>(jsonRootElement, "BusinessType");
            businessDetails.AnnualGrossSalesRevenueReceipts = GetDecimalProperty(jsonRootElement, "AnnualGrossSalesRevenueReceipts");
            businessDetails.BusinessPersonalPropertyBPP = GetDecimalProperty(jsonRootElement, "BusinessPersonalPropertyBPP");
            businessDetails.AnnualPayrollHazardExposure = GetDecimalProperty(jsonRootElement, "AnnualPayrollHazardExposure");
            businessDetails.DateStarted = GetDateTimeProperty(jsonRootElement, "DateStarted");
            businessDetails.YearsExperience = GetIntProperty(jsonRootElement, "YearsExperience");
            businessDetails.InsuranceHistory = GetStringProperty(jsonRootElement, "InsuranceHistory");
            businessDetails.LapseHistory = GetStringProperty(jsonRootElement, "LapseHistory"); // Assuming this field exists in BusinessDetails
            businessDetails.NumClaims = GetIntProperty(jsonRootElement, "NumClaims");
            businessDetails.LicenseType = GetEnumProperty<LicenseType>(jsonRootElement, "LicenseType");
            businessDetails.LicenseNumber = GetStringProperty(jsonRootElement, "LicenseNumber");
            businessDetails.EstimatedSubcontractingExpenses = GetDecimalProperty(jsonRootElement, "EstimatedSubcontractingExpenses");

            businessDetails.EstimatedGrossSales0 = GetDecimalArrayProperty(jsonRootElement, "EstimatedGrossSalesNextYearAndLastFourYears", 0);
            businessDetails.EstimatedGrossSales1 = GetDecimalArrayProperty(jsonRootElement, "EstimatedGrossSalesNextYearAndLastFourYears", 1);
            businessDetails.EstimatedGrossSales2 = GetDecimalArrayProperty(jsonRootElement, "EstimatedGrossSalesNextYearAndLastFourYears", 2);
            businessDetails.EstimatedGrossSales3 = GetDecimalArrayProperty(jsonRootElement, "EstimatedGrossSalesNextYearAndLastFourYears", 3);
            businessDetails.EstimatedGrossSales4 = GetDecimalArrayProperty(jsonRootElement, "EstimatedGrossSalesNextYearAndLastFourYears", 4);

            if (jsonRootElement.TryGetProperty("Building", out var buildingElement) && buildingElement.ValueKind == JsonValueKind.Object)
            {
                businessDetails.BuildingLocationYearBuilt = GetIntProperty(buildingElement, "YearBuilt");
                businessDetails.BuildingLocationSquareFootage = GetIntProperty(buildingElement, "SquareFootage");
                businessDetails.BuildingLocationNumberOfStories = GetIntProperty(buildingElement, "NumberOfStories");
                businessDetails.BuildingLocationSprinklered = GetBooleanProperty(buildingElement, "Sprinklered");
                businessDetails.BuildingLocationMonitoredSecurity = GetBooleanProperty(buildingElement, "MonitoredSecurity");
            }

            if (jsonRootElement.TryGetProperty("Employees", out var employeesElement) && employeesElement.ValueKind == JsonValueKind.Object)
            {
                businessDetails.NumPartTimeEmployees = GetIntProperty(employeesElement, "NumberOfPartTimeEmployees");
                businessDetails.NumFullTimeEmployees = GetIntProperty(employeesElement, "NumberOfFullTimeEmployees");
                businessDetails.EstimatedAnnualPayroll0 = GetDecimalArrayProperty(employeesElement, "EstimatedAnnualPayrollNextYearAndLastFourYears", 0);
                businessDetails.EstimatedAnnualPayroll1 = GetDecimalArrayProperty(employeesElement, "EstimatedAnnualPayrollNextYearAndLastFourYears", 1);
                businessDetails.EstimatedAnnualPayroll2 = GetDecimalArrayProperty(employeesElement, "EstimatedAnnualPayrollNextYearAndLastFourYears", 2);
                businessDetails.EstimatedAnnualPayroll3 = GetDecimalArrayProperty(employeesElement, "EstimatedAnnualPayrollNextYearAndLastFourYears", 3);
                businessDetails.EstimatedAnnualPayroll4 = GetDecimalArrayProperty(employeesElement, "EstimatedAnnualPayrollNextYearAndLastFourYears", 4);
            }
            Console.WriteLine("Finished mapping properties from Lacy schema.");
        }

        // --- Private Static Helper Methods for JSON Parsing ---
        private static string? GetStringProperty(JsonElement element, string propertyName)
        {
            if (element.TryGetProperty(propertyName, out var propertyElement) && propertyElement.ValueKind != JsonValueKind.Null)
            {
                if (propertyElement.ValueKind == JsonValueKind.Array && propertyElement.GetArrayLength() > 0)
                {
                    return propertyElement[0].GetString(); // Assuming single value in array if present
                }
                return propertyElement.GetString();
            }
            return null;
        }

        private static decimal? GetDecimalProperty(JsonElement element, string propertyName)
        {
            if (element.TryGetProperty(propertyName, out var propertyElement) && propertyElement.ValueKind != JsonValueKind.Null)
            {
                if (propertyElement.ValueKind == JsonValueKind.Number && propertyElement.TryGetDecimal(out var directDecimalValue))
                    return directDecimalValue;

                if (propertyElement.ValueKind == JsonValueKind.String)
                {
                    string? value = propertyElement.GetString();
                    if (value != null && decimal.TryParse(value.Replace("$", "").Replace(",", ""), NumberStyles.Any, CultureInfo.InvariantCulture, out var decimalValue))
                        return decimalValue;
                }
                // Array handling for single value
                if (propertyElement.ValueKind == JsonValueKind.Array && propertyElement.GetArrayLength() > 0)
                {
                    var firstElement = propertyElement[0];
                    if (firstElement.ValueKind == JsonValueKind.Number && firstElement.TryGetDecimal(out var arrayDecimalValue)) return arrayDecimalValue;
                    if (firstElement.ValueKind == JsonValueKind.String)
                    {
                        string? value = firstElement.GetString();
                        if (value != null && decimal.TryParse(value.Replace("$", "").Replace(",", ""), NumberStyles.Any, CultureInfo.InvariantCulture, out var decimalValue))
                            return decimalValue;
                    }
                }
            }
            return null;
        }

        private static decimal? GetDecimalArrayProperty(JsonElement element, string propertyName, int index)
        {
            if (element.TryGetProperty(propertyName, out var propertyElement) && propertyElement.ValueKind == JsonValueKind.Array && propertyElement.GetArrayLength() > index)
            {
                var arrayElement = propertyElement[index];
                if (arrayElement.ValueKind != JsonValueKind.Null)
                {
                    if (arrayElement.ValueKind == JsonValueKind.Number && arrayElement.TryGetDecimal(out var directDecimalValue))
                        return directDecimalValue;

                    if (arrayElement.ValueKind == JsonValueKind.String)
                    {
                        string? value = arrayElement.GetString();
                        if (value != null && decimal.TryParse(value.Replace("$", "").Replace(",", ""), NumberStyles.Any, CultureInfo.InvariantCulture, out var decimalValue))
                            return decimalValue;
                    }
                }
            }
            return null;
        }

        private static int? GetIntProperty(JsonElement element, string propertyName)
        {
            if (element.TryGetProperty(propertyName, out var propertyElement) && propertyElement.ValueKind != JsonValueKind.Null)
            {
                if (propertyElement.ValueKind == JsonValueKind.Number && propertyElement.TryGetInt32(out var directIntValue))
                    return directIntValue;

                if (propertyElement.ValueKind == JsonValueKind.String)
                {
                    var stringValue = propertyElement.GetString();
                    if (!string.IsNullOrEmpty(stringValue))
                    {
                        stringValue = new string(stringValue.Where(char.IsDigit).ToArray()); // Keep only digits
                        if (int.TryParse(stringValue, out var intValue))
                            return intValue;
                    }
                }
                if (propertyElement.ValueKind == JsonValueKind.Array && propertyElement.GetArrayLength() > 0)
                {
                    var firstElement = propertyElement[0];
                    if (firstElement.ValueKind == JsonValueKind.Number && firstElement.TryGetInt32(out var arrayIntValue)) return arrayIntValue;
                    if (firstElement.ValueKind == JsonValueKind.String)
                    {
                        var stringValue = firstElement.GetString();
                        if (!string.IsNullOrEmpty(stringValue))
                        {
                            stringValue = new string(stringValue.Where(char.IsDigit).ToArray());
                            if (int.TryParse(stringValue, out var intValue))
                                return intValue;
                        }
                    }
                }
            }
            return null;
        }

        private static bool? GetBooleanProperty(JsonElement element, string propertyName)
        {
            if (element.TryGetProperty(propertyName, out var propertyElement))
            {
                if (propertyElement.ValueKind == JsonValueKind.True) return true;
                if (propertyElement.ValueKind == JsonValueKind.False) return false;
                if (propertyElement.ValueKind == JsonValueKind.String)
                {
                    string? val = propertyElement.GetString()?.ToLowerInvariant();
                    if (val == "true" || val == "yes") return true;
                    if (val == "false" || val == "no") return false;
                }
            }
            return null;
        }

        private static TEnum? GetEnumProperty<TEnum>(JsonElement element, string propertyName) where TEnum : struct, Enum
        {
            string? stringValue = GetStringProperty(element, propertyName);
            if (string.IsNullOrEmpty(stringValue)) return null;

            if (Enum.TryParse<TEnum>(stringValue, true, out var enumValue)) return enumValue;

            foreach (TEnum val in Enum.GetValues(typeof(TEnum)))
            {
                string enumName = val.ToString();
                int underscoreIndex = enumName.IndexOf('_');
                if (underscoreIndex > 0 && string.Equals(enumName.Substring(underscoreIndex + 1), stringValue, StringComparison.OrdinalIgnoreCase)) return val;
                if (underscoreIndex >= 0 && string.Equals(enumName.Substring(0, underscoreIndex > 0 ? underscoreIndex : enumName.Length), stringValue, StringComparison.OrdinalIgnoreCase)) return val;
                if (underscoreIndex < 0 && string.Equals(enumName, stringValue, StringComparison.OrdinalIgnoreCase)) return val;
            }

            string modifiedStringValue = stringValue.Replace(" ", "_").Replace("-", "_");
            if (!string.Equals(modifiedStringValue, stringValue, StringComparison.OrdinalIgnoreCase) && Enum.TryParse<TEnum>(modifiedStringValue, true, out enumValue))
            {
                return enumValue;
            }

            Console.WriteLine($"Could not parse enum '{typeof(TEnum).Name}' for property '{propertyName}'. Value: '{stringValue}'");
            return null;
        }

        private static DateTime? GetDateTimeProperty(JsonElement element, string propertyName)
        {
            if (element.TryGetProperty(propertyName, out var propertyElement) && propertyElement.ValueKind == JsonValueKind.String)
            {
                var value = propertyElement.GetString();
                if (string.IsNullOrEmpty(value)) return null;

                if (DateTime.TryParseExact(value, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var dateValue))
                    return dateValue;
                if (DateTime.TryParse(value, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal | DateTimeStyles.AdjustToUniversal, out dateValue)) // More robust parsing
                    return dateValue.ToLocalTime(); // Convert to local if parsed as UTC
            }
            return null;
        }
    }
}