﻿@using Surefire.Domain.Clients.Models
@using Surefire.Domain.Shared.Helpers

@if (selectedClient != null)
{
    string fullAddress = selectedClient.Address != null 
        ? $"{selectedClient.Address.AddressLine1} {selectedClient.Address.AddressLine2} {selectedClient.Address.City}, {selectedClient.Address.State} {selectedClient.Address.PostalCode}"
        : "";
    
    <div class="client-header-container">
        <div class="client-info-section">
            <div class="top-rowone">
                <span class="client-name">@selectedClient.Name</span>
            </div>
            <div class="top-rowtwo">
                @if (!string.IsNullOrEmpty(fullAddress))
                {
                    <span class="data-icon"><FluentIcon Value="@(new Icons.Filled.Size20.Location())" Slot="start" CustomColor="#454545" Color="Color.Custom" /></span>
                    <Trigger Value="@fullAddress" Type="Trigger.ClickType.StreetAddress" Class="client-data" />
                }
                @{
                    var clientEmail = selectedClient.PrimaryContact?.PrimaryEmail?.Email ?? 
                                    selectedClient.PrimaryContact?.EmailAddresses?.FirstOrDefault()?.Email ?? "";
                }
                @if (!string.IsNullOrEmpty(clientEmail))
                {
                    <span class="data-icon"><FluentIcon Value="@(new Icons.Filled.Size20.Mail())" Slot="start" CustomColor="#454545" Color="Color.Custom" /></span>
                    <Trigger Value="@clientEmail" Type="Trigger.ClickType.Email" Class="client-data" />
                }
                @if (!string.IsNullOrEmpty(selectedClient.Website))
                {
                    <span class="data-icon"><FluentIcon Value="@(new Icons.Filled.Size20.Globe())" Slot="start" CustomColor="#454545" Color="Color.Custom" /></span>
                    <Trigger Value="@selectedClient.Website" Type="Trigger.ClickType.Url" Class="client-data" />
                }
            </div>
        </div>
        <span class="client-phone">
            <span class="phone-icon"><a href="tel:@StringHelper.FormatTelDialNumber(selectedClient.PhoneNumber)"><FluentIcon Value="@(new Icons.Regular.Size48.Phone())" Slot="start" CustomColor="#036ac4" Color="Color.Custom" /></a></span>
            <Trigger Value="@StringHelper.FormatPhoneNumber(selectedClient.PhoneNumber)" Type="Trigger.ClickType.Phone" Class="client-phonenumber" />
        </span>
    </div>
}
else
{
    <div class="client-header-container">
        <div class="client-info-section">
            <div class="top-rowone">
                <FluentSkeleton Shimmer="true" Width="100%" Height="50px"></FluentSkeleton>
            </div>
            <div class="top-rowtwo">
                <FluentSkeleton Shimmer="true" Width="100%" Height="25px"></FluentSkeleton>
            </div>
        </div>
    </div>
}

@code {
    [Parameter] public Client selectedClient { get; set; }
}
