{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "additionalProperties": false, "properties": {"client": {"type": "object", "description": "Details of the client this proposal is for.", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "Full name of the insured business or entity"}, "address": {"type": "string", "description": "Street address or PO Box of the client"}, "city": {"type": "string", "description": "City where the client is located"}, "state": {"type": "string", "description": "State or province of the client"}, "postalCode": {"type": "string", "description": "ZIP or postal code of the client location"}, "businessDescription": {"type": "string", "description": "A short description of the type of operations performed by the insured (e.g., roofing contractor, junk hauler, residential painter, etc)"}}}, "carrierName": {"type": "string", "description": "Insurance carrier providing coverage (not wholesaler or broker)"}, "carrierRating": {"type": "string", "description": "AM Best financial rating strength rating of the carrier (e.g., A, A+ (XIV), etc.)"}, "billingCompany": {"type": "string", "description": "The wholesaler or Managing General Agency (MGA) responsible for servicing and billing the policy (e.g., AmWins Access, Wholesure Insurance, etc."}, "effectiveDate": {"type": "string", "description": "Date the quoted and proposed policy becomes active"}, "expirationDate": {"type": "string", "description": "Date the policy expires"}, "priorPolicyNumber": {"type": "string", "description": "Number of the prior policy or policy being renewed, if applicable"}, "pleaseNote": {"type": "string", "description": "Any additional important information regarding the proposal"}, "purePremium": {"type": "string", "description": "Base or 'pure' premium before taxes and fees"}, "quoteExpiration": {"type": "string", "description": "The date that the quote is valid until"}, "totalCost": {"type": "string", "description": "Grand total cost of policy including all premium, taxes, and fees"}, "totalDeposit": {"type": "string", "description": "Deposit required for policy inception"}, "agentComissionPercent": {"type": "string", "description": "The percentage that the agent recieves as a comission, usually 8-15%"}, "totalTaxesAndFees": {"type": "string", "description": "Total taxes and fees charged"}, "minimumEarnedPercent": {"type": "string", "description": "Minimum earned premium percentage, most often 25%"}, "coverages": {"type": "array", "description": "Array of insurance coverage types being quoted and proposed", "items": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The major line of insurance in this proposal (e.g., General Liability, Work Comp, Umbrella)"}, "type": {"type": "string", "description": "Type of coverage trigger or coverage form (e.g., Per Occurrence, Claims Made, Acts Comitted)"}, "retroactiveDate": {"type": "string", "description": "If Claims Made, the retroactive date of coverage"}, "specialWordingForDeductibles": {"type": "string", "description": "Special wording or additional details for deductibles, usually denoted by a asterisk by the deductible dollar amount or deductible title"}, "specialWordingForLimits": {"type": "string", "description": "Special wording for limits, usually denoted by a asterisk by the coverage limits"}, "limits": {"type": "array", "description": "Array of coverage limits for this coverage type", "items": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "Name of the coverage limit", "examples": ["Each Occurrence", "General Aggregate", "Employer Liability", "Products and Completed Operations", "Damage to Premises Rented", "Personal and Advertising Injury"]}, "amount": {"type": "string", "description": "Limit in dollar amount (usually $10,000–$10,000,000)"}}}}, "deductibles": {"type": "array", "description": "Array of deductibles for this coverage", "items": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "Name of deductible type (e.g., <PERSON>, General Liability)"}, "amount": {"type": "string", "description": "Dollar amount of deductible"}}}}}}}, "rates": {"type": "array", "description": "Array of rate and rating basis information", "items": {"type": "object", "additionalProperties": false, "properties": {"basis": {"type": "string", "description": "How the rate is applied", "examples": ["Per $1k of Gross Sales", "Payroll", "ea.sqft", "Percentage of Gross Sales", "Flat"]}, "code": {"type": "string", "description": "Class code for rating such as a WCIRB class code (e.g., 3267-01), SIC, NAIC or internal code (e.g., 92637)"}, "description": {"type": "string", "description": "Class description (e.g., Retail Stores – Not Otherwise Classified, Machinery or Equippment - installation, servicing or repair, Blanket Coverage)"}, "locationNumber": {"type": "string", "description": "Location identifier (e.g., 1, 2)"}, "number": {"type": "string", "description": "Rating number or identifier"}, "modifier": {"type": "string", "description": "The rate, or rate modifier - sometimes called 'Net Rate', 'Gross rate' or just 'Rate' (e.g., 12.267)"}, "exposure": {"type": "string", "description": "The quoted and measurable quantity of the rating basis (sales, payroll, square footage, vehicles-miles, etc.) that the rate applies to (e.g., $256,387)"}, "premium": {"type": "string", "description": "Total premium calculated from rate and exposure (e.g., $2,345)"}, "numberOfFullTimeEmployees": {"type": "string", "description": "Number of full-time employees (only for Work Comp)"}, "numberOfPartTimeEmployees": {"type": "string", "description": "Number of part-time employees (only for Work Comp)"}}}}, "drivers": {"type": "array", "description": "Array of driver details, if applicable", "items": {"type": "object", "additionalProperties": false, "properties": {"legalName": {"type": "string", "description": "Full legal name of the driver"}, "dateOfBirth": {"type": "string", "description": "Driver's date of birth"}, "locationNumber": {"type": "string", "description": "Location identifier for this driver"}, "number": {"type": "string", "description": "Driver number or ID"}}}}, "vehicles": {"type": "array", "description": "Array of vehicle details, if applicable", "items": {"type": "object", "additionalProperties": false, "properties": {"vin": {"type": "string", "description": "Vehicle Identification Number"}, "yearMakeModel": {"type": "string", "description": "Year, make, and model of the vehicle"}, "locationNumber": {"type": "string", "description": "Location identifier for this vehicle"}, "number": {"type": "string", "description": "Vehicle number or ID"}}}}, "locations": {"type": "array", "description": "Array of location details for insured premises", "items": {"type": "object", "additionalProperties": false, "properties": {"description": {"type": "string", "description": "Name or context of the location (e.g., Headquarters, Mailing Address)"}, "fullAddress": {"type": "string", "description": "Full address of insured location"}, "locationNumber": {"type": "string", "description": "Location identifier"}}}}, "endorsements": {"type": "array", "description": "Array of policy endorsements (extract EVERY endorsement mentioned)", "items": {"type": "object", "additionalProperties": false, "properties": {"title": {"type": "string", "description": "The full name of the endorsement", "examples": ["Building and Personal Property Coverage Form", "EXCLUSION OF LOSS DUE TO VIRUS OR BACTERIA", "Vacancy Permit Applicable to Securely Locked Buildings", "Exclusion - Change in Use or Occupancy"]}, "code": {"type": "string", "description": "The form number of the endorsement", "examples": ["CG 20 10 04 13", "AP2104US-1233", "IL003 [01-23]", "CPP 020 12 20"]}}}}, "contingencies": {"type": "array", "description": "Array of contingency provisions or conditions prior to binding", "items": {"type": "object", "additionalProperties": false, "properties": {"description": {"type": "string", "description": "Description of contingency or special condition", "examples": ["Signed and date Acord applications", "Fully completed, signed and date contractors supplemental application", "D-1", "SL-2"]}}}}, "additionalTerms": {"type": "array", "description": "Array of additional terms and conditions of the policy", "items": {"type": "object", "additionalProperties": false, "properties": {"text": {"type": "string", "description": "Legal terms, conditions, and disclaimers of the policy"}}}}, "fees": {"type": "array", "description": "Array of all taxes, fees, and charges included in the total cost of the policy (extract EVERY distinct tax/fee/surcharge)", "items": {"type": "object", "additionalProperties": false, "properties": {"title": {"type": "string", "description": "Exact name of the tax/fee as it appears (e.g., State Tax, Broker Fee, Surplus Lines Tax, Policy Fee)"}, "amount": {"type": "string", "description": "Dollar amount of the tax or fee (including any $ sign)"}}}}}}