using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Surefire.Domain.Forms.Models;

namespace Surefire.Domain.Forms.Services
{
    public class ExternalPortalService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<ExternalPortalService> _logger;
        private readonly string _connectionString;

        public ExternalPortalService(IConfiguration configuration, ILogger<ExternalPortalService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _connectionString = Environment.GetEnvironmentVariable("PORTALCONNECTION") 
                ?? _configuration.GetConnectionString("PortalConnection") 
                ?? throw new InvalidOperationException("Portal connection string not found");
        }

        public async Task<List<CertificateRequest>> GetAllCertificateRequestsAsync()
        {
            var requests = new List<CertificateRequest>();
            
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    // Let's first check what columns actually exist in the table
                    var columnQuery = @"
                        SELECT COLUMN_NAME 
                        FROM INFORMATION_SCHEMA.COLUMNS 
                        WHERE TABLE_NAME = 'CertificateRequests'";
                        
                    List<string> columns = new List<string>();
                    
                    try
                    {
                        using (var cmd = new SqlCommand(columnQuery, connection))
                        {
                            using (var reader = await cmd.ExecuteReaderAsync())
                            {
                                while (await reader.ReadAsync())
                                {
                                    columns.Add(reader.GetString(0));
                                }
                            }
                        }
                        
                        _logger.LogInformation($"Found columns in CertificateRequests table: {string.Join(", ", columns)}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Could not retrieve column information: {ex.Message}");
                        // Continue with a simplified query if we can't get column info
                    }
                    
                    // Adjust query based on available columns
                    string query = "SELECT * FROM CertificateRequests";
                    
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var request = new CertificateRequest();
                                
                                // Map columns that might be named differently
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    string columnName = reader.GetName(i);
                                    
                                    if (reader.IsDBNull(i))
                                        continue;
                                        
                                    switch (columnName.ToLower())
                                    {
                                        case "id":
                                            request.CertificateRequestId = reader.GetInt32(i);
                                            request.ExternalRequestId = reader.GetInt32(i).ToString();
                                            break;
                                            
                                        case "holdername":
                                            request.HolderName = reader.GetString(i);
                                            break;
                                            
                                        case "holderattention":
                                            request.HolderAttention = reader.GetString(i);
                                            break;
                                            
                                        case "holderaddress":
                                            request.HolderAddress = reader.GetString(i);
                                            break;
                                            
                                        case "holdercity":
                                            request.HolderCity = reader.GetString(i);
                                            break;
                                            
                                        case "holderstate":
                                            request.HolderState = reader.GetString(i);
                                            break;
                                            
                                        case "holderzip":
                                            request.HolderZip = reader.GetString(i);
                                            break;
                                            
                                        case "holderemail":
                                            request.HolderEmail = reader.GetString(i);
                                            break;
                                            
                                        case "clientcompany":
                                            request.ClientCompany = reader.GetString(i);
                                            break;
                                            
                                        case "clientname":
                                            request.ClientName = reader.GetString(i);
                                            break;
                                            
                                        case "clientemail":
                                            request.ClientEmail = reader.GetString(i);
                                            break;
                                            
                                        case "clientphone":
                                            request.ClientPhone = reader.GetString(i);
                                            break;
                                            
                                        case "status":
                                            request.Status = reader.GetString(i);
                                            break;
                                            
                                        case "requestdate":
                                            request.RequestDate = reader.GetDateTime(i);
                                            break;
                                            
                                        case "lastupdated":
                                            request.ImportedDate = reader.GetDateTime(i);
                                            break;
                                            
                                        case "notes":
                                            request.Notes = reader.GetString(i);
                                            break;
                                            
                                        case "primarynoncontributary":
                                            request.PrimaryNonContributary = reader.GetBoolean(i);
                                            break;
                                            
                                        case "waiversubrogationgl":
                                            request.WaiverSubrogationGL = reader.GetBoolean(i);
                                            break;
                                            
                                        case "waiversubrogationwc":
                                            request.WaiverSubrogationWC = reader.GetBoolean(i);
                                            break;
                                            
                                        case "emailtoholder":
                                            request.EmailToHolder = reader.GetBoolean(i);
                                            break;
                                            
                                        case "emailcopytoclient":
                                            request.EmailCopyToClient = reader.GetBoolean(i);
                                            break;
                                    }
                                }
                                
                                // Set default values for required fields if they weren't found
                                if (string.IsNullOrEmpty(request.HolderName))
                                    request.HolderName = "Unknown Holder";
                                    
                                if (request.RequestDate == default(DateTime))
                                    request.RequestDate = DateTime.UtcNow;
                                    
                                if (string.IsNullOrEmpty(request.Status))
                                    request.Status = "Pending";
                                    
                                // Mark as external
                                request.IsImported = true;
                                
                                requests.Add(request);
                            }
                        }
                    }
                }
                
                _logger.LogInformation($"Retrieved {requests.Count} certificate requests from external portal database");
                return requests;
            }
            catch (SqlException ex) when (ex.Number == 207) // Invalid column name
            {
                _logger.LogWarning($"Invalid column name in external database: {ex.Message}. Returning empty result set.");
                return new List<CertificateRequest>();
            }
            catch (SqlException ex) when (ex.Number == 208) // Table or view not found
            {
                _logger.LogWarning($"Table not found in external database: {ex.Message}. Returning empty result set.");
                return new List<CertificateRequest>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving certificate requests from external portal database");
                return new List<CertificateRequest>(); // Return empty list instead of throwing
            }
        }
        
        public async Task<CertificateRequest> GetCertificateRequestByIdAsync(int requestId)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    // Use a simple query with only the Id column which we know exists
                    string query = "SELECT * FROM CertificateRequests WHERE Id = @RequestId";
                    
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.Add("@RequestId", SqlDbType.Int).Value = requestId;
                        
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                var request = new CertificateRequest();
                                
                                // Map columns that might be named differently
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    string columnName = reader.GetName(i);
                                    
                                    if (reader.IsDBNull(i))
                                        continue;
                                        
                                    switch (columnName.ToLower())
                                    {
                                        case "id":
                                            request.CertificateRequestId = reader.GetInt32(i);
                                            request.ExternalRequestId = reader.GetInt32(i).ToString();
                                            break;
                                            
                                        case "holder":
                                        case "holdername":
                                            request.HolderName = reader.GetString(i);
                                            break;
                                            
                                        case "holderattention":
                                        case "attention":
                                            request.HolderAttention = reader.GetString(i);
                                            break;
                                            
                                        case "holderaddress":
                                        case "address":
                                            request.HolderAddress = reader.GetString(i);
                                            break;
                                            
                                        case "holdercity":
                                        case "city":
                                            request.HolderCity = reader.GetString(i);
                                            break;
                                            
                                        case "holderstate":
                                        case "state":
                                            request.HolderState = reader.GetString(i);
                                            break;
                                            
                                        case "holderzip":
                                        case "zip":
                                        case "zipcode":
                                            request.HolderZip = reader.GetString(i);
                                            break;
                                            
                                        case "holderemail":
                                            request.HolderEmail = reader.GetString(i);
                                            break;
                                            
                                        case "client":
                                        case "clientname":
                                        case "clientcompany":
                                            request.ClientCompany = reader.GetString(i);
                                            break;
                                            
                                        case "contact":
                                            request.ClientName = reader.GetString(i);
                                            break;
                                            
                                        case "email":
                                        case "clientemail":
                                            request.ClientEmail = reader.GetString(i);
                                            break;
                                            
                                        case "phone":
                                        case "clientphone":
                                            request.ClientPhone = reader.GetString(i);
                                            break;
                                            
                                        case "clientid":
                                            request.ClientId = reader.GetInt32(i);
                                            break;
                                            
                                        case "status":
                                            request.Status = reader.GetString(i);
                                            break;
                                            
                                        case "requestdate":
                                        case "date":
                                        case "createdon":
                                            request.RequestDate = reader.GetDateTime(i);
                                            break;
                                            
                                        case "lastupdated":
                                            request.ImportedDate = reader.GetDateTime(i);
                                            break;
                                            
                                        case "notes":
                                        case "comments":
                                            request.Notes = reader.GetString(i);
                                            break;
                                            
                                        case "project":
                                        case "projectname":
                                            // Project details are in a separate object in the external model
                                            request.ProjectName = reader.GetString(i);
                                            break;
                                            
                                        case "projectnumber":
                                            request.ProjectNumber = reader.GetString(i);
                                            break;
                                            
                                        case "projectaddress":
                                            request.ProjectAddress = reader.GetString(i);
                                            break;
                                            
                                        case "startdate":
                                            request.StartDate = reader.GetDateTime(i);
                                            break;
                                            
                                        case "enddate":
                                            request.EndDate = reader.GetDateTime(i);
                                            break;
                                            
                                        case "description":
                                        case "details":
                                            request.Description = reader.GetString(i);
                                            break;
                                            
                                        case "primarynoncontributary":
                                        case "primaryandnoncontributary":
                                            request.PrimaryNonContributary = reader.GetBoolean(i);
                                            break;
                                            
                                        case "waiversubrogationgl":
                                        case "waiverofsubrogationgl":
                                            request.WaiverSubrogationGL = reader.GetBoolean(i);
                                            break;
                                            
                                        case "waiversubrogationwc":
                                        case "waiverofsubrogationwc":
                                            request.WaiverSubrogationWC = reader.GetBoolean(i);
                                            break;
                                            
                                        case "emailtoholder":
                                            request.EmailToHolder = reader.GetBoolean(i);
                                            break;
                                            
                                        case "emailcopytoclient":
                                            request.EmailCopyToClient = reader.GetBoolean(i);
                                            break;
                                    }
                                }
                                
                                // Set default values for required fields if they weren't found
                                if (string.IsNullOrEmpty(request.HolderName))
                                    request.HolderName = "Unknown Holder";
                                
                                if (request.RequestDate == default(DateTime))
                                    request.RequestDate = DateTime.UtcNow;
                                
                                if (string.IsNullOrEmpty(request.Status))
                                    request.Status = "Pending";
                                
                                // Mark as external
                                request.IsImported = true;
                                
                                return request;
                            }
                            
                            return null;
                        }
                    }
                }
            }
            catch (SqlException ex) when (ex.Number == 207) // Invalid column name
            {
                _logger.LogWarning($"Invalid column name in external database: {ex.Message}. Returning null for certificate request {requestId}");
                return null;
            }
            catch (SqlException ex) when (ex.Number == 208) // Table or view not found
            {
                _logger.LogWarning($"Table not found in external database: {ex.Message}. Returning null for certificate request {requestId}");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving certificate request ID {requestId} from external portal database");
                return null; // Return null instead of throwing
            }
        }
        
        public async Task<bool> UpdateCertificateRequestStatusAsync(int requestId, string status, string notes = null)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    // We know the ID column is called "Id" based on the model
                    // Check if record exists
                    using (var checkCommand = new SqlCommand("SELECT COUNT(1) FROM CertificateRequests WHERE Id = @RequestId", connection))
                    {
                        checkCommand.Parameters.Add("@RequestId", SqlDbType.Int).Value = requestId;
                        var exists = (int)await checkCommand.ExecuteScalarAsync();
                        
                        if (exists == 0)
                        {
                            _logger.LogWarning($"Certificate request {requestId} not found in external portal database");
                            return false;
                        }
                    }
                    
                    // Build update query for the known column names
                    var updateParts = new List<string>();
                    var parameters = new List<SqlParameter>();
                    
                    // Status column is definitely there based on the model
                    updateParts.Add("Status = @Status");
                    parameters.Add(new SqlParameter("@Status", SqlDbType.NVarChar, 50) { Value = status ?? "Pending" });
                    
                    // Notes column is in the model
                    updateParts.Add("Notes = @Notes");
                    parameters.Add(new SqlParameter("@Notes", SqlDbType.NVarChar, -1) { Value = (object)notes ?? DBNull.Value });
                    
                    // LastUpdated is used instead of ImportedDate
                    updateParts.Add("LastUpdated = @LastUpdated");
                    parameters.Add(new SqlParameter("@LastUpdated", SqlDbType.DateTime) { Value = DateTime.UtcNow });
                    
                    string updateQuery = $"UPDATE CertificateRequests SET {string.Join(", ", updateParts)} WHERE Id = @RequestId";
                    
                    using (var command = new SqlCommand(updateQuery, connection))
                    {
                        command.Parameters.Add("@RequestId", SqlDbType.Int).Value = requestId;
                        
                        foreach (var param in parameters)
                        {
                            command.Parameters.Add(param);
                        }
                        
                        var rowsAffected = await command.ExecuteNonQueryAsync();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (SqlException ex) when (ex.Number == 207) // Invalid column name
            {
                _logger.LogWarning($"Invalid column name in external database: {ex.Message}");
                return false;
            }
            catch (SqlException ex) when (ex.Number == 208) // Table or view not found
            {
                _logger.LogWarning($"Table not found in external database: {ex.Message}");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating certificate request status for request {requestId}");
                return false;
            }
        }
        
        // Add more methods for other external portal data as needed
        // For example:
        
        public async Task<List<ExternalClient>> GetTopExternalClientsAsync(int count = 20)
        {
            var clients = new List<ExternalClient>();
            
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.OpenAsync();
                    
                    using (var command = new SqlCommand(@"
                        SELECT TOP (@Count)
                            cl.ClientId,
                            cl.CompanyName,
                            cl.ContactName,
                            cl.EmailAddress,
                            cl.PhoneNumber,
                            COUNT(cr.CertificateRequestId) AS RequestCount,
                            MAX(cr.RequestDate) AS LastRequestDate
                        FROM Clients cl
                        LEFT JOIN CertificateRequests cr ON cl.ClientId = cr.ClientId
                        GROUP BY cl.ClientId, cl.CompanyName, cl.ContactName, cl.EmailAddress, cl.PhoneNumber
                        ORDER BY LastRequestDate DESC", connection))
                    {
                        command.Parameters.Add("@Count", SqlDbType.Int).Value = count;
                        
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                clients.Add(new ExternalClient
                                {
                                    ClientId = reader.GetInt32(reader.GetOrdinal("ClientId")),
                                    CompanyName = reader.GetString(reader.GetOrdinal("CompanyName")),
                                    ContactName = !reader.IsDBNull(reader.GetOrdinal("ContactName")) 
                                        ? reader.GetString(reader.GetOrdinal("ContactName")) 
                                        : null,
                                    EmailAddress = !reader.IsDBNull(reader.GetOrdinal("EmailAddress")) 
                                        ? reader.GetString(reader.GetOrdinal("EmailAddress")) 
                                        : null,
                                    PhoneNumber = !reader.IsDBNull(reader.GetOrdinal("PhoneNumber")) 
                                        ? reader.GetString(reader.GetOrdinal("PhoneNumber")) 
                                        : null,
                                    RequestCount = reader.GetInt32(reader.GetOrdinal("RequestCount")),
                                    LastRequestDate = !reader.IsDBNull(reader.GetOrdinal("LastRequestDate")) 
                                        ? reader.GetDateTime(reader.GetOrdinal("LastRequestDate")) 
                                        : null
                                });
                            }
                        }
                    }
                }
                
                return clients;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving external clients from portal database");
                throw;
            }
        }
    }
    
    // Simple DTO class for external clients
    public class ExternalClient
    {
        public int ClientId { get; set; }
        public string CompanyName { get; set; }
        public string ContactName { get; set; }
        public string EmailAddress { get; set; }
        public string PhoneNumber { get; set; }
        public int RequestCount { get; set; }
        public DateTime? LastRequestDate { get; set; }
    }
} 