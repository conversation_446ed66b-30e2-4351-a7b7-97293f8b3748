﻿.chopper-messaging {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    overflow: hidden;
    background-color: #1f2023;
    color: #e0e0e0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    margin-top: 50px;
}

.chopper-header {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #3a3b3e;
    background-color: #2a2b2e;
    color: #e0e0e0;
    border-radius: 8px 8px 0 0; 
}



.chopper-actions {
    display: flex;
    width: 100%;
    gap: 0;
    padding: 0;
}

.chopper-title {
    padding: 8px 16px;
    border-top: 1px solid #3a3b3e;
}

.chopper-title h3 {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    font-size: 1rem; 
    font-weight: 500;
}

.chopper-actions .flu-btn {
    position: relative;
    flex: 1;
    border-radius: 0;
    border-right: 1px solid #3a3b3e;
    padding: 8px 4px;
    font-size: 0.8rem;
    justify-content: center;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.chopper-actions .flu-btn:last-child {
    border-right: none;
}

    .chopper-actions .flu-btn.active {
        background-color: #4a90e2;
        color: white;
        background: linear-gradient(to bottom, rgba(30,35,43,1) 0%,rgba(78,92,107,1) 25%,rgba(68,82,96,1) 70%,rgba(108,133,153,1) 100%);
        opacity:1 !important;
        color:#fff !important;
    }

.chopper-actions .flu-btn.active:hover {
    background-color: #357abd;
}

.chopper-actions .flu-btn.active .flu-icon {
    color: white !important;
}

.chopper-actions .unread-badge {
    position: absolute;
    top: -2px;
    right: 2px;
    min-width: 16px;
    height: 16px;
    font-size: 0.65rem;
    font-weight: 600;
    z-index: 1;
    background-color: #e74c3c;
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 4px;
}

.message-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
}

.new-conversation-panel {
    background-color: #2a2b2e;
    border-bottom: 1px solid #3a3b3e;
    padding: 16px;
    flex-shrink: 0;
    z-index: 10;
}

.new-conversation-panel h5 {
    margin: 0 0 12px 0;
    font-size: 1rem;
    font-weight: 500;
    color: #e0e0e0;
}

.new-conversation-input {
    display: flex;
    align-items: center;
    gap: 8px;
}

.chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.chat-content.pushed-down {
    margin-top: 0;
}

.loading-messages, .no-messages {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 24px;
    gap: 12px;
    color: #a0a0a0;
    min-height: calc(100vh - 309px);
}
.message-container-insidepadding {
    padding:10px;
}
.messages-list {
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;
    overflow-y: auto;
    height: calc(100vh - 297px);
    scroll-behavior: smooth;
}

.message-item {
    display: flex;
    max-width: 75%; 
    margin-bottom: 10px;
    position: relative;
}

    .message-item.incoming {
        align-self: flex-start;
    }

    .message-item.outgoing {
        align-self: flex-end;
    }

.message-content {
    padding: 8px 10px;
    border-radius: 6px;
    position: relative;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.35);
    word-wrap: break-word;
    font-size: 0.875rem;
}

.message-item.incoming .message-content {
    background-color: #3a3b3e;
    color: #e0e0e0;
    margin-left: 8px;
}

.message-item.outgoing .message-content {
    background-color: #4a90e2;
    color: #ffffff;
    margin-right: 8px;
}

/* Message Confirmation Styles */
.message-confirmation {
    position: absolute;
    top: -4px;
    right: -4px;
    z-index: 10;
}

.confirmation-circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 10px;
    font-weight: 600;
    border: 2px solid;
}

.confirmation-circle.unconfirmed {
    background-color: transparent;
    border-color: #9d9999;
    color: #9d9999;
}

.confirmation-circle.unconfirmed:hover {
    background-color: #9d9999;
    color: #1f2023;
    transform: scale(1.1);
}

.confirmation-circle.confirmed {
    background-color: #4a90e2;
    border-color: #4a90e2;
    color: white;
    cursor: default;
}

.confirmation-circle.confirmed img {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    object-fit: cover;
}

.confirmation-circle.confirmed span {
    font-size: 10px;
    font-weight: 600;
}

/* Incoming Speech Bubble Tail */
.message-item.incoming .message-content::before {
    content: '';
    position: absolute;
    bottom: 3px;
    left: -6px;
    width: 0px;
    height: 0;
    border: 14px solid transparent;
    border-top-color: #3a3b3e;
    border-bottom: 0;
    border-left: 0;
    transform: rotate(-45deg);
    top: 11px;
}

/* Outgoing Speech Bubble Tail */
.message-item.outgoing .message-content::after {
    content: '';
    position: absolute;
    bottom: 6px;
    right: -2px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-top-color: #4a90e2;
    border-bottom: 0;
    border-right: 0;
    transform: rotate(45deg);
}

.message-content p {
    margin: 0 0 3px 0;
    line-height: 1.4;
}

.message-time {
    font-size: 0.6875rem;
    opacity: 0.7;
    display: block;
    text-align: right;
    color: #a0a0a0;
    margin-top: 3px;
}
.message-avatar {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #3a3b3e;
    background-color: #2a2b2e;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #4a90e2;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.message-sender {
    font-size: 0.75rem;
    font-weight: 500;
    color: #4a90e2;
    margin-bottom: 2px;
    opacity: 0.9;
}

/* Adjust message item layout for avatars */
.message-item.incoming {
    align-self: flex-start;
    flex-direction: row;
}

.message-item.outgoing {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-item.outgoing .message-avatar {
    order: 1;
}

.message-item.outgoing .message-content {
    order: 0;
}

.message-input {
    padding: 12px 16px;
    border-top: 1px solid #3a3b3e;
    background-color: #2a2b2e;
}

    .message-input input {
        width:300px;
    }

.message-input-textarea input {
    width: 90%;
    width: 340px !important;
    height: 34px !important;
}

.message-input-textarea input[type="text"] {
    width: 100%;
    height: 32px;
    padding: 6px 12px;
    border: 2px solid #797979;
    border-radius: 4px;
    background-color: #cbcbcb;
    color: #000000;
    font-size: 0.875rem;
    font-family: inherit;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    outline: none;
    box-sizing: border-box;
}

.message-input-textarea input[type="text"]:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.message-input-textarea input[type="text"]::placeholder {
    color: #000000;
    opacity: 0.7;
}

.message-input-textarea input[type="text"]:hover {
    border-color: #4a90e2;
}
.refresh-button {
    margin-top: 12px;
    display: flex;
    justify-content: center;
}

.action-buttons {
    margin-top: 12px;
    display: flex;
    justify-content: center;
    gap: 8px;
}
.action-buttons-conv {
    display: flex;
    justify-content: center;
}
.chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.chat-title {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.chat-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 500;
    color: #e0e0e0;
}

.message-count {
    font-size: 0.8rem;
    font-weight: 400;
    color: #a0a0a0;
    opacity: 0.8;
}

.chat-info {
    color: #a0a0a0;
    font-size: 0.7rem;
    opacity: 0.7;
}

.chat-actions {
    display: flex;
    gap: 8px;
}

.sms-actions {
    display: flex;
    justify-content: space-between;
    align-items: center; /* Vertically align items */
    margin-bottom: 8px;
    padding: 0 4px; /* Add some horizontal padding */
}

.conversation-list {
    margin-bottom: 8px;
    overflow-y: auto;
    border: 1px solid #444444;
    border-radius: 4px;
    background-color: #2a2b2e;
}

.conversation-grid {
    display: flex;
    flex-direction: column;
}

.conversation-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #3a3b3e;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
}

    .conversation-item.unread {
        background-color: rgba(255, 255, 255, 0.05);
        font-weight: 500;
    }

    .conversation-item:last-child {
        border-bottom: none;
    }

    .conversation-item:hover {
        background-color: #3a3b3e;
    }

.conversation-details {
    display: flex;
    flex-direction: column;
    gap: 2px;
    width: 75%;
    overflow: hidden;
}

.conversation-phone {
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 500;
    color: #e0e0e0;
    font-size: 0.9rem;
}

.conversation-preview {
    font-size: 0.8rem;
    color: #a0a0a0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.conversation-time {
    color: #a0a0a0;
    font-size: 0.7rem;
    align-self: flex-start;
    white-space: nowrap;
    padding-left: 8px;
}

.unread-badge {
    background-color: #4a90e2;
    color: #ffffff;
    border-radius: 50%;
    min-width: 16px; 
    height: 16px;
    font-size: 0.65rem; 
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 4px;
    font-weight: normal;
}

.conversation-search {
    padding: 8px;
    border-bottom: 1px solid #444444;
}

.current-conversation {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    margin-bottom: 8px;
    background-color: #3a3b3e;
    border-radius: 4px;
    font-weight: 500;
    color: #e0e0e0;
}

.debug-panel {
    background-color: #2a2b2e;
    border: 1px solid #444444;
    border-radius: 4px;
    padding: 10px; 
    margin: 8px;
    font-family: monospace;
    font-size: 11px;
    color: #a0a0a0;
    max-height: 150px;
    overflow-y: auto;
}

.debug-item {
    margin-bottom: 4px;
    word-break: break-all;
}

.phone-input-container {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    padding: 0 8px;
}

.conversation-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 8px;
    padding: 8px 0;
    border-top: 1px solid #3a3b3e;
}


.flu-btn {
    border: 1px solid #535353;
    vertical-align: middle;
    cursor: pointer;
    transition: background 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, opacity 0.15s ease-in-out, color 0.15s ease-in-out;
    position: relative;
    padding: 7px 5px 3px 5px;
    border-radius: 4px;
    background-color: #373737;
    opacity:.65 !important;
}
    .flu-btn:hover {
        border: 1px solid #8f8f8f;
        cursor: pointer;
        transition: background 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, opacity 0.15s ease-in-out, color 0.15s ease-in-out;
        position: relative;
        background-color: #515151;
        opacity: 1 !important;
    }
.flu-btn-md {
    padding: 4px 10px;
    font-size: 0.875rem;
    line-height: 1.3;
}

.flu-btn-sm {
    padding: 3px 8px;
    font-size: 0.75rem;
    line-height: 1.3;
    opacity: .8;
    color:#637485;
}

.flu-btn.neutral {
    background-image: linear-gradient(to bottom, #424447, #3a3b3e);
    color: #d0d0d0; 
    border-color: #2f3033;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.04), 0 1px 1px rgba(0, 0, 0, 0.3);
}

    .flu-btn.neutral:hover:not(:disabled) {
        background-image: linear-gradient(to bottom, #4a4c50, #404145);
        color: #e0e0e0;
        border-color: #38393d;
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.06), 0 1px 2px rgba(0, 0, 0, 0.35);
    }

    .flu-btn.neutral:active:not(:disabled) {
        background-image: linear-gradient(to bottom, #36373a, #3a3b3e);
        color: #f0f0f0;
        border-color: #28292c;
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.4);
    }

.flu-btn.accent {
    background: linear-gradient(to bottom, rgba(30,35,43,1) 0%,rgba(78,92,107,1) 25%,rgba(68,82,96,1) 100%);
    color: #ffffff;
    border-color: #3a4653;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.08), 0 1px 1px rgba(0, 0, 0, 0.4);
    text-align: center;
    display: flex;
    
}

    .flu-btn.accent:hover:not(:disabled) {
        background: linear-gradient(to bottom, rgba(30,35,43,1) 0%,rgba(78,92,107,1) 25%,rgba(68,82,96,1) 70%,rgba(108,133,153,1) 100%);

        border-color: #445260;
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 2px rgba(0, 0, 0, 0.45);
    }

    .flu-btn.accent:active:not(:disabled) {
        background-image: linear-gradient(to bottom, #445260, #4a5867);
        border-color: #333f4b;
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.5);
    }

.flu-btn:focus-visible {
    box-shadow: 0 0 0 2px #1f2023, 0 0 0 4px #777777;
}

.flu-btn:disabled,
.flu-btn[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
    box-shadow: none;
}
.flu-btn-text {
    position: relative;
    top: 1px;
}
.new-conversation-panel {
    background-color: #2a2b2e;
    border-bottom: 1px solid #3a3b3e;
    padding: 16px;
    flex-shrink: 0;
    z-index: 10;
    margin: 0;
    border-radius: 0;
}

.new-conversation-panel h5 {
    margin: 0 0 12px 0;
    font-size: 1rem;
    font-weight: 500;
    color: #e0e0e0;
}

.new-conversation-input {
    display: flex;
    align-items: center;
    gap: 8px;
}

.new-conversation-input .flu-textarea {
    flex: 1;
    min-height: 36px;
    max-height: 80px;
    resize: vertical;
}

.new-conversation-input .flu-textarea textarea {
    background-color: #3a3b3e;
    border: 1px solid #4a4b4e;
    color: #e0e0e0;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 0.875rem;
    resize: vertical;
}

.new-conversation-input .flu-textarea textarea:focus {
    border-color: #4a90e2;
    outline: none;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.new-conversation-input .flu-textarea textarea::placeholder {
    color: #808080;
}
.conversation-phone-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.conversation-name {
    display: flex;
    flex-direction: column;
    gap: 1px;
    font-size: .85em;
}

.contact-name {
    font-weight: 600;
    color: #cbcbcb !important;
}

.contact-title {
    font-size: 0.8em;
    color: #666;
    font-style: italic;
}

.phone-number {
    font-weight: 500;
    color: #333;
}

.conversation-phone-number {
    font-size: 0.75em;
    color: #888;
}

.current-conversation-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.current-conversation .conversation-name {
    display: flex;
    align-items: center;
    gap: 8px;
}

.current-conversation .contact-title {
    font-size: 0.85em;
    color: #666;
    font-style: italic;
}

.debug-info {
    background-color: #f0f0f0;
    padding: 8px;
    margin: 8px 0;
    border-radius: 4px;
    font-size: 0.8em;
    color: #666;
}

    .debug-info p {
        margin: 2px 0;
    }

.web-chat-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 309px);
    padding: 24px;
    color: #a0a0a0;
}

.placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    text-align: center;
}

.placeholder-content h4 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 500;
    color: #e0e0e0;
}

.placeholder-content p {
    margin: 0;
    font-size: 0.875rem;
    opacity: 0.8;
}