:root .e-dropdownlist {
    font-size: .8em !important;
    font-weight: 400;
    font-weight: normal !important;
    background-color: transparent !important;
    border: 0px !important;
    padding-left: 3px !important;
    padding-right: 0px !important;
    padding-top: 2px !important;
    padding-bottom: 2px !important;
    font-family: "montserrat", sans-serif;
    font-size: .8em !important;
}

:root .e-input {
    /*padding-left: 9px;
    padding-right: 22px;
    height: 30px;*/
}

.det-loader-container {
    position: relative;
}
.det-loader {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgb(255 255 255 / 66%);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all ease 1s;
    pointer-events: none;
    z-index: 2000;
}

.det-loader-True {
    opacity: 1;
    pointer-events: all;
}

.det-loader-False {
    opacity: 0;
    pointer-events: none;
}
:root .sf-eformbld {
    font-weight: bold !important;
}

.filter-section {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

#filter-results {
    display: block;
    transition: all 0.25s ease-in-out;
}

.results-on {
    height: 100%;
    opacity: 1;
}

.results-temp {
    height: auto;
    opacity: 1;
}

.results-off {
    height: 0%;
    overflow: hidden;
    opacity: .5;
}

.selected-on {
    height: 100%;
    opacity: 1;
}

.selected-off {
    height: 0%;
    overflow: hidden;
    opacity: .5;
}

.renewalitemlink:hover {
    cursor: pointer;
}

.spcr {
    width: 15px;
}

#renewal-view {
    display: block;
    transition: all 0.25s ease-in-out;
}

.monthholder {
    width: 100px;
    text-align: center;
}

.yearholder {
    text-align: center;
}

.userholder {
}


.homelist {
    background-color: #fff;
    border-radius: 6px;
    padding: 10px;
    box-shadow: 0px 0px 10px #ccc;
}

h1 {
    font-family: "montserrat", sans-serif;
    font-weight: 400;
    font-style: normal;
    font-size: 2.9em;
    margin-bottom: 0px;
}

:root .e-input {
    padding-left: 9px;
    border: 2px solid #f1f1f1;
    padding-right: 22px;
    height: 30px;
    background-color: #f1f1f1;
    color: #3c3c3c !important;
}

.sf-chevron {
    position: relative;
    top: 2px;
}

select {
    -webkit-appearance: none;
    -moz-appearance: none;
    text-indent: 1px;
    text-overflow: '';
}

    select::-ms-expand {
        display: none;
    }

.txt-name {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: .6em;
    color: #626262;
    position: relative;
    top: -8px;
    left: -5px;
}

.renewal-assignedto {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 4px;
    position: relative;
    top: 3px;
}

.toolbar-calendar-calendar {
    opacity: 1;
    pointer-events: all;
}

.toolbar-calendar-selected {
    opacity: .4;
    pointer-events: none;
}

.toolbar-selected-selected {
    opacity: 1;
    pointer-events: all;
}

.toolbar-selected-calendar {
    opacity: .4;
    pointer-events: none;
}

.toolbar-text-cal {
    color: #3c3c3c;
    font-family: "montserrat", sans-serif;
    font-size: .85em;
    width: 115px;
    text-align: center;
    -webkit-user-select: none; /* For Safari */
    -moz-user-select: none; /* For Firefox */
    -ms-user-select: none; /* For Internet Explorer/Edge */
    user-select: none;
}

.toolbar-link-cal {
    text-decoration: none;
}

:root .toolbar-link-cal svg {
    fill: #636363 !important;
}

.sftb {
    position: relative;
    top: 1px;
}

.sftb-disabled{
    opacity:.4;
    pointer-events:none;
}
.sf-threedot {
    opacity: .5;
    position: relative;
    top: 1px;
    left: 2px;
}

    .sf-threedot:hover {
        cursor: pointer;
        opacity: 1;
    }

.sf-completedcheckbox {
    position: relative;
    top: -4px;
    left: 2px;
    box-shadow: 0px 0px 5px #ffffffc9;
    transform: scale(1.35);
}



/* ----------------------------------------- */
/*               TASK TABLE                  */
/* ----------------------------------------- */
.table {
    border-collapse: separate; /* Use separate borders instead of collapsed */
    border-spacing: 0 4px;
}

    .table thead {
        font-family: "montserrat", sans-serif;
        font-weight: 700 !important;
        text-align: left;
        font-size: 11px;
        color: #767676;
        text-transform: uppercase;
    }
tbody tr {
    border-top: 5px solid #0f6cbd;
    font-weight: bold;
}


.sf-leftcell {
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#207cca+40,ffffff+43,b9bdcc+46,b9bdcc+46,b9bdcc+94,ffffff+97,dce7f4+100 */
    background: linear-gradient(to right, #207cca 40%,#ffffff 43%,#b9bdcc 46%,#b9bdcc 46%,#b9bdcc 94%,#ffffff 97%,#dce7f4 100%);
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    padding-right: 10px;
    min-width: 50px !important;
    width: 50px !important;
    max-width: 50px;
    overflow: hidden !important;
    white-space: nowrap !important;
}
.sf-taskname-cell {
    padding: 0px 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.sf-rightcell {
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#dce7f4+0,000000+100&0+0,0.12+100 */
    /*background: linear-gradient(to right, rgba(220,231,244,0) 0%,rgba(0,0,0,0.12) 100%);*/
    padding-left: 10px;
}
.sf-rc-round {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

.sf-status-cell {
    font-size: 11px !important;
    width: 100px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.sf-subtask-cell {
    width: 30px;
}
.sf-connector-cell {
    width: 25px;
}

/* ----------------------------------------- */
.highlighted-row td:nth-child(2) {
    background-color: #F4CAC8 !important;
    color: #000000be !important;
}
.highlighted-cell {
    background-color: #F4CAC8;
    color: #000000be;
}
.completed-row > td:not(:first-child) {
    color: #fff;
    text-shadow: 1px 1px 3px #858585;
}
.selected-row > td:not(:first-child) {
    background-color: #0f6cbd;
}
.selected-cell {
    background-color: #b9bdcc;
}
.selected-cell-connector {
    background-color: #b9bdcc;
}
.hidden-row {
    display: none;
}
.blank-row {
    background-color: #e5e5e5;
}
.blank-round-row {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    background-color: #e5e5e5;
}
/* ----------------------------------------- */
/* ----------------------------------------- */
.sf-assigned-initials {
    font-family: "montserrat", sans-serif;
    font-weight: 600;
    font-size: .9em;
    color: #fff;
    background-color: #0f6cbd;
    padding: 2px 10px 0px 5px;
    border-top-left-radius: 9px;
    border-bottom-left-radius: 9px;
    position: relative;
    left: 10px;
    width: 17px;
}
.sf-assigned-headshot {
    width: 26px;
    height: 26px;
    border-radius: 50%;
    margin-right: 4px;
    border: 0px solid #fff;
    position: relative;
    left: 4px;
    z-index: 2;
}
.sf-assigned-container {
    display: flex;
    align-items: center;
}
.sf-subtask-count {
    font-family: "montserrat", sans-serif;
    font-size: .9em;
    color: #fff;
    background-color: #676e86;
    padding: 2px 5px 0px 12px;
    border-top-right-radius: 9px;
    border-bottom-right-radius: 9px;
    position: relative;
    left: -10px;
    z-index: 1;
    letter-spacing:-1px;
    width:25px;
}
.sf-com {
    font-weight: 100;
}
.sf-sla {
    font-weight: 100;
    color: #b9bdcc;
}
.sf-tot {

}
/* ----------------------------------------- */
/* ----------------------------------------- */
.subtask-middle-container {
    /*flex: 1;
    margin-left: 1rem;
    margin-right: 1rem;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    padding-left: 1rem;
    padding-right: 1rem;*/
    background-color: #b9bdcc;
    margin: 28px 30px 0px 0px;
    border-radius: 20px;
    box-shadow: 0px 0px 20px #c2c2c2;
    z-index: 10;
    position: relative;
    left: -15px;
    width: 500px;
}
/* ----------------------------------------- */
/* ----------------------------------------- */
.taskassigned {
    width: 26px;
    height: 26px;
    border-radius: 50%;
    margin-right: 4px;
    border: 0px solid #fff;
    position: relative;
    left: 4px;
}

:root .ff-input {
    margin-left: 10px;
    margin-right: 10px;
    padding: 5px 0px;
    height: 10px !important;
    font-size: 12px !important;
    border: 0px;
    border-radius: 0px;
    background-color: transparent;
    border-bottom: 1px dashed #676767;
    width: 100%;
}

    :root .ff-input:focus {
        border: 0px;
        outline: none;
        border-bottom: 1px solid #676767;
    }

    :root .ff-input:active {
        border: 0px;
        outline: none;
        border-bottom: 1px solid #676767;
    }

.sf-tab-1 {
    padding-left: 20px;
}

.sf-tab-2 {
    padding-left: 20px;
}

.da {
    font-size: 10px;
    color: #6b6b6b !important;
}

.ds {
    font-size: 11px !important;
}

.db {
    font-size: 13px;
    font-weight: bold;
}

.eg {
    font-size: .7em;
    font-style: italic;
    color: #838383;
}

.sf-rendate {
    width: 100px;
    text-align: center;
}

.sf-rendate {
    vertical-align: central;
    align-content: center;
    margin-left: 50px;
    border-bottom-left-radius: 20px;
    position: relative;
    top: -2px;
    box-shadow: 0 10px 10px rgba(0, 0, 0, 0.2), 0 10px 15px rgba(0, 0, 0, 0.1), 0 25px 20px rgba(0, 0, 0, 0.05), 0 30px 25px rgba(0, 0, 0, 0.02);
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#f9f9f9+12,000000+100&1+0,0.05+100 */
    background: linear-gradient(to bottom, rgba(249,249,249,1) 0%,rgba(249,249,249,0.89) 12%,rgba(0,0,0,0.05) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.sf-rendate-2 {
    width: auto;
    padding-top: 4px;
    border-bottom-right-radius: 20px;
    padding-left: 15px;
    position: relative;
    line-height: 15px;
    top: -2px;
    box-shadow: 0 10px 10px rgba(0, 0, 0, 0.1), 0 10px 15px rgba(0, 0, 0, 0.06), 0 25px 20px rgba(0, 0, 0, 0.03), 0 30px 25px rgba(0, 0, 0, 0.01);
    background: linear-gradient(to bottom, rgba(249,249,249,1) 0%,rgba(249,249,249,0.89) 12%,rgba(0,0,0,0.05) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.leftpad {
    padding-left: 25px;
}

.innersep {
    float: left;
    border-right: 1px solid #0000001b;
    padding-right: 20px;
}

.sf-rent {
    font-size: .6em;
    font-weight: 600;
    letter-spacing: 2px;
    color: #797979;
    position: relative;
    top: -2px;
}

.sf-runt {
    font-size: .6em;
    font-weight: 600;
    letter-spacing: 2px;
    color: #b7b7b7;
}

.sf-linecode {
    font-family: "montserrat", sans-serif;
    font-weight: 800;
    font-size: 2em;
    color: #2b2b2b;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
}

.sf-renewaldate {
    font-family: "montserrat", sans-serif;
    font-weight: 600;
    font-size: 2.3em;
    color: #797979;
    position: relative;
    top: -3px;
}

.sf-clientname a {
    text-decoration: none;
    color: #797979;
}

    .sf-clientname a:hover {
        color: #000;
        text-decoration: none;
        cursor: pointer;
    }

.sf-rekt {
    font-family: "montserrat", sans-serif;
    font-weight: 600;
    font-size: 1em;
    color: #797979;
}

    .sf-rekt a {
        -webkit-text-decoration: none;
        text-decoration: none;
        color: inherit;
    }

        .sf-rekt a:hover {
            -webkit-text-decoration: none;
            text-decoration: none;
            color: #000;
            cursor: pointer;
        }

.sf-renewalheader {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    width: 100%;
    padding-left: 5px;
    z-index: 300;
    position: relative;
    top: -10px;
}

.sf-clientname {
    font-family: "montserrat", sans-serif;
    font-weight: 100;
    font-style: normal;
    font-size: 2em;
    position: relative;
    color: #2c2c2c;
    top: 4px;
}

    .sf-clientname h1 {
        font-family: "montserrat", sans-serif;
        font-weight: 800;
        font-style: normal;
        font-size: 2.5em;
        margin-bottom: 20px;
    }

.sf-pilltitle {
    margin-top: 10px;
    font-family: "montserrat", sans-serif;
    font-weight: 400;
    background-color: #757575;
    padding: 2px 5px;
    font-size: .8em;
    color: #d9d9d9;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}

.sf-pillvalue {
    margin-top: 10px;
    font-family: "montserrat", sans-serif;
    font-weight: 400;
    background-color: #e0e0e0;
    padding: 2px 8px;
    font-size: .8em;
    color: #2b2b2b;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
}

.sf-pillcontainers {
    margin-top: 15px;
    margin-bottom: 15px;
}

.sf-tab-container {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0px 0px 5px #ccc;
}
:root th {
    font-weight: 300 !important;
}
.sf-threedot {
    opacity: .3;
}
.sf-tab {
    background-color: #fff;
    border-radius: 5px;
}
.sf-threedot:hover {
    opacity: 1;
}
.sf-threedot svg {
    position: relative;
    top: 1px;
    left: 2px;
}

.db {
    font-size: 13px;
    font-weight: bold;
}
.newsubmission {
    border-top: 1px solid #ccc;
    padding: 5px;
}
.tweaker {
    margin: 0px;
    padding: 0px;
    font-weight: bold;
    position: relative;
    top: 30px;
    padding-right: 20px;
}
.submissioncontainer {
    background-color: #fafafa;
    padding: 10px;
    border-radius: 8px;
    border: 1px solid #ccc;
    border-left: 10px solid #0f6cbd;
}
.submissioncontainer-declined {
    background-color: #ffc6c6;
    opacity: .8;
    padding: 10px;
    border-radius: 8px;
    border: 1px solid #750000;
    border-left: 10px solid #750000;
}
.f-title {
    font-size: .6em;
    font-weight: 600;
    letter-spacing: 2px;
    color: #797979;
    position: relative;
    top: 4px;
}
.submissioncontainer {
    font-family: "montserrat", sans-serif;
}

.f-txt {
    font-size: .9em;
}
.contentflex {
    line-height: 16px;
    align-self: flex-start;
    gap: 40px;
}

.mainpremium {
    align-self: flex-start;
    text-align: center;
    background-color: #6d6d6d;
    color: #fff;
    padding: 15px;
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
    line-height: normal;
    position: relative;
    top: -10px;
}

.f-mpremium {
    font-size: 3em;
    font-weight: 800;
}

.f-tpremium {
    font-size: 1.2em;
    font-weight: 800;
    color: #c8c8c8;
    letter-spacing: 3px;
}

.maincontent {
    min-width: 500px;
}

.carrier {
    align-self: flex-start;
}

.tweaker2 {
    position: relative !important;
    top: 21px !important;
}
:root .e-plus-icon::before {
    content: '\e805';
}
.sf-col-1 {
    min-width:500px;
    min-height:75px;
}
.mf-flextwocolumn {
    border-bottom:1px solid #eeeeee;
}
.prembox {
    font-size:3em;
}
.plzselect {
    font-family: montserrat, sans-serif;
    font-size: 3em;
    padding-top: 50px;
    color: #a9a9a9;
}
/* Task Activity Layout */
.task-activity-container {
    display: flex;
    width: 100%;
}
.notepady-container {
    position: relative;
    top: 31px;
    left:-20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 16px;
    max-width: 280px;
    min-width: 250px;
    opacity:.5;
    transition: all 1s;
}
    .notepady-container:hover {
        opacity:1;
    }
    .notepady-title {
        font-family: "Montserrat", sans-serif;
        font-size: 14px;
        font-weight: 600;
        color: #495057;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 2px solid #007bff;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

.task-list-container {
    min-width: 0; /* Needed for flex items with overflow */
    overflow-x: auto;
    z-index: 9;
}

.notepady {
    min-width: 0;
    max-width: 280px;
    z-index: 9;
}

/* Rich Text Editor Styling */
.notepady-container .e-richtexteditor {
    border: none;
    box-shadow: none;
    background: transparent;
}

.notepady-container .e-richtexteditor .e-rte-content {
    font-size: 10px !important;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.4;
    background: #ffffff;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px;
    min-height: 400px;
}

.notepady-container .e-richtexteditor .e-content {
    font-size: 10px !important;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.notepady-container .e-richtexteditor .e-toolbar {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    padding: 4px 8px;
}

.notepady-container .e-richtexteditor .e-toolbar .e-btn {
    margin: 1px;
    padding: 4px;
    font-size: 12px;
}

.notepady-container .e-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 500;
    margin-top: 12px;
    transition: all 0.2s ease;
}

.notepady-container .e-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

/* Debug info styling */
.notepady-container .debug-info {
    font-size: 9px;
    color: #adb5bd;
    margin-top: 8px;
    padding: 4px 8px;
    background: rgba(248, 249, 250, 0.5);
    border-radius: 3px;
    font-family: "Courier New", monospace;
    line-height: 1.2;
}
/* Activity log styles moved to ActivityLog.razor.css */

.fluent-popover-content {
    width:95%;
}
.btnNoteTip {
    opacity:.5;
}
.btnNoteTip:hover {
    opacity:1;
    cursor:pointer;
} 