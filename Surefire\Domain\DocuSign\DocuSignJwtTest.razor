@page "/docusign-jwt-test"
@namespace Surefire.Domain.DocuSign.Components
@using System.Text
@using Microsoft.Extensions.Configuration
@using Microsoft.AspNetCore.Components.Web
@using System.Text.Json
@using System.Security.Cryptography
@inject IDocuSignConfigService ConfigService
@inject IDocuSignService DocuSignService
@inject IConfiguration Configuration
@inject IWebHostEnvironment Env
@inject ILogger<DocuSignJwtTest> Logger

<h3>DocuSign JWT Authentication Test</h3>

<div class="mt-4">
    <h4>Configuration Details</h4>
    <div class="card p-3 mb-3">
        <div class="row">
            <div class="col-md-6">
                <p><strong>Environment:</strong> @(ConfigService.IsProduction() ? "Production" : "Sandbox")</p>
                <p><strong>Integrator Key:</strong> @(MaskString(config?.IntegratorKey))</p>
                <p><strong>User ID:</strong> @(MaskString(config?.UserId))</p>
                <p><strong>Account ID:</strong> @(MaskString(config?.AccountId))</p>
            </div>
            <div class="col-md-6">
                <p><strong>Auth Server:</strong> @(config?.AuthServer)</p>
                <p><strong>Base URL:</strong> @(config?.BaseUrl)</p>
                <p><strong>Private Key Path:</strong> @(config?.PrivateKeyPath)</p>
                <p><strong>Private Key Exists:</strong> @(privateKeyExists ? "Yes" : "No")</p>
                @if (privateKeyExists && !string.IsNullOrEmpty(privateKeyFormat))
                {
                    <p><strong>Private Key Format:</strong> @privateKeyFormat</p>
                }
            </div>
        </div>
    </div>

    <h4>Environment Variables</h4>
    <div class="card p-3 mb-3">
        <p><strong>DOCUSIGN_INTEGRATOR_KEY:</strong> @(MaskString(Environment.GetEnvironmentVariable("DOCUSIGN_INTEGRATOR_KEY")))</p>
        <p><strong>DOCUSIGN_USER_ID:</strong> @(MaskString(Environment.GetEnvironmentVariable("DOCUSIGN_USER_ID")))</p>
        <p><strong>DOCUSIGN_ACCOUNT_ID:</strong> @(MaskString(Environment.GetEnvironmentVariable("DOCUSIGN_ACCOUNT_ID")))</p>
        <p><strong>DOCUSIGN_AUTH_SERVER:</strong> @(Environment.GetEnvironmentVariable("DOCUSIGN_AUTH_SERVER"))</p>
        <p><strong>DOCUSIGN_BASE_URL:</strong> @(Environment.GetEnvironmentVariable("DOCUSIGN_BASE_URL"))</p>
        <p><strong>DOCUSIGN_PRIVATE_KEY_PATH:</strong> @(Environment.GetEnvironmentVariable("DOCUSIGN_PRIVATE_KEY_PATH"))</p>
    </div>

    <h4>JWT Authentication Test</h4>
    <div class="card p-3 mb-3">
        <div class="mb-3">
            <button class="btn btn-primary" @onclick="TestJwtAuthentication" disabled="@loading">
                @if (loading)
                {
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span class="ms-2">Testing...</span>
                }
                else
                {
                    <span>Test JWT Authentication</span>
                }
            </button>
        </div>

        @if (!string.IsNullOrEmpty(errorMessage))
        {
            <div class="alert alert-danger mt-3">
                <h5>Error:</h5>
                <pre>@errorMessage</pre>

                @if (errorMessage.Contains("consent_required"))
                {
                    <div class="mt-3">
                        <p>This error indicates that consent is required for DocuSign to authorize your application.</p>
                        <a href="@consentUrl" class="btn btn-success" target="_blank">Grant Consent</a>
                    </div>
                }
                @if (errorMessage.Contains("no_valid_keys_or_signatures"))
                {
                    <div class="mt-3">
                        <p>This error indicates that the private key does not match the public key registered in DocuSign.</p>
                        <ul>
                            <li>Ensure you're using the correct authentication server (account.docusign.com for production or account-d.docusign.com for sandbox).</li>
                            <li>Verify that the private key matches the public key uploaded to DocuSign.</li>
                            <li>Try to generate a new key pair and upload the public key to DocuSign.</li>
                        </ul>
                        <a href="/api/docusign-key/generate" class="btn btn-warning" target="_blank">Generate New Key Pair</a>
                    </div>
                }
            </div>
        }

        @if (!string.IsNullOrEmpty(accessToken))
        {
            <div class="alert alert-success mt-3">
                <h5>Success!</h5>
                <p>Successfully obtained access token from DocuSign.</p>
                <div>
                    <strong>Access Token:</strong>
                    <pre style="max-height: 100px; overflow-y: auto;">@accessToken</pre>
                </div>
                @if (tokenInfo != null)
                {
                    <div class="mt-3">
                        <strong>Token Information:</strong>
                        <pre style="max-height: 200px; overflow-y: auto;">@JsonSerializer.Serialize(tokenInfo, new JsonSerializerOptions { WriteIndented = true })</pre>
                    </div>
                }
            </div>
        }
    </div>

    <h4>Recent Envelopes</h4>
    <div class="card p-3">
        <div class="mb-3">
            <button class="btn btn-secondary" @onclick="GetRecentEnvelopes" disabled="@(loading || string.IsNullOrEmpty(accessToken))">
                @if (loadingEnvelopes)
                {
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span class="ms-2">Loading...</span>
                }
                else
                {
                    <span>Get Recent Envelopes</span>
                }
            </button>
        </div>

        @if (!string.IsNullOrEmpty(envelopesError))
        {
            <div class="alert alert-danger mt-3">
                <h5>Error:</h5>
                <pre>@envelopesError</pre>
            </div>
        }

        @if (!string.IsNullOrEmpty(envelopesJson))
        {
            <div class="mt-3">
                <h5>Envelopes:</h5>
                <pre style="max-height: 400px; overflow-y: auto;">@envelopesJson</pre>
            </div>
        }
    </div>
</div>

@code {
    private DocuSignConfig? config;
    private bool loading = false;
    private bool loadingEnvelopes = false;
    private string? errorMessage;
    private string? accessToken;
    private string? envelopesJson;
    private string? envelopesError;
    private string consentUrl = "";
    private bool privateKeyExists = false;
    private string? privateKeyFormat;
    private Dictionary<string, object>? tokenInfo;

    protected override async Task OnInitializedAsync()
    {
        config = ConfigService.GetDocuSignConfig();
        Logger.LogInformation("DocuSignJwtTest initialized with config: IntegratorKey={IntegratorKey}, UserId={UserId}, AccountId={AccountId}", 
            MaskString(config.IntegratorKey), MaskString(config.UserId), MaskString(config.AccountId));

        // Check if private key exists
        if (!string.IsNullOrEmpty(config.PrivateKeyPath))
        {
            privateKeyExists = File.Exists(config.PrivateKeyPath);
            Logger.LogInformation("Private key exists: {Exists} at path: {Path}", privateKeyExists, config.PrivateKeyPath);
            
            if (privateKeyExists)
            {
                // Check key format
                try
                {
                    var keyContent = await File.ReadAllTextAsync(config.PrivateKeyPath);
                    privateKeyFormat = DetermineKeyFormat(keyContent);
                    Logger.LogInformation("Private key format: {Format}", privateKeyFormat);
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error reading private key");
                    privateKeyFormat = "Error: " + ex.Message;
                }
            }
        }

        // Generate consent URL
        string authServer = ConfigService.IsProduction() ? "account.docusign.com" : "account.docusign.com";
        consentUrl = $"https://{authServer}/oauth/auth?response_type=code&scope=signature%20impersonation&client_id={config.IntegratorKey}&redirect_uri={Uri.EscapeDataString("https://localhost:7074/docusign-callback")}";
        Logger.LogInformation("Generated consent URL: {ConsentUrl}", consentUrl);
    }

    private async Task TestJwtAuthentication()
    {
        loading = true;
        errorMessage = null;
        accessToken = null;
        tokenInfo = null;

        try
        {
            Logger.LogInformation("Testing JWT authentication");
            accessToken = await DocuSignService.GetAccessTokenAsync();
            Logger.LogInformation("Successfully obtained access token");
            
            // Try to parse token claims if possible
            try
            {
                tokenInfo = ParseAccessToken(accessToken);
                Logger.LogDebug("Parsed token information: {@TokenInfo}", tokenInfo);
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "Could not parse access token");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "JWT authentication failed");
            errorMessage = ex.Message;
        }
        finally
        {
            loading = false;
            StateHasChanged();
        }
    }

    private async Task GetRecentEnvelopes()
    {
        if (string.IsNullOrEmpty(accessToken))
        {
            return;
        }

        loadingEnvelopes = true;
        envelopesJson = null;
        envelopesError = null;

        try
        {
            Logger.LogInformation("Getting recent envelopes");
            envelopesJson = await DocuSignService.GetRecentEnvelopesAsync(accessToken);
            Logger.LogInformation("Successfully retrieved envelopes");
            
            // Format JSON for display
            var jsonDoc = JsonDocument.Parse(envelopesJson);
            envelopesJson = JsonSerializer.Serialize(jsonDoc, new JsonSerializerOptions { WriteIndented = true });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get recent envelopes");
            envelopesError = ex.Message;
        }
        finally
        {
            loadingEnvelopes = false;
            StateHasChanged();
        }
    }
    
    private string DetermineKeyFormat(string keyContent)
    {
        if (string.IsNullOrEmpty(keyContent))
            return "Empty";
            
        if (keyContent.Contains("BEGIN RSA PRIVATE KEY") && keyContent.Contains("END RSA PRIVATE KEY"))
            return "RSA Private Key (PKCS#1)";
            
        if (keyContent.Contains("BEGIN PRIVATE KEY") && keyContent.Contains("END PRIVATE KEY"))
            return "PKCS#8 Private Key";
            
        return "Unknown format";
    }
    
    private Dictionary<string, object> ParseAccessToken(string token)
    {
        // This is a simple parser that doesn't validate the token
        // It just extracts the payload to show the claims
        var parts = token.Split('.');
        if (parts.Length != 3)
            return new Dictionary<string, object> { { "error", "Invalid token format" } };
            
        var payloadJson = System.Text.Encoding.UTF8.GetString(
            Convert.FromBase64String(parts[1].PadRight(parts[1].Length + (4 - parts[1].Length % 4) % 4, '='))
        );
        
        return JsonSerializer.Deserialize<Dictionary<string, object>>(payloadJson);
    }
    
    private string MaskString(string input)
    {
        if (string.IsNullOrEmpty(input))
        {
            return "(not set)";
        }

        if (input.Length <= 8)
        {
            return "***" + input.Substring(Math.Max(0, input.Length - 4));
        }
        
        return input.Substring(0, 4) + "..." + input.Substring(input.Length - 4);
    }
}
}