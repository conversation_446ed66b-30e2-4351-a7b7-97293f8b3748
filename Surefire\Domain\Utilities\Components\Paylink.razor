﻿@namespace Surefire.Domain.Utilities
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.DropDowns
@using Surefire.Domain.Clients.Models
@using Surefire.Domain.Renewals.Models
@using Surefire.Domain.Policies.Models
@using Surefire.Domain.Contacts.Models
@using Microsoft.FluentUI.AspNetCore.Components
@using System.Globalization
@using Surefire.Domain.Clients.Services
@using Surefire.Domain.Policies.Services
@using Surefire.Domain.Renewals.Services
@inject IJSRuntime JS
@inject ClientService ClientService
@inject PolicyService PolicyService
@inject RenewalService RenewalService
@inject Surefire.Domain.Ember.EmberService EmberService
@inject IToastService ToastService

<div class="paylink-container">
    <div class="paylink-content">
        <div class="paylink-form">
            <div class="form-row">
                <div class="form-group">
                    <label>Payer Name</label>
                    <SfTextBox Id="payer" @oninput="(e) => OnInputChanged(e, 1)" Value="@payer" />
                </div>
                <div class="form-group">
                    <label>Email Address</label>
                    <SfDropDownList TItem="EmailAddress" TValue="string"
                                  DataSource="@emailAddresses"
                                  @bind-Value="@selectedEmail"
                                  AllowFiltering="true"
                                  FilterType="FilterType.Contains">
                        <DropDownListFieldSettings Text="Email" Value="Email" />
                    </SfDropDownList>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label>Comments/Notes</label>
                    <SfTextBox Id="note" @oninput="(e) => OnInputChanged(e, 3)" Value="@note" />
                </div>
                <div class="form-group">
                    <label>Amount</label>
                    <SfNumericTextBox Id="amount" @oninput="(e) => OnInputChanged(e, 4)" Value="@amount" TValue="decimal" Format="c2" />
                </div>
            </div>
        </div>

        <div class="paylink-results">
            <div class="results-section">
                <div class="results-header">
                    <span>Generated Paylink</span>
                    <div class="action-buttons">
                        <FluentButton Appearance="Appearance.Accent" OnClick="CopyToClipboard">
                            <FluentIcon Value="@(new Icons.Filled.Size24.Copy())" Color="Color.Custom" CustomColor="#fff" />
                            <span>Copy</span>
                        </FluentButton>
                        <FluentButton Appearance="Appearance.Accent" OnClick="OpenLinkInNewTab">
                            <FluentIcon Value="@(new Icons.Filled.Size24.Open())" Color="Color.Custom" CustomColor="#fff" />
                            <span>Open</span>
                        </FluentButton>
                    </div>
                </div>
                <div class="results-content">@generatedUrl</div>
            </div>

            <div class="email-preview-section">
                <div class="email-preview-header">
                    <span>Email Preview</span>
                    <div class="action-buttons">
                        <FluentButton Appearance="Appearance.Accent" OnClick="CopyRichTextToClipboard">
                            <FluentIcon Value="@(new Icons.Filled.Size24.Copy())" Color="Color.Custom" CustomColor="#fff" />
                            <span>Copy For Email</span>
                        </FluentButton>
                        <FluentButton Appearance="Appearance.Accent" OnClick="OutlookNewEmail">
                            <FluentIcon Value="@(new Icons.Filled.Size24.Mail())" Color="Color.Custom" CustomColor="#fff" />
                            <span>New Email</span>
                        </FluentButton>
                    </div>
                </div>
                <div class="email-preview-content">
                    <p>Dear @payer,</p>
                    <p>Please click the link below to make your payment:</p>
                    @((MarkupString)OutlookButtonHtml)
                    <p>
                        Payment Details:<br>
                        Amount: @amount.ToString("C2")<br>
                        @note
                    </p>
                    <p>If you have any questions, please don't hesitate to contact us.</p>
                    <p>
                        Best regards,<br>
                        Metro Insurance
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public int? ClientId { get; set; }

    [Parameter]
    public int? PolicyId { get; set; }

    [Parameter]
    public int? RenewalId { get; set; }

    private string payer;
    private string selectedEmail;
    private string note;
    private decimal amount;
    private string amountString;
    private string generatedUrl = "https://metroinsurance.epaypolicy.com/";
    private List<Contact> clientContacts;
    private List<EmailAddress> emailAddresses = new();
    private string OutlookButtonHtml => $@"<table role=""presentation"" border=""0"" cellpadding=""0"" cellspacing=""0""><tr><td align=""center"" bgcolor=""#0078d4"" style=""border-radius:4px;""><a href=""{generatedUrl}"" target=""_blank"" style=""display:inline-block;padding:10px 20px;font-family:Arial,sans-serif;font-size:16px;color:#ffffff;text-decoration:none;border-radius:4px;font-weight:500;border:1px solid #006cbe;"">Make Payment</a></td></tr></table>";

    private string OutlookEmailHtml => $@"<div style=""font-family: Arial, sans-serif; line-height: 1.5;"">
<p>Dear {payer},</p>
<p>Please click the link below to make your payment:</p>
{OutlookButtonHtml}
<p>Payment Details:<br>
Amount: {amount:C2}<br>
{note}</p>
<p>If you have any questions, please don't hesitate to contact us.</p>
<p>Best regards,<br>
Metro Insurance</p>
</div>";

    protected override async Task OnInitializedAsync()
    {
        if (ClientId.HasValue)
        {
            var client = await ClientService.GetClientById(ClientId.Value);
            if (client != null)
            {
                payer = client.Name;
                clientContacts = await ClientService.GetContactsByClientIdAsync(ClientId.Value);
                
                // Flatten all email addresses into a single list
                emailAddresses = clientContacts
                    .SelectMany(c => c.EmailAddresses)
                    .ToList();
                
                // Set default amount and email
                amount = 400;
                selectedEmail = emailAddresses.FirstOrDefault()?.Email;
                
                if (PolicyId.HasValue)
                {
                    var policy = await PolicyService.GetPolicyByIdAsync(PolicyId.Value);
                    if (policy != null && policy.EffectiveDate != null && policy.ExpirationDate != null)
                    {
                        var lineNickname = policy.Product?.LineNickname ?? "Unknown Product";
                        note = $"{policy.EffectiveDate:yy}-{policy.ExpirationDate:yy} {lineNickname} Policy Payment";
                    }
                }
                else if (RenewalId.HasValue)
                {
                    var renewal = await RenewalService.GetRenewalByIdAsync(RenewalId.Value);
                    if (renewal != null && renewal.RenewalDate != null)
                    {
                        var lineNickname = renewal.Product?.LineNickname ?? "Unknown Product";
                        note = $"{renewal.RenewalDate:yyyy} {lineNickname} Policy Payment";
                    }
                }

                BuildUrl();
            }
        }
    }

    private void OnInputChanged(Microsoft.AspNetCore.Components.ChangeEventArgs e, int field)
    {
        if (field == 1)
        {
            payer = e.Value?.ToString();
        }
        else if (field == 3)
        {
            note = e.Value?.ToString();
        }
        else if (field == 4)
        {
            if (decimal.TryParse(e.Value?.ToString(), out decimal parsedAmount))
            {
                amount = parsedAmount;
            }
            else
            {
                amount = 0;
            }
        }

        BuildUrl();
    }

    private void BuildUrl()
    {
        var url = "https://metroinsurance.epaypolicy.com/";
        var queryParams = new List<string>();

        if (!string.IsNullOrEmpty(payer))
        {
            queryParams.Add($"payer={Uri.EscapeDataString(payer)}");
        }

        if (!string.IsNullOrEmpty(selectedEmail))
        {
            queryParams.Add($"emailAddress={Uri.EscapeDataString(selectedEmail)}");
        }

        if (amount > 0)
        {
            queryParams.Add($"amount={amount.ToString("F2", CultureInfo.InvariantCulture)}");
        }

        if (!string.IsNullOrEmpty(note))
        {
            queryParams.Add($"comments={Uri.EscapeDataString(note)}");
        }

        if (queryParams.Count > 0)
        {
            generatedUrl = url + "?" + string.Join("&", queryParams);           
        }
        StateHasChanged();
    }

    private async Task CopyToClipboard()
    {
        await JS.InvokeVoidAsync("navigator.clipboard.writeText", generatedUrl);
    }

    private async Task CopyRichTextToClipboard()
    {
        await JS.InvokeVoidAsync("copyHtmlToClipboard", OutlookEmailHtml);
    }

    private void OpenLinkInNewTab()
    {
        JS.InvokeVoidAsync("window.open", generatedUrl, "_blank");
    }

    private async Task OutlookNewEmail()
    {
        var toEmail = selectedEmail;
        var subject = "Payment Link from Metro Insurance";
        var body = OutlookEmailHtml;
        if (!string.IsNullOrWhiteSpace(toEmail) && !string.IsNullOrWhiteSpace(subject) && !string.IsNullOrWhiteSpace(body))
        {
            var myParams = new List<string> { toEmail, subject, body };
            await EmberService.RunEmberFunction("OutlookEmail_CreateNew", myParams);
            ToastService.ShowSuccess("Email created successfully!");
        }
        else
        {
            ToastService.ShowError("Missing email, subject, or body.");
        }
    }
}
