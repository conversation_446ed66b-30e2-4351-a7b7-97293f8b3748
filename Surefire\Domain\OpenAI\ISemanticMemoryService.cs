//using System.Collections.Generic;
//using System.Threading.Tasks;

//namespace Surefire.Domain.OpenAI
//{
//    public interface ISemanticMemoryService
//    {
//        Task SaveInformationAsync(string collection, string id, string text, string? description = null, Dictionary<string, object>? metadata = null);
//        Task<IEnumerable<MemoryQueryResult>> SearchAsync(string collection, string query, int limit = 3);
//        Task DeleteAsync(string collection, string id);
//        Task ClearCollectionAsync(string collection);
//        Task<string> QueryDatabaseAsync(string naturalLanguageQuery);
//    }

//    public class MemoryQueryResult
//    {
//        public string Id { get; set; } = string.Empty;
//        public string Text { get; set; } = string.Empty;
//        public string? Description { get; set; }
//        public Dictionary<string, object>? Metadata { get; set; }
//        public double Relevance { get; set; }
//    }
//} 