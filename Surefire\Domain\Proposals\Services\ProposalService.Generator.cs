﻿//using System;
//using System.IO;
//using DocumentFormat.OpenXml.Packaging;
//using DocumentFormat.OpenXml.Wordprocessing;
//using Newtonsoft.Json.Linq;

//namespace Surefire.Domain.Forms.Services
//{
//    public partial class ProposalService
//    {
//        /// <summary>
//        /// Generates a proposal DOCX document by merging cleaned JSON data with a DOCX template.
//        /// Assumes content controls in the template are tagged with aliases starting with "var".
//        /// </summary>
//        public byte[] GenerateProposalDocument(string cleanedJson, string templatePath)
//        {
//            // Copy the template to a temporary output path
//            string tempOutputPath = Path.Combine(Path.GetTempPath(), $"Proposal_{Guid.NewGuid()}.docx");
//            File.Copy(templatePath, tempOutputPath, true);
//            JObject data = JObject.Parse(cleanedJson);

//            using (var doc = WordprocessingDocument.Open(tempOutputPath, true))
//            {
//                var document = doc.MainDocumentPart.Document;
//                var contentControls = document.Descendants<DocumentFormat.OpenXml.SdtElement>();
//                foreach (var ctrl in contentControls)
//                {
//                    var alias = ctrl.SdtProperties?.GetFirstChild<DocumentFormat.OpenXml.SdtAlias>()?.Val.Value;
//                    if (!string.IsNullOrEmpty(alias) && alias.StartsWith("var"))
//                    {
//                        string key = alias.Substring(3); // Remove "var" prefix
//                        var token = data.SelectToken(key);
//                        if (token != null)
//                        {
//                            var texts = ctrl.Descendants<Text>();
//                            foreach (var t in texts)
//                            {
//                                t.Text = token.ToString();
//                            }
//                        }
//                    }
//                }
//                document.Save();
//            }
//            return File.ReadAllBytes(tempOutputPath);
//        }
//    }
//}
