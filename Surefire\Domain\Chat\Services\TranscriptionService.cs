using System;
using System.IO;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Surefire.Data;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Microsoft.Extensions.Logging;
using System.Threading;

namespace Surefire.Domain.Chat.Services
{
    public class TranscriptionService
    {
        private readonly string _openAiApiKey;
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly ILogger<TranscriptionService> _logger;
        private readonly SemaphoreSlim _connectionSemaphore = new(1, 1);

        public TranscriptionService(IConfiguration configuration, IDbContextFactory<ApplicationDbContext> dbContextFactory, ILogger<TranscriptionService> logger)
        {
            _dbContextFactory = dbContextFactory;
            _logger = logger;

            // Get OpenAI API key from configuration
            _openAiApiKey = configuration["OpenAI:ApiKey"] ?? configuration["OpenAi:ApiKey"] ?? Environment.GetEnvironmentVariable("OPENAI_API_KEY") ?? Environment.GetEnvironmentVariable("OPENAI");

            if (string.IsNullOrEmpty(_openAiApiKey))
            {
                throw new InvalidOperationException("OpenAI API key not found in configuration or environment variables");
            }
        }

        /// <summary>
        /// Transcribes an audio file using OpenAI's gpt-4o-mini-transcribe model via WebSocket
        /// </summary>
        /// <param name="audioFilePath">Path to the audio file to transcribe</param>
        /// <param name="language">Optional language code (e.g., "en" for English)</param>
        /// <returns>The transcribed text</returns>
        public async Task<string> TranscribeAudioFileAsync(string audioFilePath, string language = "en")
        {
            if (!File.Exists(audioFilePath))
            {
                throw new FileNotFoundException("Audio file not found", audioFilePath);
            }

            // Read the audio file
            byte[] audioData = await File.ReadAllBytesAsync(audioFilePath);

            return await TranscribeAudioDataAsync(audioData, language);
        }

        /// <summary>
        /// Transcribes audio data using OpenAI's Whisper model via REST API
        /// </summary>
        /// <param name="audioData">The audio data to transcribe</param>
        /// <param name="language">Optional language code (e.g., "en" for English)</param>
        /// <param name="prompt">Optional prompt to guide transcription</param>
        /// <returns>The transcribed text</returns>
        public async Task<string> TranscribeAudioDataAsync(byte[] audioData, string language = "en", string prompt = null)
        {
            await _connectionSemaphore.WaitAsync();

            try
            {
                _logger.LogInformation("Starting transcription of {SizeKB} KB audio file using Whisper REST API", audioData.Length / 1024);

                // Use REST API directly for reliable transcription
                return await TranscribeAudioDataRestAsync(audioData, language, prompt);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error transcribing audio via REST API");
                return $"Transcription error: {ex.Message}";
            }
            finally
            {
                _connectionSemaphore.Release();
            }
        }

        /// <summary>
        /// Main method using REST API for transcription
        /// </summary>
        private async Task<string> TranscribeAudioDataRestAsync(byte[] audioData, string language = "en", string prompt = null)
        {
            try
            {
                using var httpClient = new HttpClient();

                // Set up the HTTP request
                var requestUrl = "https://api.openai.com/v1/audio/transcriptions";

                // Create multipart form content
                using var content = new MultipartFormDataContent();

                // Add the file content with proper format detection
                var fileContent = new ByteArrayContent(audioData);
                var (contentType, filename) = DetectAudioFormat(audioData);
                fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(contentType);
                content.Add(fileContent, "file", filename);

                // Use the standard whisper-1 model for transcription
                content.Add(new StringContent("whisper-1"), "model");

                if (!string.IsNullOrEmpty(language))
                {
                    content.Add(new StringContent(language), "language");
                }

                if (!string.IsNullOrEmpty(prompt))
                {
                    content.Add(new StringContent(prompt), "prompt");
                }

                // Add response format parameter for JSON output
                content.Add(new StringContent("json"), "response_format");

                // Set up headers
                httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _openAiApiKey);

                // Send the request
                var response = await httpClient.PostAsync(requestUrl, content);

                // Check for success
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Error from OpenAI REST API: {StatusCode}, Content: {ErrorContent}", response.StatusCode, errorContent);
                    throw new HttpRequestException($"Error from OpenAI API: {response.StatusCode}, Content: {errorContent}");
                }

                // Parse the response
                var responseContent = await response.Content.ReadAsStringAsync();

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var transcriptionResponse = JsonSerializer.Deserialize<TranscriptionResponse>(responseContent, options);

                if (transcriptionResponse == null || string.IsNullOrEmpty(transcriptionResponse.Text))
                {
                    _logger.LogWarning("No transcription text was returned from REST API");
                    return "No transcription available";
                }

                return transcriptionResponse.Text;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in REST API transcription");
                return $"Transcription error: {ex.Message}";
            }
        }



        /// <summary>
        /// Saves a transcription to the database
        /// </summary>
        /// <param name="transcription">The transcription to save</param>
        /// <returns>The ID of the saved transcription</returns>
        public async Task<int> SaveTranscriptionAsync(CallTranscription transcription)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();

            // Check if a transcription for this call already exists
            var existingTranscription = await context.CallTranscriptions
                .FirstOrDefaultAsync(t => t.CallId == transcription.CallId);

            if (existingTranscription != null)
            {
                // Update existing transcription
                existingTranscription.TranscriptionText = transcription.TranscriptionText;
                existingTranscription.IsReviewed = transcription.IsReviewed;
                existingTranscription.Notes = transcription.Notes;

                await context.SaveChangesAsync();
                return existingTranscription.Id;
            }
            else
            {
                // Add new transcription
                context.CallTranscriptions.Add(transcription);
                await context.SaveChangesAsync();
                return transcription.Id;
            }
        }

        /// <summary>
        /// Gets a transcription from the database by call ID
        /// </summary>
        /// <param name="callId">The call ID to look up</param>
        /// <returns>The transcription, or null if not found</returns>
        public async Task<CallTranscription> GetTranscriptionAsync(string callId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            return await context.CallTranscriptions
                .FirstOrDefaultAsync(t => t.CallId == callId);
        }

        /// <summary>
        /// Gets all transcriptions for a specific client
        /// </summary>
        /// <param name="clientId">The client ID to look up transcriptions for</param>
        /// <returns>A list of transcriptions for the client</returns>
        public async Task<List<CallTranscription>> GetTranscriptionsForClientAsync(int clientId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            return await context.CallTranscriptions
                .Where(t => t.ClientId == clientId)
                .OrderByDescending(t => t.CallStartTime)
                .ToListAsync();
        }

        /// <summary>
        /// Gets all transcriptions for a specific client by matching phone numbers
        /// </summary>
        /// <param name="clientId">The client ID</param>
        /// <param name="phoneNumbers">List of client phone numbers to match against</param>
        /// <returns>A list of transcriptions for the client</returns>
        public async Task<List<CallTranscription>> GetTranscriptionsForClientByPhoneNumbersAsync(int clientId, List<string> phoneNumbers)
        {
            if (phoneNumbers == null || !phoneNumbers.Any())
            {
                return await GetTranscriptionsForClientAsync(clientId);
            }

            // Normalize all phone numbers for consistent matching
            var normalizedPhoneNumbers = phoneNumbers.Select(NormalizePhoneNumber).Where(p => !string.IsNullOrEmpty(p)).ToList();

            using var context = await _dbContextFactory.CreateDbContextAsync();

            // Get transcriptions that match client ID or any of the client's phone numbers
            var transcriptions = await context.CallTranscriptions
                .Where(t =>
                    t.ClientId == clientId ||
                    normalizedPhoneNumbers.Contains(t.PhoneNumber) ||
                    normalizedPhoneNumbers.Contains(t.FromPhoneNumber) ||
                    normalizedPhoneNumbers.Contains(t.ToPhoneNumber))
                .OrderByDescending(t => t.CallStartTime)
                .ToListAsync();

            // Update ClientId for any transcriptions that match by phone number but don't have ClientId set
            foreach (var transcript in transcriptions.Where(t => t.ClientId != clientId))
            {
                transcript.ClientId = clientId;
            }

            await context.SaveChangesAsync();

            return transcriptions;
        }

        /// <summary>
        /// Gets the most recent transcriptions
        /// </summary>
        /// <param name="count">Number of recent transcriptions to retrieve</param>
        /// <returns>A list of the most recent transcriptions</returns>
        public async Task<List<CallTranscription>> GetRecentTranscriptionsAsync(int count = 5)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            return await context.CallTranscriptions
                .Where(t => !string.IsNullOrEmpty(t.TranscriptionText))
                .OrderByDescending(t => t.CallStartTime ?? DateTime.MinValue)
                .Take(count)
                .ToListAsync();
        }

        /// <summary>
        /// Detect audio format from byte array and return appropriate content type and filename
        /// </summary>
        private (string contentType, string filename) DetectAudioFormat(byte[] audioData)
        {
            if (audioData == null || audioData.Length < 12)
                return ("audio/mpeg", "audio.mp3"); // Default fallback

            var header = audioData.Take(12).ToArray();

            // Check for common audio file signatures
            try
            {
                // MP3 - Check for ID3 tag or MPEG frame sync
                if (header[0] == 0xFF && (header[1] & 0xE0) == 0xE0) // MPEG frame sync
                    return ("audio/mpeg", "audio.mp3");
                if (header[0] == 0x49 && header[1] == 0x44 && header[2] == 0x33) // ID3v2 tag
                    return ("audio/mpeg", "audio.mp3");

                // WAV - Check for RIFF header
                if (header[0] == 0x52 && header[1] == 0x49 && header[2] == 0x46 && header[3] == 0x46 && // "RIFF"
                    header[8] == 0x57 && header[9] == 0x41 && header[10] == 0x56 && header[11] == 0x45) // "WAVE"
                    return ("audio/wav", "audio.wav");

                // WebM - Check for EBML header
                if (header[0] == 0x1A && header[1] == 0x45 && header[2] == 0xDF && header[3] == 0xA3)
                    return ("audio/webm", "audio.webm");

                // M4A/MP4 - Check for ftyp box
                if (header[4] == 0x66 && header[5] == 0x74 && header[6] == 0x79 && header[7] == 0x70) // "ftyp"
                    return ("audio/mp4", "audio.m4a");

                // Default to MP3 if format is unclear (OpenAI generally handles this well)
                return ("audio/mpeg", "audio.mp3");
            }
            catch
            {
                return ("audio/mpeg", "audio.mp3"); // Safe fallback
            }
        }

        /// <summary>
        /// Normalize a phone number for consistent storage and comparison
        /// </summary>
        private string NormalizePhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber))
                return string.Empty;

            // Remove all non-digit characters
            var digitsOnly = new string(phoneNumber.Where(char.IsDigit).ToArray());

            // Ensure 10-digit US numbers have country code
            if (digitsOnly.Length == 10)
                return "1" + digitsOnly;

            // If it already has country code (11 digits starting with 1)
            if (digitsOnly.Length == 11 && digitsOnly.StartsWith("1"))
                return digitsOnly;

            // Otherwise return as is
            return digitsOnly;
        }

        /// <summary>
        /// Transcribes a call recording and saves the transcription to the database
        /// </summary>
        /// <param name="callId">The ID of the call</param>
        /// <param name="recordingId">The ID of the recording</param>
        /// <param name="phoneNumber">The phone number associated with the call</param>
        /// <param name="audioData">The audio data to transcribe</param>
        /// <param name="callStartTime">Optional start time of the call</param>
        /// <param name="callDuration">Optional duration of the call in seconds</param>
        /// <param name="clientId">Optional client ID to associate with the transcription</param>
        /// <param name="fromPhoneNumber">The "from" phone number in the call</param>
        /// <param name="toPhoneNumber">The "to" phone number in the call</param>
        /// <returns>The transcribed text</returns>
        public async Task<string> TranscribeAndSaveAsync(
            string callId,
            string recordingId,
            string phoneNumber,
            byte[] audioData,
            DateTime? callStartTime = null,
            int? callDuration = null,
            int? clientId = null,
            string fromPhoneNumber = null,
            string toPhoneNumber = null)
        {
            // First check if we already have this transcription
            var existingTranscription = await GetTranscriptionAsync(callId);
            if (existingTranscription != null && !string.IsNullOrEmpty(existingTranscription.TranscriptionText))
            {
                // Update client ID if provided and not already set
                if (clientId.HasValue && !existingTranscription.ClientId.HasValue)
                {
                    existingTranscription.ClientId = clientId;
                    await SaveTranscriptionAsync(existingTranscription);
                }

                // Update phone numbers if they weren't set before
                bool needToUpdate = false;

                if (!string.IsNullOrEmpty(fromPhoneNumber) && string.IsNullOrEmpty(existingTranscription.FromPhoneNumber))
                {
                    existingTranscription.FromPhoneNumber = fromPhoneNumber;
                    needToUpdate = true;
                }

                if (!string.IsNullOrEmpty(toPhoneNumber) && string.IsNullOrEmpty(existingTranscription.ToPhoneNumber))
                {
                    existingTranscription.ToPhoneNumber = toPhoneNumber;
                    needToUpdate = true;
                }

                if (needToUpdate)
                {
                    await SaveTranscriptionAsync(existingTranscription);
                }

                return existingTranscription.TranscriptionText;
            }

            // Check if audio data exists
            if (audioData == null || audioData.Length == 0)
            {
                throw new ArgumentException("Audio data is empty or null");
            }

            // Create specialized prompt for business/contact transcription
            var businessPrompt = @"You are transcribing a business call. Pay special attention to:
- Business names (e.g., 'TWS Facility Services', 'RT Specialty')
- Contact names and proper nouns
- Carrier names and insurance companies
- Acronyms and initial-lettered company names
- Common misheard business names like 'Quad-B Systems' (often heard as 'Quad Be') or 'SB Industrial' (sometimes misinterpreted as 'Let's Be Industrial')
Transcribe accurately, preserving all business terminology and proper names.";

            // Transcribe the audio with business-focused prompt
            var transcriptionText = await TranscribeAudioDataAsync(audioData, "en", businessPrompt);

            // Create a new transcription record
            var transcription = new CallTranscription
            {
                CallId = callId,
                RecordingId = recordingId,
                PhoneNumber = phoneNumber,
                FromPhoneNumber = fromPhoneNumber ?? string.Empty,
                ToPhoneNumber = toPhoneNumber ?? string.Empty,
                TranscriptionText = transcriptionText,
                CallStartTime = callStartTime,
                CallDuration = callDuration,
                CreatedDate = DateTime.UtcNow,
                ClientId = clientId,
                Notes = string.Empty
            };

            // Save to database
            await SaveTranscriptionAsync(transcription);

            return transcriptionText;
        }
    }
}