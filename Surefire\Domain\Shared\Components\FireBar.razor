@namespace Surefire.Domain.Shared.Components
@using Microsoft.AspNetCore.Components
@implements IDisposable

<div class="firebar-container" style="@ContainerStyle">
    <svg class="firebar-svg" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <!-- Flowing plasma gradient animation -->
            <radialGradient id="plasmaGradient1-@ComponentId" cx="50%" cy="50%" r="60%">
                <stop offset="0%" style="stop-color:#ff006e;stop-opacity:0.9">
                    <animate attributeName="stop-color" 
                             values="#ff006e;#8338ec;#3a86ff;#06ffa5;#ffbe0b;#fb5607;#ff006e" 
                             dur="4s" 
                             repeatCount="indefinite"/>
                </stop>
                <stop offset="50%" style="stop-color:#8338ec;stop-opacity:0.7">
                    <animate attributeName="stop-color" 
                             values="#8338ec;#3a86ff;#06ffa5;#ffbe0b;#fb5607;#ff006e;#8338ec" 
                             dur="3.5s" 
                             repeatCount="indefinite"/>
                </stop>
                <stop offset="100%" style="stop-color:#3a86ff;stop-opacity:0.5">
                    <animate attributeName="stop-color" 
                             values="#3a86ff;#06ffa5;#ffbe0b;#fb5607;#ff006e;#8338ec;#3a86ff" 
                             dur="3s" 
                             repeatCount="indefinite"/>
                </stop>
                <animateTransform attributeName="gradientTransform" 
                                  type="translate" 
                                  values="0,0; 20,10; -10,20; -20,-10; 10,-20; 0,0" 
                                  dur="6s" 
                                  repeatCount="indefinite"/>
            </radialGradient>

            <radialGradient id="plasmaGradient2-@ComponentId" cx="30%" cy="70%" r="50%">
                <stop offset="0%" style="stop-color:#06ffa5;stop-opacity:0.8">
                    <animate attributeName="stop-color" 
                             values="#06ffa5;#ffbe0b;#fb5607;#ff006e;#8338ec;#3a86ff;#06ffa5" 
                             dur="3.2s" 
                             repeatCount="indefinite"/>
                </stop>
                <stop offset="50%" style="stop-color:#ffbe0b;stop-opacity:0.6">
                    <animate attributeName="stop-color" 
                             values="#ffbe0b;#fb5607;#ff006e;#8338ec;#3a86ff;#06ffa5;#ffbe0b" 
                             dur="2.8s" 
                             repeatCount="indefinite"/>
                </stop>
                <stop offset="100%" style="stop-color:#fb5607;stop-opacity:0.4">
                    <animate attributeName="stop-color" 
                             values="#fb5607;#ff006e;#8338ec;#3a86ff;#06ffa5;#ffbe0b;#fb5607" 
                             dur="2.5s" 
                             repeatCount="indefinite"/>
                </stop>
                <animateTransform attributeName="gradientTransform" 
                                  type="translate" 
                                  values="0,0; -15,-15; 25,5; 5,25; -20,-5; 0,0" 
                                  dur="5s" 
                                  repeatCount="indefinite"/>
            </radialGradient>

            <linearGradient id="flowingGradient-@ComponentId" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#ff006e;stop-opacity:0.9">
                    <animate attributeName="offset" 
                             values="0%;30%;60%;90%;100%;70%;40%;10%;0%" 
                             dur="4s" 
                             repeatCount="indefinite"/>
                    <animate attributeName="stop-color" 
                             values="#ff006e;#8338ec;#3a86ff;#06ffa5;#ffbe0b;#fb5607;#ff006e" 
                             dur="3s" 
                             repeatCount="indefinite"/>
                </stop>
                <stop offset="25%" style="stop-color:#8338ec;stop-opacity:0.8">
                    <animate attributeName="offset" 
                             values="25%;55%;85%;15%;45%;75%;5%;35%;25%" 
                             dur="3.5s" 
                             repeatCount="indefinite"/>
                    <animate attributeName="stop-color" 
                             values="#8338ec;#3a86ff;#06ffa5;#ffbe0b;#fb5607;#ff006e;#8338ec" 
                             dur="2.7s" 
                             repeatCount="indefinite"/>
                </stop>
                <stop offset="50%" style="stop-color:#3a86ff;stop-opacity:0.7">
                    <animate attributeName="offset" 
                             values="50%;80%;10%;40%;70%;0%;30%;60%;50%" 
                             dur="3.2s" 
                             repeatCount="indefinite"/>
                    <animate attributeName="stop-color" 
                             values="#3a86ff;#06ffa5;#ffbe0b;#fb5607;#ff006e;#8338ec;#3a86ff" 
                             dur="2.4s" 
                             repeatCount="indefinite"/>
                </stop>
                <stop offset="75%" style="stop-color:#06ffa5;stop-opacity:0.6">
                    <animate attributeName="offset" 
                             values="75%;5%;35%;65%;95%;25%;55%;85%;75%" 
                             dur="2.8s" 
                             repeatCount="indefinite"/>
                    <animate attributeName="stop-color" 
                             values="#06ffa5;#ffbe0b;#fb5607;#ff006e;#8338ec;#3a86ff;#06ffa5" 
                             dur="2.1s" 
                             repeatCount="indefinite"/>
                </stop>
                <stop offset="100%" style="stop-color:#ffbe0b;stop-opacity:0.5">
                    <animate attributeName="offset" 
                             values="100%;30%;60%;90%;20%;50%;80%;10%;100%" 
                             dur="2.5s" 
                             repeatCount="indefinite"/>
                    <animate attributeName="stop-color" 
                             values="#ffbe0b;#fb5607;#ff006e;#8338ec;#3a86ff;#06ffa5;#ffbe0b" 
                             dur="1.8s" 
                             repeatCount="indefinite"/>
                </stop>
            </linearGradient>

            <!-- Solid fill gradient for completed portion -->
            <linearGradient id="solidGradient-@ComponentId" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" style="stop-color:@SolidColor;stop-opacity:1"/>
                <stop offset="100%" style="stop-color:@SolidColorEnd;stop-opacity:1"/>
            </linearGradient>

            <!-- S logo shape mask - clips everything to the S shape -->
            <mask id="logoMask-@ComponentId">
                <rect x="0" y="0" width="150" height="150" fill="black"/>
                <path d="M112.46,53.74c-4.37-1.83-8.88-3.62-12.74-6.28,0-.02,0-.03,0-.03-3.11-2.15-5.79-5.32-7.78-8.58-1.76-2.87-1.94-6.59,1.07-9.18,2.87-2.47,5.6-3.85,9.61-1.02,4.02,2.83,3.45,11.97,13.33,18.53,7.51,3.76,17.44,9.52,34.05-5.65-25.79,12.03-5.37-17.75-17.69-20.59-12.32-2.84-20.99-3.29-37.61-13.42-2.84-1.73-5.79-3.35-8.87-4.59C81.21,1.07,76.47.11,71.87.01c-10.11-.24-19.6,3.64-25.77,11.06.02,0,.03,0,.05,0,0,0,0,0,0,0,0,0,.01,0,.03-.01,11.47-2.17,22.69-3.41,31,7.13,3.42,4.35,4,10.56,1.77,15.07-1.33-8.51-5.66-14.36-14.25-15.21-4.76-.47-9.87.08-14.5,1.36-7.31,2.03-14.27,5.31-21.53,7.59-6.19,1.94-12.62,3.6-18.56-1.53.94,8.38,4.93,12.66,12.78,12.74,8.71.09,17.43-.85,26.14-.81,6.13.03,9.9,3.22,11.41,8.36,1.34,4.56-.48,9.92-4.33,12.72-4.15,3.02-10.38,3.14-14.82.25-1.2-.78-2.37-1.63-3.92-2.7,1.92,10.01,5.1,13.43,14.19,14.87,3.7.59,7.56.43,11.34.3,9.19-.33,18.43-1.76,27.54-1.1,19.63,1.42,32.59,18.34,29.81,37.1-.06,0-.11,0-.11,0,0,0-.23,1.78-1.36,4.33-.26-2.67-.48-5.34-.86-7.99-1.78-12.61-9.39-20.6-20.77-25.14-11.13-4.44-30.47-1.19-37.7,14.07.17-.37,3.27-.7,3.68-.78,2.66-.53,5.37-.95,8.09-1.01,5.74-.13,10.71,2.01,8.34,8.56-4.27,13.13-17.5,14.91-29.37,7.49-9.54-5.96-16.55-9.27-22.76-21.69,1.42,16.13,16.6,28.45,23.3,32.7,7.62,4.91,32.4,13.26,42.03,5.04,4.25-3.14,6.98-6.26,8.59-9.25-.8,9.11-8.05,25.6-41.04,15.9-17.97-6.21-22.21-10.9-30.55-19.11-7.88-7.76-13.51-8.22-19.67-8.15-4.31.05-8.36,2.94-10.1,4.25,2.63-1.02,9.77.69,14.26,4.14,4.49,3.45,9.26,18.31,19.06,25.29,14.21,10.19,20.33,14.17,43.04,14.17s34.82-7.63,39.78-10.62c2.51-1.31,4.8-2.71,6.72-4.2,26.57-20.68,25.6-66.32-10.4-81.44Z" fill="white"/>
            </mask>

            <!-- Progress mask for fill level -->
            <mask id="progressMask-@ComponentId">
                <rect x="0" y="0" width="150" height="150" fill="black"/>
                <rect x="0" y="@(150 - (AnimatedProgress * 150 / 100))" width="150" height="@(AnimatedProgress * 150 / 100)" fill="white"/>
            </mask>

            <!-- Combined mask: S shape AND progress -->
            <mask id="combinedMask-@ComponentId">
                <rect x="0" y="0" width="150" height="150" fill="black"/>
                <g mask="url(#logoMask-@ComponentId)">
                    <rect x="0" y="@(150 - (AnimatedProgress * 150 / 100))" width="150" height="@(AnimatedProgress * 150 / 100)" fill="white"/>
                </g>
            </mask>

            <!-- Glow effect filter -->
            <filter id="glowEffect-@ComponentId">
                <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                <feMerge> 
                    <feMergeNode in="coloredBlur"/>
                    <feMergeNode in="SourceGraphic"/> 
                </feMerge>
            </filter>

            <!-- Plasma distortion filter -->
            <filter id="plasmaDistortion-@ComponentId">
                <feTurbulence baseFrequency="0.02" numOctaves="3" result="noise">
                    <animate attributeName="baseFrequency" 
                             values="0.02;0.04;0.01;0.03;0.02" 
                             dur="8s" 
                             repeatCount="indefinite"/>
                </feTurbulence>
                <feDisplacementMap in="SourceGraphic" in2="noise" scale="3">
                    <animate attributeName="scale" 
                             values="3;6;1;4;3" 
                             dur="6s" 
                             repeatCount="indefinite"/>
                </feDisplacementMap>
            </filter>
        </defs>

        <!-- Background plasma effect (only visible when not filled) -->
        <g class="plasma-background" style="opacity: @((100 - AnimatedProgress) / 100.0)" mask="url(#logoMask-@ComponentId)">
            <!-- Layer 1: Primary plasma gradient -->
            <rect x="0" y="0" width="150" height="150" 
                  fill="url(#plasmaGradient1-@ComponentId)" 
                  filter="url(#glowEffect-@ComponentId)"
                  class="plasma-layer-1"/>
            
            <!-- Layer 2: Secondary plasma gradient -->
            <rect x="0" y="0" width="150" height="150" 
                  fill="url(#plasmaGradient2-@ComponentId)" 
                  filter="url(#plasmaDistortion-@ComponentId)"
                  class="plasma-layer-2"
                  opacity="0.8"/>
            
            <!-- Layer 3: Flowing gradient overlay -->
            <rect x="0" y="0" width="150" height="150" 
                  fill="url(#flowingGradient-@ComponentId)" 
                  class="plasma-layer-3"
                  opacity="0.6"/>
        </g>

        <!-- Filled portion -->
        <g mask="url(#combinedMask-@ComponentId)">
            <rect x="0" y="0" width="150" height="150" 
                  fill="url(#solidGradient-@ComponentId)"/>
        </g>

        <!-- Outline/border -->
        <path d="M112.46,53.74c-4.37-1.83-8.88-3.62-12.74-6.28,0-.02,0-.03,0-.03-3.11-2.15-5.79-5.32-7.78-8.58-1.76-2.87-1.94-6.59,1.07-9.18,2.87-2.47,5.6-3.85,9.61-1.02,4.02,2.83,3.45,11.97,13.33,18.53,7.51,3.76,17.44,9.52,34.05-5.65-25.79,12.03-5.37-17.75-17.69-20.59-12.32-2.84-20.99-3.29-37.61-13.42-2.84-1.73-5.79-3.35-8.87-4.59C81.21,1.07,76.47.11,71.87.01c-10.11-.24-19.6,3.64-25.77,11.06.02,0,.03,0,.05,0,0,0,0,0,0,0,0,0,.01,0,.03-.01,11.47-2.17,22.69-3.41,31,7.13,3.42,4.35,4,10.56,1.77,15.07-1.33-8.51-5.66-14.36-14.25-15.21-4.76-.47-9.87.08-14.5,1.36-7.31,2.03-14.27,5.31-21.53,7.59-6.19,1.94-12.62,3.6-18.56-1.53.94,8.38,4.93,12.66,12.78,12.74,8.71.09,17.43-.85,26.14-.81,6.13.03,9.9,3.22,11.41,8.36,1.34,4.56-.48,9.92-4.33,12.72-4.15,3.02-10.38,3.14-14.82.25-1.2-.78-2.37-1.63-3.92-2.7,1.92,10.01,5.1,13.43,14.19,14.87,3.7.59,7.56.43,11.34.3,9.19-.33,18.43-1.76,27.54-1.1,19.63,1.42,32.59,18.34,29.81,37.1-.06,0-.11,0-.11,0,0,0-.23,1.78-1.36,4.33-.26-2.67-.48-5.34-.86-7.99-1.78-12.61-9.39-20.6-20.77-25.14-11.13-4.44-30.47-1.19-37.7,14.07.17-.37,3.27-.7,3.68-.78,2.66-.53,5.37-.95,8.09-1.01,5.74-.13,10.71,2.01,8.34,8.56-4.27,13.13-17.5,14.91-29.37,7.49-9.54-5.96-16.55-9.27-22.76-21.69,1.42,16.13,16.6,28.45,23.3,32.7,7.62,4.91,32.4,13.26,42.03,5.04,4.25-3.14,6.98-6.26,8.59-9.25-.8,9.11-8.05,25.6-41.04,15.9-17.97-6.21-22.21-10.9-30.55-19.11-7.88-7.76-13.51-8.22-19.67-8.15-4.31.05-8.36,2.94-10.1,4.25,2.63-1.02,9.77.69,14.26,4.14,4.49,3.45,9.26,18.31,19.06,25.29,14.21,10.19,20.33,14.17,43.04,14.17s34.82-7.63,39.78-10.62c2.51-1.31,4.8-2.71,6.72-4.2,26.57-20.68,25.6-66.32-10.4-81.44Z" 
              fill="none" 
              stroke="@BorderColor" 
              stroke-width="@BorderWidth" 
              opacity="@BorderOpacity"/>
    </svg>

    @if (ShowPercentage)
    {
        <div class="firebar-percentage">@AnimatedProgress.ToString("F0")%</div>
    }
</div>

<style>
    .firebar-container {
        position: relative;
        display: inline-block;
        width: 100%;
        height: 100%;
    }

    .firebar-svg {
        width: 100%;
        height: 100%;
        display: block;
    }

    .firebar-percentage {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-weight: bold;
        font-size: 1.2em;
        color: @TextColor;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        pointer-events: none;
    }

    .plasma-background {
        animation: plasmaFlow 8s infinite ease-in-out;
        transform-origin: center;
    }

    .plasma-layer-1 {
        animation: plasmaWave1 6s infinite ease-in-out;
        mix-blend-mode: screen;
    }

    .plasma-layer-2 {
        animation: plasmaWave2 4s infinite ease-in-out;
        mix-blend-mode: multiply;
    }

    .plasma-layer-3 {
        animation: plasmaWave3 5s infinite ease-in-out;
        mix-blend-mode: overlay;
    }

    @@keyframes plasmaFlow {
        0% { 
            transform: scale(1) rotate(0deg); 
            filter: brightness(1) saturate(1) hue-rotate(0deg);
        }
        25% { 
            transform: scale(1.05) rotate(2deg); 
            filter: brightness(1.2) saturate(1.3) hue-rotate(15deg);
        }
        50% { 
            transform: scale(0.95) rotate(-1deg); 
            filter: brightness(0.9) saturate(1.1) hue-rotate(30deg);
        }
        75% { 
            transform: scale(1.02) rotate(1deg); 
            filter: brightness(1.1) saturate(1.4) hue-rotate(45deg);
        }
        100% { 
            transform: scale(1) rotate(0deg); 
            filter: brightness(1) saturate(1) hue-rotate(60deg);
        }
    }

    @@keyframes plasmaWave1 {
        0% { 
            transform: translateX(0px) translateY(0px) scale(1); 
            opacity: 0.9;
        }
        33% { 
            transform: translateX(10px) translateY(-5px) scale(1.1); 
            opacity: 1;
        }
        66% { 
            transform: translateX(-8px) translateY(8px) scale(0.9); 
            opacity: 0.8;
        }
        100% { 
            transform: translateX(0px) translateY(0px) scale(1); 
            opacity: 0.9;
        }
    }

    @@keyframes plasmaWave2 {
        0% { 
            transform: translateX(0px) translateY(0px) scale(1) rotate(0deg); 
            opacity: 0.8;
        }
        25% { 
            transform: translateX(-12px) translateY(6px) scale(1.05) rotate(1deg); 
            opacity: 0.9;
        }
        50% { 
            transform: translateX(15px) translateY(-10px) scale(0.95) rotate(-1deg); 
            opacity: 0.7;
        }
        75% { 
            transform: translateX(-5px) translateY(12px) scale(1.02) rotate(0.5deg); 
            opacity: 0.85;
        }
        100% { 
            transform: translateX(0px) translateY(0px) scale(1) rotate(0deg); 
            opacity: 0.8;
        }
    }

    @@keyframes plasmaWave3 {
        0% { 
            transform: scale(1) skewX(0deg); 
            opacity: 0.6;
            filter: blur(0px);
        }
        20% { 
            transform: scale(1.08) skewX(1deg); 
            opacity: 0.8;
            filter: blur(1px);
        }
        40% { 
            transform: scale(0.92) skewX(-0.5deg); 
            opacity: 0.5;
            filter: blur(0.5px);
        }
        60% { 
            transform: scale(1.03) skewX(0.8deg); 
            opacity: 0.7;
            filter: blur(1.2px);
        }
        80% { 
            transform: scale(0.97) skewX(-0.3deg); 
            opacity: 0.6;
            filter: blur(0.8px);
        }
        100% { 
            transform: scale(1) skewX(0deg); 
            opacity: 0.6;
            filter: blur(0px);
        }
    }
</style>

@code {
    [Parameter] public double Progress { get; set; } = 0;
    [Parameter] public string Width { get; set; } = "150px";
    [Parameter] public string Height { get; set; } = "150px";
    [Parameter] public string SolidColor { get; set; } = "#28a745";
    [Parameter] public string SolidColorEnd { get; set; } = "#20c997";
    [Parameter] public string BorderColor { get; set; } = "#333";
    [Parameter] public string BorderWidth { get; set; } = "2";
    [Parameter] public string BorderOpacity { get; set; } = "0.3";
    [Parameter] public string TextColor { get; set; } = "#fff";
    [Parameter] public bool ShowPercentage { get; set; } = true;

    private string ComponentId { get; set; } = Guid.NewGuid().ToString("N")[..8];
    private double AnimatedProgress { get; set; } = 0;
    private double PreviousProgress { get; set; } = 0;
    private DateTime AnimationStartTime { get; set; }
    private bool IsAnimating { get; set; } = false;
    private Timer? AnimationTimer { get; set; }

    private string ContainerStyle => $"width: {Width}; height: {Height};";

    protected override void OnParametersSet()
    {
        // Ensure progress is between 0 and 100
        var newProgress = Math.Max(0, Math.Min(100, Progress));
        
        if (Math.Abs(newProgress - AnimatedProgress) > 0.01) // Only animate if there's a meaningful change
        {
            StartAnimation(newProgress);
        }
    }

    private void StartAnimation(double targetProgress)
    {
        PreviousProgress = AnimatedProgress;
        AnimationStartTime = DateTime.Now;
        IsAnimating = true;

        // Stop any existing timer
        AnimationTimer?.Dispose();

        // Create a timer that updates every 16ms (roughly 60fps)
        AnimationTimer = new Timer(UpdateAnimation, targetProgress, 0, 16);
    }

    private void UpdateAnimation(object? state)
    {
        if (state is not double targetProgress || !IsAnimating)
            return;

        var elapsed = DateTime.Now - AnimationStartTime;
        var duration = TimeSpan.FromSeconds(2); // 2 second animation

        if (elapsed >= duration)
        {
            // Animation complete
            AnimatedProgress = targetProgress;
            IsAnimating = false;
            AnimationTimer?.Dispose();
            AnimationTimer = null;
        }
        else
        {
            // Calculate progress using ease-in-out curve
            var t = elapsed.TotalSeconds / duration.TotalSeconds;
            var easedT = EaseInOut(t);
            
            AnimatedProgress = PreviousProgress + (targetProgress - PreviousProgress) * easedT;
        }

        InvokeAsync(StateHasChanged);
    }

    private static double EaseInOut(double t)
    {
        // Cubic ease-in-out function
        return t < 0.5 ? 4 * t * t * t : 1 - Math.Pow(-2 * t + 2, 3) / 2;
    }

    protected override void OnInitialized()
    {
        AnimatedProgress = Math.Max(0, Math.Min(100, Progress));
    }

    public void Dispose()
    {
        AnimationTimer?.Dispose();
    }
} 