﻿.recent-emails-section {
    background-color: var(--neutral-layer-1, #fff);
    border-radius: var(--corner-radius-3, 8px);
    box-shadow: var(--shadow-2, 0 2px 8px rgba(0, 0, 0, 0.08));
    padding: 0rem;
    margin-bottom: 1rem;
    height: 100%;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-top: 0.25rem;
}

    .recent-emails-section:hover {
        box-shadow: var(--shadow-4, 0 6px 12px rgba(0, 0, 0, 0.12));
    }
.me-2 {
    color: #0e730e;
    float:right;
}
.txt-section {
    font-family: "montserrat", sans-serif;
    font-size: 1rem;
    font-weight: 400;
    color: #6b6b6b;
    margin-bottom: 0.25rem;
    padding-bottom: 0.1rem;
}
    .txt-section small {
        letter-spacing: 0px;
        font-size: .65rem;
        padding-left:7px;
    }
    .recent-emails-container {
        position: relative;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
    }

    .recent-emails-container::-webkit-scrollbar {
        width: 6px;
    }

    .recent-emails-container::-webkit-scrollbar-track {
        background: var(--neutral-layer-2, #f5f5f5);
        border-radius: 10px;
    }

    .recent-emails-container::-webkit-scrollbar-thumb {
        background: var(--neutral-stroke-rest, #ccc);
        border-radius: 10px;
    }

        .recent-emails-container::-webkit-scrollbar-thumb:hover {
            background: var(--neutral-stroke-hover, #aaa);
        }

/* Loading progress bar */
.loading-progress {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    z-index: 10;
}

/* Loading overlay */
.loading-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(3px);
    padding: 0.75rem;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 5;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05);
    animation: slideUp 0.3s ease-out;
}

.loading-text {
    display: flex;
    align-items: center;
    font-weight: 500;
    color: var(--accent-fill-rest, #0078d4);
}

.loading-icon {
    animation: pulse 1.5s infinite ease-in-out;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        transform: scale(0.95);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
    100% {
        transform: scale(0.95);
        opacity: 0.7;
    }
}

.empty-state {
    color: var(--neutral-foreground-hint, #6e6e6e);
    padding: 2rem 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.email-list {
    border-radius: var(--corner-radius-2, 4px);
    overflow: hidden;
}

.email-item {
    padding: 0;
    border-left: 3px solid transparent;
    border-bottom: 1px solid var(--neutral-stroke-subtle, #f0f0f0);
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    position: relative;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border-radius: 0 6px 6px 0;
    overflow: hidden;
    transform-origin: top center;
}

    .email-item:last-child {
        border-bottom: none;
    }

    .email-item:hover {
        background-color: var(--neutral-layer-3, #f5f5f5);
        transform: translateY(-2px);
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    }
.loadmore-btn {
    background-color: #fff !important;
    padding: 11px;
    position: relative;
    top: 2px;
    border: 1px solid #0b71c2;
}
.loadmore-btn-text {
    position: relative;
    color:#0b71c2;
    top:-2px;
    font-weight: bold;
}
.bottom-capitem {
    background-color: #0b71c2;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    height: 20px;
    width: 100%;
    text-align: center;
}
/* Animation for new emails */
.email-new {
    animation: popIn 0.5s cubic-bezier(0.26, 1.36, 0.5, 0.87);
}

@keyframes popIn {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.9);
    }
    70% {
        opacity: 1;
        transform: translateY(-3px) scale(1.02);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Animation for emails that will be removed (duplicates) */
.email-fadeout {
    animation: fadeOut 1s ease-out forwards;
    opacity: 0.7;
    pointer-events: none;
}

@keyframes fadeOut {
    from {
        opacity: 0.7;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.95);
        max-height: 0;
        margin-top: -10px;
        margin-bottom: 0;
        padding-top: 0;
        padding-bottom: 0;
    }
}

/* Email types */
.email-outbound {
    border-left-color: var(--accent-fill-rest, #0078d4);
}

    .email-outbound:hover {
        background-color: rgba(0, 120, 212, 0.05);
    }

.email-from-client {
    border-left-color: var(--success-fill-rest, #107c10);
}

    .email-from-client:hover {
        background-color: rgba(16, 124, 16, 0.05);
    }

.email-internal {
    border-left-color: var(--warning-fill-rest, #ffaa44);
}

    .email-internal:hover {
        background-color: rgba(255, 170, 68, 0.05);
    }

.email-subject {
    font-size: 0.8rem;
    padding: 0.6rem 0.6rem 0.3rem 0.6rem;
    font-weight: 600;
    overflow: hidden;
    color: #0269c3;
    position: relative;
    height:10px;
    text-overflow: ellipsis;
   
}

.email-preview {
    font-size: 0.8rem;
    padding: 0 0.6rem 0.6rem 0.6rem;
    color: #000000;
    line-height: 1.2;
}

.email-date {
    white-space: nowrap;
    font-size: 0.75rem;
    color: var(--neutral-foreground-hint, #6e6e6e);
}

.email-mailbox {
    font-size: 0.75rem;
    color: var(--neutral-foreground-hint, #6e6e6e);
}

.email-participants {
    max-width: 70%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.recent-emails-footer {
    font-size: 0.8rem;
    color: var(--neutral-foreground-hint, #6e6e6e);
    padding-top: 0.5rem;
    margin-top: auto;
    border-top: 1px solid var(--neutral-stroke-subtle, #f0f0f0);
}

.mailflow {
    color: #fff;
    height: 27px;
    background-color: rgba(0, 0, 0, 0.25);
    border-right: 1px solid rgba(255, 255, 255, 0.3);
    padding-top: 2px !important;
    padding-left: 4px;
    padding-right: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.recent-emails-footer {
    position: relative;
    top: -23px;
}
.email-item-header {
    margin-bottom: 0.4rem;
}

/* Email header styles based on type */
.top-emailbarbox-outbound {
    background: linear-gradient(90deg, #0078d4, #106eba);
    color: #fff;
    box-shadow: 0 1px 3px rgba(0, 120, 212, 0.3);
}

.top-emailbarbox-fromclient {
    background: linear-gradient(90deg, #107c10, #0e6e0e);
    color: #fff;
    box-shadow: 0 1px 3px rgba(16, 124, 16, 0.3);
}

.top-emailbarbox-internal {
    background: linear-gradient(90deg, #ffaa44, #f59c31);
    color: #fff;
    box-shadow: 0 1px 3px rgba(255, 170, 68, 0.3);
}

.top-tobox, .top-frombox {
    padding-top: 9px;
    max-width: 125px;
    width: 125px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
}

.top-tobox strong, .top-frombox strong {
    font-size: 9px;
    padding: 3px 5px;
    border-radius: 3px;
    background-color: rgba(255, 255, 255, 0.25);
    color: #fff;
    font-weight: 700;
    text-transform: uppercase;
    margin-right: 4px;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.2);
}

.top-datebox {
    padding-top: 7px;
    padding-right: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 16px;
    font-weight: 300;
    text-align: right;
    flex: 1;
    color: #ffffffb5;
}

/* Shimmer loading effect for the empty state */
@keyframes shimmer {
    0% {
        background-position: -1000px 0;
    }
    100% {
        background-position: 1000px 0;
    }
}

.shimmer {
    animation: shimmer 2s infinite linear;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.03) 8%, rgba(0, 0, 0, 0.06) 18%, rgba(0, 0, 0, 0.03) 33%);
    background-size: 1000px 100%;
}

