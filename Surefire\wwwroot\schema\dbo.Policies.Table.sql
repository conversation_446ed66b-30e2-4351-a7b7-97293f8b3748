USE [surefire-x1-dev]
GO

/****** Object:  Table [dbo].[Policies]    Script Date: 6/10/2025 1:49:20 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Policies](
	[PolicyId] [int] IDENTITY(1,1) NOT NULL,
	[PolicyNumber] [nvarchar](max) NOT NULL,
	[EffectiveDate] [datetime2](7) NOT NULL,
	[ExpirationDate] [datetime2](7) NOT NULL,
	[Premium] [decimal](18, 2) NOT NULL,
	[ApplicationId] [int] NULL,
	[CarrierId] [int] NULL,
	[WholesalerId] [int] NULL,
	[ProductId] [int] NULL,
	[ClientId] [int] NOT NULL,
	[Notes] [nvarchar](max) NULL,
	[Status] [nvarchar](max) NULL,
	[ePolicyId] [nvarchar](max) NULL,
	[eType] [nvarchar](max) NULL,
	[eTypeCode] [nvarchar](max) NULL,
	[ePolicyLineId] [nvarchar](max) NULL,
	[CSRId] [nvarchar](450) NULL,
	[CreatedById] [nvarchar](450) NULL,
	[ProducerId] [nvarchar](450) NULL,
	[DateCreated] [datetime2](7) NULL,
	[DateModified] [datetime2](7) NULL,
 CONSTRAINT [PK_Policies] PRIMARY KEY CLUSTERED 
(
	[PolicyId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[Policies] ADD  DEFAULT ((0)) FOR [ClientId]
GO

ALTER TABLE [dbo].[Policies]  WITH CHECK ADD  CONSTRAINT [FK_Policies_Application_ApplicationId] FOREIGN KEY([ApplicationId])
REFERENCES [dbo].[Application] ([ApplicationId])
GO

ALTER TABLE [dbo].[Policies] CHECK CONSTRAINT [FK_Policies_Application_ApplicationId]
GO

ALTER TABLE [dbo].[Policies]  WITH CHECK ADD  CONSTRAINT [FK_Policies_AspNetUsers_CreatedById] FOREIGN KEY([CreatedById])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO

ALTER TABLE [dbo].[Policies] CHECK CONSTRAINT [FK_Policies_AspNetUsers_CreatedById]
GO

ALTER TABLE [dbo].[Policies]  WITH CHECK ADD  CONSTRAINT [FK_Policies_AspNetUsers_CSRId] FOREIGN KEY([CSRId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO

ALTER TABLE [dbo].[Policies] CHECK CONSTRAINT [FK_Policies_AspNetUsers_CSRId]
GO

ALTER TABLE [dbo].[Policies]  WITH CHECK ADD  CONSTRAINT [FK_Policies_AspNetUsers_ProducerId] FOREIGN KEY([ProducerId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO

ALTER TABLE [dbo].[Policies] CHECK CONSTRAINT [FK_Policies_AspNetUsers_ProducerId]
GO

ALTER TABLE [dbo].[Policies]  WITH CHECK ADD  CONSTRAINT [FK_Policies_Carriers_CarrierId] FOREIGN KEY([CarrierId])
REFERENCES [dbo].[Carriers] ([CarrierId])
GO

ALTER TABLE [dbo].[Policies] CHECK CONSTRAINT [FK_Policies_Carriers_CarrierId]
GO

ALTER TABLE [dbo].[Policies]  WITH CHECK ADD  CONSTRAINT [FK_Policies_Carriers_WholesalerId] FOREIGN KEY([WholesalerId])
REFERENCES [dbo].[Carriers] ([CarrierId])
GO

ALTER TABLE [dbo].[Policies] CHECK CONSTRAINT [FK_Policies_Carriers_WholesalerId]
GO

ALTER TABLE [dbo].[Policies]  WITH CHECK ADD  CONSTRAINT [FK_Policies_Clients_ClientId] FOREIGN KEY([ClientId])
REFERENCES [dbo].[Clients] ([ClientId])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[Policies] CHECK CONSTRAINT [FK_Policies_Clients_ClientId]
GO

ALTER TABLE [dbo].[Policies]  WITH CHECK ADD  CONSTRAINT [FK_Policies_Products_ProductId] FOREIGN KEY([ProductId])
REFERENCES [dbo].[Products] ([ProductId])
GO

ALTER TABLE [dbo].[Policies] CHECK CONSTRAINT [FK_Policies_Products_ProductId]
GO

EXEC sys.sp_addextendedproperty @name=N'LLM_SearchHints', @value=N'Contains all policies (past, current, future). Primary key: PolicyId. Key columns: PolicyNumber, EffectiveDate, ExpirationDate (no Expiry_Date). Use exact names: EffectiveDate and ExpirationDate. Represents policies by coverage line; join Products on ProductId and filter by LineName (e.g., ''General Liability''). For expiring policies, join Renewals on PolicyId and apply filters on ExpirationDate or RenewalDate.' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Policies'
GO
