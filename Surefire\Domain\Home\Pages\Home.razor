﻿@page "/"
@page "/Home"
@using Surefire.Domain.Home.Components
@using Surefire.Domain.Shared.Services
@inject StateService StateService
@inherits AppComponentBase
@implements IDisposable

<PageTitle>Surefire: So hot right now.</PageTitle>

<img src="/img/banner-behind.jpg" class="img-fluid" style="z-index: 1; position: absolute; pointer-events:none;" />
<img src="/img/banner-topp.png" class="img-fluid" style="z-index: 199; position: absolute; pointer-events:none;" />
<img src="/img/bottom-right.png" class="bottom-right" style="z-index: 1; position: absolute; pointer-events:none; bottom: -10px;" />
<div class="page-content">
    <div class="flex-container">
        <div class="left-column">
            <div class="left-column__pad">
                <DailyTaskList />
                <RenewalFlowTasks tabTitle="My Tasks" />
            </div>
        </div>
        <div class="right-column">
            <div class="right-column__pad">
                <div class="nested-flex-container">
                    <div class="nested-left-column">
                        <div class="nested-left-column__pad">
                            <div class="middle-container">
                                <ProposalPipeline />
                                <CertificateRequestList />
                                <RecentPayments />
                                <DocuSignEnvelopeList />
                                <LeadsHomeList />
                            </div>
                        </div>
                    </div>

                    <div class="nested-right-column">
                        <div class="nested-right-column__pad">
                            <ExpiringSoon />
                            <IncompleteTasks />
                            <CheatSheet />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [CascadingParameter] public Action<string> UpdateHeader { get; set; }

    protected override async Task OnInitializedAsync()
    {
        StateService.OnHomepageDataUpdated += HandleHomepageDataUpdated;
        
        await StateService.GetHomepageDataAsync();

        // Set page header and status
        UpdateHeader?.Invoke("Home");
        StateService.UpdateStatus($"System operational. Welcome to surefire {StateService.SurefireVersion}");
    }

    private void HandleHomepageDataUpdated()
    {
        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        StateService.OnHomepageDataUpdated -= HandleHomepageDataUpdated;
    }
}