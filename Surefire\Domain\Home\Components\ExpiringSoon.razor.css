﻿.homemodule-small-header {
    font-family: "montserrat", sans-serif;
    font-size: 1.3em;
    padding-top: 5px;
    padding-bottom: 3px;
    background: linear-gradient(to right, #8b8b8b 0%, #aaaaaa 87%, #bfbfbf 94%, #fff 100%);
    color: #dcdcdc;
    text-align: center;
    text-shadow: 1px 1px 3px #2b2b2b;
    border-top-left-radius: 15px;
}
.homemodule-small-container {
    background-color: #dfdfdf87;
}
.intd {
    width: 75px;
}
.rentask-link {
    text-decoration:none;
}
.rentask-link:hover {
    cursor:pointer;
}
.prodmeme {
    background-color: #8b8b8b;
    color: #e0e0e0;
    text-shadow: 0px 0px 2px #000;
    padding-left: 3px;
    padding-top: 1px;
    padding-bottom: 1px;
    padding-right: 3px;
    font-size: .9em;
    font-weight: bold;
    min-width: 90px;
    font-family: "mono45-headline", monospace;
    font-weight: 500;
    font-style: normal;
    letter-spacing: 1.5px;
    border-right: 1px solid #00000047;
    box-shadow: 5px 0px 10px #909090c1;
}
tr .trow2 {
    display: block;
    margin-top: 0px !important;
    padding: 0px;
}

.trow2 td {
    padding: 0px;
}
#upcomingrenewals-table {
    padding-top: 10px;
    text-align: left;
    min-width: 250px;
    width: 100%;
    margin: 0;
    padding: 0;
    border-right: 5px solid #aaaaaa;
    background-color: #dfdfdfd9;
    box-shadow: inset 0px 5px 7px #a1a1a1;
}
.hexp2 {
    color: #686868;
    padding-left: 3px;
    padding-top: 1px;
    padding-bottom: 1px;
    padding-right: 4px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    font-size: .9em;
    font-family: "mono45-headline", monospace;
    font-weight: 500;
    font-style: normal;
}



.dailytask-box {
    border: 1px solid #5e5e5e;
}
.ttname2 {
    font-size: .75em;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.rentask-link {
    color: #3d3d3d !important;
}

    .rentask-link:hover {
        color: #006bb7 !important;
    }