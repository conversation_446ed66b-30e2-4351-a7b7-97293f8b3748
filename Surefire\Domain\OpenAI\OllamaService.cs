//using System;
//using System.Net.Http;
//using System.Text;
//using System.Text.Json;
//using System.Threading.Tasks;
//using Microsoft.Extensions.Logging;
//using Surefire.Domain.Logs;
//using System.Text.Json.Serialization;

//namespace Surefire.Domain.OpenAI
//{
//    public class OllamaService : IOllamaService
//    {
//        private readonly HttpClient _httpClient;
//        private readonly ILogger<OllamaService> _logger;
//        private readonly ILoggingService _logService;
//        private const string DEFAULT_MODEL = "gemma3:4b-it-qat";

//        public OllamaService(
//            IHttpClientFactory httpClientFactory,
//            ILogger<OllamaService> logger,
//            ILoggingService logService)
//        {
//            _httpClient = httpClientFactory.CreateClient("Ollama");
//            _logger = logger;
//            _logService = logService;

//            // Log the base address for debugging
//            _logger.LogInformation("OllamaService initialized with base address: {BaseAddress}", _httpClient.BaseAddress);
//        }

//        public async Task<string> GetCompletionAsync(string prompt, string model = DEFAULT_MODEL)
//        {
//            try
//            {
//                var request = new
//                {
//                    model = model,
//                    prompt = prompt,
//                    stream = false
//                };

//                var json = JsonSerializer.Serialize(request);
//                var content = new StringContent(json, Encoding.UTF8, "application/json");

//                // Log the request details
//                _logger.LogInformation("Sending completion request to Ollama:");
//                _logger.LogInformation("Endpoint: {Endpoint}", "/api/generate");
//                _logger.LogInformation("Model: {Model}", model);
//                _logger.LogInformation("Request JSON: {Json}", json);

//                var response = await _httpClient.PostAsync("/api/generate", content);
                
//                // Log the response status
//                _logger.LogInformation("Response status code: {StatusCode}", response.StatusCode);
//                _logger.LogInformation("Response headers: {Headers}", 
//                    string.Join(", ", response.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));

//                var responseContent = await response.Content.ReadAsStringAsync();
//                _logger.LogInformation("Response content: {Content}", responseContent);

//                response.EnsureSuccessStatusCode();

//                // Parse the streaming response format
//                var responseObj = JsonSerializer.Deserialize<OllamaStreamingResponse>(responseContent);
//                return responseObj?.Response ?? string.Empty;
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError(ex, "Error getting completion from Ollama");
//                await _logService.LogAsync(LogLevel.Error, 
//                    $"Error getting completion from Ollama: {ex.Message}", 
//                    "OllamaService", ex);
//                throw;
//            }
//        }

//        public async Task<float[]> GetEmbeddingsAsync(string text, string model = DEFAULT_MODEL)
//        {
//            try
//            {
//                var request = new
//                {
//                    model = model,
//                    prompt = text
//                };

//                var json = JsonSerializer.Serialize(request);
//                var content = new StringContent(json, Encoding.UTF8, "application/json");

//                // Log the request details
//                _logger.LogInformation("Sending embeddings request to Ollama:");
//                _logger.LogInformation("Endpoint: {Endpoint}", "/api/embeddings");
//                _logger.LogInformation("Model: {Model}", model);
//                _logger.LogInformation("Request JSON: {Json}", json);

//                var response = await _httpClient.PostAsync("/api/embeddings", content);
                
//                // Log the response status
//                _logger.LogInformation("Response status code: {StatusCode}", response.StatusCode);
//                _logger.LogInformation("Response headers: {Headers}", 
//                    string.Join(", ", response.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));

//                var responseContent = await response.Content.ReadAsStringAsync();
//                _logger.LogInformation("Response content: {Content}", responseContent);

//                response.EnsureSuccessStatusCode();

//                var responseObj = JsonSerializer.Deserialize<OllamaEmbeddingResponse>(responseContent);
//                return responseObj?.Embedding ?? Array.Empty<float>();
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError(ex, "Error getting embeddings from Ollama");
//                await _logService.LogAsync(LogLevel.Error, 
//                    $"Error getting embeddings from Ollama: {ex.Message}", 
//                    "OllamaService", ex);
//                throw;
//            }
//        }

//        private class OllamaStreamingResponse
//        {
//            [JsonPropertyName("model")]
//            public string Model { get; set; } = string.Empty;

//            [JsonPropertyName("created_at")]
//            public string CreatedAt { get; set; } = string.Empty;

//            [JsonPropertyName("response")]
//            public string Response { get; set; } = string.Empty;

//            [JsonPropertyName("done")]
//            public bool Done { get; set; }
//        }

//        private class OllamaEmbeddingResponse
//        {
//            [JsonPropertyName("embedding")]
//            public float[] Embedding { get; set; } = Array.Empty<float>();
//        }
//    }
//} 