﻿using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using System.Linq;
using System.IO;
using System.Threading;

namespace Surefire.Domain.Chat
{
    public partial class ChatService
    {
        /// <summary>
        /// Retrieves a call recording for a specific call ID
        /// </summary>
        /// <param name="callId">The ID of the call to retrieve the recording for</param>
        /// <returns>A tuple containing recording data as a byte array and content type, or null if no recording exists</returns>
        public async Task<(byte[] Data, string ContentType)?> GetCallRecordingAsync(string callId)
        {
            try
            {
                await AuthenticateAsync();
                
                // Build the request URL for call recording metadata
                string accountId = "~"; // Use default account
                var metadataUrl = $"{_serverUrl}/restapi/v1.0/account/{accountId}/recording/{callId}";
                
                // Set up headers
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _accessToken);
                _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                
                // First, get recording metadata
                var metadataResponse = await _httpClient.GetAsync(metadataUrl);
                
                if (!metadataResponse.IsSuccessStatusCode)
                {
                    var errorContent = await metadataResponse.Content.ReadAsStringAsync();
                    Console.WriteLine($"Error fetching recording metadata: {metadataResponse.StatusCode}, Content: {errorContent}");
                    return null;
                }
                
                var metadataContent = await metadataResponse.Content.ReadAsStringAsync();
                var recordingMetadata = JsonSerializer.Deserialize<RecordingMetadata>(metadataContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                
                if (recordingMetadata == null || string.IsNullOrEmpty(recordingMetadata.contentUri))
                {
                    Console.WriteLine("No recording content URI found in metadata");
                    return null;
                }
                
                // Now fetch the actual recording content
                var contentUrl = recordingMetadata.contentUri;
                if (!contentUrl.StartsWith("http"))
                {
                    contentUrl = $"{_serverUrl}{contentUrl}";
                }
                
                Console.WriteLine($"Fetching recording content: {contentUrl}");
                
                // Reset headers for content request
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _accessToken);
                
                var contentResponse = await _httpClient.GetAsync(contentUrl);
                
                if (!contentResponse.IsSuccessStatusCode)
                {
                    var errorContent = await contentResponse.Content.ReadAsStringAsync();
                    Console.WriteLine($"Error fetching recording content: {contentResponse.StatusCode}, Content: {errorContent}");
                    return null;
                }
                
                // Get the content type from the response
                string contentType = contentResponse.Content.Headers.ContentType?.ToString() ?? "audio/mpeg";
                
                // Read the recording data
                var recordingData = await contentResponse.Content.ReadAsByteArrayAsync();
                
                return (recordingData, contentType);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error retrieving call recording: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
                return null;
            }
        }
        
        /// <summary>
        /// Retrieves a call recording directly using its content URI
        /// </summary>
        /// <param name="contentUri">The content URI for the recording</param>
        /// <returns>A tuple containing recording data as a byte array and content type, or null if retrieval fails</returns>
        public async Task<(byte[] Data, string ContentType)?> GetCallRecordingByUriAsync(string contentUri)
        {
            try
            {
                await AuthenticateAsync();
                
                // Make sure we have a valid content URI
                if (string.IsNullOrEmpty(contentUri))
                {
                    Console.WriteLine("No content URI provided");
                    return null;
                }
                
                // Ensure the URI is fully qualified
                if (!contentUri.StartsWith("http"))
                {
                    contentUri = $"{_serverUrl}{contentUri}";
                }
                
                Console.WriteLine($"Fetching recording content directly: {contentUri}");
                
                // Set up headers for content request
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _accessToken);
                
                var contentResponse = await _httpClient.GetAsync(contentUri);
                
                if (!contentResponse.IsSuccessStatusCode)
                {
                    var errorContent = await contentResponse.Content.ReadAsStringAsync();
                    Console.WriteLine($"Error fetching recording content: {contentResponse.StatusCode}, Content: {errorContent}");
                    return null;
                }
                
                // Get the content type from the response
                string contentType = contentResponse.Content.Headers.ContentType?.ToString() ?? "audio/mpeg";
                
                // Read the recording data
                var recordingData = await contentResponse.Content.ReadAsByteArrayAsync();
                
                return (recordingData, contentType);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error retrieving call recording by URI: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
                return null;
            }
        }
        
        /// <summary>
        /// Downloads a call recording and saves it to a temporary file for transcription
        /// </summary>
        /// <param name="callId">The ID of the call to download the recording for</param>
        /// <returns>Path to the saved audio file, or null if download failed</returns>
        public async Task<string> DownloadCallRecordingAsync(string callId)
        {
            var recording = await GetCallRecordingAsync(callId);
            if (recording == null || recording.Value.Data.Length == 0)
            {
                return null;
            }
            
            try
            {
                // Create a temporary directory if it doesn't exist
                var tempDir = Path.Combine(Path.GetTempPath(), "SurefireRecordings");
                Directory.CreateDirectory(tempDir);
                
                // Determine file extension based on content type
                string extension = ".mp3"; // Default
                if (recording.Value.ContentType.Contains("wav"))
                {
                    extension = ".wav";
                }
                
                // Create a temporary file with a unique name
                var tempFile = Path.Combine(tempDir, $"recording_{callId}{extension}");
                
                // Save the recording data to the file
                await File.WriteAllBytesAsync(tempFile, recording.Value.Data);
                
                return tempFile;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving recording file: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Retrieves the call log for the past week.
        /// </summary>
        /// <returns>A list of call log records.</returns>
        public async Task<IEnumerable<CallLogRecordFire>> GetRecentCallLogsAsync()
        {
            await AuthenticateAsync();

            try
            {
                string accountId = "~";
                string extensionId = "~";
                var dateFrom = DateTime.UtcNow.AddDays(-7).ToString("o"); // ISO 8601 format

                var requestUrl = $"{_serverUrl}/restapi/v1.0/account/{accountId}/extension/{extensionId}/call-log";
                var queryParams = new List<string>
                {
                    $"dateFrom={Uri.EscapeDataString(dateFrom)}",
                    "view=Detailed", // Get detailed information
                    "perPage=1000"     // Get up to 1000 records
                };

                requestUrl += "?" + string.Join("&", queryParams);

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _accessToken);
                _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                Console.WriteLine($"Fetching Call Logs: {requestUrl}");

                var response = await _httpClient.GetAsync(requestUrl);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Get Call Log Error: {response.StatusCode}, Content: {errorContent}");
                    return Enumerable.Empty<CallLogRecordFire>();
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    NumberHandling = JsonNumberHandling.AllowReadingFromString // Handle potential string numbers
                };

                var callLogResponse = JsonSerializer.Deserialize<CallLogResponse>(responseContent, jsonOptions);

                return callLogResponse?.records ?? Enumerable.Empty<CallLogRecordFire>();

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error retrieving call logs: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
                return Enumerable.Empty<CallLogRecordFire>();
            }
        }

        /// <summary>
        /// Retrieves call logs for specific phone numbers, including transferred calls.
        /// </summary>
        /// <param name="phoneNumbers">List of phone numbers to filter by</param>
        /// <param name="showAll">If true, returns all call logs regardless of phone numbers</param>
        /// <param name="cancellationToken">Cancellation token to cancel the operation</param>
        /// <returns>A list of call log records filtered by the specified phone numbers</returns>
        public async Task<List<CallLogRecordFire>> GetCallLogsByPhoneNumbersAsync(
            List<string> phoneNumbers, 
            bool showAll = false,
            CancellationToken cancellationToken = default)
        {
            await AuthenticateAsync();

            try
            {   
                // Normalize the input phone numbers for better matching
                var normalizedPhoneNumbers = new List<string>();
                if (phoneNumbers != null && phoneNumbers.Any())
                {
                    normalizedPhoneNumbers = phoneNumbers
                        .Where(p => !string.IsNullOrWhiteSpace(p))
                        .Select(NormalizePhoneNumber)
                        .ToList();
                }
                
                // If no valid phone numbers provided and showAll is false, return empty list
                if (!showAll && (normalizedPhoneNumbers == null || !normalizedPhoneNumbers.Any()))
                {
                    Console.WriteLine("No phone numbers provided and showAll is false. Returning empty list.");
                    return new List<CallLogRecordFire>();
                }
                
                // Check if we have a valid cache
                CallLogRecordFire[] callLogRecords;
                bool usedCache = false;

                lock (_callLogsCacheLock)
                {
                    usedCache = _cachedCallLogs != null && DateTime.UtcNow < _callLogsCacheExpiration;
                    
                    if (usedCache)
                    {
                        Console.WriteLine($"Using cached call logs (expires in {(_callLogsCacheExpiration - DateTime.UtcNow).TotalSeconds:F1} seconds)");
                        callLogRecords = _cachedCallLogs;
                    }
                    else
                    {
                        callLogRecords = null;
                    }
                }
                
                // If cache is invalid, fetch from API
                if (callLogRecords == null)
                {
                    //Console.WriteLine("Cache expired or empty, fetching fresh call logs from API");
                    string accountId = "~";
                    // Get account-level call logs to capture all calls, including transferred ones
                    var requestUrl = $"{_serverUrl}/restapi/v1.0/account/{accountId}/call-log";
                    
                    // Set date range to last 14 days rather than 30 to reduce data volume
                    var dateFrom = DateTime.UtcNow.AddDays(-30).ToString("o"); // ISO 8601 format
                    
                    var queryParams = new List<string>
                    {
                        $"dateFrom={Uri.EscapeDataString(dateFrom)}",
                        "view=Detailed", // Get detailed information
                        "perPage=1000", // Get up to 1000 records
                        "withRecording=true" // Include calls with recordings
                    };

                    requestUrl += "?" + string.Join("&", queryParams);

                    _httpClient.DefaultRequestHeaders.Clear();
                    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _accessToken);
                    _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                    Console.WriteLine($"Fetching Call Logs by Phone Numbers: {requestUrl}");

                    var response = await _httpClient.GetAsync(requestUrl, cancellationToken);

                    if (!response.IsSuccessStatusCode)
                    {
                        var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                        Console.WriteLine($"Get Call Log Error: {response.StatusCode}, Content: {errorContent}");
                        return new List<CallLogRecordFire>();
                    }

                    var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                    var jsonOptions = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        NumberHandling = JsonNumberHandling.AllowReadingFromString
                    };

                    var callLogResponse = JsonSerializer.Deserialize<CallLogResponse>(responseContent, jsonOptions);
                    
                    if (callLogResponse?.records == null)
                    {
                        Console.WriteLine("No records found in API response");
                        return new List<CallLogRecordFire>();
                    }
                    
                    // Update the cache
                    lock (_callLogsCacheLock)
                    {
                        _cachedCallLogs = callLogResponse.records;
                        _callLogsCacheExpiration = DateTime.UtcNow.Add(_callLogsCacheTTL);
                        Console.WriteLine($"Updated call logs cache with {_cachedCallLogs.Length} records, expires at {_callLogsCacheExpiration}");
                    }
                    
                    callLogRecords = callLogResponse.records;
                }
                
                //Console.WriteLine($"Working with {callLogRecords.Length} total call logs from {(usedCache ? "cache" : "API")}");
                
                // If showAll is true, return all call logs
                if (showAll)
                {
                    //Console.WriteLine("ShowAll is true, returning all call logs");
                    return callLogRecords.ToList();
                }
                
                // Do more aggressive filtering if we have phone numbers
                var filteredLogs = callLogRecords.Where(log => 
                {
                    if (log.from?.phoneNumber == null && log.to?.phoneNumber == null)
                    {
                        return false;
                    }
                    
                    // Normalize the phone numbers from the log
                    string fromNormalized = log.from?.phoneNumber != null ? NormalizePhoneNumber(log.from.phoneNumber) : string.Empty;
                    string toNormalized = log.to?.phoneNumber != null ? NormalizePhoneNumber(log.to.phoneNumber) : string.Empty;
                    
                    // Check if either matches any of our normalized phone numbers
                    bool fromMatch = !string.IsNullOrEmpty(fromNormalized) && 
                        (normalizedPhoneNumbers.Contains(fromNormalized) || 
                         normalizedPhoneNumbers.Any(p => fromNormalized.EndsWith(p) || p.EndsWith(fromNormalized)));
                         
                    bool toMatch = !string.IsNullOrEmpty(toNormalized) && 
                        (normalizedPhoneNumbers.Contains(toNormalized) || 
                         normalizedPhoneNumbers.Any(p => toNormalized.EndsWith(p) || p.EndsWith(toNormalized)));
                    
                    // Also check for exact string matches on the original phone numbers
                    bool exactFromMatch = !string.IsNullOrEmpty(log.from?.phoneNumber) && 
                        phoneNumbers.Contains(log.from.phoneNumber);
                        
                    bool exactToMatch = !string.IsNullOrEmpty(log.to?.phoneNumber) && 
                        phoneNumbers.Contains(log.to.phoneNumber);
                    
                    return fromMatch || toMatch || exactFromMatch || exactToMatch;
                })
                .OrderByDescending(log => log.startTime)
                .ToList();

                return filteredLogs;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error retrieving call logs: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
                return new List<CallLogRecordFire>();
            }
        }
        
        /// <summary>
        /// Invalidates the call logs cache, forcing the next fetch to get fresh data from the API
        /// </summary>
        public void InvalidateCallLogsCache()
        {
            lock (_callLogsCacheLock)
            {
                _cachedCallLogs = null;
                _callLogsCacheExpiration = DateTime.MinValue;
            }
        }
        
        /// <summary>
        /// Normalizes a phone number for consistent comparison
        /// </summary>
        /// <param name="phoneNumber">The phone number to normalize</param>
        /// <returns>Normalized phone number</returns>
        private string NormalizePhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrEmpty(phoneNumber))
                return string.Empty;
                
            // Trim whitespace and remove common separators
            phoneNumber = phoneNumber.Trim().Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "").Replace(".", "");
                
            // Remove any non-digit characters
            var digitsOnly = new string(phoneNumber.Where(char.IsDigit).ToArray());
            
            // Handle different country code formats: +1, 001, 1
            if (digitsOnly.StartsWith("001"))
                digitsOnly = digitsOnly.Substring(3);
                
            // Ensure 10-digit US numbers have country code
            if (digitsOnly.Length == 10)
                return "1" + digitsOnly;
                
            // If it already has country code (11 digits starting with 1)
            if (digitsOnly.Length == 11 && digitsOnly.StartsWith("1"))
                return digitsOnly;
                
            // If it's longer than 11 digits but starts with a country code, try to extract the main number
            if (digitsOnly.Length > 11 && digitsOnly.StartsWith("1"))
                return digitsOnly.Substring(0, 11);
                
            // Otherwise return as is
            return digitsOnly;
        }
    }
}
