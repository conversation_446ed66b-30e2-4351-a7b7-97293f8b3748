using System.Threading;
using System.Threading.Tasks;

namespace Surefire.Domain.Agents.Interfaces
{
    public interface IEmbeddingService
    {
        /// <summary>
        /// Generates an embedding vector for the given text.
        /// </summary>
        /// <param name="text">Input text to embed.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>Embedding vector as float array.</returns>
        Task<float[]> GenerateEmbeddingAsync(string text, CancellationToken cancellationToken = default);
    }
}
