@using Surefire.Domain.Forms.Models
@using Syncfusion.Blazor.RichTextEditor

<div class="template-form">
    <div class="form-top-row">
        <div class="form-group name-field">
            <FluentTextField @bind-Value="Template.Name" Label="Template Name" Required="true" Placeholder="Enter a descriptive name" />
        </div>
        <div class="form-group description-field">
            <FluentTextField @bind-Value="Template.Description" Label="Description" Placeholder="Optional description for internal use" />
        </div>
        <div class="form-group status-toggle">
            <FluentCheckbox @bind-Value="Template.IsActive" Label="Active" />
            <span class="status-help">Inactive templates won't be available for use</span>
        </div>
    </div>
    
    <hr class="form-divider" />
    
    <div class="form-group subject-field">
        <FluentTextField @bind-Value="Template.Subject" Label="Email Subject" Required="true" Placeholder="Enter email subject line" />
    </div>
    
    <div class="form-group">
        <label class="editor-label">Email Body</label>
        <div class="editor-wrapper">
            <SfRichTextEditor @bind-Value="Template.Body" Height="450px">
                <RichTextEditorToolbarSettings Items="@Tools" />
            </SfRichTextEditor>
        </div>
    </div>
    
    <hr class="form-divider" />
</div>

@code {
    [Parameter]
    public EmailTemplate Template { get; set; }
    
    [Parameter]
    public List<ToolbarItemModel> Tools { get; set; }
} 