@namespace Surefire.Domain.Carriers.Components.Dialogs
@using Surefire.Domain.Carriers.Models
@using Surefire.Domain.Carriers.Services
@using Surefire.Domain.Shared.Components
@using Surefire.Domain.Shared.Services
@using Microsoft.FluentUI.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Forms
@using Syncfusion.Blazor.Inputs

<BaseDialog DialogId="@DialogId" 
            Title="@(CurrentCredential?.CredentialId > 0 ? "Edit Credential" : "Add Credential")"
            @bind-Hidden="Hidden">
    <ChildContent>
        @if (EditContext != null)
        {
            <EditForm EditContext="@EditContext">
                <div class="form-group">
                    <SfTextBox @bind-Value="CurrentCredential.Username" Placeholder="Username" FloatLabelType="FloatLabelType.Always" />
                </div>
                <div class="form-group">
                    <SfTextBox @bind-Value="CurrentCredential.Password" Placeholder="Password" FloatLabelType="FloatLabelType.Always" EnablePassword="true" />
                </div>
                <div class="form-group">
                    <SfTextBox @bind-Value="CurrentCredential.Website" Placeholder="Website URL" FloatLabelType="FloatLabelType.Always" />
                </div>
                <div class="form-group">
                    <SfTextBox @bind-Value="CurrentCredential.Notes" Placeholder="Notes" FloatLabelType="FloatLabelType.Always" Multiline="true" />
                </div>
            </EditForm>
        }
    </ChildContent>
    <FooterContent>
        <FluentButton Appearance="Appearance.Accent" OnClick="SaveCredential">Save</FluentButton>
        <FluentButton Appearance="Appearance.Outline" OnClick="CancelDialog">Cancel</FluentButton>
    </FooterContent>
</BaseDialog>

@code {
    [Parameter] public string DialogId { get; set; } = "credential-dialog";
    [Parameter] public bool Hidden { get; set; } = true;
    [Parameter] public EventCallback<bool> HiddenChanged { get; set; }
    [Parameter] public EventCallback<Credential> OnSave { get; set; }
    [Parameter] public int CarrierId { get; set; }
    [Parameter] public Credential? CredentialForEdit { get; set; }

    [Inject] public SurefireDialogService DialogService { get; set; }
    [Inject] public CarrierService CarrierService { get; set; }

    public Credential CurrentCredential { get; private set; } = new Credential();
    public EditContext EditContext { get; private set; }

    protected override async Task OnParametersSetAsync()
    {
        if (!Hidden && (CredentialForEdit != CurrentCredential || EditContext == null)) 
        {
            InitializeDialogState();
        }
        
        await base.OnParametersSetAsync();
    }

    private void InitializeDialogState()
    {
        if (CredentialForEdit != null)
        {
            CurrentCredential = new Credential
            {
                CredentialId = CredentialForEdit.CredentialId,
                Username = CredentialForEdit.Username,
                Password = CredentialForEdit.Password,
                Website = CredentialForEdit.Website,
                Notes = CredentialForEdit.Notes,
                CarrierId = CredentialForEdit.CarrierId,
                DateCreated = CredentialForEdit.DateCreated,
                DateModified = DateTime.UtcNow
            };
        }
        else
        {
            CurrentCredential = new Credential
            {
                CarrierId = CarrierId,
                DateCreated = DateTime.UtcNow,
                DateModified = DateTime.UtcNow
            };
        }

        EditContext = new EditContext(CurrentCredential);
        StateHasChanged();
    }

    private async Task SaveCredential()
    {
        if (EditContext == null) return;

        if (EditContext.Validate())
        {
            try
            {
                if (CurrentCredential.CredentialId > 0)
                {
                    await CarrierService.UpdateCredentialAsync(CurrentCredential);
                }
                else
                {
                    await CarrierService.AddCredentialAsync(CarrierId, CurrentCredential);
                }
                
                await OnSave.InvokeAsync(CurrentCredential);
                
                CancelDialog();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving credential: {ex.Message}");
            }
        }
    }
    
    private void CancelDialog()
    {
        Hidden = true;
        HiddenChanged.InvokeAsync(Hidden);
    }
} 