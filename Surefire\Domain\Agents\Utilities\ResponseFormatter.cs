﻿using System.Text.RegularExpressions;
using Surefire.Domain.Agents.Models;

namespace Surefire.Domain.Agents.Utilities
{
    /// <summary>
    /// Formats responses for display in the UI
    /// </summary>
    public static class ResponseFormatter
    {
        private static readonly Regex BoldMarkdownRegex = new(@"\*\*(.+?)\*\*", RegexOptions.Compiled);
        private static readonly Regex ItalicMarkdownRegex = new(@"\*(.+?)\*", RegexOptions.Compiled);
        private static readonly Regex CodeBlockRegex = new(@"```(.+?)```", RegexOptions.Compiled | RegexOptions.Singleline);
        private static readonly Regex InlineCodeRegex = new(@"`(.+?)`", RegexOptions.Compiled);
        private static readonly Regex QuotedNameRegex = new(@"'([^']+)'", RegexOptions.Compiled);

        /// <summary>
        /// Formats a raw text response with HTML formatting
        /// </summary>
        /// <param name="rawText">The raw text to format</param>
        /// <returns>HTML-formatted text</returns>
        public static string FormatToHtml(string rawText)
        {
            if (string.IsNullOrWhiteSpace(rawText))
            {
                return string.Empty;
            }

            // Convert markdown bold (**text**) to HTML strong tags
            var processed = BoldMarkdownRegex.Replace(rawText, "<strong>$1</strong>");

            // Convert markdown italic (*text*) to HTML em tags
            processed = ItalicMarkdownRegex.Replace(processed, "<em>$1</em>");

            // Convert code blocks (```code```) to HTML pre tags
            processed = CodeBlockRegex.Replace(processed, "<pre><code>$1</code></pre>");

            // Convert inline code (`code`) to HTML code tags
            processed = InlineCodeRegex.Replace(processed, "<code>$1</code>");

            // Convert 'quoted names' to bold
            processed = QuotedNameRegex.Replace(processed, "<strong>$1</strong>");

            // Convert newlines to <br> tags
            processed = processed.Replace("\n", "<br>");

            return processed;
        }

        /// <summary>
        /// Formats a database query result as HTML
        /// </summary>
        /// <param name="data">The data to format</param>
        /// <param name="explanation">Optional explanation of the data</param>
        /// <returns>HTML-formatted table</returns>
        public static string FormatDatabaseResultToHtml(object data, string explanation = null)
        {
            if (data == null)
            {
                return "<p>No data returned.</p>";
            }

            var result = new System.Text.StringBuilder();

            // Add explanation if provided
            if (!string.IsNullOrWhiteSpace(explanation))
            {
                result.Append($"<div class=\"query-explanation\">{explanation}</div>");
            }

            // Format data as table if it's a list of dictionaries
            if (data is List<Dictionary<string, object>> rows && rows.Count > 0)
            {
                result.Append("<table class=\"result-table\">");

                // Add headers
                result.Append("<thead><tr>");
                foreach (var key in rows[0].Keys)
                {
                    result.Append($"<th>{key}</th>");
                }
                result.Append("</tr></thead>");

                // Add data rows
                result.Append("<tbody>");
                foreach (var row in rows)
                {
                    result.Append("<tr>");
                    foreach (var value in row.Values)
                    {
                        result.Append($"<td>{value}</td>");
                    }
                    result.Append("</tr>");
                }
                result.Append("</tbody></table>");
            }
            else
            {
                // Just convert to string if it's not a table structure
                result.Append($"<div class=\"query-result\">{data}</div>");
            }

            return result.ToString();
        }

        /// <summary>
        /// Formats an agent result for display
        /// </summary>
        /// <param name="result">The agent result to format</param>
        /// <returns>Formatted HTML</returns>
        public static string FormatAgentResult(AgentResult result)
        {
            if (result == null)
            {
                return "<p>No result available.</p>";
            }

            var html = new System.Text.StringBuilder();

            // Add status indicator
            html.Append($"<div class=\"agent-result {(result.Success ? "success" : "error")}\">");

            // Add message
            html.Append($"<div class=\"result-message\">{FormatToHtml(result.Message)}</div>");

            // Add payload if available
            if (result.Payload != null)
            {
                html.Append("<div class=\"result-payload\">");
                html.Append(FormatPayload(result.Payload));
                html.Append("</div>");
            }

            //// Add execution time if available
            //if (result.ExecutionTime.HasValue)
            //{
            //    html.Append($"<div class=\"execution-time\">Completed in {result.ExecutionTime.Value.TotalSeconds:F2}s</div>");
            //}

            html.Append("</div>");

            return html.ToString();
        }

        private static string FormatPayload(object payload)
        {
            // Format payload based on its types
            if (payload is string str)
            {
                return FormatToHtml(str);
            }

            // For other types, convert to JSON and format as code
            var json = System.Text.Json.JsonSerializer.Serialize(payload, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            });

            return $"<pre><code>{json}</code></pre>";
        }
    }
}