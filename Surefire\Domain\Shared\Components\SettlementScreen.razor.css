﻿h3, h4 {
    margin-bottom: 10px;
}

div {
    margin-bottom: 20px;
}

label {
    display: inline-block;
    width: 150px;
}

input, select, textarea {
    width: 300px;
    margin-bottom: 10px;
}
.header-card {
    background-color: #ccc;
    font-family: "montserrat", sans-serif;
    font-size: .9rem;
    font-weight: 400;
    color: #626262;
    text-shadow: 1px 1px 1px #ffffffb9;
    text-transform: uppercase;
    padding: 4px 15px 4px 12px;
    border-top-right-radius: 10px;
    letter-spacing: .2em;
    position: absolute;
    top: 0px;
    left: 0px;
    width:100%;
}

.header-acc {
    background-color: #f2f2f2 !important;
    color: #9b9b9bff;
    text-shadow: none;
}
.header-card-tweak {
    background-color: #ccc;
    font-family: "montserrat", sans-serif;
    font-size: .9rem;
    font-weight: 400;
    color: #626262;
    text-shadow: 1px 1px 1px #ffffffb9;
    text-transform: uppercase;
    padding: 4px 15px 4px 12px;
    border-top-right-radius: 10px;
    letter-spacing: .2em;
    position: relative;
    top: -20px;
    left: -21px;
    border-top-left-radius: 10px;
    width: 373px;
    margin-bottom: -15px;
}

.bigfield {
    font-size: 2em !important;
    font-weight: bold !important;
}
.financet {
    text-align: right;
}
.financet2 {
    text-align: right;
    position:relative;
    top:-10px;
}
.lbg {
    height: 20px;
    color: #ffffff64;
    text-align: left;
    font-size: .7em;
    text-transform:uppercase;
}

    .lbg th {
        padding-left: 4px !important;
        color: #979797;
    }

.wh {
    color:#efefef;
}

.billingswitches {
    position: relative;
    top: 8px;
    left: 20px;
    height: 50px;
    overflow: hidden;
    margin-right: 0px !important;
    margin-left: 0px !important;
    margin-top: 0px !important;
    margin-bottom: 15px !important;
    padding: 0px !important;
}
.switchstyle {
    font-size: .9em !important;
    color: #ccc !important;
}

/*//Reciept--------------*/
#settlementmath {
    font-family: "montserrat", sans-serif;
    color: #fff;
    padding-bottom:70px;
}
.rechead {
    font-size:2.2em;
    font-weight:100;
}
.jagged-box {
    width: 200px;
    padding: 20px;
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#1e1d5f+0,7a133e+100 */
    background: linear-gradient(135deg, rgba(30,29,95,1) 0%,rgba(122,19,62,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    border: 1px solid #ccc;
    position: relative;
    clip-path: polygon( 0 0, 100% 0, 100% 90%, 90% 95%, 80% 90%, 70% 95%, 60% 90%, 50% 95%, 40% 90%, 30% 95%, 20% 90%, 10% 95%, 0 90% );
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}
.fl {
    float:left;
}
.fr {
    float:right;
}
.phone-icon-cell {
    width: 40px;
}
.phone-icon {
    position: relative;
    top: 0px;
    left: 5px;
}
.pay-lognum {
    font-size: 1.8em;
    font-weight: 300;
    color: #000;
    min-width: 155px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: bold;
}
.phone-longago {
    
}
.phonetxt {
    font-size: .9em;
    color: #000;
}
.phonetxt2 {
    font-size: 1em;
    font-weight:bold;
    color: #000;
}
.txt-bold {
    font-size:1.1em;
    margin-bottom: 8px !important;
}
.whatever {
    font-size: 1.5em;
    font-weight: bold;
    color: #000;
    padding:0px;
    margin-bottom:5px;
}
