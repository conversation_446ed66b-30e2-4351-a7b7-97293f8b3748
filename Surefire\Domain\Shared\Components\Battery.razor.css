﻿.batteryContainer {
    width: 50px;
    height: 8px;
    border-top: 1px solid #929292;
    border-left: 1px solid #676767;
    border-right: 0px solid #676767;
    border-bottom: 0px solid #676767;
    box-shadow: 2px 2px 4px #00000056;
    /*border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;*/
    position: relative;
    background-color: lightgray;
    overflow: hidden;
}

.batteryFill {
    height: 100%;
    transition: width 0.3s ease-in-out, background-color 0.3s ease-in-out;
}

    .batteryFill.shimmer {
        background-image: linear-gradient(90deg, rgba(0, 255, 0, 0.3) 25%, green 50%, rgba(0, 255, 0, 0.3) 75%);
        background-size: 200% 100%;
        animation: shimmer 2s infinite;
    }

@keyframes shimmer {
    0% {
        background-position: -100% 0;
    }

    100% {
        background-position: 100% 0;
    }
}
