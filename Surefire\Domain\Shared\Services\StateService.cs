using Surefire.Data;
using Surefire.Domain.Plugins;
using Surefire.Domain.Carriers.Models;
using Surefire.Domain.Policies.Models;
using Surefire.Domain.Renewals.Models;
using Surefire.Domain.Shared.Models;
using Surefire.Domain.DocuSign;
using Surefire.Domain.Home.Models;
using Surefire.Domain.Renewals.ViewModels;
using Surefire.Domain.Clients.Models;
using Surefire.Domain.Proposals;
using Surefire.Domain.Chat.Services;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using System.Collections.Concurrent;
using Newtonsoft.Json;

namespace Surefire.Domain.Shared.Services
{
    public class StateService : IDisposable
    {
        // ... (other members remain unchanged)

        // Database context and service provider
        private readonly IServiceProvider _serviceProvider;
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly IConfiguration _configuration;
        private readonly IDocuSignService _docuSignService;

        private readonly Surefire.Domain.Clients.Services.ClientStateService _clientStateService;

        public StateService(IServiceProvider serviceProvider, 
                           IDbContextFactory<ApplicationDbContext> dbContextFactory, 
                           IConfiguration configuration,
                           IDocuSignService docuSignService,
                           Surefire.Domain.Clients.Services.ClientStateService clientStateService)
        {
            _serviceProvider = serviceProvider;
            _dbContextFactory = dbContextFactory;
            _configuration = configuration;
            _docuSignService = docuSignService;
            _clientStateService = clientStateService;
        }

        //=============================================
        //             ** STATIC DATA **              //
        //=============================================
        
        private bool _isInitialized = false;
        public bool IsInitialized => _isInitialized;
        public string DatabaseProvider { get; private set; } = string.Empty;
        public string SurefireVersion = "v0.0.0";

        // Static lists Props ----------------------------------------------------------------//
        private Task<List<Carrier>>? _allCarriersTask;
        private Task<List<Carrier>>? _allWholesalersTask;
        private Task<List<Product>>? _allProductsTask;
        private Task<List<ApplicationUser>>? _allUsersTask;
        public Task<List<Carrier>> AllCarriers => _allCarriersTask ??= LoadCarriersAsync();
        public Task<List<Carrier>> AllWholesalers => _allWholesalersTask ??= LoadWholesalersAsync();
        public Task<List<Product>> AllProducts => _allProductsTask ??= LoadProductsAsync();
        public Task<List<ApplicationUser>> AllUsers => _allUsersTask ??= LoadUsersAsync();

        // Static lists Methods---------------------------------------------------------------//
        private readonly SemaphoreSlim _initializationSemaphore = new(1, 1);

        // --- Last selected submission per renewal ---
        private readonly Dictionary<int, int> _lastSelectedSubmissionPerRenewal = new();
        
        public void SetLastSelectedSubmission(int renewalId, int submissionId)
        {
            _lastSelectedSubmissionPerRenewal[renewalId] = submissionId;
        }

        public int? GetLastSelectedSubmission(int renewalId)
        {
            if (_lastSelectedSubmissionPerRenewal.TryGetValue(renewalId, out int submissionId))
                return submissionId;
            return null;
        }

        // Add TaskCompletionSource for proper initialization signaling
        private readonly TaskCompletionSource<bool> _initializationTcs = new();

        // Property to await initialization
        public Task InitializationTask => _initializationTcs.Task;

        public async Task InitializeStateAsync(Task<AuthenticationState> authStateTask)
        {
            if (_isInitialized)
            {
                return;
            }

            await _initializationSemaphore.WaitAsync();
            try
            {
                // Double-check after acquiring the semaphore
                if (_isInitialized)
                {
                    return;
                }

                // Load system settings first
                var settings = await GetSystemSettingsAsync();
                _disablePlugins = settings?.DisablePlugins ?? false;
    

                // User initialization
                SurefireVersion = _configuration["Surefire:System:Version"] ?? "v0.0.0";
                var authState = await authStateTask;
                var user = authState.User;
                
                using var context = _dbContextFactory.CreateDbContext();
                DatabaseProvider = context.Database.ProviderName ?? string.Empty;
                
                if (user.Identity?.IsAuthenticated == true)
                {
                    var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
                    CurrentUser = await context.Users.FirstOrDefaultAsync(u => u.Id == userId);

                    if (CurrentUser != null)
                    {
                        // Update LastLogin timestamp
                        CurrentUser.LastLogin = DateTime.UtcNow;
                        context.Users.Update(CurrentUser);
                        await context.SaveChangesAsync();
                        
                        // Set HtmlUser after CurrentUser is loaded
                        //HtmlUser = CurrentUser.Id;
                    }
                }

                // CORE INITIALIZATION - Only essential app data that blocks UI
                var coreInitializationTasks = new List<Task>
                {
                    LoadCarriersAsync(),
                    LoadWholesalersAsync(),
                    LoadProductsAsync(),
                    LoadUsersAsync(),
                    SetMostRecentlyOpenedClientIdAsync(),
                    LoadInitialUnreadCountsAsync() // Add initial unread count loading
                };

                await Task.WhenAll(coreInitializationTasks);

                // Mark as initialized BEFORE background tasks
                _isInitialized = true;
                _initializationTcs.SetResult(true);

                // Start background data loading - do NOT await
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await StartBackgroundDataLoading();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Background data loading failed: {ex.Message}");
                    }
                });
            }
            finally
            {
                _initializationSemaphore.Release();
            }
        }
        private async Task<List<Carrier>> LoadCarriersAsync()
        {
            using var context = _dbContextFactory.CreateDbContext();
            try
            {
                return await context.Carriers
                    .AsNoTracking()
                    .Where(c => c.IssuingCarrier)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading carriers: {ex.Message}");
                return new List<Carrier>();
            }
        }
        private async Task<List<Carrier>> LoadWholesalersAsync()
        {
            using var context = _dbContextFactory.CreateDbContext();
            try
            {
                return await context.Carriers
                    .AsNoTracking()
                    .Where(c => c.Wholesaler)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading wholesalers: {ex.Message}");
                return new List<Carrier>();
            }
        }
        private async Task<List<Product>> LoadProductsAsync()
        {
            using var context = _dbContextFactory.CreateDbContext();
            try
            {
                return await context.Products
                    .AsNoTracking()
                    .OrderBy(p => p.LineName)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading products: {ex.Message}");
                return new List<Product>();
            }
        }
        private async Task<List<ApplicationUser>> LoadUsersAsync()
        {
            // Create a fresh context for each request to avoid threading issues
            using var context = _dbContextFactory.CreateDbContext();
            try
            {
                return await context.Users
                    .AsNoTracking() // Use AsNoTracking to prevent EF from tracking these entities
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading users: {ex.Message}");
                return new List<ApplicationUser>();
            }
        }
        // CurrentUser and State Props -----------------------------------------------------------------//
        public ApplicationUser? CurrentUser { get; private set; }
        public int ContactId { get; set; } = 0;
        public int CarrierId { get; set; } = 0;


        //=============================================
        //               RENEWAL STATE               //
        //=============================================
        public int HtmlRenewalId { get; set; } = 0;
        public int HtmlMonth { get; set; } = DateTime.Now.Month;
        public int HtmlYear { get; set; } = DateTime.Now.Year;
        public string HtmlTab { get; set; } = "tab-1";
        public int HtmlSubTaskId { get; set; } = 0;
        public int HtmlTabId { get; set; } = 0;
        public string HtmlUser { get; set; } = "Everyone";
        public string HtmlView { get; set; } = "list";
        public bool IsLoading { get; set; } = false;
        public List<RenewalListItemViewModel> RenewalList { get; set; } = new();
        public List<Policy> PolicyOrphanList { get; set; } = new();
        public Func<int, Task>? LoadRenewalFromSearch { get; set; }


        //=============================================
        //             CLIENTS SCREEN                 //
        //=============================================
        public event Action? OnAttachmentListUpdated;
        public void NotifyAttachmentListUpdated() => OnAttachmentListUpdated?.Invoke();
        public Func<int, Task>? LoadClientFromSearch { get; set; }
        public string ClientTab { get; set; } = "tab-1"; //Depreciated
        public int ClientId { get; set; } = 0; //Depreciated
        public async Task SetMostRecentlyOpenedClientIdAsync()
        {
            using var context = _dbContextFactory.CreateDbContext();
            // Fetch the most recently opened client ID
            var mostRecentClientId = await context.Clients
                .OrderByDescending(c => c.DateOpened)
                .Select(c => (int?)c.ClientId)
                .FirstOrDefaultAsync();

            if (mostRecentClientId.HasValue && mostRecentClientId.Value != 0)
            {
                ClientId = mostRecentClientId.Value;
                _clientStateService.SelectedClientId = mostRecentClientId.Value;
                _clientStateService.ActiveTab = "tab-1";
            }
            else
            {
                // No recent client found, try to get any client
                var anyClientId = await context.Clients
                    .OrderBy(c => c.ClientId)
                    .Select(c => (int?)c.ClientId)
                    .FirstOrDefaultAsync();

                if (anyClientId.HasValue && anyClientId.Value != 0)
                {
                    ClientId = anyClientId.Value;
                    _clientStateService.SelectedClientId = anyClientId.Value;
                    _clientStateService.ActiveTab = "tab-1";
                }
                else
                {
                    ClientId = 0;
                    _clientStateService.SelectedClientId = null;
                    _clientStateService.ActiveTab = "tab-1";
                }
            }
        }

        //=============================================
        //               RECENT PAYMENTS              //
        //=============================================
        private readonly ConcurrentDictionary<string, List<RecentTransactions>> _paymentCache = new();
        private DateTime _lastPaymentFetchTime = DateTime.MinValue;
        private readonly TimeSpan _paymentCacheDuration = TimeSpan.FromMinutes(15);
        public async Task<List<RecentTransactions>> GetRecentPaymentsAsync(CancellationToken cancellationToken)
        {
            var now = DateTime.UtcNow;

            if (_lastPaymentFetchTime.Add(_paymentCacheDuration) < now)
            {
                await RefreshRecentPaymentsAsync(cancellationToken);
            }

            return _paymentCache.Values.SelectMany(transactions => transactions).ToList();
        }
        private async Task RefreshRecentPaymentsAsync(CancellationToken cancellationToken)
        {
            if (DisablePlugins)
            {
                Console.WriteLine("Recent Payments disabled due to PluginsDisabled system setting. ---");
                return;
            }

            var plugin = _serviceProvider.GetService<IPayLogPlugin>();

            if (plugin == null || !plugin.IsActive)
            {
                Console.Error.WriteLine("ePayPolicy plugin not available.");
                return;
            }

            try
            {
                var response = await plugin.GetRecentPayments(cancellationToken);

                if (response != null && response.success)
                {
                    var transactionsResponse = JsonConvert.DeserializeObject<TransactionsResponse>(response.jsonresponse);
                    var transactions = transactionsResponse?.Transactions ?? new List<RecentTransactions>();

                    _paymentCache.Clear();
                    foreach (var transaction in transactions)
                    {
                        var key = transaction.Email ?? "unknown";
                        if (!_paymentCache.ContainsKey(key))
                        {
                            _paymentCache[key] = new List<RecentTransactions>();
                        }

                        _paymentCache[key].Add(transaction);
                    }

                    _lastPaymentFetchTime = DateTime.UtcNow;
                    Console.WriteLine($"Recent payments refreshed with {_paymentCache.Values.Sum(list => list.Count)} transactions.");
                }
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Failed to refresh recent payments: {ex.Message}");
            }
        }
        
        //=============================================
        //             DOCUSIGN ENVELOPES             //
        //=============================================
        private List<EnvelopeInfo>? _docuSignEnvelopesCache;
        private DateTime _lastEnvelopesFetchTime = DateTime.MinValue;
        private readonly TimeSpan _envelopesCacheDuration = TimeSpan.FromMinutes(30);
        
        public async Task<List<EnvelopeInfo>> GetDocuSignEnvelopesAsync(CancellationToken cancellationToken)
        {
            var now = DateTime.UtcNow;

            if (_docuSignEnvelopesCache == null || _lastEnvelopesFetchTime.Add(_envelopesCacheDuration) < now)
            {
                await RefreshDocuSignEnvelopesAsync(cancellationToken);
            }

            return _docuSignEnvelopesCache != null 
                ? _docuSignEnvelopesCache
                    .Where(e => e.CreatedDateTime >= DateTime.Now.AddDays(-30))
                    .OrderByDescending(e => e.StatusChangedDateTime)
                    .ToList()
                : new List<EnvelopeInfo>();
        }
        
        private async Task RefreshDocuSignEnvelopesAsync(CancellationToken cancellationToken)
        {
            if (DisablePlugins)
            {
                Console.WriteLine("DocuSignAPI disabled due to PluginsDisabled system setting. ---"); return;
            }

            try
            {
                // Get access token first
                var accessToken = await _docuSignService.GetAccessTokenAsync();
                
                if (string.IsNullOrEmpty(accessToken))
                {
                    Console.Error.WriteLine("Failed to obtain DocuSign access token.");
                    return;
                }
                
                // Get recent envelopes
                var envelopesJson = await _docuSignService.GetRecentEnvelopesAsync(accessToken);
                
                if (string.IsNullOrEmpty(envelopesJson))
                {
                    Console.Error.WriteLine("Empty response from DocuSign API.");
                    return;
                }
                
                // Parse the JSON response
                var envelopesResponse = System.Text.Json.JsonDocument.Parse(envelopesJson);
                var envelopesList = new List<EnvelopeInfo>();
                
                if (envelopesResponse.RootElement.TryGetProperty("envelopes", out var envelopesArray))
                {
                    foreach (var envelope in envelopesArray.EnumerateArray())
                    {
                        var envelopeInfo = new EnvelopeInfo
                        {
                            EnvelopeId = GetDocuSignStringProperty(envelope, "envelopeId"),
                            Status = GetDocuSignStringProperty(envelope, "status"),
                            EmailSubject = GetDocuSignStringProperty(envelope, "emailSubject"),
                            CreatedDateTime = GetDocuSignDateTimeProperty(envelope, "createdDateTime"),
                            SentDateTime = GetDocuSignDateTimeProperty(envelope, "sentDateTime"),
                            StatusChangedDateTime = GetDocuSignDateTimeProperty(envelope, "statusChangedDateTime")
                        };
                        
                        envelopesList.Add(envelopeInfo);
                    }
                }
                
                _docuSignEnvelopesCache = envelopesList;
                _lastEnvelopesFetchTime = DateTime.UtcNow;
                Console.WriteLine($"DocuSign envelopes refreshed with {_docuSignEnvelopesCache.Count} envelopes.");
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Failed to refresh DocuSign envelopes: {ex.Message}");
                _docuSignEnvelopesCache = new List<EnvelopeInfo>();
            }
        }
        
        private string GetDocuSignStringProperty(System.Text.Json.JsonElement element, string propertyName)
        {
            return element.TryGetProperty(propertyName, out var property) ? property.GetString() ?? string.Empty : string.Empty;
        }
        
        private DateTime GetDocuSignDateTimeProperty(System.Text.Json.JsonElement element, string propertyName)
        {
            if (element.TryGetProperty(propertyName, out var property))
            {
                if (DateTime.TryParse(property.GetString(), out var dateTime))
                {
                    return dateTime;
                }
            }
            return DateTime.MinValue;
        }
        
        //=============================================
        //               STATUS MESSAGE               //
        //=============================================
        public event Action? OnStatusChanged;
        public event Action? OnSectionChanged;
        private string _traceSection = "Home";
        private string _statusMessage = "Loading...";
        private bool _statusLoading = false;
        public string StatusMessage
        {
            get => _statusMessage;
            private set
            {
                if (_statusMessage != value)
                {
                    _statusMessage = value;
                    NotifyStatusChanged();
                }
            }
        }
        public bool StatusLoading
        {
            get => _statusLoading;
            private set
            {
                if (_statusLoading != value)
                {
                    _statusLoading = value;
                    NotifyStatusChanged();
                }
            }
        }
        public void UpdateStatus(string newStatus, bool? isLoading = null)
        {
            StatusMessage = newStatus;
            if (isLoading.HasValue)
            {
                StatusLoading = isLoading.Value;
            }
        }
        private void NotifyStatusChanged() => OnStatusChanged?.Invoke();



        //=============================================
        //                  PLUGINS                  //
        //=============================================
        // Runs a specific method on all plugins or a specific plugin
        public event Func<string, Task>? OnPluginEventTriggered;
        public async Task TriggerPluginEvent(string eventName)
        {
            if (DisablePlugins)
            {
                Console.WriteLine("Plugins are current disabled via system settings.");
                return;
            }

            if (OnPluginEventTriggered != null)
            {
                await OnPluginEventTriggered.Invoke(eventName);
            }
        }

        public async Task<List<PluginMethodResponse>> RunPluginMethodAsync(string methodName, object[] parameters, CancellationToken cancellationToken)
        {
            if (DisablePlugins)
            {
                Console.WriteLine("RunPlugin disabled because of plugin disabled setting.");
                return new List<PluginMethodResponse>();
            }

            var plugins = _serviceProvider.GetServices<IPlugin>().Where(plugin => plugin.IsActive).ToList();
            
            if (!plugins.Any())
            {
                Console.Error.WriteLine("PLUGIN: No active plugins available.");
                return new List<PluginMethodResponse>();
            }

            var responses = new List<PluginMethodResponse>();
            foreach (var plugin in plugins)
            {
                try
                {
                    var response = await plugin.ExecuteAsync(methodName, parameters, cancellationToken);
                    responses.Add(response);
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Error executing {methodName} on plugin {plugin.Name}: {ex.Message}");
                    responses.Add(new PluginMethodResponse { success = false, message = ex.Message });
                }
            }

            return responses;
        }


        //=============================================
        //              SYSTEM SETTINGS              //
        //=============================================
        private bool _disablePlugins;
        public bool DisablePlugins => _disablePlugins;

        public async Task<Settings?> GetSystemSettingsAsync()
        {
            try
            {
                using var context = _dbContextFactory.CreateDbContext();
                var settings = await context.Settings
                    .OrderBy(s => s.SettingsId) // Add ordering to prevent EF warning
                    .FirstOrDefaultAsync();
                _disablePlugins = settings?.DisablePlugins ?? false;
                return settings;
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error fetching system settings: {ex.Message}");
                return null;
            }
        }

        public async Task SaveSystemSettingsAsync(Settings settings)
        {
            try
            {
                using var context = _dbContextFactory.CreateDbContext();
                var existingSettings = await context.Settings
                    .OrderBy(s => s.SettingsId) // Add ordering to prevent EF warning
                    .FirstOrDefaultAsync();

                if (existingSettings == null)
                {
                    // If no settings exist, add the new settings
                    context.Settings.Add(settings);
                }
                else
                {
                    // Update existing settings
                    existingSettings.OpenAiApiKey = settings.OpenAiApiKey;
                    existingSettings.DbType = settings.DbType;
                    existingSettings.DbConnectionString = settings.DbConnectionString;
                    existingSettings.PayLinkStringTemplate = settings.PayLinkStringTemplate;
                    existingSettings.AzureBlobConnectionString = settings.AzureBlobConnectionString;
                    existingSettings.AzureBlobContainerName = settings.AzureBlobContainerName;
                    existingSettings.FileServerMappedPath = settings.FileServerMappedPath;
                    existingSettings.DisablePlugins = settings.DisablePlugins;

                    context.Settings.Update(existingSettings);
                }

                // Update the local state before saving to DB
                _disablePlugins = settings.DisablePlugins;
                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error saving system settings: {ex.Message}");
                throw; // Re-throw the exception to handle it in the UI
            }
        }

        public async Task LoadClient(int clientId)
        {
            ClientId = clientId;
            // Notify any components that need to refresh their client data
            if (OnClientUpdated != null)
            {
                await OnClientUpdated.Invoke(clientId);
            }
        }

        // Add an event that components can subscribe to
        public event Func<int, Task>? OnClientUpdated;

        // Client cache invalidation methods
        public async Task InvalidateClientsCacheAsync()
        {
            await _clientStateService.InvalidateClientsCacheAsync();
        }

        public async Task InvalidateFormPdfsCacheAsync()
        {
            await _clientStateService.InvalidateFormPdfsCacheAsync();
        }
        
        public void Dispose()
        {
            _initializationSemaphore?.Dispose();
            _homepageDataSemaphore?.Dispose();
        }

        //=============================================
        //               HOMEPAGE DATA CACHE          //
        //=============================================
        private readonly SemaphoreSlim _homepageDataSemaphore = new(1, 1);
        private DateTime _lastHomepageDataFetchTime = DateTime.MinValue;
        private readonly TimeSpan _homepageDataCacheDuration = TimeSpan.FromMinutes(5); // Refresh every 5 minutes

        // Cached homepage data properties
        private List<HomePageRenFlowTasksViewModel>? _cachedRenFlowTasks;
        private List<HomePageTasksViewModel>? _cachedIncompleteTasks;
        private List<DailyTask>? _cachedDailyTasks;
        private List<DailyTask>? _cachedDailyTasksCompleted;
        private List<Policy>? _cachedUpcomingRenewals;
        private List<Lead>? _cachedLeads;
        private List<Proposal>? _cachedProposals;

        // Public properties to access cached data
        public List<HomePageRenFlowTasksViewModel> CachedRenFlowTasks => _cachedRenFlowTasks ?? new();
        public List<HomePageTasksViewModel> CachedIncompleteTasks => _cachedIncompleteTasks ?? new();
        public List<DailyTask> CachedDailyTasks => _cachedDailyTasks ?? new();
        public List<DailyTask> CachedDailyTasksCompleted => _cachedDailyTasksCompleted ?? new();
        public List<Policy> CachedUpcomingRenewals => _cachedUpcomingRenewals ?? new();
        public List<Lead> CachedLeads => _cachedLeads ?? new();
        public List<Proposal> CachedProposals => _cachedProposals ?? new();

        // Event to notify when homepage data is updated
        public event Action? OnHomepageDataUpdated;

        public bool IsHomepageDataCached => _cachedRenFlowTasks != null;

        // Chat/Messaging State Management ------------------------------------------------//
        private int _unreadStaffMessageCount = 0;
        private int _unreadSmsMessageCount = 0;
        private bool _isChopperVisible = false;
        private Dictionary<string, int> _unreadSmsCountsByPhone = new();
        
        public int UnreadStaffMessageCount => _unreadStaffMessageCount;
        public int UnreadSmsMessageCount => _unreadSmsMessageCount;
        public bool IsChopperVisible => _isChopperVisible;
        public event Action? OnUnreadStaffMessageCountChanged;
        public event Action? OnUnreadSmsMessageCountChanged;
        public event Action? OnChopperVisibilityChanged;

        public void IncrementUnreadStaffMessages()
        {
            _unreadStaffMessageCount++;
            OnUnreadStaffMessageCountChanged?.Invoke();
        }

        public void ResetUnreadStaffMessages()
        {
            _unreadStaffMessageCount = 0;
            OnUnreadStaffMessageCountChanged?.Invoke();
        }

        public void IncrementUnreadSmsMessages(string phoneNumber)
        {
            _unreadSmsMessageCount++;
            if (!_unreadSmsCountsByPhone.ContainsKey(phoneNumber))
            {
                _unreadSmsCountsByPhone[phoneNumber] = 0;
            }
            _unreadSmsCountsByPhone[phoneNumber]++;
            OnUnreadSmsMessageCountChanged?.Invoke();
        }

        public void ResetUnreadSmsMessages(string? phoneNumber = null)
        {
            if (phoneNumber == null)
            {
                // Reset all SMS unread counts
                _unreadSmsMessageCount = 0;
                _unreadSmsCountsByPhone.Clear();
            }
            else
            {
                // Reset specific phone number
                if (_unreadSmsCountsByPhone.ContainsKey(phoneNumber))
                {
                    _unreadSmsMessageCount -= _unreadSmsCountsByPhone[phoneNumber];
                    _unreadSmsCountsByPhone[phoneNumber] = 0;
                }
            }
            OnUnreadSmsMessageCountChanged?.Invoke();
        }

        public int GetUnreadSmsCountForPhone(string phoneNumber)
        {
            return _unreadSmsCountsByPhone.TryGetValue(phoneNumber, out int count) ? count : 0;
        }

        /// <summary>
        /// Updates the unconfirmed SMS counts from the database
        /// </summary>
        public void UpdateUnconfirmedSmsCounts(Dictionary<string, int> unconfirmedCounts)
        {
            _unreadSmsCountsByPhone.Clear();
            foreach (var kvp in unconfirmedCounts)
            {
                _unreadSmsCountsByPhone[kvp.Key] = kvp.Value;
            }
            
            _unreadSmsMessageCount = _unreadSmsCountsByPhone.Values.Sum();
            OnUnreadSmsMessageCountChanged?.Invoke();
        }

        /// <summary>
        /// Gets the current unconfirmed SMS counts by phone number
        /// </summary>
        public Dictionary<string, int> GetUnconfirmedSmsCounts()
        {
            return new Dictionary<string, int>(_unreadSmsCountsByPhone);
        }

        public void SetChopperVisibility(bool isVisible)
        {
            _isChopperVisible = isVisible;
            OnChopperVisibilityChanged?.Invoke();
        }

        public async Task<bool> GetHomepageDataAsync(CancellationToken cancellationToken = default)
        {
            var now = DateTime.UtcNow;
            
            // Always refresh renflowtasks
            await RefreshRenFlowTasksAsync(cancellationToken);
            
            // Check if we need to refresh the rest of the cache
            if (_lastHomepageDataFetchTime.Add(_homepageDataCacheDuration) < now || !IsHomepageDataCached)
            {
                return await RefreshHomepageDataAsync(cancellationToken);
            }
            
            return true; // Data is already cached and fresh
        }

        public async Task<bool> RefreshHomepageDataAsync(CancellationToken cancellationToken = default)
        {
            if (CurrentUser == null) return false;
            
            await _homepageDataSemaphore.WaitAsync(cancellationToken);
            try
            {
                var homeService = _serviceProvider.GetRequiredService<HomeService>();
                var proposalService = _serviceProvider.GetRequiredService<ProposalService>();
                
                // Load all homepage data in parallel
                var loadingTasks = new List<Task>
                {
                    LoadCachedTaskData(homeService, cancellationToken),
                    LoadCachedRenewalData(homeService, cancellationToken),
                    LoadCachedProposalData(proposalService, cancellationToken),
                    LoadCachedUserSpecificData(homeService, cancellationToken)
                };

                await Task.WhenAll(loadingTasks);
                
                _lastHomepageDataFetchTime = DateTime.UtcNow;
                
                // Notify subscribers that data has been updated
                OnHomepageDataUpdated?.Invoke();
                
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error refreshing homepage data: {ex.Message}");
                return false;
            }
            finally
            {
                _homepageDataSemaphore.Release();
            }
        }

        private async Task LoadCachedTaskData(HomeService homeService, CancellationToken cancellationToken)
        {
            try
            {
                var renflowtasksTask = homeService.GetHomePageRenFlowTasksAsync();
                var incompleteTasksTask = homeService.GetIncompleteTasksForCurrentUserAsync();
                var dailyTasksTask = homeService.GetDailyTasksAsync();
                var dailyTasksCompletedTask = homeService.GetDailyCompletedTasksAsync();

                await Task.WhenAll(renflowtasksTask, incompleteTasksTask, dailyTasksTask, dailyTasksCompletedTask);
                
                _cachedRenFlowTasks = await renflowtasksTask;
                _cachedIncompleteTasks = await incompleteTasksTask;
                _cachedDailyTasks = await dailyTasksTask;
                _cachedDailyTasksCompleted = await dailyTasksCompletedTask;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading cached task data: {ex.Message}");
                _cachedRenFlowTasks = new();
                _cachedIncompleteTasks = new();
                _cachedDailyTasks = new();
                _cachedDailyTasksCompleted = new();
            }
        }

        private async Task LoadCachedRenewalData(HomeService homeService, CancellationToken cancellationToken)
        {
            try
            {
                _cachedUpcomingRenewals = await homeService.GetUpcomingRenewalsAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading cached renewal data: {ex.Message}");
                _cachedUpcomingRenewals = new();
            }
        }

        private async Task LoadCachedProposalData(ProposalService proposalService, CancellationToken cancellationToken)
        {
            try
            {
                _cachedProposals = await proposalService.GetProposalHomepageListAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading cached proposal data: {ex.Message}");
                _cachedProposals = new();
            }
        }

        private async Task LoadCachedUserSpecificData(HomeService homeService, CancellationToken cancellationToken)
        {
            if (CurrentUser?.UserName != "<EMAIL>")
            {
                _cachedLeads = new();
                return;
            }

            try
            {
                _cachedLeads = await homeService.GetAllLeadsAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading cached user-specific data: {ex.Message}");
                _cachedLeads = new();
            }
        }

        // Method to invalidate homepage cache when data changes
        public void InvalidateHomepageCache()
        {
            _lastHomepageDataFetchTime = DateTime.MinValue;
        }

        // Method to force refresh homepage data
        public async Task<bool> ForceRefreshHomepageDataAsync(CancellationToken cancellationToken = default)
        {
            _lastHomepageDataFetchTime = DateTime.MinValue;
            return await RefreshHomepageDataAsync(cancellationToken);
        }

        public async Task RefreshRenFlowTasksAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var homeService = _serviceProvider.GetRequiredService<HomeService>();
                _cachedRenFlowTasks = await homeService.GetHomePageRenFlowTasksAsync();
                OnHomepageDataUpdated?.Invoke();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error refreshing renflow tasks: {ex.Message}");
                _cachedRenFlowTasks = new();
            }
        }

        public void UpdateCachedRenFlowTasks(List<HomePageRenFlowTasksViewModel> tasks)
        {
            _cachedRenFlowTasks = tasks;
            OnHomepageDataUpdated?.Invoke();
        }

        private async Task StartBackgroundDataLoading()
        {
            // Background tasks that don't block app startup
            var backgroundTasks = new List<Task>
            {
                RefreshRecentPaymentsAsync(CancellationToken.None),
                RefreshDocuSignEnvelopesAsync(CancellationToken.None)
                // Note: Removed RefreshHomepageDataAsync - this should be loaded on-demand
            };

            // Run background tasks but don't throw exceptions to the UI
            await Task.WhenAll(backgroundTasks.Select(async task =>
            {
                try
                {
                    await task;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Background task failed: {ex.Message}");
                }
            }));

            Console.WriteLine("Background data loading completed");
        }

        // Method to load initial unread counts from the database
        private async Task LoadInitialUnreadCountsAsync()
        {
            try
            {
                // Create a scope to resolve SmsMessageService
                using var scope = _serviceProvider.CreateScope();
                var smsMessageService = scope.ServiceProvider.GetRequiredService<SmsMessageService>();
                
                // Get all unconfirmed counts by phone number
                var unconfirmedCounts = await smsMessageService.GetUnconfirmedCountsByPhoneAsync();
                
                // Update StateService with current unconfirmed counts
                UpdateUnconfirmedSmsCounts(unconfirmedCounts);
                
                Console.WriteLine($"Loaded initial unread counts: {unconfirmedCounts.Count} phone numbers with unconfirmed messages");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading initial unconfirmed SMS counts: {ex.Message}");
            }
        }
    }
    
    // EnvelopeInfo class to match what the component uses
    public class EnvelopeInfo
    {
        public string EnvelopeId { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string EmailSubject { get; set; } = string.Empty;
        public DateTime CreatedDateTime { get; set; }
        public DateTime SentDateTime { get; set; }
        public DateTime StatusChangedDateTime { get; set; }
    }
}
