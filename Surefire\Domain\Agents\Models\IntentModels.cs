using System.Text.Json.Serialization;

namespace Surefire.Domain.Agents.Models
{
    /// <summary>
    /// Represents the different types of user intents that can be detected
    /// </summary>
    public enum IntentType
    {
        /// <summary>
        /// Commands that trigger business workflows or agent actions
        /// Examples: "Send loss run for Acme Corp", "Request certificates for Pacific Security"
        /// </summary>
        AgentAction,

        /// <summary>
        /// Questions about data in the system that require database queries
        /// Examples: "How many policies does Acme have?", "What carriers do we work with?"
        /// </summary>
        DatabaseQuery,

        /// <summary>
        /// General knowledge or conversational queries
        /// Examples: "What is workers compensation?", "Explain general liability"
        /// </summary>
        GeneralAI,

        /// <summary>
        /// Navigation requests to different parts of the application
        /// Examples: "Open client screen for TWS", "Show policies for Baron's Heating"
        /// </summary>
        Navigation,

        /// <summary>
        /// Unknown or ambiguous intent that requires clarification
        /// </summary>
        Unknown
    }

    /// <summary>
    /// Result of intent classification analysis
    /// </summary>
    public class IntentClassificationResult
    {
        /// <summary>
        /// The detected intent type
        /// </summary>
        public IntentType Intent { get; set; }

        /// <summary>
        /// Confidence score between 0.0 and 1.0
        /// </summary>
        public double Confidence { get; set; }

        /// <summary>
        /// Explanation of why this intent was chosen
        /// </summary>
        public string Reasoning { get; set; } = string.Empty;
    }

    /// <summary>
    /// Request for unified input processing
    /// </summary>
    public class UnifiedRequest
    {
        /// <summary>
        /// The user's input text
        /// </summary>
        public string Input { get; set; } = string.Empty;

        /// <summary>
        /// ID of the user making the request
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// Session ID for conversation context
        /// </summary>
        public string SessionId { get; set; } = string.Empty;

        /// <summary>
        /// Additional context from previous interactions
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new();
    }

    /// <summary>
    /// Response from unified input processing
    /// </summary>
    public class UnifiedResponse
    {
        /// <summary>
        /// Whether the request was processed successfully
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// The detected intent type
        /// </summary>
        public IntentType DetectedIntent { get; set; }

        /// <summary>
        /// The main response text
        /// </summary>
        public string Response { get; set; } = string.Empty;

        /// <summary>
        /// Additional structured data (e.g., query results, agent outputs)
        /// </summary>
        public object? Data { get; set; }

        /// <summary>
        /// Suggested follow-up actions or questions
        /// </summary>
        public List<string> Suggestions { get; set; } = new();

        /// <summary>
        /// Time taken to process the request
        /// </summary>
        public TimeSpan ExecutionTime { get; set; }

        /// <summary>
        /// Error message if processing failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Confidence score of the intent detection
        /// </summary>
        public double IntentConfidence { get; set; }
    }

    /// <summary>
    /// Streaming response chunk for real-time updates
    /// </summary>
    public class StreamingResponseChunk
    {
        /// <summary>
        /// Type of the chunk (status, partial_response, final_response, error)
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// Content of the chunk
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// Additional data for the chunk
        /// </summary>
        public object? Data { get; set; }

        /// <summary>
        /// Whether this is the final chunk
        /// </summary>
        public bool IsFinal { get; set; }
    }
}