using Microsoft.EntityFrameworkCore;
using Surefire.Data;
using Surefire.Domain.Chat;
using Surefire.Domain.Shared.Services;
using Surefire.Domain.Shared.Helpers;

namespace Surefire.Domain.Chat.Services
{
    public class SmsMessageService
    {
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly StateService _stateService;

        public SmsMessageService(IDbContextFactory<ApplicationDbContext> dbContextFactory, StateService stateService)
        {
            _dbContextFactory = dbContextFactory;
            _stateService = stateService;
        }

        /// <summary>
        /// Stores an SMS message in the database
        /// </summary>
        public async Task<SmsMessageEntity?> StoreSmsMessageAsync(SmsMessage message)
        {
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                
                // Check if message already exists
                var existingMessage = await context.SmsMessages
                    .FirstOrDefaultAsync(m => m.RingCentralId == message.Id);

                if (existingMessage != null)
                {
                    return existingMessage;
                }

                // Create new message entity with normalized phone number
                var messageEntity = new SmsMessageEntity
                {
                    RingCentralId = message.Id,
                    PhoneNumber = StringHelper.NormalizePhoneNumber(message.PhoneNumber),
                    Text = message.Text,
                    Timestamp = message.Timestamp,
                    IsInbound = message.IsInbound,
                    ConfirmedBy = null,
                    ConfirmedOn = null
                };

                context.SmsMessages.Add(messageEntity);
                await context.SaveChangesAsync();

                return messageEntity;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error storing SMS message: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Confirms an SMS message by setting the confirmed user and timestamp
        /// </summary>
        public async Task<bool> ConfirmSmsMessageAsync(string ringCentralId, string userId)
        {
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                
                var message = await context.SmsMessages
                    .Include(m => m.ConfirmedByUser)
                    .FirstOrDefaultAsync(m => m.RingCentralId == ringCentralId);

                if (message == null)
                {
                    Console.WriteLine($"Message with RingCentralId {ringCentralId} not found");
                    return false;
                }

                // Check if already confirmed
                if (message.ConfirmedBy != null)
                {
                    Console.WriteLine($"Message {ringCentralId} is already confirmed by {message.ConfirmedBy}");
                    return true; // Return true since it's already confirmed
                }

                message.ConfirmedBy = userId;
                message.ConfirmedOn = DateTime.UtcNow;

                await context.SaveChangesAsync();

                // Update unread counts in StateService
                await UpdateUnreadCountsAsync();

                Console.WriteLine($"Successfully confirmed message {ringCentralId} by user {userId}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error confirming SMS message: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets the count of unconfirmed SMS messages for a specific phone number
        /// </summary>
        public async Task<int> GetUnconfirmedCountAsync(string phoneNumber)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            var normalizedPhone = StringHelper.NormalizePhoneNumber(phoneNumber);
            return await context.SmsMessages
                .CountAsync(m => m.PhoneNumber == normalizedPhone && m.IsInbound && m.ConfirmedBy == null);
        }

        /// <summary>
        /// Gets all phone numbers with unconfirmed messages and their counts
        /// </summary>
        public async Task<Dictionary<string, int>> GetUnconfirmedCountsByPhoneAsync()
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            var results = await context.SmsMessages
                .Where(m => m.IsInbound && m.ConfirmedBy == null)
                .GroupBy(m => m.PhoneNumber)
                .Select(g => new { PhoneNumber = g.Key, Count = g.Count() })
                .ToListAsync();

            return results.ToDictionary(r => r.PhoneNumber, r => r.Count);
        }

        /// <summary>
        /// Updates the unread counts in StateService based on database state
        /// </summary>
        private async Task UpdateUnreadCountsAsync()
        {
            try
            {
                var unconfirmedCounts = await GetUnconfirmedCountsByPhoneAsync();
                
                // Update StateService with current unconfirmed counts
                _stateService.UpdateUnconfirmedSmsCounts(unconfirmedCounts);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating unread counts: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets SMS messages for a conversation with confirmation status
        /// </summary>
        public async Task<List<SmsMessageEntity>> GetConversationMessagesAsync(string phoneNumber)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            var normalizedPhone = StringHelper.NormalizePhoneNumber(phoneNumber);
            var messages = await context.SmsMessages
                .Include(m => m.ConfirmedByUser)
                .Where(m => m.PhoneNumber == normalizedPhone)
                .OrderBy(m => m.Timestamp)
                .ToListAsync();

            // Ensure ConfirmedByUser is loaded for all confirmed messages
            await EnsureConfirmedByUserLoadedAsync(messages);
            
            return messages;
        }

        /// <summary>
        /// Ensures that ConfirmedByUser is properly loaded for all confirmed messages
        /// </summary>
        private async Task EnsureConfirmedByUserLoadedAsync(List<SmsMessageEntity> messages)
        {
            try
            {
                // Get all unique user IDs that need to be loaded
                var userIdsToLoad = messages
                    .Where(m => m.ConfirmedBy != null && m.ConfirmedByUser == null)
                    .Select(m => m.ConfirmedBy)
                    .Distinct()
                    .ToList();

                if (!userIdsToLoad.Any())
                    return;

                // Load all users in a single query
                using var context = await _dbContextFactory.CreateDbContextAsync();
                var users = await context.Users
                    .Where(u => userIdsToLoad.Contains(u.Id))
                    .ToDictionaryAsync(u => u.Id, u => u);

                // Assign users to messages
                foreach (var message in messages.Where(m => m.ConfirmedBy != null && m.ConfirmedByUser == null))
                {
                    if (users.TryGetValue(message.ConfirmedBy, out var user))
                    {
                        message.ConfirmedByUser = user;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading confirmed by user data: {ex.Message}");
            }
        }
    }
} 