﻿@page "/Renewals/CreateFromPolicyId/{PolicyId:int}"
@using Surefire.Domain.Renewals.Models
@using Surefire.Domain.Renewals.Services
@using Surefire.Domain.Policies.Models
@using Surefire.Domain.Policies.Services
@using Surefire.Domain.Shared.Services
@using Microsoft.FluentUI.AspNetCore.Components
@using Microsoft.EntityFrameworkCore
@using Surefire.Data
@using System
@inject TaskService TaskService
@inject PolicyService PolicyService
@inject RenewalService RenewalService
@inject NavigationManager NavManager
@inject IDbContextFactory<ApplicationDbContext> _dbContextFactory
@inject StateService _stateService

<div class="container">
    @if (loading)
    {
        <div class="loading-indicator">
            <p>Loading...</p>
        </div>
    }
    else if (policy != null)
    {
        <h2>Create Renewal from Policy</h2>
        <div class="policy-details">
            <h3>Policy Details</h3>
            <div class="detail-row">
                <div class="detail-label">Policy Number:</div>
                <div class="detail-value">@policy.PolicyNumber</div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Client:</div>
                <div class="detail-value">@policy.Client?.Name</div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Effective Date:</div>
                <div class="detail-value">@policy.EffectiveDate.ToShortDateString()</div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Expiration Date:</div>
                <div class="detail-value">@policy.ExpirationDate.ToShortDateString()</div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Premium:</div>
                <div class="detail-value">@policy.Premium.ToString("C")</div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Carrier:</div>
                <div class="detail-value">@policy.Carrier?.CarrierName</div>
            </div>
        </div>

        <div class="renewal-form">
            <h3>Create Renewal for this Policy</h3>
            <div class="form-group">
                <label for="taskGroupSelect">Renewal Type:</label>
                <FluentSelect TOption="TaskGroup"
                    Label="Renewal Type:"
                    Items="@AllTaskGroups"
                    @bind-SelectedOption="selectedTaskGroup"
                    OptionText="@(tg => tg.Name)"
                    OptionValue="@(tg => tg.TaskGroupId.ToString())"
                    Style="min-width: 300px;"
                    Placeholder="-- Select a Task Group --"/>
            </div>

            <div class="button-row">
                <FluentButton OnClick="CancelRenewal">Cancel</FluentButton>
                <FluentButton Appearance="Appearance.Accent" OnClick="CreateRenewal">Continue</FluentButton>
            </div>
        </div>
    }
    else
    {
        <div class="error-message">
            <p>Policy not found</p>
        </div>
    }
</div>

<style>
    .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    height: 100vh;
    }
    .policy-details {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    }
    .detail-row {
    display: flex;
    margin-bottom: 8px;
    }
    .detail-label {
    font-weight: bold;
    width: 150px;
    }
    .renewal-form {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    }
    .form-group {
    margin-bottom: 15px;
    }
    .form-control {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    }
    .button-row {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    }
    .btn {
    padding: 8px 16px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    }
    .btn-primary {
    background-color: #007bff;
    color: white;
    }
    .btn-secondary {
    background-color: #6c757d;
    color: white;
    }
    .loading-indicator, .error-message {
    text-align: center;
    padding: 20px;
    }
</style>

@code {
    [Parameter] public int PolicyId { get; set; }

    private Policy policy;
    private bool loading = true;
    private TaskGroup selectedTaskGroup { get; set; }
    private int? existingRenewalId;

    public List<TaskGroup> AllTaskGroups { get; set; } = new List<TaskGroup>();

    protected override async Task OnInitializedAsync()
    {
        loading = true;
        AllTaskGroups = await TaskService.GetAllTaskGroupsAsync();

        try {
            existingRenewalId = await CheckForExistingRenewal();

            if (existingRenewalId.HasValue)
            {
                NavManager.NavigateTo($"/Renewals/Details/{existingRenewalId}");
                return;
            }

            // Load the policy details
            policy = await PolicyService.GetPolicyByIdAsync(PolicyId);

        } catch (Exception ex) {
            Console.WriteLine($"Error: {ex.Message}");
        } finally {
            loading = false;
        }
    }

    private async Task<int?> CheckForExistingRenewal()
    {
        using var context = await _dbContextFactory.CreateDbContextAsync();
        var existingRenewal = await context.Renewals
            .Where(r => r.PolicyId == PolicyId)
            .Select(r => new { r.RenewalId })
            .FirstOrDefaultAsync();

        return existingRenewal?.RenewalId;
    }

    private void CancelRenewal()
    {

        if (policy != null)
        {
            NavManager.NavigateTo($"/Clients/{policy.ClientId}");
        }
        else
        {
            NavManager.NavigateTo("/Renewals");
        }
    }

    private async Task CreateRenewal()
    {
        if (selectedTaskGroup == null)
        {
            Console.WriteLine("No task group selected");
            return;
        }

        Console.WriteLine($"Creating renewal for policy #{policy.PolicyId} with task group {selectedTaskGroup.TaskGroupId}");
        if (policy == null) return;
        
        try
        {
            int renewalId = await RenewalService.CreateRenewalWithTaskGroupAsync(PolicyId, selectedTaskGroup.TaskGroupId);
            NavManager.NavigateTo($"/Renewals/Details/{renewalId}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error creating renewal: {ex.Message}");
        }
    }
}
