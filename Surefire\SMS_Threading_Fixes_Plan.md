# SMS Threading & Architecture Fixes Plan

## Overview
The SMS messaging system has critical threading issues and architectural coupling problems that need to be addressed systematically. This plan outlines the fixes needed for thread safety, proper separation of concerns, and elimination of race conditions.

## Priority 1: Critical Threading Issues (Fix Immediately)

### 1.1 StateService Thread Safety
**Problem**: `_unreadSmsCountsByPhone` is a plain Dictionary accessed from multiple threads
**Fix**: Replace with thread-safe collections
- [ ] Replace `Dictionary<string, int> _unreadSmsCountsByPhone` with `ConcurrentDictionary<string, int>`
- [ ] Add proper locking around all read/write operations for SMS counts
- [ ] Create thread-safe methods for updating SMS counts

### 1.2 ChopperMessaging Collections Thread Safety
**Problem**: `ObservableCollection<MessageItem> messages` and `List<SmsMessageEntity> smsMessages` modified from multiple threads
**Fix**: Implement proper thread-safe patterns
- [ ] Replace `ObservableCollection<MessageItem>` with thread-safe backing store
- [ ] Use `ConcurrentQueue<MessageItem>` for staff messages
- [ ] Create UI-thread-only methods for updating collections
- [ ] Always use `InvokeAsync(StateHasChanged)` when updating from background threads
- [ ] Copy items to local collections before rendering

### 1.3 Optimistic Message Dictionary Thread Safety
**Problem**: `optimisticMessageTimes` Dictionary accessed from multiple threads
**Fix**: Thread-safe implementation
- [ ] Replace with `ConcurrentDictionary<string, DateTime>`
- [ ] Add proper cleanup methods with thread safety

## Priority 2: DbContext Re-entrancy Issues

### 2.1 Single DbContext Operations
**Problem**: Multiple operations on same DbContext instance causing "A second operation was started" errors
**Fix**: Ensure one operation per context
- [ ] Audit all DbContext usage in components
- [ ] Replace any direct `ApplicationDbContext` injection with `IDbContextFactory`
- [ ] Ensure each operation gets fresh context via `using var context = await factory.CreateDbContextAsync()`
- [ ] Never reuse DbContext across awaits

### 2.2 SmsMessageService Context Management
**Problem**: Some methods may still have context overlap
**Fix**: Verify and fix context usage
- [ ] Review all methods in `SmsMessageService` for proper context disposal
- [ ] Ensure each database operation uses its own context instance
- [ ] Add logging to track context creation/disposal

## Priority 3: Background Service & Event Handler Issues

### 3.1 Async Void Event Handlers
**Problem**: `OnNewSmsMessage` is `async void` - exceptions can crash process
**Fix**: Use proper async Task pattern
- [ ] Change `OnNewSmsMessage` to `async Task`
- [ ] Store returned tasks and await them properly
- [ ] Add proper exception handling and logging

### 3.2 Timer Thread Safety
**Problem**: Timer callbacks modify UI state from background threads
**Fix**: Proper thread marshaling
- [ ] Ensure all timer callbacks use `InvokeAsync` for UI updates
- [ ] Add proper timer disposal in component cleanup
- [ ] Prevent timer callbacks after component disposal

## Priority 4: SignalR Group Management

### 4.1 Group Membership Leaks
**Problem**: Joining SMS chat groups without leaving previous ones
**Fix**: Proper group lifecycle management
- [ ] Track current SMS group membership
- [ ] Leave previous group before joining new one
- [ ] Add cleanup on component disposal
- [ ] Add group management logging

### 4.2 Connection Lifecycle
**Problem**: Incomplete connection cleanup
**Fix**: Robust connection management
- [ ] Ensure all groups are left on disconnect
- [ ] Add reconnection group re-joining logic
- [ ] Handle connection state properly

## Priority 5: Architectural Decoupling

### 5.1 Separate Unread Count Management
**Problem**: Unread count logic tightly coupled with SignalR/API
**Fix**: Create dedicated service layer
- [ ] Create `ISmsUnreadCountService` interface
- [ ] Implement `SmsUnreadCountService` with thread-safe operations
- [ ] Move all count logic out of StateService and components
- [ ] Create clean events for count updates

### 5.2 Message State Management
**Problem**: Message state scattered across multiple services
**Fix**: Centralized message state
- [ ] Create `ISmsMessageStateService` for managing message collections
- [ ] Implement thread-safe message collection management
- [ ] Separate database operations from UI state
- [ ] Create proper event system for state changes

### 5.3 Background Service Decoupling
**Problem**: Background service directly manipulates UI state
**Fix**: Event-driven architecture
- [ ] Create domain events for new messages
- [ ] Use event bus pattern for loose coupling
- [ ] Remove direct StateService manipulation from background service
- [ ] Add proper event handlers in components

## Priority 6: Performance & Memory Optimizations

### 6.1 JavaScript Interop Issues
**Problem**: Repeated `JSRuntime.InvokeAsync("eval")` calls
**Fix**: Proper JS function exports
- [ ] Create dedicated JS functions instead of eval
- [ ] Export functions in proper JS modules
- [ ] Remove eval usage for CSP compliance

### 6.2 Memory Leak Prevention
**Problem**: Various potential memory leaks
**Fix**: Proper cleanup patterns
- [ ] Ensure all timers are disposed
- [ ] Clear all event subscriptions
- [ ] Dispose all SignalR connections properly
- [ ] Add memory usage monitoring

## Implementation Strategy

### Phase 1: Critical Fixes (Days 1-2)
1. Fix thread safety issues in StateService
2. Fix ChopperMessaging collection safety
3. Fix async void handlers

### Phase 2: Database & SignalR (Days 3-4)
1. Fix DbContext re-entrancy
2. Fix SignalR group management
3. Add proper logging

### Phase 3: Architecture Refactor (Days 5-7)
1. Create dedicated services
2. Implement event-driven patterns
3. Decouple components

### Phase 4: Optimization & Testing (Days 8-10)
1. Performance optimizations
2. Memory leak fixes
3. Comprehensive testing
4. Load testing

## Testing Requirements

### Unit Tests
- [ ] Thread safety tests for all concurrent collections
- [ ] DbContext usage tests
- [ ] Event handler exception tests

### Integration Tests
- [ ] SignalR group management tests
- [ ] Background service integration tests
- [ ] Component lifecycle tests

### Load Tests
- [ ] Concurrent user scenarios
- [ ] High message volume tests
- [ ] Memory usage under load

## Monitoring & Observability

### Logging
- [ ] Add structured logging for all threading operations
- [ ] Log DbContext creation/disposal
- [ ] Monitor SignalR group membership
- [ ] Track message processing pipeline

### Metrics
- [ ] Message processing latency
- [ ] Memory usage trends
- [ ] Connection count tracking
- [ ] Error rate monitoring

## Success Criteria

1. ✅ No more "Collection was modified" exceptions
2. ✅ No more "A second operation was started" DbContext errors  
3. ✅ No unhandled exceptions from async void handlers
4. ✅ Memory usage remains stable under load
5. ✅ Unread count logic completely decoupled from UI/SignalR
6. ✅ All threading issues eliminated
7. ✅ Clean separation of concerns between services
8. ✅ Comprehensive test coverage for concurrent scenarios

## Notes

- All changes should be made incrementally with thorough testing
- Maintain backward compatibility where possible
- Document all threading assumptions and patterns
- Consider using a state management library (like Fluxor) for complex state
- Add circuit breaker patterns for external API calls
- Implement proper retry policies for database operations 