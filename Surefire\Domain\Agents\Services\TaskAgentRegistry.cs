using Surefire.Data;
using Surefire.Domain.Agents.Interfaces;
using Surefire.Domain.Agents.Models;
using Surefire.Domain.Agents.TaskAgents;
using Surefire.Domain.Policies.Services;
using Surefire.Domain.Clients.Services;
using Surefire.Domain.Ember;
using System.Collections.Concurrent;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;

namespace Surefire.Domain.Agents.Services
{
    /// <summary>
    /// Registry service for managing task agents
    /// </summary>
    public class TaskAgentRegistry : ITaskAgentRegistry
    {
        private readonly ConcurrentDictionary<string, ITaskAgent> _agents = new();
        private readonly ConcurrentDictionary<string, TaskAgentDefinition> _registeredAgents = new();
        private readonly ConcurrentDictionary<string, Type> _agentTypes = new();
        private readonly ILogger<TaskAgentRegistry> _logger;

        public TaskAgentRegistry(ILogger<TaskAgentRegistry> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Register a task agent instance
        /// </summary>
        public void RegisterAgent(ITaskAgent agent)
        {
            if (agent?.Definition == null)
                throw new ArgumentException("Agent and its definition cannot be null", nameof(agent));

            if (string.IsNullOrWhiteSpace(agent.Definition.AgentId))
                throw new ArgumentException("Agent ID cannot be null or empty", nameof(agent));

            if (_agents.TryAdd(agent.Definition.AgentId, agent))
            {
                _registeredAgents.TryAdd(agent.Definition.AgentId, agent.Definition);
                _agentTypes.TryAdd(agent.Definition.AgentId, agent.GetType());
                
                _logger.LogInformation("Registered task agent: {AgentId} - {Name}", 
                    agent.Definition.AgentId, agent.Definition.Name);
            }
            else
            {
                _logger.LogWarning("Agent with ID {AgentId} is already registered", agent.Definition.AgentId);
                throw new InvalidOperationException($"Agent with ID '{agent.Definition.AgentId}' is already registered");
            }
        }

        /// <summary>
        /// Get all registered agents
        /// </summary>
        public IEnumerable<TaskAgentDefinition> GetAllAgents()
        {
            return _agents.Values.Select(a => a.Definition).ToList();
        }

        /// <summary>
        /// Get available agents (alias for GetAllAgents for compatibility)
        /// </summary>
        public List<TaskAgentDefinition> GetAvailableAgents()
        {
            return GetAllAgents().ToList();
        }

        /// <summary>
        /// Get agents by category
        /// </summary>
        public IEnumerable<TaskAgentDefinition> GetAgentsByCategory(string category)
        {
            if (string.IsNullOrWhiteSpace(category))
                return Enumerable.Empty<TaskAgentDefinition>();

            return _agents.Values
                .Where(a => string.Equals(a.Definition.Category, category, StringComparison.OrdinalIgnoreCase))
                .Select(a => a.Definition)
                .ToList();
        }

        /// <summary>
        /// Get agent by ID
        /// </summary>
        public TaskAgentDefinition? GetAgentById(string agentId)
        {
            if (string.IsNullOrWhiteSpace(agentId))
                return null;

            return _agents.TryGetValue(agentId, out var agent) ? agent.Definition : null;
        }

        /// <summary>
        /// Get a specific agent definition by ID
        /// </summary>
        public TaskAgentDefinition? GetAgentDefinition(string agentId)
        {
            return _registeredAgents.TryGetValue(agentId, out var definition) ? definition : null;
        }

        /// <summary>
        /// Get the type of an agent by ID
        /// </summary>
        public Type? GetAgentType(string agentId)
        {
            return _agentTypes.TryGetValue(agentId, out var type) ? type : null;
        }

        /// <summary>
        /// Get agent instance by ID for execution
        /// </summary>
        public ITaskAgent? GetAgentInstance(string agentId)
        {
            if (string.IsNullOrWhiteSpace(agentId))
                return null;

            return _agents.TryGetValue(agentId, out var agent) ? agent : null;
        }

        /// <summary>
        /// Find the best matching agent for user input
        /// </summary>
        public AgentMatchResult FindBestMatch(string userInput, AgentExecutionContext context)
        {
            if (string.IsNullOrWhiteSpace(userInput))
                return new AgentMatchResult { Agent = null, Confidence = 0.0 };

            var results = new List<(TaskAgentDefinition Agent, double Confidence)>();
            var userInputLower = userInput.ToLowerInvariant();

            foreach (var agent in _agents.Values)
            {
                // Check if agent supports the context
                var supportsContext = context switch
                {
                    AgentExecutionContext.AIChat => agent.Definition.SupportsAIChat,
                    AgentExecutionContext.ActionButton => agent.Definition.SupportsActionButton,
                    _ => false
                };

                if (!supportsContext)
                    continue;

                var confidence = CalculateMatchConfidence(userInputLower, agent.Definition);
                if (confidence > 0.1) // Only include if there's some confidence
                {
                    results.Add((agent.Definition, confidence));
                }
            }

            // Get the best match
            var bestMatch = results.OrderByDescending(r => r.Confidence).FirstOrDefault();
            return new AgentMatchResult 
            { 
                Agent = bestMatch.Agent, 
                Confidence = bestMatch.Confidence 
            };
        }

        /// <summary>
        /// Find agents that might match the given user input
        /// </summary>
        public async Task<List<(TaskAgentDefinition Agent, double Confidence)>> FindMatchingAgentsAsync(
            string userInput, int topResults = 5)
        {
            if (string.IsNullOrWhiteSpace(userInput))
                return new List<(TaskAgentDefinition, double)>();

            var results = new List<(TaskAgentDefinition Agent, double Confidence)>();
            var userInputLower = userInput.ToLowerInvariant();

            foreach (var agent in _agents.Values)
            {
                var confidence = CalculateMatchConfidence(userInputLower, agent.Definition);
                if (confidence > 0.1) // Only include if there's some confidence
                {
                    results.Add((agent.Definition, confidence));
                }
            }

            // Sort by confidence and take top results
            return results
                .OrderByDescending(r => r.Confidence)
                .Take(topResults)
                .ToList();
        }

        /// <summary>
        /// Check if an agent exists and supports the given execution context
        /// </summary>
        public bool SupportsContext(string agentId, AgentExecutionContext context)
        {
            if (!_agents.TryGetValue(agentId, out var agent))
                return false;

            return context switch
            {
                AgentExecutionContext.AIChat => agent.Definition.SupportsAIChat,
                AgentExecutionContext.ActionButton => agent.Definition.SupportsActionButton,
                _ => false
            };
        }

        /// <summary>
        /// Calculate confidence score for how well user input matches an agent
        /// This is a simple implementation that can be enhanced with embedding similarity
        /// </summary>
        private double CalculateMatchConfidence(string userInputLower, TaskAgentDefinition agentDef)
        {
            double confidence = 0.0;

            // Check exact trigger phrase matches (highest confidence)
            foreach (var trigger in agentDef.TriggerPhrases)
            {
                if (userInputLower.Contains(trigger.ToLowerInvariant()))
                {
                    confidence = Math.Max(confidence, 0.9);
                }
            }

            // Check name and description matches (medium confidence)
            if (userInputLower.Contains(agentDef.Name.ToLowerInvariant()))
            {
                confidence = Math.Max(confidence, 0.7);
            }

            var descWords = agentDef.Description.ToLowerInvariant().Split(' ', StringSplitOptions.RemoveEmptyEntries);
            var inputWords = userInputLower.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            
            var matchingWords = descWords.Intersect(inputWords).Count();
            if (matchingWords > 0)
            {
                var wordMatchScore = (double)matchingWords / Math.Max(descWords.Length, inputWords.Length);
                confidence = Math.Max(confidence, wordMatchScore * 0.6);
            }

            // Check category matches (lower confidence)
            if (userInputLower.Contains(agentDef.Category.ToLowerInvariant()))
            {
                confidence = Math.Max(confidence, 0.4);
            }

            return confidence;
        }

        /// <summary>
        /// Initialize the registry with some example agents for testing
        /// This method can be removed once real agents are registered
        /// </summary>
        public void RegisterExampleAgents()
        {
            try
            {
                // This will be replaced with actual agent implementations
                _logger.LogInformation("Example agents would be registered here");
                
                // Example of how to register an agent:
                // RegisterAgent(new LossRunRequestAgent(...));
                // RegisterAgent(new CertificateRequestAgent(...));
                // RegisterAgent(new PaymentLinkAgent(...));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering example agents");
            }
        }

        /// <summary>
        /// Initialize the registry with real task agents from the DI container
        /// </summary>
        public void InitializeAgents(IServiceProvider serviceProvider)
        {
            try
            {
                _logger.LogInformation("=== STARTING TASK AGENT REGISTRY INITIALIZATION ===");
                _logger.LogInformation("Service provider type: {ServiceProviderType}", serviceProvider.GetType().Name);

                // Test if we can resolve basic services first
                _logger.LogInformation("Testing basic service resolution...");
                var dbContextFactory = serviceProvider.GetService<IDbContextFactory<ApplicationDbContext>>();
                _logger.LogInformation("DbContextFactory resolved: {IsResolved}", dbContextFactory != null);
                
                var emberService = serviceProvider.GetService<EmberService>();
                _logger.LogInformation("EmberService resolved: {IsResolved}", emberService != null);
                
                var clientService = serviceProvider.GetService<ClientService>();
                _logger.LogInformation("ClientService resolved: {IsResolved}", clientService != null);
                
                var policyService = serviceProvider.GetService<PolicyService>();
                _logger.LogInformation("PolicyService resolved: {IsResolved}", policyService != null);

                // Register the Loss Run Request Agent
                _logger.LogInformation("=== ATTEMPTING TO RESOLVE LossRunRequestAgent ===");
                try
                {
                    var lossRunAgent = serviceProvider.GetService<LossRunRequestAgent>();
                    if (lossRunAgent != null)
                    {
                        _logger.LogInformation("✅ LossRunRequestAgent resolved successfully!");
                        _logger.LogInformation("Agent type: {AgentType}", lossRunAgent.GetType().FullName);
                        _logger.LogInformation("Agent ID: {AgentId}", lossRunAgent.Definition?.AgentId ?? "NULL");
                        _logger.LogInformation("Agent Name: {AgentName}", lossRunAgent.Definition?.Name ?? "NULL");
                        
                        RegisterAgent(lossRunAgent);
                        _logger.LogInformation("✅ LossRunRequestAgent registered successfully!");
                    }
                    else
                    {
                        _logger.LogError("❌ LossRunRequestAgent could not be resolved from DI container");
                        
                        // Try to get more information about why it failed
                        try
                        {
                            using var scope = serviceProvider.CreateScope();
                            var scopedAgent = scope.ServiceProvider.GetService<LossRunRequestAgent>();
                            _logger.LogInformation("Scoped resolution attempt: {IsResolved}", scopedAgent != null);
                        }
                        catch (Exception scopeEx)
                        {
                            _logger.LogError(scopeEx, "Error during scoped resolution attempt");
                        }
                    }
                }
                catch (Exception agentEx)
                {
                    _logger.LogError(agentEx, "❌ Exception while resolving LossRunRequestAgent");
                    _logger.LogError("Exception details: {Message}", agentEx.Message);
                    _logger.LogError("Inner exception: {InnerException}", agentEx.InnerException?.Message ?? "None");
                }

                // Register the Payment Link Agent
                _logger.LogInformation("=== ATTEMPTING TO RESOLVE PaymentLinkAgent ===");
                try
                {
                    var paymentLinkAgent = serviceProvider.GetService<PaymentLinkAgent>();
                    if (paymentLinkAgent != null)
                    {
                        _logger.LogInformation("✅ PaymentLinkAgent resolved successfully!");
                        RegisterAgent(paymentLinkAgent);
                        _logger.LogInformation("✅ PaymentLinkAgent registered successfully!");
                    }
                    else
                    {
                        _logger.LogWarning("❌ PaymentLinkAgent could not be resolved from DI container");
                    }
                }
                catch (Exception agentEx)
                {
                    _logger.LogError(agentEx, "❌ Exception while resolving PaymentLinkAgent");
                }

                // Register the Simple Pay Link Sender Agent
                _logger.LogInformation("=== ATTEMPTING TO RESOLVE SimplePayLinkSender ===");
                try
                {
                    var simplePayLinkAgent = serviceProvider.GetService<SimplePayLinkSender>();
                    if (simplePayLinkAgent != null)
                    {
                        _logger.LogInformation("✅ SimplePayLinkSender resolved successfully!");
                        RegisterAgent(simplePayLinkAgent);
                        _logger.LogInformation("✅ SimplePayLinkSender registered successfully!");
                    }
                    else
                    {
                        _logger.LogWarning("❌ SimplePayLinkSender could not be resolved from DI container");
                    }
                }
                catch (Exception agentEx)
                {
                    _logger.LogError(agentEx, "❌ Exception while resolving SimplePayLinkSender");
                }

                _logger.LogInformation("=== TASK AGENT REGISTRY INITIALIZATION COMPLETED ===");
                _logger.LogInformation("Total registered agents: {Count}", _agents.Count);
                
                // Log all registered agents for debugging
                if (_agents.Any())
                {
                    _logger.LogInformation("📋 REGISTERED AGENTS SUMMARY:");
                    foreach (var agent in _agents.Values)
                    {
                        _logger.LogInformation("  ✓ {AgentId} - {Name} - {Category}", 
                            agent.Definition.AgentId, agent.Definition.Name, agent.Definition.Category);
                    }
                }
                else
                {
                    _logger.LogError("❌ NO AGENTS WERE REGISTERED! This is a critical issue.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "=== CRITICAL ERROR DURING TASK AGENT INITIALIZATION ===");
                _logger.LogError("Error message: {Message}", ex.Message);
                _logger.LogError("Stack trace: {StackTrace}", ex.StackTrace);
                throw; // Re-throw to make failures visible
            }
        }
    }
} 