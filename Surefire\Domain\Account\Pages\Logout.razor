﻿@page "/Account/Logout"
@namespace Surefire.Components.Account.Pages
@layout EmptyLayout
@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity
@using Surefire.Data
@inject SignInManager<ApplicationUser> SignInManager
@inject ILogger<Logout> Logger
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager
@inject StateService _stateService
@attribute [AllowAnonymous]
@attribute [ExcludeFromInteractiveRouting]

<PageTitle>Logging out...</PageTitle>

<div class="page-content">
    <section style="width:300px; padding-left:40px;">
        <h2>Logging out...</h2>
    </section>
</div>

@code {
    private bool _hasLoggedOut = false;

    protected override async Task OnInitializedAsync()
    {

        if (!_hasLoggedOut)
        {
            _hasLoggedOut = true;
            try
            {
                await SignInManager.SignOutAsync();
                Logger.LogInformation("User logged out.");
                
                // Use a simple navigation without forceLoad to avoid NavigationException
                NavigationManager.NavigateTo("/Account/Login", replace: true);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error occurred during logout.");
                NavigationManager.NavigateTo("/Account/Login", replace: true);
            }
        }
    }
}

