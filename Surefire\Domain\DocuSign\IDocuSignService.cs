﻿namespace Surefire.Domain.DocuSign
{
    public interface IDocuSignService
    {
        /// <summary>
        /// Gets an OAuth access token using the JWT flow.
        /// </summary>
        Task<string> GetAccessTokenAsync();

        /// <summary>
        /// Retrieves recent envelopes.
        /// </summary>
        Task<string> GetRecentEnvelopesAsync(string accessToken);
        
        /// <summary>
        /// Downloads a document as PDF from a specific envelope.
        /// </summary>
        /// <param name="accessToken">The DocuSign OAuth access token</param>
        /// <param name="envelopeId">The envelope ID to download</param>
        /// <returns>The PDF document as a byte array</returns>
        Task<byte[]> DownloadDocumentPdfAsync(string accessToken, string envelopeId);
    }
}
