namespace Surefire.Domain.Agents.Interfaces
{
    /// <summary>
    /// Service interface for handling voice recording and transcription in the chat interface
    /// </summary>
    public interface IVoiceRecordingService
    {
        /// <summary>
        /// Transcribes audio data using OpenAI's Whisper API
        /// </summary>
        /// <param name="audioData">Audio data as byte array</param>
        /// <param name="language">Language code (default: "en")</param>
        /// <returns>Transcribed text</returns>
        Task<string> TranscribeAudioAsync(byte[] audioData, string language = "en");

        /// <summary>
        /// Validates that the audio data is in a supported format and within size limits
        /// </summary>
        /// <param name="audioData">Audio data to validate</param>
        /// <returns>Validation result</returns>
        VoiceValidationResult ValidateAudioData(byte[] audioData);
    }

    /// <summary>
    /// Result of audio data validation
    /// </summary>
    public class VoiceValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public double FileSizeMB { get; set; }
    }
} 