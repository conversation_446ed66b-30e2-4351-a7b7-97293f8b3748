﻿.submissions-redesigned {
    padding: 10px;
}

/* View Toggle Bar */
.view-toggle-bar {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

/* Submissions header removed - now handled by parent toolbar */
/* Stepper column styling moved to carriers-list section below */
/* Container Styles */
.wholesaler-container,
.direct-appointment-container {
    margin-bottom: 30px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Combined Contact and Links Layout */
.contact-links-combined {
    display: flex;
    background: #fafafa;
    border-bottom: 1px solid #e0e0e0;
    min-height: 90px;
}

.carrier-name-area {
    min-width: 200px;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    border-right: 1px solid #e0e0e0;
}

.carrier-name-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

    .carrier-name-link:hover {
        color: #0078d4;
    }

    .carrier-name-link h3 {
        margin: 0 0 4px 0;
        font-size: 2rem;
        font-weight: 600;
        color: inherit;
        transition: color 0.2s ease;
    }

.premium-tbd {
    font-size: 4rem;
    font-weight: 300;
    font-family: "Segoe UI", "Aptos", system-ui, sans-serif;
    font-style: italic;
    color: #0078d4;
    letter-spacing: -0.5px;
    color:#bdbdbd;
}

.carrier-subtitle {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

.contact-slider-area {
    flex: 1;
    padding: 15px 20px;
    display: flex;
    align-items: center;
}

.square-buttons-grid {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 10px;
    gap: 8px;
    background: #ffffff;
    border-left: 1px solid #e0e0e0;
}

/* Custom Square Buttons */
.square-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 60px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    color: #495057;
    padding: 8px 4px;
}

    .square-button:hover {
        background: #e9ecef;
        border-color: #adb5bd;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .square-button:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    .square-button fluent-icon {
        margin-bottom: 4px;
        color: #495057;
    }

    .square-button span {
        font-size: 0.7rem;
        font-weight: 500;
        text-align: center;
        line-height: 1.1;
        color: #495057;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Accent button for Email Underwriter */
    .square-button.accent-button {
        background: #0078d4;
        border-color: #106ebe;
        color: white;
    }

        .square-button.accent-button:hover {
            background: #106ebe;
            border-color: #005a9e;
        }

        .square-button.accent-button fluent-icon,
        .square-button.accent-button span {
            color: white;
        }

.carrier-submission {
    border-bottom: 1px solid #f0f0f0;
    padding: 20px;
}

    .carrier-submission:last-child {
        border-bottom: none;
    }

.carrier-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.carrier-info h4 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 500;
    color: #333;
}

.carrier-meta {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-top: 8px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-0 {
    background: #f0f0f0;
    color: #666;
}

.status-1 {
    background: #e3f2fd;
    color: #1976d2;
}

.status-2 {
    background: #fff3e0;
    color: #f57c00;
}

.status-3 {
    background: #f3e5f5;
    color: #7b1fa2;
}

.status-4 {
    background: #e8f5e8;
    color: #388e3c;
}

.status-5 {
    background: #ffebee;
    color: #d32f2f;
}

.status-6 {
    background: #e8f5e8;
    color: #2e7d32;
}

.premium {
    font-weight: 600;
    color: #0078d4;
    font-size: 1.1rem;
}

.tbd {
    color: #999;
    font-style: italic;
}

.carrier-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.carrier-link {
    color: #0078d4;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

    .carrier-link:hover {
        background-color: rgba(0, 120, 212, 0.1);
    }

/* Collapsible Sections */
.collapsible-sections {
    margin-top: 15px;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-bottom: 5px;
    font-weight: 500;
}

    .section-header:hover {
        background: #e9ecef;
    }

.section-content {
    padding: 15px;
    background: #fafafa;
    border: 1px solid #e0e0e0;
    border-top: none;
    border-radius: 0 0 4px 4px;
    margin-bottom: 10px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

    .empty-state h3 {
        margin: 20px 0 10px 0;
        font-size: 1.5rem;
    }

    .empty-state p {
        margin-bottom: 30px;
        font-size: 1.1rem;
    }

/* Dialog Styles */
.add-submission-dialog h2 {
    margin-top: 0;
    border-bottom: 1px solid #eaeaea;
    padding-bottom: 10px;
}

.form-grid {
    display: grid;
    gap: 20px;
    padding: 10px 0;
}

.form-row {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

    .form-row label {
        font-weight: 500;
        font-size: 0.9rem;
    }

/* Stepper styles */
.carrier-status {
    margin: 15px 0;
    padding: 10px 0;
}

.carrier-info .carrier-status .e-stepper {
    min-height: auto;
}

    .carrier-info .carrier-status .e-stepper .e-step-label {
        font-size: 0.75rem;
    }

/* Fix for SfStepper icons */
:root .x-plus-icon::before {
    content: '\e805';
}

:root .x-pencil-icon::before {
    content: '\e740';
}

:root .x-changes-icon::before {
    content: '\e7a8';
}

:root .x-send-icon::before {
    content: '\e71d';
}

:root .x-trash-icon::before {
    content: '\e820';
}

:root .x-check-icon::before {
    content: '\e727';
}

:root .x-signature-icon::before {
    content: '\e897';
}


/* Rejection Status Toggle */
.rejection-status-column {
    display: flex;
    justify-content: center;
}

.rejection-toggle-group {
    display: flex;
    gap: 4px;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    padding: 2px;
    background: #f8f9fa;
    height: 58px;
}

.rejection-toggle {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    background: transparent;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #666;
    font-size: 11px;
    min-width: 70px;
    min-height: 50px;
}

    .rejection-toggle:hover {
        background: rgba(0, 120, 212, 0.1);
        color: #0078d4;
    }

    .rejection-toggle.active {
        background: white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        font-weight: 500;
    }

        .rejection-toggle.active.declined {
            color: #d32f2f;
            border: 1px solid #d32f2f;
        }

        .rejection-toggle.active.rejected {
            color: #f57c00;
            border: 1px solid #f57c00;
        }

        .rejection-toggle.active.non-renewed {
            color: #7b1fa2;
            border: 1px solid #7b1fa2;
        }

    .rejection-toggle span {
        margin-top: 4px;
        line-height: 1.1;
    }

/* Carriers List within Wholesaler Container */
.carriers-list {
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    padding-bottom: 15px;
}

.carrier-submission-row {
    padding: 10px 10px;
    margin: 0 15px;
    background: white;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    margin-bottom: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
    position: relative;
}

    .carrier-submission-row:first-child {
        margin-top: 15px;
    }

    .carrier-submission-row:last-child {
        margin-bottom: 0;
    }

    .carrier-submission-row:hover {
        background-color: #ffffff;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    }

    .carrier-submission-row::before {
        content: '';
        position: absolute;
        left: -15px;
        top: 50%;
        transform: translateY(-50%);
        width: 3px;
        height: 40px;
        background: #d1d1d1;
        border-radius: 2px;
    }

    .carrier-submission-row .fluent-stack {
        display: flex;
        align-items: center;
        gap: 25px;
        width: 100%;
    }

.carrier-name-column {
    width: 336px;
    height: 63px;
    border-right:1px solid #e6e6e6;
}

    .carrier-name-column h4 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 500;
        color: #555;
        line-height: 1.3;
        position: relative;
        top: 18px;
    }

.stepper-column {
    width: 550px;
}
.stepper-fix {
    position: relative;
    top:6px;
}
.premium-column {
    width: 200px;
    justify-content: center;
    align-items: center;
    position: relative;
    top: 20px;
}

.premium-amount {
    font-size: 4rem;
    font-weight: 500;
    color: #0078d4;
    font-family: "Segoe UI", "Aptos", system-ui, sans-serif;
    letter-spacing: -0.5px;
    height: 63px;
}

.rejection-column {
    min-width: 240px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-shrink: 0;
}

/* Rejected submission opacity */
.rejected-opacity {
    opacity: 0.5;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 1400px) {
    .carrier-submission-row .fluent-stack {
        flex-direction: column;
        align-items: stretch;
        gap: 20px;
    }

    .carrier-name-column,
    .premium-column,
    .rejection-column {
        min-width: auto;
        width: 100%;
        justify-content: center;
    }

    .stepper-column {
        width: 550px;
        align-self: center;
    }

    .carrier-submission-row {
        padding: 25px;
        margin: 0 10px;
    }
}

@media (max-width: 768px) {
    .carrier-submission-row {
        padding: 20px 15px;
        margin: 0 5px;
    }

    .stepper-column {
        width: 100%;
        max-width: 550px;
    }

    .rejection-toggle-group {
        scale: 0.9;
    }

    .premium-amount, .premium-tbd {
        font-size: 1.8rem;
    }
}

/* Premium Edit Styles */
.premium-display {
    display: flex;
    align-items: center;
    gap: 8px;
}

.premium-edit-button {
    background: none;
    border: none;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    transition: all 0.2s ease;
    opacity: 0.7;
}

    .premium-edit-button:hover {
        background: #f5f5f5;
        color: #0078d4;
        opacity: 1;
        transform: scale(1.1);
    }

    .premium-edit-button:active {
        transform: scale(0.95);
    }

.premium-display:hover .premium-edit-button {
    opacity: 1;
}

/* Carrier Edit Styles */
.carrier-name-display {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
}

    .carrier-name-display.direct-appointment-header {
        justify-content: space-between;
    }

.carrier-edit-button {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    transition: all 0.2s ease;
    opacity: 0.7;
    flex-shrink: 0;
}

    .carrier-edit-button:hover {
        background: #f5f5f5;
        color: #0078d4;
        opacity: 1;
        transform: scale(1.1);
    }

    .carrier-edit-button:active {
        transform: scale(0.95);
    }

.carrier-name-display:hover .carrier-edit-button {
    opacity: 1;
}

/* New Submission View Styles */
.new-submission-view {
    padding: 0px;
}

.submission-top-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 20px;
    gap: 30px;
}

.carrier-section,
.wholesaler-section {
    flex: 1;
    min-width: 200px;
}

.save-section {
    display: flex;
    gap: 12px;
    align-items: center;
}

.section-label {
    display: block;
    font-size: 0.9rem;
    font-weight: 600;
    color: #323130;
    margin-bottom: 8px;
}

.carrier-display,
.wholesaler-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    padding: 3px;
    background: white;
    border: 1px solid #d1d1d1;
    border-radius: 4px;
    min-height: 5px;
}

.carrier-name,
.wholesaler-name {
    font-weight: 500;
    color: #323130;
    flex: 1;
}

.placeholder {
    color: #666;
    font-style: italic;
    flex: 1;
}

.carrier-picker-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

/* Responsive design updates */
@media (max-width: 768px) {
    .submission-top-bar {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .carrier-section,
    .wholesaler-section {
        min-width: unset;
    }

    .save-section {
        justify-content: center;
    }
}

/* Incumbent carrier star styling */
.incumbent-star {
    color: #FFD700 !important;
    margin-left: 8px;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2));
    animation: twinkle 2s ease-in-out infinite;
}

@keyframes twinkle {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}
