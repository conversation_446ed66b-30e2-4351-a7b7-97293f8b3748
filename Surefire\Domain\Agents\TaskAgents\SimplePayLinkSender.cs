using Surefire.Domain.Agents.Interfaces;
using Surefire.Domain.Agents.Models;
using Surefire.Domain.Clients.Services;
using Surefire.Domain.Contacts.Models;
using Surefire.Domain.Ember;
using Surefire.Data;
using Microsoft.EntityFrameworkCore;
using System.Globalization;

namespace Surefire.Domain.Agents.TaskAgents
{
    /// <summary>
    /// Agent for sending payment links to clients
    /// Example: "Send a pay link to <PERSON> (contact) at Acme Widgets, Inc (client) for $400 (paymentamount) for the work comp broker fee (paymentdescription)"
    /// </summary>
    public class SimplePayLinkSender : ITaskAgent
    {
        private readonly ILogger<SimplePayLinkSender> _logger;
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly EmberService _emberService;
        private readonly ClientService _clientService;

        public SimplePayLinkSender(
            ILogger<SimplePayLinkSender> logger,
            IDbContextFactory<ApplicationDbContext> dbContextFactory,
            EmberService emberService,
            ClientService clientService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dbContextFactory = dbContextFactory ?? throw new ArgumentNullException(nameof(dbContextFactory));
            _emberService = emberService ?? throw new ArgumentNullException(nameof(emberService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
        }

        /// <summary>
        /// Agent definition with metadata, parameters, and capabilities
        /// </summary>
        public TaskAgentDefinition Definition => new()
        {
            AgentId = "simple_paylink_sender",
            Name = "Simple Pay Link Sender",
            Description = "Send payment links to clients via email",
            Category = "Payments",
            TriggerPhrases = new List<string>
            {
                "send pay link",
                "send payment link",
                "create pay link",
                "payment link",
                "pay link"
            },
            Parameters = new List<AgentParameterDefinition>
            {
                new()
                {
                    Name = "clientid",
                    Description = "Client ID",
                    ParameterType = typeof(int),
                    IsRequired = true,
                    ExtractionPrompt = "Extract the client ID or identify the client from the input",
                    ClarificationQuestion = "Which client would you like to send a payment link to?"
                },
                new()
                {
                    Name = "contactid",
                    Description = "Contact ID for the recipient",
                    ParameterType = typeof(int),
                    IsRequired = true,
                    ExtractionPrompt = "Extract the contact ID or identify the contact person from the input",
                    ClarificationQuestion = "Which contact should receive the payment link?"
                },
                new()
                {
                    Name = "paymentamount",
                    Description = "Payment amount in dollars",
                    ParameterType = typeof(decimal),
                    IsRequired = true,
                    DefaultValue = 400.00m,
                    ExtractionPrompt = "Extract the payment amount from the input (e.g., '$400', '400 dollars')",
                    ClarificationQuestion = "What is the payment amount?"
                },
                new()
                {
                    Name = "paymentdescription",
                    Description = "Description of what the payment is for",
                    ParameterType = typeof(string),
                    IsRequired = true,
                    ExtractionPrompt = "Extract the payment description or purpose from the input",
                    ClarificationQuestion = "What is this payment for?"
                }
            },
            OutcomeType = AgentOutcomeType.Message,
            SupportsAIChat = true,
            SupportsActionButton = true,
            EstimatedExecutionSeconds = 15,
            RequiresConfirmation = true
        };

        /// <summary>
        /// Execute the payment link sender agent
        /// </summary>
        public async Task<TaskAgentResult> ExecuteAsync(TaskAgentRequest request, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;
            
            try
            {
                _logger.LogInformation("Executing payment link sender for user {UserId}", request.UserId);

                // Extract parameters
                var clientId = Convert.ToInt32(request.Parameters["clientid"]);
                var contactId = Convert.ToInt32(request.Parameters["contactid"]);
                var paymentAmount = Convert.ToDecimal(request.Parameters["paymentamount"]);
                var paymentDescription = request.Parameters["paymentdescription"]?.ToString() ?? "";

                // Process the payment link request
                var result = await ProcessPaymentLinkRequestAsync(
                    clientId, contactId, paymentAmount, paymentDescription, cancellationToken);

                // Generate suggestions based on results
                var suggestions = new List<string>();
                if (result.Success)
                {
                    suggestions.Add("📧 Review and send the email draft in Outlook");
                    suggestions.Add("🔗 You can copy the payment link directly to share via other channels");
                    suggestions.Add("💰 Would you like to create additional payment links for other contacts?");
                    suggestions.Add("📋 Should I create a follow-up reminder for this payment?");
                }

                return new TaskAgentResult
                {
                    Success = result.Success,
                    OutcomeType = result.Success ? AgentOutcomeType.Message : AgentOutcomeType.Error,
                    Message = result.Message,
                    Data = result.Data,
                    ErrorMessage = result.Success ? null : result.Message,
                    ExecutionTime = DateTime.UtcNow - startTime,
                    Suggestions = suggestions
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing payment link sender");
                
                return new TaskAgentResult
                {
                    Success = false,
                    OutcomeType = AgentOutcomeType.Error,
                    ErrorMessage = "Failed to send payment link: " + ex.Message,
                    ExecutionTime = DateTime.UtcNow - startTime
                };
            }
        }

        /// <summary>
        /// Validate parameters for the agent
        /// </summary>
        public async Task<AgentParameterValidation> ValidateParametersAsync(Dictionary<string, object> parameters)
        {
            var validation = new AgentParameterValidation();
            var validatedParameters = new Dictionary<string, object>();

            // Validate clientid
            if (parameters.TryGetValue("clientid", out var clientIdValue) && 
                int.TryParse(clientIdValue?.ToString(), out var clientId) && clientId > 0)
            {
                validatedParameters["clientid"] = clientId;
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "clientid",
                    Status = ParameterStatus.Valid,
                    Value = clientId
                });
            }
            else
            {
                validation.MissingParameters.Add("clientid");
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "clientid",
                    Status = ParameterStatus.Missing,
                    Message = "Client ID is required"
                });
            }

            // Validate contactid
            if (parameters.TryGetValue("contactid", out var contactIdValue) && 
                int.TryParse(contactIdValue?.ToString(), out var contactId) && contactId > 0)
            {
                validatedParameters["contactid"] = contactId;
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "contactid",
                    Status = ParameterStatus.Valid,
                    Value = contactId
                });
            }
            else
            {
                validation.MissingParameters.Add("contactid");
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "contactid",
                    Status = ParameterStatus.Missing,
                    Message = "Contact ID is required"
                });
            }

            // Validate paymentamount
            if (parameters.TryGetValue("paymentamount", out var amountValue) && 
                decimal.TryParse(amountValue?.ToString(), out var amount) && amount > 0)
            {
                validatedParameters["paymentamount"] = amount;
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "paymentamount",
                    Status = ParameterStatus.Valid,
                    Value = amount
                });
            }
            else
            {
                validation.MissingParameters.Add("paymentamount");
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "paymentamount",
                    Status = ParameterStatus.Missing,
                    Message = "Payment amount is required and must be greater than 0"
                });
            }

            // Validate paymentdescription
            if (parameters.TryGetValue("paymentdescription", out var descriptionValue) && 
                !string.IsNullOrWhiteSpace(descriptionValue?.ToString()))
            {
                validatedParameters["paymentdescription"] = descriptionValue.ToString().Trim();
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "paymentdescription",
                    Status = ParameterStatus.Valid,
                    Value = descriptionValue
                });
            }
            else
            {
                validation.MissingParameters.Add("paymentdescription");
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "paymentdescription",
                    Status = ParameterStatus.Missing,
                    Message = "Payment description is required"
                });
            }

            validation.ValidatedParameters = validatedParameters;
            validation.IsComplete = validation.MissingParameters.Count == 0 && 
                                   !validation.Parameters.Any(p => p.Status == ParameterStatus.Invalid);

            return validation;
        }

        /// <summary>
        /// Get execution preview for confirmation
        /// </summary>
        public async Task<string> GetExecutionPreviewAsync(Dictionary<string, object> parameters)
        {
            var clientId = Convert.ToInt32(parameters.GetValueOrDefault("clientid", 0));
            var contactId = Convert.ToInt32(parameters.GetValueOrDefault("contactid", 0));
            var paymentAmount = Convert.ToDecimal(parameters.GetValueOrDefault("paymentamount", 0));
            var paymentDescription = parameters.GetValueOrDefault("paymentdescription")?.ToString() ?? "";

            try
            {
                using var context = _dbContextFactory.CreateDbContext();
                
                // Get client and contact information
                var client = await context.Clients.FirstOrDefaultAsync(c => c.ClientId == clientId);
                var contact = await context.Contacts
                    .Include(c => c.EmailAddresses)
                    .FirstOrDefaultAsync(c => c.ContactId == contactId);

                var clientName = client?.Name ?? $"Client ID {clientId}";
                var contactName = contact != null ? $"{contact.FirstName} {contact.LastName}" : $"Contact ID {contactId}";
                var contactEmail = contact?.EmailAddresses?.FirstOrDefault()?.Email ?? "Unknown email";

                var preview = $"Send a payment link to {contactName} ({contactEmail}) at {clientName} for {paymentAmount:C} for {paymentDescription}.";
                preview += " This will create an email with the payment link and open it in Outlook for review before sending.";

                return preview;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating execution preview");
                return $"Send payment link for {paymentAmount:C} for {paymentDescription}";
            }
        }

        /// <summary>
        /// Process the payment link request using business logic similar to Paylink.razor
        /// </summary>
        private async Task<(bool Success, string Message, object Data)> ProcessPaymentLinkRequestAsync(
            int clientId, 
            int contactId, 
            decimal paymentAmount, 
            string paymentDescription,
            CancellationToken cancellationToken)
        {
            try
            {
                using var context = _dbContextFactory.CreateDbContext();

                // 1. Get client information
                var client = await context.Clients.FirstOrDefaultAsync(c => c.ClientId == clientId, cancellationToken);
                if (client == null)
                {
                    return (false, $"Client with ID {clientId} not found.", null);
                }

                // 2. Get contact information with email addresses
                var contact = await context.Contacts
                    .Include(c => c.EmailAddresses)
                    .FirstOrDefaultAsync(c => c.ContactId == contactId, cancellationToken);

                if (contact == null)
                {
                    return (false, $"Contact with ID {contactId} not found.", null);
                }

                var primaryEmail = contact.EmailAddresses?.FirstOrDefault()?.Email;
                if (string.IsNullOrEmpty(primaryEmail))
                {
                    return (false, $"No email address found for contact {contact.FirstName} {contact.LastName}.", null);
                }

                // 3. Generate payment link using same logic as Paylink.razor
                var paymentLink = GeneratePaymentLink(client.Name, primaryEmail, paymentAmount, paymentDescription);

                // 4. Create and send email
                await SendPaymentLinkEmail(client.Name, contact, primaryEmail, paymentAmount, paymentDescription, paymentLink);

                // 5. Create result data
                var data = new
                {
                    ClientId = clientId,
                    ClientName = client.Name,
                    ContactId = contactId,
                    ContactName = $"{contact.FirstName} {contact.LastName}",
                    ContactEmail = primaryEmail,
                    PaymentAmount = paymentAmount,
                    PaymentDescription = paymentDescription,
                    PaymentLink = paymentLink,
                    EmailSent = true
                };

                var successMessage = $"Payment link created and email sent to {contact.FirstName} {contact.LastName} ({primaryEmail}) for {paymentAmount:C}. Email draft has been opened in Outlook for review.";

                return (true, successMessage, data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing payment link request");
                return (false, $"Error processing request: {ex.Message}", null);
            }
        }

        /// <summary>
        /// Generate payment link using same logic as Paylink.razor
        /// </summary>
        private string GeneratePaymentLink(string payerName, string emailAddress, decimal amount, string comments)
        {
            var url = "https://metroinsurance.epaypolicy.com/";
            var queryParams = new List<string>();

            if (!string.IsNullOrEmpty(payerName))
            {
                queryParams.Add($"payer={Uri.EscapeDataString(payerName)}");
            }

            if (!string.IsNullOrEmpty(emailAddress))
            {
                queryParams.Add($"emailAddress={Uri.EscapeDataString(emailAddress)}");
            }

            if (amount > 0)
            {
                queryParams.Add($"amount={amount.ToString("F2", CultureInfo.InvariantCulture)}");
            }

            if (!string.IsNullOrEmpty(comments))
            {
                queryParams.Add($"comments={Uri.EscapeDataString(comments)}");
            }

            if (queryParams.Count > 0)
            {
                return url + "?" + string.Join("&", queryParams);
            }

            return url;
        }

        /// <summary>
        /// Send payment link email using EmberService like Paylink.razor
        /// </summary>
        private async Task SendPaymentLinkEmail(
            string clientName,
            Contact contact,
            string toEmail,
            decimal amount,
            string paymentDescription,
            string paymentLink)
        {
            try
            {
                var contactName = $"{contact.FirstName} {contact.LastName}";
                var subject = "Payment Link from Metro Insurance";
                
                // Generate HTML email body similar to Paylink.razor
                var buttonHtml = $@"<table role=""presentation"" border=""0"" cellpadding=""0"" cellspacing=""0""><tr><td align=""center"" bgcolor=""#0078d4"" style=""border-radius:4px;""><a href=""{paymentLink}"" target=""_blank"" style=""display:inline-block;padding:10px 20px;font-family:Arial,sans-serif;font-size:16px;color:#ffffff;text-decoration:none;border-radius:4px;font-weight:500;border:1px solid #006cbe;"">Make Payment</a></td></tr></table>";

                var body = $@"<div style=""font-family: Arial, sans-serif; line-height: 1.5;"">
<p>Dear {contactName},</p>
<p>Please click the link below to make your payment:</p>
{buttonHtml}
<p>Payment Details:<br>
Amount: {amount:C}<br>
{paymentDescription}</p>
<p>If you have any questions, please don't hesitate to contact us.</p>
<p>Best regards,<br>
Metro Insurance</p>
</div>";

                _logger.LogInformation("Creating payment link email for {ContactName} to {Email}", contactName, toEmail);
                
                var parameters = new List<string> { toEmail, subject, body };
                await _emberService.RunEmberFunction("OutlookEmail_CreateNew", parameters);
                
                _logger.LogInformation("Successfully created payment link email for {ContactName}", contactName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending payment link email to {Email}", toEmail);
                throw;
            }
        }
    }
} 