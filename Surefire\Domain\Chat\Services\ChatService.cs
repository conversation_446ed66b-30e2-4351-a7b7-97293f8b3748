﻿using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using System.Linq;
using System.IO;
using System.Collections.Concurrent;

namespace Surefire.Domain.Chat
{
    public partial class ChatService
    {
        private readonly HttpClient _httpClient;
        private readonly string _clientId;
        private readonly string _clientSecret;
        private readonly string _serverUrl;
        private readonly string _jwt;
        private string _accessToken;
        private DateTime _tokenExpiration = DateTime.MinValue;

        // Call log caching
        private static CallLogRecordFire[] _cachedCallLogs;
        private static DateTime _callLogsCacheExpiration = DateTime.MinValue;
        private static readonly TimeSpan _callLogsCacheTTL = TimeSpan.FromMinutes(5); // Cache for 5 minutes
        private static readonly object _callLogsCacheLock = new object();

        // Staff Chat in-memory storage
        private static readonly ConcurrentQueue<MessageItem> _staffChatMessages = new();
        private static readonly object _staffChatLock = new object();
        private static readonly int _maxStaffChatMessages = 1000; // Limit to prevent memory issues

        // SMS message caching
        private static readonly ConcurrentDictionary<string, List<SmsMessage>> _smsMessageCache = new();
        private static readonly object _smsCacheLock = new object();
        
        // SMS conversation list caching
        private static List<ConversationInfo>? _smsConversationListCache;
        private static DateTime _lastConversationListUpdate = DateTime.MinValue;
        private static readonly TimeSpan _conversationListCacheTTL = TimeSpan.FromSeconds(30); // Cache for 30 seconds

        public ChatService(HttpClient httpClient, IConfiguration configuration)
        {
            _httpClient = httpClient;
            
            // Get RingCentral credentials from environment variables
            _clientId = Environment.GetEnvironmentVariable("RINGCENTRAL_CLIENT_ID");
            _clientSecret = Environment.GetEnvironmentVariable("RINGCENTRAL_CLIENT_SECRET");
            _serverUrl = Environment.GetEnvironmentVariable("RINGCENTRAL_SERVER_URL");
            _jwt = Environment.GetEnvironmentVariable("RINGCENTRAL_JWT");
            
            if (string.IsNullOrEmpty(_clientId) || string.IsNullOrEmpty(_clientSecret) || 
                string.IsNullOrEmpty(_serverUrl) || string.IsNullOrEmpty(_jwt))
            {
                throw new InvalidOperationException("RingCentral credentials not found in environment variables");
            }
        }
        
        /// <summary>
        /// Authenticates with RingCentral API using JWT grant type
        /// </summary>
        private async Task AuthenticateAsync()
        {
            // If token is still valid, don't re-authenticate
            if (_accessToken != null && DateTime.UtcNow < _tokenExpiration)
            {
                return;
            }
            
            var content = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("grant_type", "urn:ietf:params:oauth:grant-type:jwt-bearer"),
                new KeyValuePair<string, string>("assertion", _jwt)
            });
            
            var request = new HttpRequestMessage(HttpMethod.Post, $"{_serverUrl}/restapi/oauth/token")
            {
                Content = content
            };
            
            // Add authorization header with Basic Auth (client_id:client_secret)
            var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{_clientId}:{_clientSecret}"));
            request.Headers.Authorization = new AuthenticationHeaderValue("Basic", credentials);
            
            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();
            
            var responseContent = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(responseContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
            
            _accessToken = tokenResponse.access_token;
            _tokenExpiration = DateTime.UtcNow.AddSeconds(tokenResponse.expires_in - 60); // Subtract a minute for safety
        }

        // Staff Chat Methods
        /// <summary>
        /// Adds a message to the staff chat in-memory storage
        /// </summary>
        public void AddStaffChatMessage(MessageItem message)
        {
            lock (_staffChatLock)
            {
                _staffChatMessages.Enqueue(message);
                
                // Remove old messages if we exceed the limit
                while (_staffChatMessages.Count > _maxStaffChatMessages)
                {
                    _staffChatMessages.TryDequeue(out _);
                }
            }
        }

        /// <summary>
        /// Gets all staff chat messages
        /// </summary>
        public List<MessageItem> GetStaffChatMessages()
        {
            lock (_staffChatLock)
            {
                return _staffChatMessages.ToList();
            }
        }


        /// <summary>
        /// Clears all staff chat messages (for testing/admin purposes)
        /// </summary>
        public void ClearStaffChatMessages()
        {
            lock (_staffChatLock)
            {
                _staffChatMessages.Clear();
            }
        }


        // SMS Message Caching Methods
        public void InvalidateSmsCache()
        {
            lock (_smsCacheLock)
            {
                _smsMessageCache.Clear();
                //_lastSmsCacheUpdate = DateTime.MinValue;
            }
        }

        // Enhanced SMS caching methods
        public void InvalidateSmsCacheForPhone(string phoneNumber)
        {
            lock (_smsCacheLock)
            {
                _smsMessageCache.TryRemove(phoneNumber, out _);
            }
        }

        public void InvalidateAllSmsCaches()
        {
            lock (_smsCacheLock)
            {
                var messageCacheCount = _smsMessageCache.Count;
                var conversationCacheExists = _smsConversationListCache != null;
                
                _smsMessageCache.Clear();
                //_lastSmsCacheUpdate = DateTime.MinValue;
                _smsConversationListCache = null;
                _lastConversationListUpdate = DateTime.MinValue;
                
                Console.WriteLine($"InvalidateAllSmsCaches: Cleared {messageCacheCount} message cache entries and {(conversationCacheExists ? "conversation" : "no conversation")} cache");
            }
        }

        public bool IsConversationListCacheValid()
        {
            lock (_smsCacheLock)
            {
                return _smsConversationListCache != null && 
                       DateTime.UtcNow - _lastConversationListUpdate < _conversationListCacheTTL;
            }
        }

        public List<ConversationInfo>? GetCachedConversationList()
        {
            lock (_smsCacheLock)
            {
                return _smsConversationListCache;
            }
        }

        public void UpdateConversationListCache(List<ConversationInfo> conversations)
        {
            lock (_smsCacheLock)
            {
                _smsConversationListCache = conversations;
                _lastConversationListUpdate = DateTime.UtcNow;
            }
        }

        // Method to check for new SMS messages and update unread counts
        public async Task<List<SmsMessage>> GetNewSmsMessagesSinceAsync(DateTime since)
        {
            try
            {
                await AuthenticateAsync();
                
                var allMessages = await GetAllSmsMessagesAsync();
                
                // Filter messages on our side to find ones newer than 'since'
                var newMessages = allMessages?.Messages?.Where(m => m.Timestamp > since).ToList() ?? new List<SmsMessage>();
                
                return newMessages;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting new SMS messages: {ex.Message}");
                return new List<SmsMessage>();
            }
        }

        // Method to process new SMS messages and update unread counts
        public async Task ProcessNewSmsMessagesAsync(DateTime since, Action<SmsMessage> onNewMessage)
        {
            try
            {
                var newMessages = await GetNewSmsMessagesSinceAsync(since);
                
                foreach (var message in newMessages)
                {                    
                    // Only process inbound messages for unread counts
                    if (message.IsInbound)
                    {
                        onNewMessage?.Invoke(message);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing new SMS messages: {ex.Message}");
            }
        }
    }
    
}
