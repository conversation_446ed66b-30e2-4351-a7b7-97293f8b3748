﻿using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Globalization;
using Surefire.Domain.Shared.Helpers;

namespace Surefire.Domain.Chat
{
    public partial class ChatService
    {   
        /// <summary>
        /// Sends an SMS message to the specified phone number
        /// </summary>
        /// <param name="toNumber">The recipient's phone number (E.164 format recommended, e.g. +***********)</param>
        /// <param name="message">The message text to send</param>
        /// <returns>True if the message was sent successfully</returns>
        public async Task<bool> SendSmsAsync(string toNumber, string message)
        {
            try
            {
                await AuthenticateAsync();
                
                // Get the account extension ID to use as the from number
                string accountId = "~"; // Use default account
                string extensionId = "~"; // Use default extension
                
                // Normalize the phone number for consistent storage
                var normalizedToNumber = StringHelper.NormalizePhoneNumber(toNumber);
                
                // Format for RingCentral SMS API
                var smsRequest = new
                {
                    from = new 
                    { 
                        phoneNumber = "***********" // Use default
                    },
                    to = new[] 
                    { 
                        new 
                        { 
                            phoneNumber = toNumber // Keep original format for API call
                        } 
                    },
                    text = message
                };
                
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = false // No need for indentation in production
                };
                
                var json = JsonSerializer.Serialize(smsRequest, jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                // Set up headers
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _accessToken);
                _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                
                // Make the request
                var endpoint = $"{_serverUrl}/restapi/v1.0/account/{accountId}/extension/{extensionId}/sms";
                
                var response = await _httpClient.PostAsync(endpoint, content);
                
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"SMS Error: {response.StatusCode}, Content: {errorContent}");
                }
                else
                {
                    // Invalidate cache when a new message is sent
                    InvalidateSmsCache();
                }
                
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                // Log the exception (In a production app, use a proper logging mechanism)
                Console.WriteLine($"Error sending SMS: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
                return false;
            }
        }
        
        /// <summary>
        /// Gets all SMS messages from the RingCentral Message Store
        /// </summary>
        /// <param name="dateFrom">Optional start date for filtering messages</param>
        /// <returns>A conversation object containing all messages</returns>
        public async Task<SmsConversation> GetAllSmsMessagesAsync(DateTime? dateFrom = null)
        {
            Console.WriteLine($"GetAllSmsMessagesAsync called with dateFrom: {dateFrom}");
            await AuthenticateAsync();
            
            var conversation = new SmsConversation
            {
                PhoneNumber = string.Empty, // No specific phone number as we're getting all messages
                ContactName = string.Empty,
                Messages = new List<SmsMessage>()
            };
            
            try
            {
                // Get the account extension ID to use
                string accountId = "~"; // Use default account
                string extensionId = "~"; // Use default extension
                
                // Build the request URL - we want all SMS messages
                var requestUrl = $"{_serverUrl}/restapi/v1.0/account/{accountId}/extension/{extensionId}/message-store";
                var queryParams = new List<string>
                {
                    "messageType=SMS",
                    "perPage=1000" // Get a large number of messages
                };
                
                if (dateFrom.HasValue)
                {
                    queryParams.Add($"dateFrom={Uri.EscapeDataString(dateFrom.Value.ToString("o"))}");
                }
                
                requestUrl += "?" + string.Join("&", queryParams);
                
                // Set up headers
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _accessToken);
                _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                
                Console.WriteLine($"All Messages Endpoint: {requestUrl}");
                
                var response = await _httpClient.GetAsync(requestUrl);
                
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Get All Messages Error: {response.StatusCode}, Content: {errorContent}");
                    return conversation;
                }
                
                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Response content length: {responseContent.Length}");
                
                // Try direct approach to extract from JSON first - for debugging
                try 
                {
                    using (JsonDocument doc = JsonDocument.Parse(responseContent))
                    {
                        JsonElement root = doc.RootElement;
                        
                        if (root.TryGetProperty("records", out JsonElement records) && records.ValueKind == JsonValueKind.Array)
                        {
                            Console.WriteLine($"Found {records.GetArrayLength()} records in JSON response");
                            
                            if (records.GetArrayLength() > 0)
                            {
                                JsonElement firstRecord = records[0];
                                if (firstRecord.TryGetProperty("id", out JsonElement id))
                                {
                                    Console.WriteLine($"First record ID type: {id.ValueKind}, Value: {id.ToString()}");
                                }
                                
                                if (firstRecord.TryGetProperty("from", out JsonElement from) && from.ValueKind == JsonValueKind.Object)
                                {
                                    if (from.TryGetProperty("phoneNumber", out JsonElement phoneNumber))
                                    {
                                        Console.WriteLine($"From phone number: {phoneNumber.ToString()}");
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception jsonEx)
                {
                    Console.WriteLine($"Error parsing JSON directly: {jsonEx.Message}");
                }
                
                // Now proceed with the regular deserialization
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    NumberHandling = JsonNumberHandling.AllowReadingFromString
                };
                
                MessageStoreResponse messageStoreResponse;
                try
                {
                    messageStoreResponse = JsonSerializer.Deserialize<MessageStoreResponse>(responseContent, jsonOptions);
                }
                catch (JsonException jsonEx)
                {
                    Console.WriteLine($"JSON deserialization error: {jsonEx.Message}");
                    Console.WriteLine($"Path: {jsonEx.Path}, LineNumber: {jsonEx.LineNumber}");
                    
                    // Try alternative approach if the standard deserialization fails
                    Console.WriteLine("Trying alternative deserialization approach...");
                    var records = ExtractMessagesManually(responseContent);
                    
                    Console.WriteLine($"Manual extraction returned {records.Count} messages");
                    
                    foreach (var record in records)
                    {
                        conversation.Messages.Add(record);
                    }
                    
                    Console.WriteLine($"Added {conversation.Messages.Count} messages to conversation from manual extraction");
                    
                    return conversation;
                }
                
                if (messageStoreResponse?.records != null)
                {
                    Console.WriteLine($"Successfully deserialized {messageStoreResponse.records.Length} records");
                    
                    foreach (var record in messageStoreResponse.records)
                    {
                        // Skip messages without to/from information
                        if (record.from == null || record.to == null || record.to.Length == 0)
                        {
                            Console.WriteLine("Skipping message with missing to/from information");
                            continue;
                        }
                            
                        // Extract the other party's phone number (not our number)
                        string otherPartyNumber = null;
                        bool isInbound = record.direction == "Inbound";
                        
                        if (isInbound)
                        {
                            // For inbound messages, the from field has the other party's number
                            otherPartyNumber = record.from.phone_number;
                        }
                        else
                        {
                            // For outbound messages, the to field has the other party's number
                            // We use the first recipient
                            otherPartyNumber = record.to[0].phone_number;
                        }
                        
                        // Skip messages without a valid phone number
                        if (string.IsNullOrEmpty(otherPartyNumber))
                        {
                            Console.WriteLine("Skipping message with empty phone number");
                            continue;
                        }
                        
                        // Normalize the phone number for consistent storage and comparison
                        var normalizedPhoneNumber = StringHelper.NormalizePhoneNumber(otherPartyNumber);
                        
                        // Add this message to our conversation
                        try
                        {
                            conversation.Messages.Add(new SmsMessage
                            {
                                Id = record.id.ToString(), // Convert numeric ID to string to match the ChatModels.cs model
                                PhoneNumber = normalizedPhoneNumber,
                                Text = record.text ?? record.subject ?? "(No message content)",
                                Timestamp = record.creation_time,
                                IsInbound = isInbound
                            });
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error adding message to conversation: {ex.Message}");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("No records found in response");
                }
                
                // Sort messages by timestamp (oldest first)
                conversation.Messages.Sort((a, b) => a.Timestamp.CompareTo(b.Timestamp));
                
                Console.WriteLine($"Returning conversation with {conversation.Messages.Count} messages");
                return conversation;
            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Error retrieving all SMS messages: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
                return conversation;
            }
        }
        
        /// <summary>
        /// Manual extraction of messages from JSON when deserialization fails
        /// </summary>
        private List<SmsMessage> ExtractMessagesManually(string jsonContent)
        {
            var messages = new List<SmsMessage>();
            
            try
            {
                using (JsonDocument doc = JsonDocument.Parse(jsonContent))
                {
                    JsonElement root = doc.RootElement;
                    
                    if (root.TryGetProperty("records", out JsonElement records) && records.ValueKind == JsonValueKind.Array)
                    {
                        for (int i = 0; i < records.GetArrayLength(); i++)
                        {
                            try
                            {
                                JsonElement record = records[i];
                                
                                string id = record.TryGetProperty("id", out JsonElement idProp) ? idProp.ToString() : Guid.NewGuid().ToString();
                                string direction = record.TryGetProperty("direction", out JsonElement dirProp) ? dirProp.GetString() : "";
                                bool isInbound = direction == "Inbound";
                                
                                DateTime creationTime = DateTime.UtcNow;
                                if (record.TryGetProperty("creationTime", out JsonElement createTimeProp) || 
                                    record.TryGetProperty("creation_time", out createTimeProp))
                                {
                                    if (createTimeProp.ValueKind == JsonValueKind.String)
                                    {
                                        string timeString = createTimeProp.GetString();
                                        // Try parsing as UTC first, then as local time
                                        if (DateTime.TryParse(timeString, null, DateTimeStyles.RoundtripKind, out DateTime parsedTime))
                                        {
                                            creationTime = parsedTime;
                                        }
                                        else if (DateTime.TryParse(timeString, out DateTime localTime))
                                        {
                                            // If it's not explicitly UTC, assume it's UTC and convert
                                            creationTime = DateTime.SpecifyKind(localTime, DateTimeKind.Utc);
                                        }
                                    }
                                }
                                
                                // Get message text content
                                string text = "";
                                if (record.TryGetProperty("text", out JsonElement textProp) && textProp.ValueKind == JsonValueKind.String)
                                {
                                    text = textProp.GetString();
                                }
                                else if (record.TryGetProperty("subject", out JsonElement subjectProp) && subjectProp.ValueKind == JsonValueKind.String)
                                {
                                    text = subjectProp.GetString();
                                }
                                
                                // Extract phone number
                                string phoneNumber = "";
                                
                                if (isInbound)
                                {
                                    // For inbound, get from.phoneNumber
                                    if (record.TryGetProperty("from", out JsonElement fromProp) && fromProp.ValueKind == JsonValueKind.Object)
                                    {
                                        if (fromProp.TryGetProperty("phoneNumber", out JsonElement fromPhoneProp) || 
                                            fromProp.TryGetProperty("phone_number", out fromPhoneProp))
                                        {
                                            phoneNumber = fromPhoneProp.GetString();
                                        }
                                    }
                                }
                                else
                                {
                                    // For outbound, get to[0].phoneNumber
                                    if (record.TryGetProperty("to", out JsonElement toProp) && toProp.ValueKind == JsonValueKind.Array)
                                    {
                                        if (toProp.GetArrayLength() > 0)
                                        {
                                            JsonElement firstTo = toProp[0];
                                            if (firstTo.TryGetProperty("phoneNumber", out JsonElement toPhoneProp) || 
                                                firstTo.TryGetProperty("phone_number", out toPhoneProp))
                                            {
                                                phoneNumber = toPhoneProp.GetString();
                                            }
                                        }
                                    }
                                }
                                
                                if (!string.IsNullOrEmpty(phoneNumber))
                                {
                                    // Normalize the phone number for consistent storage and comparison
                                    var normalizedPhoneNumber = StringHelper.NormalizePhoneNumber(phoneNumber);
                                    
                                    messages.Add(new SmsMessage
                                    {
                                        Id = id,
                                        PhoneNumber = normalizedPhoneNumber,
                                        Text = text ?? "(No message content)",
                                        Timestamp = creationTime,
                                        IsInbound = isInbound
                                    });
                                }
                            }
                            catch (Exception recordEx)
                            {
                                Console.WriteLine($"Error processing record {i}: {recordEx.Message}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in manual extraction: {ex.Message}");
            }
            
            return messages;
        }
    }
}
