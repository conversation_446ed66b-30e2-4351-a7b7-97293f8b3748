﻿@namespace Surefire.Domain.Forms.Components
@using Surefire.Domain.Clients.Models
@using Surefire.Domain.Forms.Models
@using Microsoft.FluentUI.AspNetCore.Components


@if (certList is null)
{
    <FluentSkeleton Width="100%" Height="30px" Shimmer="true"></FluentSkeleton>
}
else
{
    <table class="table" width="100%">
        <thead>
            <tr style="text-align:left;">
                <th>ID</th>
                <th>Holder Name</th>
                <th>Desc</th>
                <th>Created</th>
                <th>Modified</th>
            </tr>
        </thead>
        <tbody>

        @foreach (var item in certList)
        {
            <tr>
                <td>@item.CertificateId</td>
                <td><a href="/Forms/Certificate/@item.CertificateId">@item.HolderName</a></td>
                    <td>@(item.ProjectName?.Length > 100 ? item.ProjectName.Substring(0, 100) : item.ProjectName)</td>
                <td>@item.DateCreated?.ToString("MM/dd/yyyy") - @item.CreatedBy?.FirstName</td>
                <td>@item.DateModified?.ToString("MM/dd/yyyy") - @item.ModifiedBy?.FirstName</td>
            </tr>
        }
        </tbody>
    </table>
}
@code {
    [Parameter]
    public List<Certificate> certList { get; set; }
}
