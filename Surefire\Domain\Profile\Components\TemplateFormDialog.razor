@using Surefire.Domain.Forms.Models
@using Syncfusion.Blazor.RichTextEditor
@using Microsoft.FluentUI.AspNetCore.Components
@using Surefire.Interfaces
@implements IDialogContentComponent<EmailTemplate>

<div class="template-form">
    <div class="form-top-row">
        <div class="form-group name-field">
            <FluentTextField @bind-Value="Content.Name" Label="Template Name" Required="true" Placeholder="Enter a descriptive name" />
        </div>
        <div class="form-group description-field">
            <FluentTextField @bind-Value="Content.Description" Label="Description" Placeholder="Optional description for internal use" />
        </div>
        <div class="form-group status-toggle">
            <FluentCheckbox @bind-Value="Content.IsActive" Label="Active" />
            <span class="status-help">Inactive templates won't be available for use</span>
        </div>
    </div>
    
    <hr class="form-divider" />
    
    <div class="form-group subject-field">
        <FluentTextField @bind-Value="Content.Subject" Label="Email Subject" Required="true" Placeholder="Enter email subject line" />
    </div>
    
    <div class="form-group">
        <label class="editor-label">Email Body</label>
        <div class="editor-wrapper">
            <SfRichTextEditor @bind-Value="Content.Body" Height="450px">
                <RichTextEditorToolbarSettings Items="@Tools" />
            </SfRichTextEditor>
        </div>
    </div>
    
    <hr class="form-divider" />
</div>

<FluentDialogFooter>
    <FluentButton Appearance="Appearance.Accent" OnClick="SaveAsync">Save Template</FluentButton>
    <FluentButton OnClick="CancelAsync">Cancel</FluentButton>
</FluentDialogFooter>

@code {
    [CascadingParameter] 
    public FluentDialog Dialog { get; set; }
    
    [Parameter]
    public EmailTemplate Content { get; set; }
    
    [Inject] 
    private IEmailTemplateService EmailTemplateService { get; set; }
    
    private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
    {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.StrikeThrough },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.FontColor },
        new ToolbarItemModel() { Command = ToolbarCommand.BackgroundColor },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.Alignments },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.CreateLink },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.NumberFormatList },
        new ToolbarItemModel() { Command = ToolbarCommand.BulletFormatList },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.Outdent },
        new ToolbarItemModel() { Command = ToolbarCommand.Indent },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo }
    };
    
    private async Task SaveAsync()
    {
        if (Content.EmailTemplateId == 0)
        {
            await EmailTemplateService.CreateTemplateAsync(Content);
        }
        else
        {
            await EmailTemplateService.UpdateTemplateAsync(Content);
        }
        
        await Dialog.CloseAsync(Content);
    }
    
    private async Task CancelAsync()
    {
        await Dialog.CancelAsync();
    }
} 