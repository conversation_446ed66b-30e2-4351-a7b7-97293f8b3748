@namespace Surefire.Domain.Shared.Components
@using System.Linq
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@using Microsoft.FluentUI.AspNetCore.Components
@using Syncfusion.Blazor.RichTextEditor
@using Surefire.Domain.Forms.Models
@using Surefire.Interfaces
@using Surefire.Data
@using Surefire.Components
@using Surefire.Components.Layout
@using Surefire.Domain.Ember
@inject IJSRuntime JSRuntime
@inject IEmailTemplateService EmailTemplateService
@inject EmberService EmberService

<div class="template-manager">
    @if (!isEditing)
    {
        <div class="template-list-section">
            <div class="template-header">
                <h3>Email Templates</h3>
                <FluentButton Appearance="Appearance.Accent" OnClick="CreateNewTemplate">
                    <FluentIcon Value="@(new Icons.Regular.Size16.Add())" />
                    New Template
                </FluentButton>
            </div>

            @if (templates == null)
            {
                <div class="loading-container">
                    <FluentProgressRing Width="40px" Color="#1b8ce3" />
                    <span>Loading templates...</span>
                </div>
            }
            else if (!templates.Any())
            {
                <div class="no-templates">
                    <FluentIcon Value="@(new Icons.Regular.Size48.Mail())" />
                    <p>No email templates found. Create one to get started.</p>
                    <FluentButton Appearance="Appearance.Accent" OnClick="CreateNewTemplate">
                        Create Template
                    </FluentButton>
                </div>
            }
            else
            {
                <FluentDataGrid Items="@templates.AsQueryable()" TGridItem="EmailTemplate" GridTemplateColumns="1fr 1.5fr 1.5fr 100px 120px">
                    <PropertyColumn Title="Name" Property="@(t => t.Name)" Sortable="true"></PropertyColumn>
                    <PropertyColumn Title="Subject" Property="@(t => t.Subject)" Sortable="true"></PropertyColumn>
                    <PropertyColumn Title="Description" Property="@(t => t.Description)"></PropertyColumn>
                    <TemplateColumn Title="Status">
                        <ChildContent>
                            <div class="status-badge @(context.IsActive ? "active" : "inactive")">
                                @(context.IsActive ? "Active" : "Inactive")
                            </div>
                        </ChildContent>
                    </TemplateColumn>
                    <TemplateColumn Title="Actions">
                        <ChildContent>
                            <FluentStack Orientation="Orientation.Horizontal" Gap="8">
                                <FluentButton Appearance="Appearance.Outline" OnClick="() => EditTemplate(context)" Title="Edit">
                                    <FluentIcon Value="@(new Icons.Regular.Size16.Edit())" />
                                </FluentButton>
                                <FluentButton Appearance="Appearance.Outline" OnClick="() => DeleteTemplate(context)" Title="Delete">
                                    <FluentIcon Value="@(new Icons.Regular.Size16.Delete())" />
                                </FluentButton>
                            </FluentStack>
                        </ChildContent>
                    </TemplateColumn>
                </FluentDataGrid>
            }
        </div>
    }
    else
    {
        <div class="template-form">
            <div class="form-header">
                <h4>@(selectedTemplate.EmailTemplateId == 0 ? "Create Template" : "Edit Template")</h4>
                <div class="form-actions header-actions">
                    <FluentButton Appearance="Appearance.Accent" OnClick="SaveTemplate">Save Template</FluentButton>
                    <FluentButton OnClick="CancelEdit">Cancel</FluentButton>
                </div>
            </div>

            <div class="form-top-row">
                <div class="form-group name-field">
                    <FluentTextField @bind-Value="selectedTemplate.Name" Label="Template Name" Required="true" Placeholder="Enter a descriptive name" />
                </div>
                <div class="form-group description-field">
                    <FluentTextField @bind-Value="selectedTemplate.Description" Label="Description" Placeholder="Optional description for internal use" />
                </div>
                <div class="form-group status-toggle">
                    <FluentCheckbox @bind-Value="selectedTemplate.IsActive" Label="Active" />
                    <span class="status-help">Inactive templates won't be available for use</span>
                </div>
            </div>

            <hr class="form-divider" />
            
            <div class="form-requirements">
                <h5>Template Requirements</h5>
                <div class="requirements-checkboxes">
                    <FluentCheckbox @bind-Value="selectedTemplate.NeedsPolicy" Label="Requires Policy Selection" />
                    <FluentCheckbox @bind-Value="selectedTemplate.NeedsContact" Label="Requires Contact Selection" />
                    <FluentCheckbox @bind-Value="selectedTemplate.NeedsProduct" Label="Requires Product Selection" />
                    <FluentCheckbox @bind-Value="selectedTemplate.NeedsPayment" Label="Requires Payment Amount" />
                    <FluentCheckbox @bind-Value="selectedTemplate.NeedsDownPayment" Label="Requires Down Payment Amount" />
                </div>
                
                <div class="form-group custom-function">
                    <FluentTextField @bind-Value="selectedTemplate.CustomFunction" Label="Custom Function" Placeholder="Leave blank for standard template" />
                    <span class="custom-function-help">Set to "LossRunRequester" for Loss Run functionality</span>
                </div>
            </div>

            <hr class="form-divider" />

            <div class="form-group subject-field">
                <FluentTextField @bind-Value="selectedTemplate.Subject" Label="Email Subject" Required="true" Placeholder="Enter email subject line" />
            </div>

            <div class="form-group">
                <label class="editor-label">Email Body</label>
                <div class="editor-wrapper">
                    <SfRichTextEditor @bind-Value="selectedTemplate.Body" Height="450px">
                        <RichTextEditorToolbarSettings Items="@Tools" />
                    </SfRichTextEditor>
                    <div class="available-variables">
                        Currently available variables: {PolicyType}{PolicyType64}{PolicyNumber}{ExpirationDate}{ClientName}{ClientName64}{ContactFirstName}{ContactEmail}{PaymentAmount}{DownPaymentAmount}
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private List<EmailTemplate> templates;
    private EmailTemplate selectedTemplate;
    private bool isEditing;

    private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
    {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.StrikeThrough },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.FontColor },
        new ToolbarItemModel() { Command = ToolbarCommand.BackgroundColor },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.Alignments },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.CreateLink },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.NumberFormatList },
        new ToolbarItemModel() { Command = ToolbarCommand.BulletFormatList },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.Outdent },
        new ToolbarItemModel() { Command = ToolbarCommand.Indent },
        new ToolbarItemModel() { Command = ToolbarCommand.Separator },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo },
        new ToolbarItemModel() { Command = ToolbarCommand.ClearFormat },
        new ToolbarItemModel() { Command = ToolbarCommand.SourceCode }
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadTemplates();
    }

    private async Task LoadTemplates()
    {
        templates = await EmailTemplateService.GetAllTemplatesAsync();
    }

    private void CreateNewTemplate()
    {
        selectedTemplate = new EmailTemplate
            {
                IsActive = true
            };
        isEditing = true;
    }

    private void EditTemplate(EmailTemplate template)
    {
        selectedTemplate = new EmailTemplate
            {
                EmailTemplateId = template.EmailTemplateId,
                Name = template.Name,
                Subject = template.Subject,
                Body = template.Body,
                Description = template.Description,
                IsActive = template.IsActive,
                NeedsPolicy = template.NeedsPolicy,
                NeedsContact = template.NeedsContact,
                NeedsProduct = template.NeedsProduct,
                NeedsPayment = template.NeedsPayment,
                NeedsDownPayment = template.NeedsDownPayment,
                CustomFunction = template.CustomFunction
            };
        isEditing = true;
    }

    private async Task SaveTemplate()
    {
        if (selectedTemplate.EmailTemplateId == 0)
        {
            await EmailTemplateService.CreateTemplateAsync(selectedTemplate);
        }
        else
        {
            await EmailTemplateService.UpdateTemplateAsync(selectedTemplate);
        }

        await LoadTemplates();
        CancelEdit();
    }

    private void CancelEdit()
    {
        selectedTemplate = null;
        isEditing = false;
    }

    private async Task DeleteTemplate(EmailTemplate template)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"Are you sure you want to delete the template '{template.Name}'?");
        if (confirmed)
        {
            await EmailTemplateService.DeleteTemplateAsync(template.EmailTemplateId);
            await LoadTemplates();
        }
    }
}
