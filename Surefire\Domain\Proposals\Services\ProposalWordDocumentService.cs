using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using DocumentFormat.OpenXml;
using Newtonsoft.Json.Linq;
using Surefire.Domain.Attachments.Models;
using Surefire.Domain.Attachments.Services;
using Surefire.Domain.Clients.Models;
using Surefire.Domain.Renewals.Models;
using Surefire.Domain.Shared.Models;
using Surefire.Domain.Contacts.Models;
using Surefire.Domain.Logs;
using Surefire.Domain.Shared.Helpers;
using Microsoft.EntityFrameworkCore;
using Surefire.Data;

namespace Surefire.Domain.Proposals.Services
{
    public class ProposalWordDocumentService
    {
        private readonly AttachmentService _attachmentService;
        private readonly ILoggingService _log;
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

        public ProposalWordDocumentService(
            AttachmentService attachmentService,
            ILoggingService log,
            IDbContextFactory<ApplicationDbContext> contextFactory)
        {
            _attachmentService = attachmentService;
            _log = log;
            _contextFactory = contextFactory;
        }

        /// <summary>
        /// Creates a Word document proposal from the refined JSON data
        /// </summary>
        /// <param name="refinedJsonAttachment">The refined JSON attachment containing proposal data</param>
        /// <param name="clientId">Client ID for additional data</param>
        /// <param name="renewalId">Renewal ID for additional data</param>
        /// <param name="attachmentGroupId">Attachment group ID for saving the document</param>
        /// <returns>The created Word document attachment</returns>
        public async Task<Attachment> CreateProposalWordDocumentAsync(
            Attachment refinedJsonAttachment, 
            int clientId, 
            int renewalId, 
            int attachmentGroupId)
        {
            try
            {
                // Load the refined JSON data
                var jsonData = await LoadJsonDataAsync(refinedJsonAttachment);
                
                // Load additional data from database
                var (client, renewal, proposal) = await LoadAdditionalDataAsync(clientId, renewalId);

                // Create the Word document
                var wordDocumentAttachment = await GenerateWordDocumentAsync(jsonData, client, renewal, proposal, attachmentGroupId, clientId, renewalId);

                return wordDocumentAttachment;
            }
            catch (Exception ex)
            {
                await _log.LogAsync(LogLevel.Error, $"Error creating Word document: {ex.Message}", "ProposalWordDocumentService", ex);
                throw;
            }
        }

        private async Task<JObject> LoadJsonDataAsync(Attachment jsonAttachment)
        {
            var jsonFilePath = Path.Combine(
                Directory.GetCurrentDirectory(), 
                "wwwroot", 
                jsonAttachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()), 
                jsonAttachment.HashedFileName);

            if (!File.Exists(jsonFilePath))
            {
                throw new FileNotFoundException($"JSON file not found: {jsonFilePath}");
            }

            var jsonContent = await File.ReadAllTextAsync(jsonFilePath);
            var jsonData = JObject.Parse(jsonContent);

            return jsonData;
        }

        private async Task<(Client client, Renewal renewal, Proposal proposal)> LoadAdditionalDataAsync(int clientId, int renewalId)
        {
            using var context = _contextFactory.CreateDbContext();
            
            var client = await context.Clients
                .Include(c => c.Address)
                .Include(c => c.PrimaryContact)
                .FirstOrDefaultAsync(c => c.ClientId == clientId);

            var renewal = await context.Renewals
                .Include(r => r.Carrier)
                .Include(r => r.Product)
                .Include(r => r.AssignedTo)
                .FirstOrDefaultAsync(r => r.RenewalId == renewalId);

            var proposal = await context.Proposals
                .FirstOrDefaultAsync(p => p.RenewalId == renewalId);

            return (client, renewal, proposal);
        }

        private async Task<Attachment> GenerateWordDocumentAsync(
            JObject jsonData, 
            Client client, 
            Renewal renewal, 
            Proposal proposal,
            int attachmentGroupId, 
            int clientId, 
            int renewalId)
        {
            // Template path
            var templatePath = Path.Combine(Directory.GetCurrentDirectory(), "Domain", "Proposals", "Context", "worddoc-template.docx");

            if (!File.Exists(templatePath))
            {
                throw new FileNotFoundException($"Template file not found: {templatePath}");
            }

            // Create output file path - use uploads/{clientid}/{attachmentgroupid} structure
            var outputFileName = $"Proposal_{client?.Name?.Replace(" ", "_") ?? "Unknown"}_{DateTime.Now:yyyyMMdd_HHmmss}.docx";
            var outputDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", "clients", clientId.ToString(), attachmentGroupId.ToString());

            if (!Directory.Exists(outputDirectory))
            {
                Directory.CreateDirectory(outputDirectory);
            }

            var hash = StringHelper.GenerateFiveCharacterHash(outputFileName);
            var hashedFileName = $"{Path.GetFileNameWithoutExtension(outputFileName)}_{hash}.docx";
            var outputPath = Path.Combine(outputDirectory, hashedFileName);

            // Copy template to output location
            File.Copy(templatePath, outputPath, true);

            // Process the document
            using (var document = WordprocessingDocument.Open(outputPath, true))
            {
                var mainPart = document.MainDocumentPart;
                if (mainPart?.Document?.Body == null)
                {
                    throw new InvalidOperationException("Invalid Word document template");
                }

                // Fill in simple content controls
                await FillContentControlsAsync(mainPart, jsonData, client, renewal, proposal);

                // Handle repeating sections for arrays
                await ProcessRepeatingRowsAsync(mainPart, jsonData);

                // Handle bullet point lists for terms and contingencies
                await ProcessBulletPointListsAsync(mainPart, jsonData);

                // Perform document cleanup
                await PerformDocumentCleanupAsync(mainPart, jsonData);

                // Process payment links
                await ProcessPaymentLinksAsync(document, client, renewal, proposal, jsonData);

                // Remove content controls while preserving their content
                await RemoveContentControlsAsync(mainPart);

                mainPart.Document.Save();
            }

            // Create attachment record
            var attachment = new Attachment
            {
                OriginalFileName = outputFileName,
                HashedFileName = hashedFileName,
                FileFormat = ".docx",
                FileSize = new FileInfo(outputPath).Length,
                LocalPath = Path.Combine("uploads", "clients", clientId.ToString(), attachmentGroupId.ToString()).Replace("\\", "/"),
                Description = "Generated Proposal Document",
                AttachmentGroupId = attachmentGroupId,
                ClientId = clientId,
                RenewalId = renewalId,
                IsProposal = true,
                DateCreated = DateTime.UtcNow
            };

            // Save attachment to database
            await _attachmentService.SaveAttachmentDirectlyAsync(attachment);

            return attachment;
        }

        private async Task FillContentControlsAsync(
            MainDocumentPart mainPart, 
            JObject jsonData, 
            Client client, 
            Renewal renewal,
            Proposal proposal)
        {
            var contentControls = mainPart.Document.Descendants<SdtElement>().ToList();

            // Calculate derived values first
            var calculatedValues = CalculateValues(jsonData, proposal);

            foreach (var control in contentControls)
            {
                var alias = control.SdtProperties?.GetFirstChild<SdtAlias>()?.Val?.Value;
                var tag = control.SdtProperties?.GetFirstChild<Tag>()?.Val?.Value;

                if (string.IsNullOrEmpty(alias)) 
                {
                    // Try using Tag if Alias is empty
                    alias = tag;
                    if (string.IsNullOrEmpty(alias)) continue;
                }

                var value = GetValueForAlias(alias, jsonData, client, renewal, proposal, calculatedValues);

                if (!string.IsNullOrEmpty(value))
                {
                    SetContentControlValue(control, value);
                }
            }

            await Task.CompletedTask;
        }

        private Dictionary<string, string> CalculateValues(JObject jsonData, Proposal proposal)
        {
            var calculated = new Dictionary<string, string>();

            // Helper functions
            decimal ParseCurrency(string s)
            {
                if (string.IsNullOrWhiteSpace(s)) return 0m;
                decimal.TryParse(s.Replace("$", "").Replace(",", "").Trim(), out var val);
                return val;
            }

            // Get base values from JSON
            var purePremium = ParseCurrency(GetJsonValue("purePremium", jsonData));
            var totalTaxesAndFees = ParseCurrency(GetJsonValue("totalTaxesAndFees", jsonData));
            var brokerFee = proposal?.BrokerFee ?? 400m;
            var minimumEarnedPercent = ParseCurrency(GetJsonValue("minimumEarnedPercent", jsonData));
            if (minimumEarnedPercent == 0) minimumEarnedPercent = 25m;

            // Calculate derived values
            var totalTaxesAndFeesWithBroker = totalTaxesAndFees + brokerFee;
            var totalCost = purePremium + totalTaxesAndFeesWithBroker;
            var minimumEarnedAmount = (purePremium * (minimumEarnedPercent / 100m)) + totalTaxesAndFeesWithBroker;

            // Store calculated values (formatted with commas when appropriate)
            calculated["calcTotalPure"] = purePremium.ToString("N2");
            calculated["calcTotalFees"] = totalTaxesAndFeesWithBroker.ToString("N2");
            calculated["calcTotal"] = totalCost.ToString("N2");
            calculated["calcDown"] = minimumEarnedAmount.ToString("N2");
            calculated["calcMinEarn"] = (purePremium * (minimumEarnedPercent / 100m)).ToString("N2");
            calculated["intPurePrem"] = purePremium.ToString("N2");
            calculated["intRbf"] = brokerFee.ToString("N2");
            calculated["varRbf2"] = brokerFee.ToString("N2");

            return calculated;
        }

        private string GetValueForAlias(string alias, JObject jsonData, Client client, Renewal renewal, Proposal proposal, Dictionary<string, string> calculatedValues)
        {
            // Check calculated values first
            if (calculatedValues.ContainsKey(alias))
            {
                return calculatedValues[alias];
            }

            // Handle special cases based on CSV mappings
            return alias switch
            {
                "varTodaysDate" => DateTime.Now.ToString("M/d/yyyy"),
                "varTitleCoverage" => renewal?.Product?.LineName ?? string.Empty,
                "varTitlePresentedTo" => client?.Name ?? string.Empty,
                "varClientAddressLine1" => client?.Name ?? string.Empty,
                "varClientAddressLine2" => client?.Address?.AddressLine1 ?? string.Empty,
                "varClientAddressLine3" => $"{client?.Address?.City ?? ""}, {client?.Address?.State ?? ""} {client?.Address?.PostalCode ?? ""}".Trim(),
                "varClientAddressLine4" => string.Empty,
                "varConfirmClientName" => client?.Name ?? string.Empty,
                "varConfirmAddress1" => client?.Address?.AddressLine1 ?? string.Empty,
                "varConfirmAddress2" => $"{client?.Address?.City ?? ""}, {client?.Address?.State ?? ""} {client?.Address?.PostalCode ?? ""}".Trim(),
                "varClientFirstName" => client?.PrimaryContact?.FirstName ?? string.Empty,
                "varClientCoverageFor" => renewal?.Product?.LineName ?? string.Empty,
                "varPrimaryCoverage" => renewal?.Product?.LineName ?? string.Empty,
                "varConfirmCoverage" => renewal?.Product?.LineName ?? string.Empty,
                
                // JSON mappings
                "varClientEffectiveDate" => FormatDateWithoutLeadingZeros(jsonData.SelectToken("effectiveDate")),
                "varConfirmRenewalOf" => GetJsonValueWithDefault("priorPolicyNumber", jsonData, "n/a"),
                "varCarrierName" => GetJsonValue("carrierName", jsonData),
                "varConfirmCarrier" => GetJsonValue("carrierName", jsonData),
                "varCarrierRating" => GetJsonValue("carrierRating", jsonData),
                "varEffectiveDate" => FormatDateWithoutLeadingZeros(jsonData.SelectToken("effectiveDate")),
                "varConfirmEffective" => FormatDateWithoutLeadingZeros(jsonData.SelectToken("effectiveDate")),
                "varExpirationDate" => GetExpirationDate(jsonData),
                "varConfirmExpiration" => GetExpirationDate(jsonData),
                "varCoverageName" => GetJsonValue("coverages[0].name", jsonData),
                "varCoverageType" => GetJsonValueWithDefault("coverages[0].type", jsonData, "n/a"),
                "varCoverageRetroActiveDate" => FormatDateWithDefaultFallback(jsonData.SelectToken("coverages[0].retroactiveDate"), "n/a"),
                "varLimitSpecialWording" => GetJsonValue("coverages[0].specialWordingForLimits", jsonData),
                "varDeductibleSpecialWording" => GetJsonValue("coverages[0].specialWordingForDeductibles", jsonData),
                "varDisMinEarned" => GetJsonValueAsWholeNumber("minimumEarnedPercent", jsonData),
                "varTermsValidUntil" => FormatDateWithoutLeadingZeros(jsonData.SelectToken("quoteExpiration")),
                "varPleaseNote" => GetJsonValue("pleaseNote", jsonData),
                
                // Array fields that should get first item if available
                "varLimitName" => GetJsonValue("coverages[0].limits[0].name", jsonData),
                "varLimitDollarAmount" => GetJsonValue("coverages[0].limits[0].amount", jsonData),
                "varDeductibleName" => GetJsonValue("coverages[0].deductibles[0].name", jsonData),
                "varDeductibleDollarAmount" => GetJsonValue("coverages[0].deductibles[0].amount", jsonData),
                "varRateNum" => GetJsonValue("rates[0].number", jsonData),
                "varRateLoc" => GetJsonValue("rates[0].locationNumber", jsonData),
                "varRateCode" => GetJsonValue("rates[0].code", jsonData),
                "varRateDescription" => GetJsonValue("rates[0].description", jsonData),
                "varRateBasis" => GetJsonValue("rates[0].basis", jsonData),
                "varRateExp" => FormatExposureAsCurrency(jsonData.SelectToken("rates[0].exposure")),
                "varRate" => GetJsonValue("rates[0].modifier", jsonData),
                "varRatePr" => GetJsonValue("rates[0].premium", jsonData),
                "varDriverNum" => GetJsonValue("drivers[0].number", jsonData),
                "varDriverLoc" => GetJsonValue("drivers[0].locationNumber", jsonData),
                "varDriverName" => GetJsonValue("drivers[0].legalName", jsonData),
                "varDriverDob" => FormatDateWithoutLeadingZeros(jsonData.SelectToken("drivers[0].dateOfBirth")),
                "varVehNum" => GetJsonValue("vehicles[0].number", jsonData),
                "varVehLoc" => GetJsonValue("vehicles[0].locationNumber", jsonData),
                "varVehVin" => GetJsonValue("vehicles[0].vin", jsonData),
                "varVehDescription" => GetJsonValue("vehicles[0].yearMakeModel", jsonData),
                "varLocationNum" => GetJsonValue("locations[0].locationNumber", jsonData),
                "varLocationAddress" => GetJsonValue("locations[0].fullAddress", jsonData),
                "varLocationDescription" => GetJsonValue("locations[0].description", jsonData),
                "varEndTitle" => GetJsonValue("endorsements[0].title", jsonData),
                "varEndCode" => GetJsonValue("endorsements[0].code", jsonData),
                "varTermsConting" => GetJsonValue("contingencies[0].description", jsonData),
                "varTermsMore" => GetJsonValue("additionalTerms[0].text", jsonData),
                
                // Proposal mappings (formatted with commas)
                "intRbf" => proposal?.BrokerFee?.ToString("N2") ?? "400.00",
                "varRbf2" => proposal?.BrokerFee?.ToString("N2") ?? "400.00",
                
                _ => string.Empty
            };
        }

        private string GetJsonValue(string path, JObject jsonData)
        {
            try
            {
                var token = jsonData.SelectToken(path);
                return token?.ToString() ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        private string GetJsonValueAsWholeNumber(string path, JObject jsonData)
        {
            try
            {
                var token = jsonData.SelectToken(path);
                if (token == null) return string.Empty;
                
                var value = token.ToString();
                
                // Remove currency symbols, percentage signs, and commas
                value = value.Replace("$", "").Replace("%", "").Replace(",", "").Trim();
                
                // Try to parse as decimal and return as whole number
                if (decimal.TryParse(value, out var numericValue))
                {
                    return ((int)Math.Round(numericValue)).ToString();
                }
                
                return value;
            }
            catch
            {
                return string.Empty;
            }
        }

        private string GetPlainNumberFromToken(JToken token)
        {
            if (token == null) return string.Empty;
            
            var value = token.ToString();
            value = value.Replace("$", "").Replace("%", "").Replace(",", "").Trim();
            
            // Try to parse as decimal to ensure it's a valid number
            if (decimal.TryParse(value, out var numericValue))
            {
                return numericValue.ToString("F2");
            }
            
            return value;
        }

        private string FormatExposureAsCurrency(JToken token)
        {
            if (token == null) return string.Empty;
            
            var value = token.ToString();
            if (string.IsNullOrWhiteSpace(value)) return string.Empty;
            
            // Remove any existing currency symbols, commas, and whitespace
            value = value.Replace("$", "").Replace(",", "").Trim();
            
            // Try to parse as decimal and format as currency
            if (decimal.TryParse(value, out var numericValue))
            {
                return numericValue.ToString("C0"); // Currency format with no decimal places
            }
            
            // If not a valid number, return the original value
            return token.ToString();
        }

        private string FormatDateWithoutLeadingZeros(string dateString)
        {
            if (string.IsNullOrWhiteSpace(dateString)) return string.Empty;
            
            // Try to parse the date string
            if (DateTime.TryParse(dateString, out var parsedDate))
            {
                return parsedDate.ToString("M/d/yyyy"); // No leading zeros for month and day
            }
            
            // If parsing fails, return the original string
            return dateString;
        }

        private string FormatDateWithoutLeadingZeros(JToken token)
        {
            if (token == null) return string.Empty;
            
            var dateString = token.ToString();
            return FormatDateWithoutLeadingZeros(dateString);
        }

        private string FormatDateWithDefaultFallback(JToken token, string defaultValue)
        {
            if (token == null) return defaultValue;
            
            var dateString = token.ToString();
            if (string.IsNullOrWhiteSpace(dateString)) return defaultValue;
            
            // Try to parse the date string
            if (DateTime.TryParse(dateString, out var parsedDate))
            {
                return parsedDate.ToString("M/d/yyyy"); // No leading zeros for month and day
            }
            
            // If parsing fails, return the default value
            return defaultValue;
        }

        private string GetExpirationDate(JObject jsonData)
        {
            var expirationDate = GetJsonValue("expirationDate", jsonData);
            
            // If expiration date is blank or null, but there is an effective date, make the expiration date one year after the effective date
            if (string.IsNullOrWhiteSpace(expirationDate))
            {
                var effectiveDate = GetJsonValue("effectiveDate", jsonData);
                if (!string.IsNullOrWhiteSpace(effectiveDate))
                {
                    if (DateTime.TryParse(effectiveDate, out var parsedEffectiveDate))
                    {
                        return parsedEffectiveDate.AddYears(1).ToString("M/d/yyyy");
                    }
                }
            }
            
            return expirationDate;
        }

        private string GetJsonValueWithDefault(string path, JObject jsonData, string defaultValue)
        {
            var value = GetJsonValue(path, jsonData);
            return string.IsNullOrWhiteSpace(value) ? defaultValue : value;
        }

        private void SetContentControlValue(SdtElement control, string value)
        {
            // Handle both empty and non-empty values (empty values clear the content)
            var textElements = control.Descendants<Text>().ToList();
            if (textElements.Any())
            {
                textElements.First().Text = value ?? string.Empty;
                foreach (var extra in textElements.Skip(1).ToList())
                {
                    extra.Parent?.Remove();
                }
            }
            else
            {
                var run = control.Descendants<Run>().FirstOrDefault();
                if (run != null)
                {
                    run.AppendChild(new Text(value ?? string.Empty));
                }
            }
        }

        private async Task ProcessRepeatingRowsAsync(MainDocumentPart mainPart, JObject jsonData)
        {
            var tables = mainPart.Document.Descendants<Table>().ToList();
            await _log.LogAsync(LogLevel.Information, $"Found {tables.Count} tables for processing", "ProposalWordDocumentService");

            // Process each table type based on CSV mappings
            var duplicationConfigs = new[]
            {
                new { Identifier = "varLimitName", ArrayPath = "coverages[0].limits", Fields = new[] { "name", "amount" } },
                new { Identifier = "varDeductibleName", ArrayPath = "coverages[0].deductibles", Fields = new[] { "name", "amount" } },
                new { Identifier = "varRateNum", ArrayPath = "rates", Fields = new[] { "number", "locationNumber", "code", "description", "basis", "exposure", "netRate", "premium" } },
                new { Identifier = "varDriverNum", ArrayPath = "drivers", Fields = new[] { "number", "locationNumber", "legalName", "dateOfBirth" } },
                new { Identifier = "varVehNum", ArrayPath = "vehicles", Fields = new[] { "number", "locationNumber", "vin", "yearMakeModel" } },
                new { Identifier = "varLocationNum", ArrayPath = "locations", Fields = new[] { "locationNumber", "fullAddress", "description" } },
                new { Identifier = "varEndTitle", ArrayPath = "endorsements", Fields = new[] { "title", "code" } },
                new { Identifier = "varFeeTitle", ArrayPath = "fees", Fields = new[] { "title", "amount" } }
            };

            foreach (var config in duplicationConfigs)
            {
                await ProcessTableForArray(tables, config.Identifier, config.ArrayPath, config.Fields, jsonData);
            }
        }

        private async Task ProcessBulletPointListsAsync(MainDocumentPart mainPart, JObject jsonData)
        {
            var contentControls = mainPart.Document.Descendants<SdtElement>().ToList();

            // Process contingencies (content control approach)
            var contingenciesArray = jsonData.SelectToken("contingencies") as JArray;
            if (contingenciesArray != null && contingenciesArray.Count > 0)
            {
                var contingencyItems = contingenciesArray.Select(c => c.SelectToken("description")?.ToString() ?? "").Where(s => !string.IsNullOrEmpty(s)).ToList();
                if (contingencyItems.Any())
                {
                    await ProcessContentControlBulletList(contentControls, "varTermsConting", contingencyItems);
                }
            }

            // Process additional terms (content control approach)
            var additionalTermsArray = jsonData.SelectToken("additionalTerms") as JArray;
            if (additionalTermsArray != null && additionalTermsArray.Count > 0)
            {
                var termItems = additionalTermsArray.Select(t => t.SelectToken("text")?.ToString() ?? "").Where(s => !string.IsNullOrEmpty(s)).ToList();
                if (termItems.Any())
                {
                    await ProcessContentControlBulletList(contentControls, "varTermsMore", termItems);
                }
            }

            await Task.CompletedTask;
        }

        private async Task ProcessContentControlBulletList(List<SdtElement> contentControls, string targetAlias, List<string> items)
        {
            if (items == null || items.Count == 0) return;

            foreach (var control in contentControls)
            {
                var alias = control.SdtProperties?.GetFirstChild<SdtAlias>()?.Val?.Value;
                var tag = control.SdtProperties?.GetFirstChild<Tag>()?.Val?.Value;
                
                // Use Tag if Alias is empty (which is the case for this template)
                var controlId = !string.IsNullOrEmpty(alias) ? alias : tag;
                
                if (controlId == targetAlias)
                {   
                    // Fill the content control with the first item
                    SetContentControlValue(control, items[0]);

                    var parentParagraph = control.Ancestors<Paragraph>().FirstOrDefault();
                    if (parentParagraph != null && items.Count > 1)
                    {
                        // Add additional bullet points by cloning the paragraph structure
                        var currentParagraph = parentParagraph;
                        
                        for (int i = 1; i < items.Count; i++)
                        {
                            // Clone the paragraph to maintain bullet formatting
                            var newParagraph = (Paragraph)currentParagraph.CloneNode(true);
                            var clonedControls = newParagraph.Descendants<SdtElement>().ToList();
                            foreach (var clonedControl in clonedControls)
                            {
                                clonedControl.Remove();
                            }
                            
                            // Set the text for this bullet point
                            var textElements = newParagraph.Descendants<Text>().ToList();
                            if (textElements.Any())
                            {
                                textElements.First().Text = items[i];
                                foreach (var extra in textElements.Skip(1).ToList())
                                {
                                    extra.Parent?.Remove();
                                }
                            }
                            else
                            {
                                // Create new text if none exists
                                var run = newParagraph.Descendants<Run>().FirstOrDefault();
                                if (run != null)
                                {
                                    run.RemoveAllChildren<Text>();
                                    run.AppendChild(new Text(items[i]));
                                }
                                else
                                {
                                    var newRun = new Run();
                                    newRun.AppendChild(new Text(items[i]));
                                    newParagraph.AppendChild(newRun);
                                }
                            }
                            
                            // Insert after the current paragraph
                            currentParagraph.InsertAfterSelf(newParagraph);
                            currentParagraph = newParagraph;
                        }
                    }
                    break;
                }
            }

            await Task.CompletedTask;
        }



        private async Task ProcessTableForArray(List<Table> tables, string identifier, string arrayPath, string[] fields, JObject jsonData)
        {
            foreach (var table in tables)
            {
                var templateRow = FindTemplateRowWithIdentifier(table, identifier);
                if (templateRow == null) continue;

                await _log.LogAsync(LogLevel.Information, $"Processing table for {identifier}", "ProposalWordDocumentService");

                var arrayData = jsonData.SelectToken(arrayPath) as JArray;
                if (arrayData == null || arrayData.Count == 0) continue;

                var rowIndex = table.ChildElements.ToList().IndexOf(templateRow);
                var templateClone = (TableRow)templateRow.CloneNode(true);

                // Insert new rows for each array item
                for (int i = 0; i < arrayData.Count; i++)
                {
                    var newRow = (TableRow)templateClone.CloneNode(true);
                    var item = arrayData[i] as JObject;

                    if (item != null)
                    {
                        await FillRowWithData(newRow, identifier, fields, item, i);
                    }

                    table.InsertAt(newRow, rowIndex + i);
                }

                // Remove the template row
                templateRow.Remove();
            }

            await Task.CompletedTask;
        }

        private TableRow FindTemplateRowWithIdentifier(Table table, string identifier)
        {
            var rows = table.Descendants<TableRow>().ToList();
            
            foreach (var row in rows)
            {
                var contentControls = row.Descendants<SdtElement>().ToList();
                foreach (var control in contentControls)
                {
                    var alias = control.SdtProperties?.GetFirstChild<SdtAlias>()?.Val?.Value;
                    var tag = control.SdtProperties?.GetFirstChild<Tag>()?.Val?.Value;
                    
                    // Use Tag if Alias is empty (which is the case for this template)
                    var controlId = !string.IsNullOrEmpty(alias) ? alias : tag;
                    
                    if (controlId == identifier)
                    {
                        return row;
                    }
                }
            }

            return null;
        }

        private async Task FillRowWithData(TableRow row, string identifier, string[] fields, JObject item, int rowIndex)
        {
            var contentControls = row.Descendants<SdtElement>().ToList();
            
            foreach (var control in contentControls)
            {
                var alias = control.SdtProperties?.GetFirstChild<SdtAlias>()?.Val?.Value;
                var tag = control.SdtProperties?.GetFirstChild<Tag>()?.Val?.Value;
                
                // Use Tag if Alias is empty (which is the case for this template)
                var controlId = !string.IsNullOrEmpty(alias) ? alias : tag;
                if (string.IsNullOrEmpty(controlId)) continue;

                // Map controlId to field based on CSV mappings
                var value = GetRowFieldValue(controlId, item, identifier, rowIndex);
                
                // Always set the value, even if it's empty (to clear labels on subsequent rows)
                if (value != null)
                {
                    SetContentControlValue(control, value);
                }
            }
        }

        private string GetRowFieldValue(string alias, JObject item, string identifier, int rowIndex)
        {
            // Map based on CSV field mappings
            return alias switch
            {
                "varLimitName" => item.SelectToken("name")?.ToString() ?? string.Empty,
                "varLimitDollarAmount" => item.SelectToken("amount")?.ToString() ?? string.Empty,
                "varDeductibleName" => item.SelectToken("name")?.ToString() ?? string.Empty,
                "varDeductibleDollarAmount" => item.SelectToken("amount")?.ToString() ?? string.Empty,
                // Handle label content controls - only show on first row
                "txtLimits" => rowIndex == 0 ? "Limits" : string.Empty,
                "txtDeductible" => rowIndex == 0 ? "Deductible" : string.Empty,
                "varRateNum" => item.SelectToken("number")?.ToString() ?? string.Empty,
                "varRateLoc" => item.SelectToken("locationNumber")?.ToString() ?? string.Empty,
                "varRateCode" => item.SelectToken("code")?.ToString() ?? string.Empty,
                "varRateDescription" => item.SelectToken("description")?.ToString() ?? string.Empty,
                "varRateBasis" => item.SelectToken("basis")?.ToString() ?? string.Empty,
                "varRateExp" => FormatExposureAsCurrency(item.SelectToken("exposure")),
                "varRate" => item.SelectToken("modifier")?.ToString() ?? string.Empty,
                "varRatePr" => item.SelectToken("premium")?.ToString() ?? string.Empty,
                "varDriverNum" => item.SelectToken("number")?.ToString() ?? string.Empty,
                "varDriverLoc" => item.SelectToken("locationNumber")?.ToString() ?? string.Empty,
                "varDriverName" => item.SelectToken("legalName")?.ToString() ?? string.Empty,
                "varDriverDob" => FormatDateWithoutLeadingZeros(item.SelectToken("dateOfBirth")),
                "varVehNum" => item.SelectToken("number")?.ToString() ?? string.Empty,
                "varVehLoc" => item.SelectToken("locationNumber")?.ToString() ?? string.Empty,
                "varVehVin" => item.SelectToken("vin")?.ToString() ?? string.Empty,
                "varVehDescription" => item.SelectToken("yearMakeModel")?.ToString() ?? string.Empty,
                "varLocationNum" => item.SelectToken("locationNumber")?.ToString() ?? string.Empty,
                "varLocationAddress" => item.SelectToken("fullAddress")?.ToString() ?? string.Empty,
                "varLocationDescription" => item.SelectToken("description")?.ToString() ?? string.Empty,
                "varEndCode" => item.SelectToken("code")?.ToString() ?? string.Empty,
                "varEndTitle" => item.SelectToken("title")?.ToString() ?? string.Empty,
                "varFeeTitle" => item.SelectToken("title")?.ToString() ?? string.Empty,
                "intFeeAmount" => GetPlainNumberFromToken(item.SelectToken("amount")),
                _ => string.Empty
            };
        }

        private async Task PerformDocumentCleanupAsync(MainDocumentPart mainPart, JObject jsonData)
        {
            // Remove empty content controls for special wording fields
            await RemoveEmptyContentControlsAsync(mainPart, new[] { "varLimitSpecialWording", "varDeductibleSpecialWording" });

            // Remove drivers/vehicles table if both arrays are empty
            await RemoveDriversVehiclesTableIfEmptyAsync(mainPart, jsonData);

            await Task.CompletedTask;
        }

        private async Task RemoveEmptyContentControlsAsync(MainDocumentPart mainPart, string[] controlAliases)
        {
            var contentControls = mainPart.Document.Descendants<SdtElement>().ToList();

            foreach (var alias in controlAliases)
            {
                foreach (var control in contentControls)
                {
                    var controlAlias = control.SdtProperties?.GetFirstChild<SdtAlias>()?.Val?.Value;
                    var controlTag = control.SdtProperties?.GetFirstChild<Tag>()?.Val?.Value;
                    
                    // Use Tag if Alias is empty
                    var controlId = !string.IsNullOrEmpty(controlAlias) ? controlAlias : controlTag;
                    
                    if (controlId == alias)
                    {   
                        control.Remove();
                        break;
                    }
                }
            }

            await Task.CompletedTask;
        }

        private async Task RemoveDriversVehiclesTableIfEmptyAsync(MainDocumentPart mainPart, JObject jsonData)
        {
            // Check if both drivers and vehicles arrays are empty
            var driversArray = jsonData.SelectToken("drivers") as JArray;
            var vehiclesArray = jsonData.SelectToken("vehicles") as JArray;
            
            bool driversEmpty = driversArray == null || driversArray.Count == 0;
            bool vehiclesEmpty = vehiclesArray == null || vehiclesArray.Count == 0;

            if (driversEmpty && vehiclesEmpty)
            {
                var tables = mainPart.Document.Descendants<Table>().ToList();
                
                foreach (var table in tables)
                {
                    // Look for a table that contains both divDrivers and divVehicles content controls
                    var contentControls = table.Descendants<SdtElement>().ToList();
                    bool hasDivDrivers = false;
                    bool hasDivVehicles = false;

                    foreach (var control in contentControls)
                    {
                        var alias = control.SdtProperties?.GetFirstChild<SdtAlias>()?.Val?.Value;
                        var tag = control.SdtProperties?.GetFirstChild<Tag>()?.Val?.Value;
                        var controlId = !string.IsNullOrEmpty(alias) ? alias : tag;

                        if (controlId == "divDrivers") hasDivDrivers = true;
                        if (controlId == "divVehicles") hasDivVehicles = true;
                    }

                    // If this table contains both divDrivers and divVehicles, remove it
                    if (hasDivDrivers && hasDivVehicles)
                    {   
                        table.Remove();
                        break;
                    }
                }
            }

            await Task.CompletedTask;
        }

        private async Task RemoveContentControlsAsync(MainDocumentPart mainPart)
        {
            var contentControls = mainPart.Document.Descendants<SdtElement>().ToList();

            int removedCount = 0;
            int preservedCount = 0;

            foreach (var control in contentControls)
            {
                var alias = control.SdtProperties?.GetFirstChild<SdtAlias>()?.Val?.Value;
                var tag = control.SdtProperties?.GetFirstChild<Tag>()?.Val?.Value;
                var controlId = !string.IsNullOrEmpty(alias) ? alias : tag;

                // Check if this content control should be preserved
                bool shouldPreserve = ShouldPreserveContentControl(control, controlId);

                if (shouldPreserve)
                {
                    preservedCount++;
                }
                else
                {
                    // Remove the content control but preserve its content
                    RemoveContentControlPreservingContent(control);
                    removedCount++;
                }
            }

            await Task.CompletedTask;
        }

        private bool ShouldPreserveContentControl(SdtElement control, string controlId)
        {
            // Preserve intFeeAmount content controls
            if (controlId == "intFeeAmount")
            {
                return true;
            }

            // Check if the content control has the "Cannot be deleted" property
            var sdtProperties = control.SdtProperties;
            if (sdtProperties != null)
            {
                // Look for SdtContentLock element which controls deletion
                var lockElement = sdtProperties.GetFirstChild<DocumentFormat.OpenXml.Wordprocessing.Lock>();
                if (lockElement != null)
                {
                    // Check if deletion is locked
                    var lockValue = lockElement.Val?.Value;
                    if (lockValue == LockingValues.SdtLocked || 
                        lockValue == LockingValues.ContentLocked ||
                        lockValue == LockingValues.SdtContentLocked)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private void RemoveContentControlPreservingContent(SdtElement control)
        {
            // Get the content of the content control
            OpenXmlElement sdtContent = control.GetFirstChild<SdtContentBlock>();
            if (sdtContent == null)
                sdtContent = control.GetFirstChild<SdtContentRun>();
            if (sdtContent == null)
                sdtContent = control.GetFirstChild<SdtContentCell>();

            if (sdtContent != null)
            {
                var parent = control.Parent;
                if (parent != null)
                {
                    // Clone all children from the content control
                    var childrenToMove = sdtContent.ChildElements.ToList();
                    
                    // Insert the children before the content control
                    foreach (var child in childrenToMove)
                    {
                        var clonedChild = child.CloneNode(true);
                        parent.InsertBefore(clonedChild, control);
                    }
                }
            }
            control.Remove();
        }

        /// <summary>
        /// Process payment links in the document
        /// </summary>
        private async Task ProcessPaymentLinksAsync(WordprocessingDocument doc, Client client, Renewal renewal, Proposal proposal, JObject jsonData)
        {
            await _log.LogAsync(LogLevel.Information, "Processing payment links...", "ProposalWordDocumentService");
            
            var hyperlinks = doc.MainDocumentPart.Document.Descendants<Hyperlink>().ToList();
            await _log.LogAsync(LogLevel.Information, $"Found {hyperlinks.Count} hyperlinks in document", "ProposalWordDocumentService");
            
            // Calculate the values we need for the payment links
            var calculatedValues = CalculateValues(jsonData, proposal);
            
            // Parse the calculated values as decimals for URL formatting
            decimal.TryParse(calculatedValues["calcTotal"].Replace(",", ""), out var totalCost);
            decimal.TryParse(calculatedValues["calcDown"].Replace(",", ""), out var minimumEarnedAmount);
            var brokerFee = proposal?.BrokerFee ?? 400m;
            
            // Get client name and sanitize it
            var clientName = client?.Name ?? "";
            clientName = Regex.Replace(clientName, @"[^a-zA-Z0-9\s]", "").Trim();
            
            // Get coverage name
            var coverageName = renewal?.Product?.LineName ?? "";
            coverageName = Regex.Replace(coverageName, @"[^a-zA-Z0-9\s]", "").Trim();

            foreach (var hyperlink in hyperlinks)
            {
                try
                {
                    // Get relationship ID
                    var relationshipId = hyperlink.Id?.Value;
                    if (string.IsNullOrEmpty(relationshipId))
                        continue;
                    
                    // Get current URL
                    var relationship = doc.MainDocumentPart.HyperlinkRelationships
                        .FirstOrDefault(r => r.Id == relationshipId);
                        
                    if (relationship == null)
                        continue;
                        
                    string currentUrl = relationship.Uri.ToString();
                    await _log.LogAsync(LogLevel.Information, $"Processing link with URL: {currentUrl}", "ProposalWordDocumentService");
                    
                    string newUrl = "";
                    
                    // Check which placeholder URL we're dealing with
                    if (currentUrl.Contains("(varAmountFull)", StringComparison.OrdinalIgnoreCase))
                    {
                        await _log.LogAsync(LogLevel.Information, "Found full payment link placeholder", "ProposalWordDocumentService");
                        
                        // Build the full payment link
                        var commentFull = Uri.EscapeDataString($"Full Premium Payment for {coverageName}");
                        newUrl = $"https://metroinsurance.epaypolicy.com/?payer={Uri.EscapeDataString(clientName)}&emailAddress=&amount={totalCost:F2}&comments={commentFull}";
                        
                        await _log.LogAsync(LogLevel.Information, $"Created full payment link: {newUrl}", "ProposalWordDocumentService");
                    }
                    else if (currentUrl.Contains("(varAmountDown)", StringComparison.OrdinalIgnoreCase))
                    {
                        await _log.LogAsync(LogLevel.Information, "Found down payment link placeholder", "ProposalWordDocumentService");
                        
                        // Build the down payment link
                        var commentDown = Uri.EscapeDataString($"Down Payment for {coverageName}");
                        newUrl = $"https://metroinsurance.epaypolicy.com/?payer={Uri.EscapeDataString(clientName)}&emailAddress=&amount={minimumEarnedAmount:F2}&comments={commentDown}";
                        
                        await _log.LogAsync(LogLevel.Information, $"Created down payment link: {newUrl}", "ProposalWordDocumentService");
                    }
                    else if (currentUrl.Contains("(varAmountBrokerFee)", StringComparison.OrdinalIgnoreCase))
                    {
                        await _log.LogAsync(LogLevel.Information, "Found broker fee payment link placeholder", "ProposalWordDocumentService");
                        
                        // Build the broker fee payment link
                        var commentBrokerFee = Uri.EscapeDataString($"Broker Fee for {coverageName}");
                        newUrl = $"https://metroinsurance.epaypolicy.com/?payer={Uri.EscapeDataString(clientName)}&emailAddress=&amount={brokerFee:F2}&comments={commentBrokerFee}";
                        
                        await _log.LogAsync(LogLevel.Information, $"Created broker fee payment link: {newUrl}", "ProposalWordDocumentService");
                    }
                    
                    // If we have a new URL, update the hyperlink
                    if (!string.IsNullOrEmpty(newUrl))
                    {
                        // Get original link text and formatting
                        var originalRun = hyperlink.Descendants<Run>().FirstOrDefault();
                        string linkText = hyperlink.Descendants<Text>().FirstOrDefault()?.Text ?? "Make a Payment";
                        
                        // Preserve original run properties (including styles)
                        RunProperties originalRunProperties = null;
                        if (originalRun?.RunProperties != null)
                        {
                            originalRunProperties = (RunProperties)originalRun.RunProperties.CloneNode(true);
                        }
                        
                        // Remove old relationship
                        doc.MainDocumentPart.DeleteReferenceRelationship(relationship);
                        
                        // Create new relationship
                        var newRelationship = doc.MainDocumentPart.AddHyperlinkRelationship(
                            new Uri(newUrl, UriKind.Absolute), true);
                        hyperlink.Id = newRelationship.Id;
                        
                        // Create a new run with the original text and preserved formatting
                        var run = new Run(new Text(linkText));
                        
                        // Apply original formatting if it exists
                        if (originalRunProperties != null)
                        {
                            run.PrependChild(originalRunProperties);
                            await _log.LogAsync(LogLevel.Information, "Preserved original hyperlink formatting", "ProposalWordDocumentService");
                        }
                        else
                        {
                            // Fallback: Apply "PayLink" style if it exists, otherwise use default formatting
                            var runProperties = new RunProperties();
                            
                            // Try to apply PayLink style
                            var runStyle = new RunStyle() { Val = "PayLink" };
                            runProperties.Append(runStyle);
                            
                            // Add default hyperlink formatting as fallback
                            runProperties.Append(new Bold());
                            runProperties.Append(new Underline() { Val = UnderlineValues.Single });
                            runProperties.Append(new Color() { Val = "0000FF" });
                            
                            run.PrependChild(runProperties);
                            await _log.LogAsync(LogLevel.Information, "Applied PayLink style with fallback formatting", "ProposalWordDocumentService");
                        }
                        
                        // Update the hyperlink
                        hyperlink.RemoveAllChildren();
                        hyperlink.Append(run);
                        
                        await _log.LogAsync(LogLevel.Information, $"Updated hyperlink with new URL: {newUrl}", "ProposalWordDocumentService");
                    }
                }
                catch (Exception ex)
                {
                    await _log.LogAsync(LogLevel.Error, $"Error processing hyperlink: {ex.Message}", "ProposalWordDocumentService", ex);
                }
            }
        }
    }
}