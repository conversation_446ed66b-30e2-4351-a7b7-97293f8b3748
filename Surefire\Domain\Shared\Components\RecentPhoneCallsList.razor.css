﻿.sf-calltable {
    line-height:10px;
    border-collapse:collapse;

}
    .sf-calltable td {
        text-align: left;
        padding: 5px 0px;
        padding: 5px 0px;
    }
.phone-call-buttons {
    min-width:105px;
    max-width:200px;
    border:1px solid #ccc;
    position: relative;
    top: -2px;
}
.phone-icon {
    position: relative;
    top: 0px;
    left: 5px;
}
.phone-icon-cell {
    width: 40px;
    position: relative;
    top: 3px;
}
.phone-longago {
    font-size: .8em;
    position: relative;
    top: -3px;
    left: 10px;
    color: #808080;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.phonetxt {
    font-size: .8em;
    position: relative;
    top: -3px;
    left: 10px;
    color: #808080;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.phone-lognum-out {
    font-size: 1.8em;
    font-weight: 100;
    color: #d44942;
    min-width: 155px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.phone-lognum-in {
    font-size: 1.8em;
    font-weight: 100;
    color: #0f6cbd;
    min-width: 155px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.phone-none {
    font-size:1em;
    color:#b7b7b7;
    position:relative;
    top:-8px;
}
.phone-lognum {
    width: 190px !important;
    position: relative;
    left: -10px;
    top: 2px;
    font-family: 'montserrat';
}
.phonecall-infotext {
    width: 140px;
    overflow: hidden;
    position:relative;
    top:3px;
}
.dialog-content {
    max-height: 70vh;
    overflow-y: auto;
}

.dialog-header {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #eee;
}

.ai-summary-section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #1b8ce3;
}

.ai-summary-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

    .ai-summary-header h4 {
        margin: 0;
        color: #1b8ce3;
    }

.ai-summary-content {
    line-height: 1.5;
}

    .ai-summary-content ul {
        margin: 0;
        padding-left: 20px;
    }

    .ai-summary-content li {
        margin-bottom: 5px;
    }

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
}