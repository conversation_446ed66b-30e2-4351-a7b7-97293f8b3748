﻿.specpadding {
    padding: 20px 20px;
    height: calc(100vh - 117px);
    overflow-y: scroll;
}
.page-title {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: #6c757d;
    margin-bottom: 2rem;
}

.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.available-agents .agent-item {
    padding: 0.75rem;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
}

.available-agents .agent-item-small {
    padding: 0.5rem;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    margin-bottom: 0.25rem;
    background-color: #f8f9fa;
    font-size: 0.9em;
}

.execution-result {
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    border-left: 4px solid #6c757d;
}

    .execution-result.success {
        background-color: #d4edda;
        border-left-color: #28a745;
    }

    .execution-result.error {
        background-color: #f8d7da;
        border-left-color: #dc3545;
    }

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.result-time {
    font-size: 0.9em;
    color: #6c757d;
}

.result-message {
    margin-bottom: 0.75rem;
}

.error-message {
    color: #dc3545;
    font-size: 0.9em;
    margin-top: 0.5rem;
}

.result-data {
    margin-bottom: 0.75rem;
}

    .result-data pre {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 0.25rem;
        padding: 0.75rem;
        margin-top: 0.25rem;
        max-height: 200px;
        overflow-y: auto;
    }

.result-suggestions ul {
    margin-bottom: 0;
    margin-top: 0.25rem;
}

.badge {
    font-size: 0.75em;
    margin-right: 0.25rem;
}

.d-flex {
    display: flex;
}

.gap-2 {
    gap: 0.5rem;
}

.float-end {
    float: right;
}

.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-muted {
    color: #6c757d !important;
}

.bg-secondary {
    background-color: #6c757d !important;
}

.bg-info {
    background-color: #17a2b8 !important;
}

#row1 {
    width: 300px;
}