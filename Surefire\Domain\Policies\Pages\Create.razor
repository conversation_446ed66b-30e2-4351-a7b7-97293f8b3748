﻿@page "/Policies/Create/{ClientId:int}"
@using Microsoft.EntityFrameworkCore
@using Surefire.Domain.Policies.Models
@using Surefire.Domain.Policies.Services
@using Surefire.Domain.Carriers.Models
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.DropDowns
@inject PolicyService PolicyService
@inject StateService _stateService

<_toolbar PageName="Create" />

<div class="page-content">
    <div class="mh1">Create Policy</div>
    @if (Policy is null || Products is null || Carriers is null || Wholesalers is null)
    {
        <p><em>Loading...</em></p>
    }
    else
    {
        <EditForm Model="Policy" OnValidSubmit="CreatePolicy">
            <DataAnnotationsValidator />
            <ValidationSummary />
            <div class="mf-flex mf-flex-row mf-row-container">
                <div class="mf-item-lg">

                    <div class="mb-3">
                        <SfDropDownList TValue="int?" TItem="Product" DataSource="@Products" Placeholder="Line of Business" @bind-Value="Policy.ProductId" AllowFiltering=true PopupHeight="230px" ShowClearButton=true FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Text="LineName" Value="ProductId" />
                        </SfDropDownList>
                    </div>
                    <div class="mb-3">
                        <SfTextBox id="policynumber" @bind-Value="Policy.PolicyNumber" Placeholder="Policy Number" FloatLabelType="FloatLabelType.Always" />
                        <ValidationMessage For="() => Policy.PolicyNumber" class="text-danger" />
                    </div>
                    <div class="mb-3">
                        <SfNumericTextBox TValue="decimal" @bind-Value="Policy.Premium" Placeholder="Premium" FloatLabelType="FloatLabelType.Always" Format="C2" />
                        <ValidationMessage For="() => Policy.Premium" class="text-danger" />
                    </div>
                    <div class="mb-3">
                        <SfTextBox @bind-Value="Policy.Notes" Placeholder="Notes" FloatLabelType="FloatLabelType.Always" />
                        <ValidationMessage For="() => Policy.Notes" class="text-danger" />
                    </div>
                    <div class="mb-3">
                        <SfTextBox @bind-Value="Policy.Status" Placeholder="Status" FloatLabelType="FloatLabelType.Always" />
                        <ValidationMessage For="() => Policy.Status" class="text-danger" />
                    </div>
                </div>
                <div class="mf-item-lg mf-pad-left">
                    <div class="mb-3">
                        <SfDatePicker TValue="DateTime" @bind-Value="Policy.EffectiveDate" Placeholder="Effective Date" FloatLabelType="FloatLabelType.Always" />
                        <ValidationMessage For="() => Policy.EffectiveDate" class="text-danger" />
                    </div>
                    <div class="mb-3">
                        <SfDatePicker TValue="DateTime" @bind-Value="Policy.ExpirationDate" Placeholder="Expiration Date" FloatLabelType="FloatLabelType.Always" />
                        <ValidationMessage For="() => Policy.ExpirationDate" class="text-danger" />
                    </div>

                    <br />

                    <div class="mb-3">
                        <SfDropDownList TValue="int?" TItem="Carrier" DataSource="@Carriers" Placeholder="Issuing Carrier" @bind-Value="Policy.CarrierId" AllowFiltering=true PopupHeight="230px" ShowClearButton=true FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Text="CarrierName" Value="CarrierId" />
                        </SfDropDownList>
                    </div>
                    <div class="mb-3">
                        <SfDropDownList TValue="int?" TItem="Carrier" DataSource="@Wholesalers" Placeholder="Wholesaler" @bind-Value="Policy.WholesalerId" AllowFiltering=true PopupHeight="230px" ShowClearButton=true FloatLabelType="FloatLabelType.Always">
                            <DropDownListFieldSettings Text="CarrierName" Value="CarrierId" />
                        </SfDropDownList>
                    </div>
                </div>
            </div>
            <br />
            <FluentButton Type="ButtonType.Button" Appearance="Appearance.Outline" OnClick="GoBack">Cancel</FluentButton>
            <FluentButton Type="ButtonType.Submit" Appearance="Appearance.Accent">Create Policy</FluentButton>
        </EditForm>
    }
</div>

@code {
    [Parameter]
    public int ClientId { get; set; }

    private Policy Policy { get; set; } = new Policy();
    private List<Product> Products { get; set; }
    private List<Carrier> Carriers { get; set; }
    private List<Carrier> Wholesalers { get; set; }
    [CascadingParameter] public Action<string> UpdateHeader { get; set; }

    protected override async Task OnInitializedAsync()
    {
        UpdateHeader?.Invoke("Policies");
        Products = await _stateService.AllProducts;
        Carriers = await _stateService.AllCarriers;
        Wholesalers = await _stateService.AllWholesalers;
    }

    private async Task CreatePolicy()
    {
        var policyId = await PolicyService.CreatePolicyAsync(Policy, ClientId);
        Navigation.NavigateTo($"/Policies/Details/{policyId}");
        
    }

    public void GoBack()
    {
        Navigation.NavigateTo("/Policies");
    }
}
