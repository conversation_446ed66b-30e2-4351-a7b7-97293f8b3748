namespace Surefire.Domain.Agents.Models
{
    /// <summary>
    /// Request model for navigation operations
    /// </summary>
    public class NavigationRequest
    {
        public string Input { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string SessionId { get; set; } = string.Empty;
        public Dictionary<string, object> Context { get; set; } = new();
    }

    /// <summary>
    /// Response model for navigation operations
    /// </summary>
    public class NavigationResponse
    {
        public bool Success { get; set; }
        public string NavigationUrl { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public Dictionary<string, object> StateChanges { get; set; } = new();
        public NavigationType Type { get; set; }
        public List<string> ExtractedEntities { get; set; } = new();
    }

    /// <summary>
    /// Types of navigation operations
    /// </summary>
    public enum NavigationType
    {
        ClientDetails,
        ClientPolicies,
        ClientTranscriptions,
        CarrierDetails,
        RenewalDetails,
        RenewalProposal,
        Unknown
    }

    /// <summary>
    /// Parsed navigation intent with extracted entities
    /// </summary>
    public class ParsedNavigationIntent
    {
        public NavigationType Type { get; set; }
        public Dictionary<string, string> Entities { get; set; } = new();
        public double Confidence { get; set; }
        public string Intent { get; set; } = string.Empty;
    }

    /// <summary>
    /// Database lookup result for navigation entities
    /// </summary>
    public class NavigationEntityLookup
    {
        public bool Found { get; set; }
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public Dictionary<string, object> AdditionalData { get; set; } = new();
    }
} 