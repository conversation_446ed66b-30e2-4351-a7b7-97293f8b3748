﻿@if (IsOpen)
{
    <div class="custom-menu" style="position: absolute; top: @(Y)px; left: @(X)px;">
        @* Toolbar row for quick actions *@
        @if (ToolbarItems != null && ToolbarItems.Any())
        {
            <div class="toolbar-row">
                @foreach (var item in ToolbarItems)
                {
                    <button @onclick="() => item.Action.Invoke(ItemId)" class="toolbar-button" title="@item.Text">
                        <FluentIcon Value="@item.IconValue" Class="toolbar-icon" />
                    </button>
                }
            </div>
        }
        
        <ul>
            @foreach (var item in MenuItems)
            {
                <li @onclick="() => item.Action.Invoke(ItemId)" class="menu-item">
                    <FluentIcon Value="@item.IconValue" Class="menu-icon" /> @item.Text
                </li>
            }
            @* Add a New Subtask menu item if a callback is provided *@
            @if (OnNewSubtask != null)
            {
                <li @onclick="() => OnNewSubtask.Invoke(ItemId)" class="menu-item">
                    <FluentIcon Value="@(new Icons.Regular.Size24.AddCircle())" Class="menu-icon" /> New Subtask
                </li>
            }
        </ul>
    </div>
}

@code {
    [Parameter] public int ItemId { get; set; } // The id of the associated item
    [Parameter] public List<MenuItem> MenuItems { get; set; } = new();
    [Parameter] public List<ToolbarItem> ToolbarItems { get; set; } = new(); // New toolbar items
    [Parameter] public int X { get; set; } // X-coordinate
    [Parameter] public int Y { get; set; } // Y-coordinate

    [Parameter] public bool IsOpen { get; set; } // Visibility toggle
    [Parameter] public Action<int>? OnNewSubtask { get; set; } // Callback for new subtask
    
    public class MenuItem
    {
        public string Text { get; set; }
        public Icon IconValue { get; set; } // Use FluentIcon value
        public Action<int> Action { get; set; } // Accepts the item id
    }
    
    public class ToolbarItem
    {
        public string Text { get; set; }
        public Icon IconValue { get; set; } // Use FluentIcon value
        public Action<int> Action { get; set; } // Accepts the item id
    }
}
