using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Surefire.Domain.Proposals
{
    public class ProposalFeeItem
    {
        [Key]
        public int ProposalFeeItemId { get; set; }

        public string? Description { get; set; }

        public decimal? Amount { get; set; }

        [ForeignKey("Proposal")]
        public int ProposalId { get; set; }
        public Proposal Proposal { get; set; } = null!;
    }
}
