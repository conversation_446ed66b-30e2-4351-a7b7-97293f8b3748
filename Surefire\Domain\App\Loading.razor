﻿@namespace Surefire.Components
@page "/loading"
@layout UnauthLayout
@using Surefire.Components.Layout
@inject StateService _stateService
@inject AuthenticationStateProvider AuthStateProvider

<h3>Loading, please wait...</h3>

@code {
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        // StateService initialization is handled by MainLayout to avoid conflicts
        // Loading page should not initialize StateService
        await Task.Delay(100);
    }
}
