using Surefire.Domain.Agents.Interfaces;
using Surefire.Domain.Agents.Models;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace Surefire.Domain.Agents.Services
{
    /// <summary>
    /// Service for extracting and validating parameters from user input
    /// </summary>
    public class ParameterExtractionService : IParameterExtractionService
    {
        private readonly IOpenAIAgent _openAIAgent;
        private readonly IEntityExtractionService? _entityExtractionService;
        private readonly ILogger<ParameterExtractionService> _logger;

        public ParameterExtractionService(
            IOpenAIAgent openAIAgent,
            IEntityExtractionService? entityExtractionService = null,
            ILogger<ParameterExtractionService>? logger = null)
        {
            _openAIAgent = openAIAgent ?? throw new ArgumentNullException(nameof(openAIAgent));
            _entityExtractionService = entityExtractionService;
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Extract parameters from user input using AI analysis
        /// </summary>
        public async Task<AgentParameterValidation> ExtractParametersAsync(
            string userInput,
            List<AgentParameterDefinition> parameterDefinitions,
            Dictionary<string, object> context = null,
            CancellationToken cancellationToken = default)
        {
            var validation = new AgentParameterValidation();
            var extractedParameters = new Dictionary<string, object>();

            foreach (var paramDef in parameterDefinitions)
            {
                var paramResult = await ExtractSingleParameterAsync(userInput, paramDef, context, cancellationToken);
                validation.Parameters.Add(paramResult);

                if (paramResult.Status == ParameterStatus.Valid && paramResult.Value != null)
                {
                    extractedParameters[paramDef.Name] = paramResult.Value;
                }
                else if (paramResult.Status == ParameterStatus.Missing && paramDef.IsRequired)
                {
                    validation.MissingParameters.Add(paramDef.Name);
                }
                else if (paramResult.Status == ParameterStatus.Ambiguous)
                {
                    validation.AmbiguousParameters.Add(paramDef.Name);
                }
            }

            validation.ValidatedParameters = extractedParameters;
            validation.IsComplete = validation.MissingParameters.Count == 0 && validation.AmbiguousParameters.Count == 0;

            if (!validation.IsComplete)
            {
                validation.ClarificationMessage = await GenerateClarificationMessageAsync(validation, userInput);
            }

            return validation;
        }

        /// <summary>
        /// Validate provided parameters against definitions
        /// </summary>
        public async Task<AgentParameterValidation> ValidateParametersAsync(
            Dictionary<string, object> parameters,
            List<AgentParameterDefinition> parameterDefinitions)
        {
            var validation = new AgentParameterValidation();
            var validatedParameters = new Dictionary<string, object>();

            foreach (var paramDef in parameterDefinitions)
            {
                var paramResult = new ParameterValidationResult
                {
                    ParameterName = paramDef.Name
                };

                if (parameters.TryGetValue(paramDef.Name, out var value))
                {
                    // Validate the provided value
                    var validationResult = ValidateParameterValue(value, paramDef);
                    paramResult.Status = validationResult.IsValid ? ParameterStatus.Valid : ParameterStatus.Invalid;
                    paramResult.Value = validationResult.IsValid ? validationResult.ConvertedValue : null;
                    paramResult.Message = validationResult.ErrorMessage;

                    if (validationResult.IsValid)
                    {
                        validatedParameters[paramDef.Name] = validationResult.ConvertedValue;
                    }
                }
                else if (paramDef.DefaultValue != null)
                {
                    // Use default value
                    paramResult.Status = ParameterStatus.Valid;
                    paramResult.Value = paramDef.DefaultValue;
                    validatedParameters[paramDef.Name] = paramDef.DefaultValue;
                }
                else if (paramDef.IsRequired)
                {
                    // Missing required parameter
                    paramResult.Status = ParameterStatus.Missing;
                    validation.MissingParameters.Add(paramDef.Name);
                }

                validation.Parameters.Add(paramResult);
            }

            validation.ValidatedParameters = validatedParameters;
            validation.IsComplete = validation.MissingParameters.Count == 0 && validation.AmbiguousParameters.Count == 0;

            return validation;
        }

        /// <summary>
        /// Generate clarification questions for missing or ambiguous parameters
        /// </summary>
        public async Task<string> GenerateClarificationMessageAsync(
            AgentParameterValidation validation,
            string userInput)
        {
            var questions = new List<string>();

            // Add questions for missing parameters
            foreach (var missingParam in validation.MissingParameters)
            {
                var paramDef = validation.Parameters.FirstOrDefault(p => p.ParameterName == missingParam);
                if (paramDef != null)
                {
                    var paramDefinition = GetParameterDefinition(missingParam, validation);
                    if (!string.IsNullOrEmpty(paramDefinition?.ClarificationQuestion))
                    {
                        questions.Add(paramDefinition.ClarificationQuestion);
                    }
                    else
                    {
                        questions.Add($"What is the {missingParam.Replace("_", " ")}?");
                    }
                }
            }

            // Add questions for ambiguous parameters
            foreach (var ambiguousParam in validation.AmbiguousParameters)
            {
                var paramResult = validation.Parameters.FirstOrDefault(p => p.ParameterName == ambiguousParam);
                if (paramResult?.SuggestedValues?.Any() == true)
                {
                    questions.Add($"Which {ambiguousParam.Replace("_", " ")} did you mean: {string.Join(", ", paramResult.SuggestedValues)}?");
                }
            }

            if (questions.Count == 1)
            {
                return questions[0];
            }
            else if (questions.Count > 1)
            {
                return "I need some clarification:\n" + string.Join("\n", questions.Select((q, i) => $"{i + 1}. {q}"));
            }

            return "I need more information to proceed with your request.";
        }

        /// <summary>
        /// Attempt to resolve ambiguous parameters using additional context
        /// </summary>
        public async Task<(string? Value, double Confidence)> ResolveAmbiguousParameterAsync(
            string parameterName,
            string userInput,
            List<string> possibleValues,
            Dictionary<string, object> context = null)
        {
            if (!possibleValues.Any())
                return (null, 0.0);

            // Simple fuzzy matching approach
            var userInputLower = userInput.ToLowerInvariant();
            var bestMatch = "";
            var bestScore = 0.0;

            foreach (var value in possibleValues)
            {
                var score = CalculateStringSimilarity(userInputLower, value.ToLowerInvariant());
                if (score > bestScore)
                {
                    bestScore = score;
                    bestMatch = value;
                }
            }

            // Only return if confidence is reasonable
            return bestScore > 0.6 ? (bestMatch, bestScore) : (null, 0.0);
        }

        /// <summary>
        /// Extract a single parameter from user input
        /// </summary>
        private async Task<ParameterValidationResult> ExtractSingleParameterAsync(
            string userInput,
            AgentParameterDefinition paramDef,
            Dictionary<string, object> context,
            CancellationToken cancellationToken)
        {
            var result = new ParameterValidationResult
            {
                ParameterName = paramDef.Name
            };

            try
            {
                // Try entity extraction first if configured
                if (paramDef.UseEntityExtraction && _entityExtractionService != null && !string.IsNullOrEmpty(paramDef.EntityType))
                {
                    var entityResult = await ExtractUsingEntityService(userInput, paramDef.EntityType);
                    if (entityResult.Success)
                    {
                        result.Status = ParameterStatus.Valid;
                        result.Value = entityResult.Value;
                        return result;
                    }
                }

                // Fall back to AI-based extraction
                if (!string.IsNullOrEmpty(paramDef.ExtractionPrompt))
                {
                    var aiResult = await ExtractUsingAI(userInput, paramDef, cancellationToken);
                    if (aiResult.Success)
                    {
                        result.Status = ParameterStatus.Valid;
                        result.Value = aiResult.Value;
                        return result;
                    }
                }

                // Try simple pattern matching for common types
                var patternResult = ExtractUsingPatterns(userInput, paramDef);
                if (patternResult.Success)
                {
                    result.Status = ParameterStatus.Valid;
                    result.Value = patternResult.Value;
                    return result;
                }

                // Parameter not found
                if (paramDef.IsRequired)
                {
                    result.Status = ParameterStatus.Missing;
                    result.Message = $"Could not find {paramDef.Description} in your request";
                }
                else
                {
                    result.Status = ParameterStatus.Valid;
                    result.Value = paramDef.DefaultValue;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting parameter {ParameterName}", paramDef.Name);
                result.Status = ParameterStatus.Invalid;
                result.Message = "Error processing parameter";
            }

            return result;
        }

        /// <summary>
        /// Extract parameter using entity extraction service
        /// </summary>
        private async Task<(bool Success, object? Value)> ExtractUsingEntityService(string userInput, string entityType)
        {
            if (_entityExtractionService == null)
                return (false, null);

            try
            {
                var results = entityType.ToLowerInvariant() switch
                {
                    "clientname" => await _entityExtractionService.ExtractClientNamesAsync(userInput, 1),
                    "carriername" => await _entityExtractionService.ExtractCarrierNamesAsync(userInput, 1),
                    "policytype" => await _entityExtractionService.ExtractPolicyTypesAsync(userInput, 1),
                    _ => await _entityExtractionService.ExtractEntitiesAsync(userInput, entityType, 1)
                };

                if (results.Any() && results[0].Confidence > 0.7)
                {
                    return (true, results[0].Item1);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in entity extraction for type {EntityType}", entityType);
            }

            return (false, null);
        }

        /// <summary>
        /// Extract parameter using AI analysis
        /// </summary>
        private async Task<(bool Success, object? Value)> ExtractUsingAI(
            string userInput,
            AgentParameterDefinition paramDef,
            CancellationToken cancellationToken)
        {
            try
            {
                var prompt = $@"Extract the {paramDef.Description} from this user input: '{userInput}'

Extraction instructions: {paramDef.ExtractionPrompt}

If you find the parameter, respond with just the value. If not found, respond with 'NOT_FOUND'.";

                var request = new UnifiedRequest
                {
                    Input = prompt,
                    Context = new Dictionary<string, object>
                    {
                        ["parameter_extraction"] = true,
                        ["parameter_name"] = paramDef.Name
                    }
                };

                var response = await _openAIAgent.ProcessRequestAsync(request, cancellationToken);
                
                if (response.Success && !string.IsNullOrEmpty(response.Response))
                {
                    var value = response.Response.Trim();
                    if (value != "NOT_FOUND")
                    {
                        return (true, value);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in AI parameter extraction for {ParameterName}", paramDef.Name);
            }

            return (false, null);
        }

        /// <summary>
        /// Extract parameter using simple pattern matching
        /// </summary>
        private (bool Success, object? Value) ExtractUsingPatterns(string userInput, AgentParameterDefinition paramDef)
        {
            // Simple patterns for common parameter types
            switch (paramDef.Name.ToLowerInvariant())
            {
                case "years" or "time_period":
                    var yearMatch = Regex.Match(userInput, @"(\d+)\s*years?", RegexOptions.IgnoreCase);
                    if (yearMatch.Success && int.TryParse(yearMatch.Groups[1].Value, out var years))
                    {
                        return (true, years);
                    }
                    break;

                case "amount" or "payment_amount":
                    var amountMatch = Regex.Match(userInput, @"\$?(\d+(?:,\d{3})*(?:\.\d{2})?)", RegexOptions.IgnoreCase);
                    if (amountMatch.Success && decimal.TryParse(amountMatch.Groups[1].Value.Replace(",", ""), out var amount))
                    {
                        return (true, amount);
                    }
                    break;

                case "email":
                    var emailMatch = Regex.Match(userInput, @"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b");
                    if (emailMatch.Success)
                    {
                        return (true, emailMatch.Value);
                    }
                    break;
            }

            return (false, null);
        }

        /// <summary>
        /// Validate a parameter value against its definition
        /// </summary>
        private (bool IsValid, object? ConvertedValue, string? ErrorMessage) ValidateParameterValue(
            object value, AgentParameterDefinition paramDef)
        {
            if (value == null)
                return (false, null, "Value cannot be null");

            try
            {
                // Type conversion
                var convertedValue = Convert.ChangeType(value, paramDef.ParameterType);

                // Check valid values if specified
                if (paramDef.ValidValues.Any())
                {
                    var stringValue = convertedValue?.ToString();
                    if (!paramDef.ValidValues.Contains(stringValue, StringComparer.OrdinalIgnoreCase))
                    {
                        return (false, null, $"Value must be one of: {string.Join(", ", paramDef.ValidValues)}");
                    }
                }

                return (true, convertedValue, null);
            }
            catch (Exception ex)
            {
                return (false, null, $"Invalid value for {paramDef.Description}: {ex.Message}");
            }
        }

        /// <summary>
        /// Get parameter definition from validation results
        /// </summary>
        private AgentParameterDefinition? GetParameterDefinition(string paramName, AgentParameterValidation validation)
        {
            // This would typically come from the agent definition
            // For now, return a basic definition
            return new AgentParameterDefinition
            {
                Name = paramName,
                Description = paramName.Replace("_", " "),
                ClarificationQuestion = $"What is the {paramName.Replace("_", " ")}?"
            };
        }

        /// <summary>
        /// Calculate string similarity for fuzzy matching
        /// </summary>
        private double CalculateStringSimilarity(string s1, string s2)
        {
            if (string.IsNullOrEmpty(s1) || string.IsNullOrEmpty(s2))
                return 0.0;

            var longer = s1.Length > s2.Length ? s1 : s2;
            var shorter = s1.Length > s2.Length ? s2 : s1;

            if (longer.Length == 0)
                return 1.0;

            var editDistance = CalculateLevenshteinDistance(longer, shorter);
            return (longer.Length - editDistance) / (double)longer.Length;
        }

        /// <summary>
        /// Calculate Levenshtein distance between two strings
        /// </summary>
        private int CalculateLevenshteinDistance(string s1, string s2)
        {
            var matrix = new int[s1.Length + 1, s2.Length + 1];

            for (var i = 0; i <= s1.Length; i++)
                matrix[i, 0] = i;

            for (var j = 0; j <= s2.Length; j++)
                matrix[0, j] = j;

            for (var i = 1; i <= s1.Length; i++)
            {
                for (var j = 1; j <= s2.Length; j++)
                {
                    var cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
                    matrix[i, j] = Math.Min(
                        Math.Min(matrix[i - 1, j] + 1, matrix[i, j - 1] + 1),
                        matrix[i - 1, j - 1] + cost);
                }
            }

            return matrix[s1.Length, s2.Length];
        }
    }
} 