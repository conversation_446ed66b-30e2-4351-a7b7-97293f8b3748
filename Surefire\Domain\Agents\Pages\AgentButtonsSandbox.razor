@page "/agents/sandbox"
@using Surefire.Domain.Agents.Components
@using Surefire.Domain.Agents.Models
@using Surefire.Domain.Agents.Interfaces
@using Surefire.Domain.Agents.TaskAgents
@using Microsoft.FluentUI.AspNetCore.Components
@inject ILogger<AgentButtonsSandbox> Logger
@inject ITaskAgentOrchestrator AgentOrchestrator
@inject IMessageService MessageService

<PageTitle>Task Agent Buttons Sandbox</PageTitle>

<div class="container-fluid specpadding">
   
    <FluentStack>
        <div id="row1">
            <h3>Agent Sandbox</h3>
            <p>Test the Task Agent framework with loss run requests. c: 92 G&G, p: 4 (Auto)</p>
            
            <!-- MessageBar Actions Panel -->
            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Agent Results</h5>
                    <FluentButton Appearance="Appearance.Neutral" OnClick="@(() => MessageService.Clear())">
                        Clear Messages
                    </FluentButton>
                </div>
            </div>
            
            <div class="card">
                <div class="card-body">
                    <div class="form-group mb-3">
                        <FluentNumberField @bind-Value="@clientId"
                                           @bind-Value:after="@UpdatePageContext"
                                           Label="Client ID"
                                           Placeholder="Enter client ID" />
                    </div>

                    <div class="form-group mb-3">
                        <FluentNumberField @bind-Value="@productId"
                                           @bind-Value:after="@UpdatePageContext"
                                           Label="Product ID"
                                           Placeholder="Enter product ID" />
                    </div>

                    <div class="form-group mb-3">
                        <FluentNumberField @bind-Value="@numberOfYears"
                                           @bind-Value:after="@UpdatePageContext"
                                           Label="Number of Years"
                                           Placeholder="Enter number of years" />
                    </div>

                    <div class="form-group mb-3">
                        <FluentTextField @bind-Value="@carrierName"
                                         @bind-Value:after="@UpdatePageContext"
                                         Label="Carrier Name (Optional)"
                                         Placeholder="Enter carrier name" />
                    </div>

                    <div class="form-group mb-3">
                        <FluentTextField @bind-Value="@userId"
                                         @bind-Value:after="@UpdatePageContext"
                                         Label="User ID"
                                         Placeholder="Enter user ID for testing" />
                    </div>

                    <div class="alert alert-info mt-3">
                        <strong>Context Preview:</strong>
                        <pre style="white-space: pre-wrap; font-size: 0.9em;">@GetContextPreview()</pre>
                    </div>
                </div>
            </div>

            <!-- Manual Testing Panel -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5>Manual Agent Testing</h5>
                </div>
                <div class="card-body">
                    <div class="form-group mb-3">
                        <FluentTextField @bind-Value="@testInput"
                                         Label="Natural Language Input"
                                         Placeholder="e.g., 'Send loss run requests for client 123 work comp for last 5 years'"
                                         Style="width: 100%;" />
                    </div>

                    <div class="d-flex gap-2">
                        <FluentButton Appearance="Appearance.Accent"
                                      OnClick="TestNaturalLanguageInput"
                                      Disabled="@(string.IsNullOrWhiteSpace(testInput))">
                            <i class="fas fa-play"></i>
                            Test Natural Language
                        </FluentButton>

                        <FluentButton Appearance="Appearance.Outline"
                                      OnClick="TestDirectExecution">
                            <i class="fas fa-cog"></i>
                            Test Direct Execution
                        </FluentButton>
                    </div>
                </div>
            </div>
            <div class="card mt-3">
                <div class="card-header">
                    <h5>Available Agents (Loss Runs Category)</h5>
                </div>
                <div class="card-body">
                    @if (!availableAgents.Any())
                    {
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>No agents found!</strong>
                            <br />
                            This could mean:
                            <ul class="mb-0 mt-2">
                                <li>Agents are not registered in the DI container</li>
                                <li>InitializeAgents() was not called</li>
                                <li>No agents support ActionButton context</li>
                                <li>No agents match the "Loss Runs" category</li>
                            </ul>
                        </div>
                    }
                    else
                    {
                        <div class="available-agents">
                            @foreach (var agent in availableAgents)
                            {
                                <div class="agent-item">
                                    <strong>@agent.Name</strong> <code>(@agent.AgentId)</code>
                                    <br />
                                    <small class="text-muted">@agent.Description</small>
                                    <br />
                                    <span class="badge bg-secondary">@agent.Category</span>
                                    <span class="badge bg-info">@agent.OutcomeType</span>
                                    <span class="badge @(agent.SupportsActionButton ? "bg-success" : "bg-danger")">
                                        Button: @(agent.SupportsActionButton ? "Yes" : "No")
                                    </span>
                                    <span class="badge @(agent.SupportsAIChat ? "bg-success" : "bg-secondary")">
                                        Chat: @(agent.SupportsAIChat ? "Yes" : "No")
                                    </span>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>

            <!-- All Agents Debug Info -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6>Debug: All Registered Agents</h6>
                </div>
                <div class="card-body">
                    @if (!allAgents.Any())
                    {
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i>
                            <strong>No agents registered at all!</strong>
                            <br />
                            Check that InitializeAgents() is being called in Program.cs
                        </div>
                    }
                    else
                    {
                        <div class="small">
                            <strong>Total: @allAgents.Count agents supporting ActionButton context</strong>
                            <div class="available-agents">
                                @foreach (var agent in allAgents)
                                {
                                    <div class="agent-item-small">
                                        <strong>@agent.Name</strong> <code>(@agent.AgentId)</code>
                                        <span class="badge bg-secondary">@agent.Category</span>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div><!-- row 1 -->
        <div id="row2">
            <FluentMessageBarProvider />
            <!-- Task Agent Buttons Panel -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>Task Agent Buttons</h4>
                        <p class="mb-0">These buttons are generated based on the page context and available agents</p>
                    </div>
                    <div class="card-body">
                        <TaskAgentButtons PageContext="@pageContext"
                                          Category="Loss Runs"
                                          UserId="@userId"
                                          OnAgentExecuted="@OnAgentExecuted" />
                    </div>
                </div>

                <!-- Execution Results -->
                @if (executionResults.Any())
                {
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5>Execution Results</h5>
                            <button class="btn btn-sm btn-outline-secondary float-end" @onclick="ClearResults">
                                Clear Results
                            </button>
                        </div>
                        <div class="card-body">
                            @foreach (var result in executionResults.AsEnumerable().Reverse())
                            {
                                <div class="execution-result @(result.Success ? "success" : "error")">
                                    <div class="result-header">
                                        <span class="result-status">
                                            @if (result.Success)
                                            {
                                                <i class="fas fa-check-circle text-success"></i>
                                                <strong>Success</strong>
                                            }
                                            else
                                            {
                                                <i class="fas fa-exclamation-circle text-danger"></i>
                                                <strong>Failed</strong>
                                            }
                                        </span>
                                        <span class="result-time">@result.ExecutionTime.TotalSeconds.ToString("F2")s</span>
                                    </div>

                                    <div class="result-message">
                                        @result.Message
                                        @if (!string.IsNullOrEmpty(result.ErrorMessage))
                                        {
                                            <div class="error-message">
                                                <strong>Error:</strong> @result.ErrorMessage
                                            </div>
                                        }
                                    </div>

                                    @if (result.Data != null)
                                    {
                                        <div class="result-data">
                                            <strong>Data:</strong>
                                            <pre style="white-space: pre-wrap; font-size: 0.9em;">@System.Text.Json.JsonSerializer.Serialize(result.Data, new System.Text.Json.JsonSerializerOptions { WriteIndented = true })</pre>
                                        </div>
                                    }

                                    @if (result.Suggestions?.Any() == true)
                                    {
                                        <div class="result-suggestions">
                                            <strong>Suggestions:</strong>
                                            <ul>
                                                @foreach (var suggestion in result.Suggestions)
                                                {
                                                    <li>@suggestion</li>
                                                }
                                            </ul>
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                    </div>
                }
            </div>
        </div><!-- row 2 -->
    </FluentStack>
</div>

@code {
    private int? clientId = 1;
    private int? productId = 1;
    private int? numberOfYears = 5;
    private string carrierName = "";
    private string userId = "sandbox_user";
    private string testInput = "";
    
    private Dictionary<string, object> pageContext = new();
    private List<TaskAgentResult> executionResults = new();
    private List<TaskAgentDefinition> availableAgents = new();
    private List<TaskAgentDefinition> allAgents = new();

    protected override async Task OnInitializedAsync()
    {
        UpdatePageContext();
        await LoadAvailableAgents();
        await TestAgentRegistration();
    }

    private void UpdatePageContext()
    {
        pageContext = new Dictionary<string, object>();
        
        if (clientId.HasValue)
            pageContext["client_id"] = clientId.Value;
            
        if (productId.HasValue)
            pageContext["product_id"] = productId.Value;
            
        if (numberOfYears.HasValue)
            pageContext["time_period"] = numberOfYears.Value;
            
        if (!string.IsNullOrWhiteSpace(carrierName))
            pageContext["carrier_name"] = carrierName;

        StateHasChanged();
    }

    private async Task LoadAvailableAgents()
    {
        try
        {
            // Get ALL available agents first for debugging
            allAgents = AgentOrchestrator.GetAvailableAgents(AgentExecutionContext.ActionButton).ToList();
            Logger.LogInformation("Total agents supporting ActionButton: {Count}", allAgents.Count);
            
            foreach (var agent in allAgents)
            {
                Logger.LogInformation("Agent: {AgentId} - {Name} - Category: {Category}", 
                    agent.AgentId, agent.Name, agent.Category);
            }
            
            // Get agents specifically for Loss Runs category
            availableAgents = AgentOrchestrator.GetAvailableAgents(AgentExecutionContext.ActionButton, "Loss Runs").ToList();
            Logger.LogInformation("Loaded {Count} Loss Runs agents", availableAgents.Count);
            
            if (!availableAgents.Any())
            {
                Logger.LogWarning("No Loss Runs agents found. Total action button agents: {Total}", allAgents.Count);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading available agents");
        }
    }

    private async Task OnAgentExecuted(TaskAgentResult result)
    {
        executionResults.Add(result);
        Logger.LogInformation("Agent execution completed: {Success}, {Message}", result.Success, result.Message);
        
        // Handle MessageBar display for structured results
        await HandleMessageBarDisplay(result);
        
        StateHasChanged();
    }

    private async Task HandleMessageBarDisplay(TaskAgentResult result)
    {
        try
        {
            // Clear previous messages
            MessageService.Clear();

            if (result.Success && result.Data != null)
            {
                // Try to extract MessageBarData from the result
                var data = result.Data as dynamic;
                var messageBarData = data?.MessageBarData;
                
                if (messageBarData != null)
                {
                    // Cast to our known type
                    var typedMessageBarData = System.Text.Json.JsonSerializer.Deserialize<LossRunRequestAgent.LossRunMessageBarData>(
                        System.Text.Json.JsonSerializer.Serialize(messageBarData));

                    if (typedMessageBarData != null)
                    {
                        // Display summary message first
                        MessageService.ShowMessageBar(options =>
                        {
                            options.Title = "✅ Loss Run Request Completed";
                            options.Body = typedMessageBarData.SummaryMessage;
                            options.Intent = MessageIntent.Success;
                            options.ClearAfterNavigation = false;
                        });

                        // Display each carrier group as separate message bars
                        foreach (var group in typedMessageBarData.CarrierGroups)
                        {
                            await DisplayCarrierMessageGroup(group);
                        }
                    }
                }
                else
                {
                    // Fallback to regular message display
                    MessageService.ShowMessageBar(options =>
                    {
                        options.Title = result.Success ? "✅ Task Completed" : "❌ Task Failed";
                        options.Body = result.Message;
                        options.Intent = result.Success ? MessageIntent.Success : MessageIntent.Error;
                        options.ClearAfterNavigation = false;
                    });
                }
            }
            else
            {
                // Error case
                MessageService.ShowMessageBar(options =>
                {
                    options.Title = "❌ Task Failed";
                    options.Body = result.ErrorMessage ?? "Unknown error occurred";
                    options.Intent = MessageIntent.Error;
                    options.ClearAfterNavigation = false;
                });
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error displaying message bars");
            // Fallback to simple message
            MessageService.ShowMessageBar(options =>
            {
                options.Title = result.Success ? "✅ Task Completed" : "❌ Task Failed";
                options.Body = result.Message;
                options.Intent = result.Success ? MessageIntent.Success : MessageIntent.Error;
                options.ClearAfterNavigation = false;
            });
        }
    }

    private async Task DisplayCarrierMessageGroup(dynamic group)
    {
        await Task.Run(() =>
        {
            MessageService.ShowMessageBar(options =>
            {
                options.Title = group.Title?.ToString();
                options.Body = group.Body?.ToString();
                options.Intent = group.Intent?.ToString() switch
                {
                    "Success" => MessageIntent.Success,
                    "Warning" => MessageIntent.Warning,
                    "Error" => MessageIntent.Error,
                    _ => MessageIntent.Info
                };
                options.ClearAfterNavigation = false;

                // Add links if available
                var links = group.Links as IEnumerable<dynamic>;
                if (links != null && links.Any())
                {
                    var firstLink = links.First();
                    options.Link = new ActionLink<Microsoft.FluentUI.AspNetCore.Components.Message>
                    {
                        Text = firstLink.Text?.ToString() ?? "Open Link",
                        Href = firstLink.Href?.ToString() ?? "#",
                        OnClick = (e) => { 
                            //Logger.LogInformation("Carrier link clicked: {Href}", firstLink.Href?.ToString()); 
                            return Task.CompletedTask; 
                        }
                    };
                }

                // Add actions if available
                var actions = group.Actions as IEnumerable<dynamic>;
                if (actions != null && actions.Any())
                {
                    var actionsList = actions.ToList();
                    
                    if (actionsList.Count > 0)
                    {
                        var firstAction = actionsList[0];
                        options.PrimaryAction = new ActionButton<Microsoft.FluentUI.AspNetCore.Components.Message>
                        {
                            Text = firstAction.Text?.ToString() ?? "Action",
                            OnClick = (e) => HandleCarrierAction(firstAction)
                        };
                    }
                    
                    if (actionsList.Count > 1)
                    {
                        var secondAction = actionsList[1];
                        options.SecondaryAction = new ActionButton<Microsoft.FluentUI.AspNetCore.Components.Message>
                        {
                            Text = secondAction.Text?.ToString() ?? "Action",
                            OnClick = (e) => HandleCarrierAction(secondAction)
                        };
                    }
                }
            });
        });
    }

    private async Task HandleCarrierAction(dynamic action)
    {
        try
        {
            var actionType = action.ActionType?.ToString();
            //Logger.LogInformation("Handling carrier action: {ActionType}", actionType);

            switch (actionType)
            {
                case "OpenOutlook":
                    MessageService.ShowMessageBar(options =>
                    {
                        options.Title = "📧 Outlook Action";
                        options.Body = "Opening Outlook to review email drafts...";
                        options.Intent = MessageIntent.Info;
                        options.ClearAfterNavigation = false;
                    });
                    break;

                case "ViewCarrierContacts":
                    MessageService.ShowMessageBar(options =>
                    {
                        options.Title = "📞 Contact Information";
                        options.Body = "Feature coming soon: View carrier contact details";
                        options.Intent = MessageIntent.Info;
                        options.ClearAfterNavigation = false;
                    });
                    break;

                default:
                    //Logger.LogInformation("Unknown action type: {ActionType}", actionType);
                    break;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error handling carrier action");
        }

        await Task.CompletedTask;
    }

    private async Task TestNaturalLanguageInput()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(testInput))
                return;

            Logger.LogInformation("Testing natural language input: {Input}", testInput);
            
            var result = await AgentOrchestrator.ExecuteFromChatAsync(
                testInput,
                userId,
                pageContext
            );

            await OnAgentExecuted(result);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error testing natural language input");
            await OnAgentExecuted(new TaskAgentResult
            {
                Success = false,
                OutcomeType = AgentOutcomeType.Error,
                ErrorMessage = $"Error testing input: {ex.Message}",
                ExecutionTime = TimeSpan.Zero
            });
        }
    }

    private async Task TestDirectExecution()
    {
        try
        {
            var parameters = new Dictionary<string, object>();
            
            if (clientId.HasValue)
                parameters["client_id"] = clientId.Value;
                
            if (productId.HasValue)
                parameters["product_id"] = productId.Value;
                
            if (numberOfYears.HasValue)
                parameters["time_period"] = numberOfYears.Value;
                
            if (!string.IsNullOrWhiteSpace(carrierName))
                parameters["carrier_name"] = carrierName;

            Logger.LogInformation("Testing direct execution with parameters: {Parameters}", parameters);
            
            var result = await AgentOrchestrator.ExecuteFromButtonAsync(
                "loss_run_request",
                parameters,
                userId,
                pageContext
            );

            await OnAgentExecuted(result);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error testing direct execution");
            await OnAgentExecuted(new TaskAgentResult
            {
                Success = false,
                OutcomeType = AgentOutcomeType.Error,
                ErrorMessage = $"Error testing direct execution: {ex.Message}",
                ExecutionTime = TimeSpan.Zero
            });
        }
    }

    private void ClearResults()
    {
        executionResults.Clear();
        StateHasChanged();
    }

    private string GetContextPreview()
    {
        if (!pageContext.Any())
            return "No context set";
            
        return System.Text.Json.JsonSerializer.Serialize(pageContext, new System.Text.Json.JsonSerializerOptions 
        { 
            WriteIndented = true 
        });
    }

    private async Task TestAgentRegistration()
    {
        try
        {
            Logger.LogInformation("=== DEBUGGING AGENT REGISTRATION ===");
            
            // Test if we can resolve the LossRunRequestAgent directly from DI
            object? lossRunAgent = await Task.Run(() => 
            {
                try
                {
                    var scope = AgentOrchestrator.GetType().Assembly.GetType("Microsoft.Extensions.DependencyInjection.ServiceProvider");
                    Logger.LogInformation("Attempting to resolve LossRunRequestAgent from DI container...");
                    return (object?)null; // We can't access the service provider directly from here
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error testing agent resolution");
                    return (object?)null;
                }
            });
            
            // Test the registry directly
            Logger.LogInformation("Testing if registry has any agents at all...");
            
            // Check if any service exists
            Logger.LogInformation("AgentOrchestrator type: {Type}", AgentOrchestrator.GetType().Name);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error in TestAgentRegistration");
        }
    }
}
