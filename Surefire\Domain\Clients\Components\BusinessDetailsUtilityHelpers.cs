﻿using System;
using System.Globalization;
using System.Linq;
using System.Reflection; // For reflection-based setters
using System.Text.Json;
using System.Text; // For StringBuilder

namespace Surefire.Domain.Clients.Helpers
{
    public static class BusinessDetailUtilityHelpers
    {
        public static string GetJsonSnippetOnError(string fullJson, long? errorLineNumber)
        {
            if (errorLineNumber == null || errorLineNumber <= 0) return "[Could not determine error location]";

            int lineNum = (int)errorLineNumber.Value;
            var lines = fullJson.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries); // More robust split
            int startLine = Math.Max(0, lineNum - 3);
            int endLine = Math.Min(lines.Length - 1, lineNum + 1);

            StringBuilder snippet = new StringBuilder();
            for (int i = startLine; i <= endLine; i++)
            {
                if (i >= lines.Length) break; // Boundary check
                string prefix = (i + 1 == lineNum) ? ">> " : "   ";
                string lineContent = System.Net.WebUtility.HtmlEncode(lines[i].TrimEnd('\r'));
                snippet.AppendLine($"{prefix}L{i + 1}: {lineContent}"); // Add line number for context
            }
            return snippet.ToString();
        }

        // These methods were in the original code but not directly used by the primary parsing logic.
        // They provide a generic reflection-based way to set properties.
        public static void SetBusinessDetailsPropertiesFromJson(JsonElement jsonElement, object targetObject)
        {
            foreach (var prop in targetObject.GetType().GetProperties())
            {
                string propName = prop.Name;
                JsonElement matchedElement;

                try
                {
                    if (jsonElement.TryGetProperty(propName, out matchedElement))
                    {
                        SetPropertyFromJsonElement(prop, matchedElement, targetObject);
                    }
                    else if (prop.PropertyType.IsClass && prop.PropertyType != typeof(string) && !prop.PropertyType.IsArray)
                    {
                        // Attempt to find a nested object in JSON if the property name matches a JSON object
                        if (jsonElement.TryGetProperty(propName, out var nestedJsonElement) && nestedJsonElement.ValueKind == JsonValueKind.Object)
                        {
                            var nestedObject = prop.GetValue(targetObject) ?? Activator.CreateInstance(prop.PropertyType);
                            if (nestedObject != null)
                            {
                                prop.SetValue(targetObject, nestedObject);
                                SetBusinessDetailsPropertiesFromJson(nestedJsonElement, nestedObject); // Recurse into nested JSON object
                            }
                        }
                        // If the target property is a class, but no direct match for its name as a JSON object,
                        // then recurse using the *current* jsonElement to see if its properties match the nested object's properties.
                        // This handles cases where the JSON is flat but the C# model is nested.
                        else
                        {
                            var nestedObject = prop.GetValue(targetObject) ?? Activator.CreateInstance(prop.PropertyType);
                            if (nestedObject != null)
                            {
                                prop.SetValue(targetObject, nestedObject);
                                SetBusinessDetailsPropertiesFromJson(jsonElement, nestedObject); // Recurse with current jsonElement
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error processing property '{propName}' for reflection-based mapping: {ex.Message}");
                }
            }
        }

        private static void SetPropertyFromJsonElement(PropertyInfo prop, JsonElement jsonElement, object targetObject)
        {
            try
            {
                object? convertedValue = null;
                var targetType = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;

                if (jsonElement.ValueKind == JsonValueKind.Null)
                {
                    prop.SetValue(targetObject, null);
                    return;
                }

                if (targetType == typeof(int))
                {
                    convertedValue = jsonElement.TryGetInt32(out var val) ? val : (int?)null;
                }
                else if (targetType == typeof(decimal))
                {
                    if (jsonElement.TryGetDecimal(out var decVal))
                    {
                        convertedValue = decVal;
                    }
                    else if (jsonElement.ValueKind == JsonValueKind.String &&
                             decimal.TryParse(jsonElement.GetString()?.Replace("$", "").Replace(",", ""), NumberStyles.Any, CultureInfo.InvariantCulture, out var parsedDecVal))
                    {
                        convertedValue = parsedDecVal;
                    }
                }
                else if (targetType == typeof(DateTime))
                {
                    convertedValue = jsonElement.TryGetDateTime(out var dateVal) ? dateVal : (DateTime?)null;
                }
                else if (targetType == typeof(bool))
                {
                    convertedValue = jsonElement.ValueKind == JsonValueKind.True ? true :
                                     jsonElement.ValueKind == JsonValueKind.False ? false : (bool?)null;
                }
                else if (targetType.IsEnum)
                {
                    if (Enum.TryParse(targetType, jsonElement.GetString(), true, out var enumValue))
                    {
                        convertedValue = enumValue;
                    }
                }
                else if (targetType == typeof(string))
                {
                    convertedValue = jsonElement.GetString();
                }

                if (convertedValue != null || prop.PropertyType.IsClass || Nullable.GetUnderlyingType(prop.PropertyType) != null)
                {
                    prop.SetValue(targetObject, convertedValue);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error converting/setting JSON value for '{prop.Name}': {ex.Message}");
            }
        }
    }

    // This would be your existing StringHelper class, or you can implement it.
    // For demonstration, here's a very basic placeholder.
    public static class StringHelper
    {
        public static string ColorizeJSON(string json)
        {
            // A real implementation would parse the JSON and wrap parts in spans with classes.
            // This is a placeholder to make the code compile.
            // You'd replace this with your actual JSON colorizing logic.
            // Example: return json.Replace(":", "<span class='colon'>:</span>");
            return "<pre>" + System.Net.WebUtility.HtmlEncode(json) + "</pre>";
        }
    }
}