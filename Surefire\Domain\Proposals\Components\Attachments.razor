@using Microsoft.FluentUI.AspNetCore.Components
@using Microsoft.Extensions.Hosting
@using Syncfusion.Blazor.Inputs
@using Surefire.Domain.Proposals.Models
@using Surefire.Domain.Proposals.Services
@using Surefire.Domain.Attachments.Components
@using Surefire.Domain.Attachments.Models
@using Surefire.Domain.Attachments.Services
@using Surefire.Domain.Renewals.Models
@using Surefire.Domain.Renewals.Services
@using Surefire.Domain.Forms.Services
@using Surefire.Domain.Users.Services
@using Surefire.Domain.Shared.Services
@using Surefire.Domain.Ember
@using Surefire.Domain.Logs

@inject ProposalService ProposalService
@inject AttachmentService AttachmentService
@inject RenewalService RenewalService
@inject UserService UserService
@inject StateService StateService
@inject EmberService EmberService
@inject FormService FormService

@inject IWebHostEnvironment Env
@inject ILoggingService _log
@inject IJSRuntime JS

@if (isLoading)
{
    <FluentProgressRing />
}
else if (Proposal == null)
{
    <FluentMessageBar Intent="MessageIntent.Error">Proposal not found.</FluentMessageBar>
}
else
{
    <div style="width:100%; padding-top:20px;">
        <FluentStack>
            <div class="flat-class-container">
                <div class="flat-card">Quote</div>
                <div class="drop-zone-quote" id="dzquote-files">
                    @if (QuoteAttachment != null)
                    {
                        <div class="attachment-thumbnail">
                            <a href="/@QuoteAttachment.LocalPath/@QuoteAttachment.HashedFileName" target="_blank">
                                <img src="@GetThumbnailPath(QuoteAttachment)" class="thumb-icon" />
                            </a>
                            <div class="attachment-actions">
                                <FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" OnClick="() => OnDeleteAttachmentClicked(QuoteAttachment)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" OnClick="() => OpenWithWindows(QuoteAttachment, true)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" OnClick="() => OpenWithWindows(QuoteAttachment, false)" />
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="attachment-thumbnail">
                            <img src="/img/icons/document-upload.png" class="thumb-icon" />
                        </div>
                    }
                </div>
                <SfUploader @ref="UploaderQuote" DropEffect="DropEffect.Copy" DropArea="#dzquote-files">
                    <UploaderTemplates><Template></Template></UploaderTemplates>
                    <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "quote"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                </SfUploader>
            </div>
            <div class="flat-class-container">
                <div class="flat-card">Acord</div>
                <div class="drop-zone-acord" id="dzacord-files">
                    @if (AcordAttachment != null)
                    {
                        <div class="attachment-thumbnail">
                            <a href="/@AcordAttachment.LocalPath/@AcordAttachment.HashedFileName" target="_blank">
                                <img src="@GetThumbnailPath(AcordAttachment)" class="thumb-icon" />
                            </a>
                            <div class="attachment-actions">
                                <FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" OnClick="() => OnDeleteAttachmentClicked(AcordAttachment)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" OnClick="() => OpenWithWindows(AcordAttachment, true)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" OnClick="() => OpenWithWindows(AcordAttachment, false)" />
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="attachment-thumbnail">
                            <img src="/img/icons/document-upload.png" class="thumb-icon" />
                        </div>
                    }
                </div>
                <SfUploader @ref="UploaderAcord" DropEffect="DropEffect.Copy" DropArea="#dzacord-files">
                    <UploaderTemplates><Template></Template></UploaderTemplates>
                    <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "acord"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                </SfUploader>
            </div>
            <div class="flat-class-container">
                <div class="flat-card">Enclosures</div>
                <div class="drop-zone-enclosures" id="dzenclosures-files">
                    @if (EnclosuresAttachment != null)
                    {
                        <div class="attachment-thumbnail">
                            <a href="/@EnclosuresAttachment.LocalPath/@EnclosuresAttachment.HashedFileName" target="_blank">
                                <img src="@GetThumbnailPath(EnclosuresAttachment)" class="thumb-icon" />
                            </a>
                            <div class="attachment-actions">
                                <FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" OnClick="() => OnDeleteAttachmentClicked(EnclosuresAttachment)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" OnClick="() => OpenWithWindows(EnclosuresAttachment, true)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" OnClick="() => OpenWithWindows(EnclosuresAttachment, false)" />
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="attachment-thumbnail">
                            <img src="/img/icons/document-upload.png" class="thumb-icon" />
                        </div>
                    }
                </div>
                <SfUploader @ref="UploaderEnclosures" DropEffect="DropEffect.Copy" DropArea="#dzenclosures-files">
                    <UploaderTemplates><Template></Template></UploaderTemplates>
                    <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "enclosures"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                </SfUploader>
            </div>

            <div class="flat-class-container">
                <div class="flat-card">Supplemental</div>
                <div class="drop-zone-acord" id="dzsupp-files">
                    @if (SupplementalAttachment != null)
                    {
                        <div class="attachment-thumbnail">
                            <a href="/@SupplementalAttachment.LocalPath/@SupplementalAttachment.HashedFileName" target="_blank">
                                <img src="@GetThumbnailPath(SupplementalAttachment)" class="thumb-icon" />
                            </a>
                            <div class="attachment-actions">
                                <FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" OnClick="() => OnDeleteAttachmentClicked(SupplementalAttachment)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" OnClick="() => OpenWithWindows(SupplementalAttachment, true)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" OnClick="() => OpenWithWindows(SupplementalAttachment, false)" />
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="attachment-thumbnail">
                            <img src="/img/icons/document-upload.png" class="thumb-icon" />
                        </div>
                    }
                </div>
                <SfUploader @ref="UploaderSupplemental" DropEffect="DropEffect.Copy" DropArea="#dzsupp-files">
                    <UploaderTemplates><Template></Template></UploaderTemplates>
                    <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "supplemental"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                </SfUploader>
            </div>
            <div class="flat-class-container">
                <div class="flat-card">Proposal</div>
                <div class="drop-zone-acord" id="dzprop-files">
                    @if (ProposalAttachment != null)
                    {
                        <div class="attachment-thumbnail">
                            <a class="docbutton" @onclick="OpenFile">
                                <img src="img/icons/proposal-docx.png" class="thumb-icon" />
                            </a>
                            <div class="attachment-actions">
                                <FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" OnClick="() => OnDeleteAttachmentClicked(ProposalAttachment)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" OnClick="() => OpenWithWindows(ProposalAttachment, true)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" OnClick="() => OpenWithWindows(ProposalAttachment, false)" />
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="attachment-thumbnail">
                            <img src="/img/icons/document-upload.png" class="thumb-icon" />
                        </div>
                    }
                </div>
                <SfUploader @ref="UploaderProposal" DropEffect="DropEffect.Copy" DropArea="#dzprop-files">
                    <UploaderTemplates><Template></Template></UploaderTemplates>
                    <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "proposal"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                </SfUploader>
            </div>
        </FluentStack>
        <FluentStack Style="position:relative; top:-20px;">

            <div class="flat-class-container">
                <div class="flat-card">SL-2</div>
                <div class="drop-zone-sl2" id="dzsl2-files">
                    @if (SL2Attachment != null)
                    {
                        <div class="attachment-thumbnail">
                            <a href="/@SL2Attachment.LocalPath/@SL2Attachment.HashedFileName" target="_blank">
                                <img src="/img/sl2.jpg" class="thumb-icon" />
                            </a>
                            <div class="attachment-actions">
                                <FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" OnClick="() => OnDeleteAttachmentClicked(SL2Attachment)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" OnClick="() => OpenWithWindows(SL2Attachment, true)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" OnClick="() => OpenWithWindows(SL2Attachment, false)" />
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="attachment-thumbnail">
                            <img src="/img/icons/document-upload.png" class="thumb-icon" />
                        </div>
                    }
                </div>
                <SfUploader @ref="UploaderSL2" DropEffect="DropEffect.Copy" DropArea="#dzsl2-files">
                    <UploaderTemplates><Template></Template></UploaderTemplates>
                    <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "sl2"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                </SfUploader>
            </div>
            <div class="flat-class-container">
                <div class="flat-card">Binder</div>
                <div class="drop-zone-binder" id="dzbinder-files">
                    @if (BinderAttachment != null)
                    {
                        <div class="attachment-thumbnail">
                            <a href="/@BinderAttachment.LocalPath/@BinderAttachment.HashedFileName" target="_blank">
                                <img src="@GetThumbnailPath(BinderAttachment)" class="thumb-icon" />
                            </a>
                            <div class="attachment-actions">
                                <FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" OnClick="() => OnDeleteAttachmentClicked(BinderAttachment)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" OnClick="() => OpenWithWindows(BinderAttachment, true)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" OnClick="() => OpenWithWindows(BinderAttachment, false)" />
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="attachment-thumbnail">
                            <img src="/img/icons/document-upload.png" class="thumb-icon" />
                        </div>
                    }
                </div>
                <SfUploader @ref="UploaderBinder" DropEffect="DropEffect.Copy" DropArea="#dzbinder-files">
                    <UploaderTemplates><Template></Template></UploaderTemplates>
                    <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "binder"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                </SfUploader>
            </div>
            <div class="flat-class-container">
                <div class="flat-card">Invoice</div>
                <div class="drop-zone-invoice" id="dzinvoice-files">
                    @if (InvoiceAttachment != null)
                    {
                        <div class="attachment-thumbnail">
                            <a href="/@InvoiceAttachment.LocalPath/@InvoiceAttachment.HashedFileName" target="_blank">
                                <img src="@GetThumbnailPath(InvoiceAttachment)" class="thumb-icon" />
                            </a>
                            <div class="attachment-actions">
                                <FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" OnClick="() => OnDeleteAttachmentClicked(InvoiceAttachment)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" OnClick="() => OpenWithWindows(InvoiceAttachment, true)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" OnClick="() => OpenWithWindows(InvoiceAttachment, false)" />
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="attachment-thumbnail">
                            <img src="/img/icons/document-upload.png" class="thumb-icon" />
                        </div>
                    }
                </div>
                <SfUploader @ref="UploaderInvoice" DropEffect="DropEffect.Copy" DropArea="#dzinvoice-files">
                    <UploaderTemplates><Template></Template></UploaderTemplates>
                    <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "invoice"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                </SfUploader>
            </div>
            <div class="flat-class-container">
                <div class="flat-card">Loss Runs</div>
                <div class="drop-zone-lossruns" id="dzlossruns-files">
                    @if (LossRunsAttachment != null)
                    {
                        <div class="attachment-thumbnail">
                            <a href="/@LossRunsAttachment.LocalPath/@LossRunsAttachment.HashedFileName" target="_blank">
                                <img src="@GetThumbnailPath(LossRunsAttachment)" class="thumb-icon" />
                            </a>
                            <div class="attachment-actions">
                                <FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" OnClick="() => OnDeleteAttachmentClicked(LossRunsAttachment)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" OnClick="() => OpenWithWindows(LossRunsAttachment, true)" />
                                <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" OnClick="() => OpenWithWindows(LossRunsAttachment, false)" />
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="attachment-thumbnail">
                            <img src="/img/icons/document-upload.png" class="thumb-icon" />
                        </div>
                    }
                </div>
                <SfUploader @ref="UploaderLossRuns" DropEffect="DropEffect.Copy" DropArea="#dzlossruns-files">
                    <UploaderTemplates><Template></Template></UploaderTemplates>
                    <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "lossruns"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                </SfUploader>
            </div>
        </FluentStack>
    </div>
}

<style>
    .e-numsmall {
        font-size: .9em;
        height: 27px
    }

    :root .e-re {
        background-color: #fefefe !important;
        overflow: hidden;
        box-sizing: border-box;
        margin-right: 12px;
        margin-bottom: 4px !important;
        padding: 8px 8px 10px !important;
        min-width: 220px !important;
        max-width: 250px !important;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,.04)
    }

    .e-upload {
        border: 0 !important
    }

        .e-upload .e-file-select-wrap {
            padding: 0 !important;
            margin: 0 !important
        }

            .e-upload .e-file-select-wrap .e-btn {
                top: -25px !important;
                left: 0 !important
            }

        .e-upload .e-upload-files .e-upload-file-list {
            position: unset !important;
            min-height: unset !important;
            height: unset !important
        }
</style>

<Surefire.Domain.Shared.Components.ConfirmationDialog DialogId="delete-attachment-dialog-files"
                                                      Title="Delete Attachment"
                                                      Message=@(deleteDialogMessage)
                                                      ConfirmText="Delete"
                                                      CancelText="Cancel"
                                                      @bind-Hidden="isDeleteDialogHidden"
                                                      OnConfirm="OnDeleteDialogConfirmed" />

@code {
    [Parameter] public int ClientId { get; set; }
    [Parameter] public int RenewalId { get; set; }
    [Parameter] public EventCallback OnAttachmentChanged { get; set; }

    private Proposal Proposal { get; set; }
    private List<Attachment> AttachmentsList { get; set; } = new();
    private bool isLoading = true;

    protected override async Task OnParametersSetAsync()
    {
        try
        {
            isLoading = true;
            Proposal = await ProposalService.GetProposalByRenewalIdAsync(RenewalId);

            // Ensure CreatedById is set
            if (string.IsNullOrEmpty(Proposal.CreatedById))
            {
                Proposal.CreatedById = StateService.CurrentUser?.Id ?? string.Empty;
            }

            if (Proposal.AttachmentGroupId.HasValue)
            {
                AttachmentsList = await AttachmentService.GetAttachmentsByGroupIdAsync(Proposal.AttachmentGroupId.Value);
            }
            else
            {
                AttachmentsList = new List<Attachment>();
            }

            isLoading = false;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error in OnParametersSetAsync: {ex.Message}", "Attachments", ex);
            isLoading = false;
            StateHasChanged();
        }
    }

    public async Task OpenFile()
    {
        if (ProposalAttachment != null)
        {
            await EmberService.WindowsOpenFile(ProposalAttachment);
        }
    }

    // ------------------------------------------------- Dialogs
    //----------------------------------------------------------
    private bool isDeleteDialogHidden = true;
    private Attachment? attachmentToDelete = null;
    private bool deleteFromFilesystem = false;
    private string deleteDialogMessage => attachmentToDelete == null
        ? "Are you sure you want to delete this attachment?"
        : $"Are you sure you want to delete '{attachmentToDelete.OriginalFileName}'?";

    private void OnDeleteAttachmentClicked(Attachment? attachment)
    {
        attachmentToDelete = attachment;
        deleteFromFilesystem = false;
        isDeleteDialogHidden = false;
    }

    private async Task OnDeleteDialogConfirmed(bool confirmed)
    {
        if (confirmed && attachmentToDelete != null)
        {
            if (deleteFromFilesystem)
            {
                await AttachmentService.DeleteAttachmentAndFileAsync(attachmentToDelete.AttachmentId);
            }
            else
            {
                await AttachmentService.DeleteAttachmentAsync(attachmentToDelete.AttachmentId);
            }
            await RefreshAttachments();
        }
        isDeleteDialogHidden = true;
        attachmentToDelete = null;
        deleteFromFilesystem = false;
        StateHasChanged();
    }

    // --------------------------------------- Images and Thumbs
    //----------------------------------------------------------
    // Returns true if the file format is an image
    private bool IsImage(string fileFormat)
    {
        if (string.IsNullOrEmpty(fileFormat)) return false;
        var ext = fileFormat.ToLower();
        return ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".gif";
    }

    // Returns the icon path for a given file format
    private string GetIconPath(string fileFormat)
    {
        if (string.IsNullOrEmpty(fileFormat)) return "/img/icons/document.png";
        var ext = fileFormat.ToLower();
        if (ext == ".pdf") return "/img/icons/pdf.png";
        if (ext == ".doc" || ext == ".docx") return "/img/icons/word.png";
        if (ext == ".xls" || ext == ".xlsx") return "/img/icons/excel.png";
        // add more as needed
        return "/img/icons/document.png";
    }

    // Returns the thumbnail path for an image attachment
    private string GetThumbnailPath(Attachment attachment)
    {
        if (attachment == null || string.IsNullOrEmpty(attachment.HashedFileName) || string.IsNullOrEmpty(attachment.LocalPath))
            return string.Empty;

        if(attachment.FileFormat == ".pdf")
        {
            // Always jpg for thumbnails
            var baseName = attachment.HashedFileName;
            // Replace extension with _thumb.jpg
            var thumbName = baseName;
            var lastDot = baseName.LastIndexOf('.');
            if (lastDot >= 0)
            {
                thumbName = baseName.Substring(0, lastDot) + "_thumb.jpg";
            }
            else
            {
                thumbName = baseName + "_thumb.jpg";
            }
            // Ensure forward slashes
            var localPath = attachment.LocalPath.Replace("\\", "/").TrimEnd('/');
            return $"/{localPath}/{thumbName}";
        }else if (attachment.FileFormat == ".docx")
        {
            return "/img/icons/proposal-docx.png";
        }else if (attachment.FileFormat == ".msg")
        {
            return "/img/icons/file-msg.png";
        }
        else
        {
            return "/img/icons/file-other.png";
        }

        
    }

    // ---------------------------------------- Attachment Slots
    //----------------------------------------------------------
    private Attachment QuoteAttachment => AttachmentsList?.Where(a => a.IsQuote).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment AcordAttachment => AttachmentsList?.Where(a => a.IsAcord).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment SupplementalAttachment => AttachmentsList?.Where(a => a.IsSupplemental).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment ProposalAttachment => AttachmentsList?.Where(a => a.IsProposal).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment EnclosuresAttachment => AttachmentsList?.Where(a => a.IsEnclosure).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment SL2Attachment => AttachmentsList?.Where(a => a.IsSL2).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment BinderAttachment => AttachmentsList?.Where(a => a.IsBinder).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment InvoiceAttachment => AttachmentsList?.Where(a => a.IsInvoice).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment LossRunsAttachment => AttachmentsList?.Where(a => a.Description != null && a.Description.ToLower().Contains("loss runs")).OrderByDescending(a => a.DateCreated).FirstOrDefault();

    private SfUploader UploaderQuote;
    private SfUploader UploaderAcord;
    private SfUploader UploaderSupplemental;
    private SfUploader UploaderProposal;
    private SfUploader UploaderEnclosures;
    private SfUploader UploaderSL2;
    private SfUploader UploaderBinder;
    private SfUploader UploaderInvoice;
    private SfUploader UploaderLossRuns;

    // Handles upload for each slot
    private async void OnUploadSlotSuccess(Syncfusion.Blazor.Inputs.ActionCompleteEventArgs args, string slotType)
    {
        if (!Proposal.AttachmentGroupId.HasValue)
        {
            await ProposalService.SaveProposalAsync(Proposal);
            Proposal = await ProposalService.GetProposalByRenewalIdAsync(RenewalId);
        }
        // Create new Attachment (simulate, real logic may differ depending on backend)
        var newAttachment = new Attachment
        {
            OriginalFileName = args.FileData.FirstOrDefault().Name,
            FileFormat = System.IO.Path.GetExtension(args.FileData.FirstOrDefault().Name),
            FileSize = args.FileData.FirstOrDefault().Size,
            DateCreated = DateTime.Now,
            AttachmentGroupId = Proposal.AttachmentGroupId ?? 0,
            IsQuote = slotType == "quote",
            IsAcord = slotType == "acord",
            IsSupplemental = slotType == "supplemental",
            IsProposal = slotType == "proposal",
            IsEnclosure = slotType == "enclosures",
            IsSL2 = slotType == "sl2",
            IsBinder = slotType == "binder",
            IsInvoice = slotType == "invoice",
            Description = slotType == "lossruns" ? "Loss Runs - " + args.FileData.FirstOrDefault().Name : args.FileData.FirstOrDefault().Name,
            RenewalId = RenewalId,
            ClientId = ClientId,
        };
        await AttachmentService.SaveDropZoneAttachmentAsync(newAttachment);
        await RefreshAttachments();

        // Notify parent component that attachments have changed
        if (OnAttachmentChanged.HasDelegate)
        {
            await OnAttachmentChanged.InvokeAsync();
        }

        StateHasChanged();
    }

    // Actually refresh the attachments list from the backend
    private async Task RefreshAttachments()
    {
        if (Proposal?.AttachmentGroupId != null)
        {
            AttachmentsList = await AttachmentService.GetAttachmentsByGroupIdAsync(Proposal.AttachmentGroupId.Value);
        }
        StateHasChanged();
    }

    // ------------------------------------------------- Uploads
    //----------------------------------------------------------
    private async Task OnChange(UploadChangeEventArgs args)
    {
        try
        {
            foreach (var file in args.Files)
            {
                await _log.LogAsync(LogLevel.Error, $"Uploading file: {file.FileInfo.Name}", "Attachments");
                var path = $"wwwroot/uploads/temp/{file.FileInfo.Name}";
                var fileName = file.FileInfo.Name;
                FileStream filestream = new FileStream(path, FileMode.Create, FileAccess.Write);
                await file.File.OpenReadStream(long.MaxValue).CopyToAsync(filestream);
                filestream.Close();
                //Check if file exists
                if (System.IO.File.Exists(path))
                {
                    await _log.LogAsync(LogLevel.Error, $"File exists: {path}", "Attachments");
                }
            }
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error uploading file: {ex.Message}", "Attachments", ex);
        }
    }

    private void OnUploadFailure(FailureEventArgs args)
    {
        _log.LogAsync(LogLevel.Error, $"Upload failure: {args.Response}", "Attachments");
        Console.WriteLine("File upload failed: " + args.File.Name);
    }

    private async Task OpenWithWindows(Attachment attachment, bool openFolderOnly)
    {
        Console.WriteLine("Opening with windows...");
        try
        {
            await _log.LogAsync(LogLevel.Information, $"Opening folder: {attachment?.LocalPath}", "Attachments");

            if (!string.IsNullOrEmpty(attachment?.LocalPath))
            {
                if (openFolderOnly)
                {
                    List<string> mylistfiles = new List<string> { StringHelper.BuildWindowsPath(attachment, false) };
                    await EmberService.RunEmberFunction("Windows_OpenFolder", mylistfiles);
                }
                else
                {
                    List<string> mylistfiles = new List<string> { StringHelper.BuildWindowsPath(attachment, false) };
                    await EmberService.RunEmberFunction("Windows_OpenFile", mylistfiles);
                }
            }
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, ex.ToString(), "Attachments");
        }
    }

    // Public method to refresh attachments from parent components
    public async Task RefreshAttachmentsFromParent()
    {
        await RefreshAttachments();
    }
}