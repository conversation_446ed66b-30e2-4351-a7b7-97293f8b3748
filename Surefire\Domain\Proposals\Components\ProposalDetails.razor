@using Microsoft.FluentUI.AspNetCore.Components
@using Microsoft.Extensions.Hosting
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Calendars
@using Surefire.Domain.Proposals
@using Surefire.Domain.Proposals.Models
@using Surefire.Domain.Proposals.Services
@using Surefire.Domain.Attachments.Components
@using Surefire.Domain.Attachments.Models
@using Surefire.Domain.Attachments.Services
@using Surefire.Domain.Renewals.Models
@using Surefire.Domain.Renewals.Services
@using Surefire.Domain.Forms.Services
@using Surefire.Domain.Users.Services
@using Surefire.Domain.Shared.Services
@using Surefire.Domain.Ember
@using Surefire.Domain.Logs

@inject ProposalWordDocumentService ProposalWordDocumentService
@inject LLMWhispererService LLMWhispererService
@inject AzureFormService AzureFormService
@inject AttachmentService AttachmentService
@inject ProposalService ProposalService
@inject RenewalService RenewalService
@inject PackagerService PackagerService
@inject NavigationManager Navigation
@inject UserService UserService
@inject StateService StateService
@inject EmberService EmberService
@inject FormService FormService
@inject OpenAIPro OpenAIPro

@inject IWebHostEnvironment Env
@inject ILoggingService _log
@inject IJSRuntime JS

@if (isLoading)
{
    <FluentProgressRing />
}
else if (Proposal == null)
{
    <FluentMessageBar Intent="MessageIntent.Error">Proposal not found.</FluentMessageBar>
}
else
{
    <FluentStack class="proposal-details-stack" Orientation="Orientation.Horizontal" HorizontalGap="32">

        <!-- Column 1 -->
        <FluentStack class="proposal-details-col" Orientation="Orientation.Vertical" Style="min-width:320px;max-width:350px;" VerticalGap="24">
            <!-- Status -->
            <FluentCard MinimalStyle="true" Class="CardStyle proposal-details-card-status">
                    <FluentStack>  
                        <span class="field-label-status">
                            Proposal Status:
                        </span>
                        <span class="field-value">
                            <SfDropDownList TValue="string" TItem="StatusOption" @bind-Value="StatusSaveString" DataSource="@StatusOptions">
                                <DropDownListEvents TValue="string" TItem="StatusOption" ValueChange="OnProposalStatusChanged" />
                                <DropDownListFieldSettings Text="Label" Value="Value" />
                            </SfDropDownList>
                        </span>
                    </FluentStack>  
            </FluentCard>

                <!-- Request Basics -->
            <FluentCard MinimalStyle="true" Class="CardStyle proposal-details-card">
                <div class="header-card">Request Basics</div><div style="height:25px;"></div>
                    
                <FluentStack>
                    <span class="field-label">
                        Requested By:
                    </span>
                    <span class="field-value">
                        <SfDropDownList TValue="string" TItem="ApplicationUser" DataSource="@AllUsers" @bind-Value="Proposal.CreatedById" OnChange="@(args => SaveProposal(true))">
                            <DropDownListEvents TValue="string" TItem="ApplicationUser" ValueChange="OnStatusChanged" />
                            <DropDownListFieldSettings Text="FullName" Value="Id" />
                        </SfDropDownList>
                    </span>
                </FluentStack>
                        
                <FluentStack>
                    <span class="field-label">
                        Bill Type:
                    </span>
                    <span class="field-value">
                        <FluentRadioGroup @bind-Value="BillTypeProxy">
                            <FluentRadio Value="@BillType.Direct">Direct</FluentRadio>
                            <FluentRadio Value="@BillType.Agency">Agency</FluentRadio>
                        </FluentRadioGroup>
                    </span>
                </FluentStack>

                <FluentStack>
                    <span class="field-label">
                        Minimum Earned:
                    </span>
                    <span class="field-value">
                        <SfNumericTextBox TValue="int?" @bind-Value="Proposal.DownPaymentPercent" Min="0" Max="100" OnChange="@(args => SaveProposal(true))" />
                    </span>
                </FluentStack>
                <FluentStack>
                    <span class="field-label">
                        Broker Fee:
                    </span>
                    <span class="field-value">
                        <SfNumericTextBox TValue="int?" @bind-Value="Proposal.BrokerFee" Min="0" OnChange="@(args => SaveProposal(true))" />
                    </span>
                </FluentStack>
                <FluentStack>
                    <span class="field-label">
                        Send Date:
                    </span>
                    <span class="field-value">
                        <SfDatePicker TValue="DateTime?" @bind-Value="SendDateProxy" />
                    </span>
                </FluentStack>
                  
                <div class="mb-2">
                    <SfTextBox @bind-Value="Proposal.SpecialInstructions" Placeholder="Special Instructions" FloatLabelType="FloatLabelType.Always" OnChange="@(args => SaveProposal(true))" />
                </div>
            </FluentCard>

            <FluentCard MinimalStyle="true" Class="CardStyle proposal-details-card">
                <div class="header-card">Accoutrements</div><div style="height:25px;"></div>
                    
                <FluentStack>
                    <span class="inc-sl2">
                        <SfCheckBox @bind-Checked="Proposal.IncludeSL2" @onchange="@SaveProposalSimple" Label="SL2"></SfCheckBox>
                        <span @onclick="CreateSL2" class="sl2-btn">
                            <span class="sl2create-icon"><FluentIcon Value="@(new Icons.Regular.Size20.FormNew())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" /></span>
                            <span class="sl2create-txt">Create</span>
                        </span>
                    </span>

                    <span class="inc-item">
                        <SfCheckBox @bind-Checked="Proposal.IncludeD1" @onchange="@SaveProposalSimple"></SfCheckBox>
                        <span class="accou" @onclick="OpenD1Form">D1</span>
                    </span>
                    
                    <span class="inc-item">
                        <SfCheckBox @bind-Checked="Proposal.IncludeD2" @onchange="@SaveProposalSimple"></SfCheckBox>
                        <span class="accou" @onclick="OpenD2Form">D2</span>
                    </span>
                </FluentStack>
                       
                               
                @if (Proposal.IncludeSL2 == true)
                {
                    <FluentStack>
                        <span class="field-label">
                            Industry Type:
                        </span>
                        <span class="field-value">
                             <SfTextBox @bind-Value="Proposal.IncludeSL2BusinessDesc" OnChange="@(args => SaveProposal(true))" />
                        </span>
                    </FluentStack>
                    <FluentStack>
                        <span class="field-label">
                            Coverage Code:
                        </span>
                        <span class="field-value">
                            <SfDropDownList TValue="string" TItem="CoverageCode" @bind-Value="Proposal.IncludeSL2Code" DataSource="@CoverageCodes">
                                <DropDownListEvents TValue="string" TItem="CoverageCode" ValueChange="OnStatusChanged" />
                                <DropDownListFieldSettings Text="Description" Value="Value" />
                            </SfDropDownList>
                        </span>
                    </FluentStack>
                    @if (slerror == true)
                    {
                        <div class="inst-me" style="color:red">Industry type and coverage code required! </div>
                    }
                    
                }
            </FluentCard>
        </FluentStack>

        <!-- Column 2 -->
        <FluentStack class="proposal-details-col" Style="min-width:320px;max-width:530px;overflow:hidden;" Orientation="Orientation.Vertical" VerticalGap="24">
            
                <FluentStack>
                    <div class="flat-class-container">
                        <div class="flat-card">Quote</div>
                        <div class="drop-zone-quote" id="dzquote">
                            @if (QuoteAttachment != null)
                            {
                                <div class="attachment-thumbnail">
                                    <a href="/@QuoteAttachment.LocalPath/@QuoteAttachment.HashedFileName" target="_blank">
                                        <img src="@GetThumbnailPath(QuoteAttachment)" class="thumb-icon" />
                                    </a>
                                    <div class="attachment-actions">
                                    <FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" OnClick="() => OnDeleteAttachmentClicked(QuoteAttachment)" />
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" OnClick="() => OpenWithWindows(QuoteAttachment, true)" />
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" OnClick="() => OpenWithWindows(QuoteAttachment, false)" />
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="attachment-thumbnail">
                                    <img src="/img/icons/document-upload.png" class="thumb-icon" />
                                </div>
                            }
                        </div>
                        <SfUploader @ref="UploaderQuote" DropEffect="DropEffect.Copy" DropArea="#dzquote">
                            <UploaderTemplates><Template></Template></UploaderTemplates>
                            <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "quote"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                        </SfUploader>
                    </div>
                    <div class="flat-class-container">
                        <div class="flat-card">Acord</div>
                        <div class="drop-zone-acord" id="dzacord">
                            @if (AcordAttachment != null)
                            {
                                <div class="attachment-thumbnail">
                                    <a href="/@AcordAttachment.LocalPath/@AcordAttachment.HashedFileName" target="_blank">
                                        <img src="@GetThumbnailPath(AcordAttachment)" class="thumb-icon" />
                                    </a>
                                    <div class="attachment-actions">
                                    <FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" OnClick="() => OnDeleteAttachmentClicked(AcordAttachment)" />
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" OnClick="() => OpenWithWindows(AcordAttachment, true)" />
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" OnClick="() => OpenWithWindows(AcordAttachment, false)" />
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="attachment-thumbnail">
                                    <img src="/img/icons/document-upload.png" class="thumb-icon" />
                                </div>
                            }
                        </div>
                        <SfUploader @ref="UploaderAcord" DropEffect="DropEffect.Copy" DropArea="#dzacord">
                            <UploaderTemplates><Template></Template></UploaderTemplates>
                            <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "acord"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                        </SfUploader>
                    </div>
                    <div class="flat-class-container">
                        <div class="flat-card">Enclosures</div>
                        <div class="drop-zone-enclosures" id="dzenclosures">
                            @if (EnclosuresAttachment != null)
                            {
                                <div class="attachment-thumbnail">
                                    <a href="/@EnclosuresAttachment.LocalPath/@EnclosuresAttachment.HashedFileName" target="_blank">
                                        <img src="@GetThumbnailPath(EnclosuresAttachment)" class="thumb-icon" />
                                    </a>
                                    <div class="attachment-actions">
                                    <FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" OnClick="() => OnDeleteAttachmentClicked(EnclosuresAttachment)" />
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" OnClick="() => OpenWithWindows(EnclosuresAttachment, true)" />
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" OnClick="() => OpenWithWindows(EnclosuresAttachment, false)" />
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="attachment-thumbnail">
                                    <img src="/img/icons/document-upload.png" class="thumb-icon" />
                                </div>
                            }
                        </div>
                        <SfUploader @ref="UploaderEnclosures" DropEffect="DropEffect.Copy" DropArea="#dzenclosures">
                            <UploaderTemplates><Template></Template></UploaderTemplates>
                            <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "enclosures"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                        </SfUploader>
                    </div>
                </FluentStack>
                <FluentStack>
                    <div class="flat-class-container flat-up">
                        <div class="flat-card">Supplemental</div>
                        <div class="drop-zone-acord" id="dzsupp">
                            @if (SupplementalAttachment != null)
                            {
                                <div class="attachment-thumbnail">
                                    <a href="/@SupplementalAttachment.LocalPath/@SupplementalAttachment.HashedFileName" target="_blank">
                                        <img src="@GetThumbnailPath(SupplementalAttachment)" class="thumb-icon" />
                                    </a>
                                    <div class="attachment-actions">
                                    <FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" OnClick="() => OnDeleteAttachmentClicked(SupplementalAttachment)" />
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" OnClick="() => OpenWithWindows(SupplementalAttachment, true)" />
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" OnClick="() => OpenWithWindows(SupplementalAttachment, false)" />
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="attachment-thumbnail">
                                    <img src="/img/icons/document-upload.png" class="thumb-icon" />
                                </div>
                            }
                        </div>
                        <SfUploader @ref="UploaderSupplemental" DropEffect="DropEffect.Copy" DropArea="#dzsupp">
                            <UploaderTemplates><Template></Template></UploaderTemplates>
                            <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "supplemental"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                        </SfUploader>
                    </div>

                    <div class="flat-class-container flat-up">
                        <div class="flat-card">Proposal</div>
                        <div class="drop-zone-acord" id="dzprop">
                            @if (ProposalAttachment != null)
                            {
                                <div class="attachment-thumbnail">
                                    <a class="docbutton" @onclick="OpenFile">
                                        <img src="img/icons/proposal-docx.png" class="thumb-icon" />
                                    </a>
                                    <div class="attachment-actions">
                                    <FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" OnClick="() => OnDeleteAttachmentClicked(ProposalAttachment)" />
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" OnClick="() => OpenWithWindows(ProposalAttachment, true)" />
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" OnClick="() => OpenWithWindows(ProposalAttachment, false)" />
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="attachment-thumbnail">
                                    <img src="/img/icons/document-upload.png" class="thumb-icon" />
                                </div>
                            }
                        </div>
                        <SfUploader @ref="UploaderProposal" DropEffect="DropEffect.Copy" DropArea="#dzprop">
                            <UploaderTemplates><Template></Template></UploaderTemplates>
                            <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "proposal"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                        </SfUploader>
                    </div>
                    <div class="flat-class-container flat-up">
                        <div class="flat-card">SL-2</div>
                        <div class="drop-zone-sl2" id="dzsl2">
                            @if (SL2Attachment != null)
                            {
                                <div class="attachment-thumbnail">
                                    <a href="/@SL2Attachment.LocalPath/@SL2Attachment.HashedFileName" target="_blank">
                                        <img src="/img/sl2.jpg" class="thumb-icon" />
                                    </a>
                                    <div class="attachment-actions">
                                    <FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" OnClick="() => OnDeleteAttachmentClicked(SL2Attachment)" />
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" OnClick="() => OpenWithWindows(SL2Attachment, true)" />
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" OnClick="() => OpenWithWindows(SL2Attachment, false)" />
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="attachment-thumbnail">
                                    <img src="/img/icons/document-upload.png" class="thumb-icon" />
                                </div>
                            }
                        </div>
                        <SfUploader @ref="UploaderSL2" DropEffect="DropEffect.Copy" DropArea="#dzsl2">
                            <UploaderTemplates><Template></Template></UploaderTemplates>
                            <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "sl2"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                        </SfUploader>
                    </div>
                </FluentStack>
            
        </FluentStack>

        <!-- Column 3: Attachments Slots -->
        <FluentStack class="proposal-details-col" Style="max-width: 190px;" Orientation="Orientation.Vertical" VerticalGap="10">

            <!-- Status -->
            <FluentCard MinimalStyle="true" Class="CardStyle proposal-details-card">
                <div class="header-card">Proposler</div><div style="height:25px;"></div>
                @if (QuoteAttachment != null)
                {
                    <div class="page-extractor">
                        <div class="page-inputs">
                            @for (int i = 0; i < 5; i++)
                            {
                                var index = i;
                                <input type="text" 
                                       class="page-input @(GetPageInputClass(index))"
                                       value="@GetPageValue(index)"
                                       @oninput="(e) => OnPageInput(index, e.Value?.ToString())"
                                       @onkeydown="(e) => OnPageKeyDown(index, e)"
                                       maxlength="3"
                                       placeholder="Page" />
                            }
                        </div>
                        <div class="page-extractor-instruction">
                            Enter up to five pages for extraction
                        </div>
                    </div>

                    <FluentButton OnClick="RunFullProposalProcess" 
                                Disabled="@(QuoteAttachment == null || isRunningFullProcess || !HasAnyPagesEntered())" 
                                Class="@GetProposlerButtonClass()"
                                Style="width:100%;">
                        @if (isRunningFullProcess)
                        {
                            <span>Proposaling...</span>
                        }
                        else
                        {
                            <span>Run the Proposler</span>
                        }
                    </FluentButton>

                    @if (!string.IsNullOrEmpty(azureProcessingStatus))
                    {
                        <div style="width:150px; margin-left:auto; margin-right:auto;">
                        <Sicks Progress="@currentProgress" Width="150px" Height="150px" SolidColor="#6f42c1" SolidColorEnd="#e83e8c" ShowPercentage="false" />
                        </div>

                        <div style="margin-top: 10px; padding: 8px; background-color: #f0f8ff; border-radius: 4px; font-size: 0.9em;">
                            @azureProcessingStatus
                        </div>
                    }

                    @if(advancedMode)
                    {


                        <FluentStack HorizontalAlignment="HorizontalAlignment.SpaceBetween">
                            <FluentButton OnClick="ExtractPages" Disabled="@(QuoteAttachment == null || isProcessingAzure)">
                                @if (isProcessingAzure)
                                {
                                    <span>Processing...</span>
                                }
                                else
                                {
                                    <span>Extract ></span>
                                }
                            </FluentButton>
                            <FluentButton OnClick="ProcessQuoteWithAzure" Disabled="@(QuoteAttachment == null || isProcessingAzure)">
                                @if (isProcessingAzure)
                                {
                                    <span>Processing...</span>
                                }
                                else
                                {
                                    <span>Prep ></span>
                                }
                            </FluentButton>
                            <FluentButton OnClick="ProcessQuoteWithLLMWhisperer" Disabled="@(QuoteAttachment == null || isProcessingLLMWhisperer)">
                                @if (isProcessingLLMWhisperer)
                                {
                                    <span>Processing...</span>
                                }
                                else
                                {
                                    <span>Read ></span>
                                }
                            </FluentButton>
                            <FluentButton OnClick="ProcessRefinedData" Disabled="@(JsonAttachments.Count == 0 || TxtAttachments.Count == 0 || isProcessingRefinedData)">
                                @if (isProcessingRefinedData)
                                {
                                    <span>Processing...</span>
                                }
                                else
                                {
                                    <span>Refine ></span>
                                }
                            </FluentButton>
                            <FluentButton OnClick="CreateWordDocument" Disabled="@(RefinedJsonAttachments.Count == 0 || isCreatingWordDoc)">
                                @if (isCreatingWordDoc)
                                {
                                    <span>Creating...</span>
                                }
                                else
                                {
                                    <span>Build ></span>
                                }
                            </FluentButton>
                        </FluentStack>

                        @if (JsonAttachments.Any())
                        {
                            <div style="margin-top: 10px;">
                                <strong>Extracted JSON Files:</strong>
                                @foreach (var jsonAttachment in JsonAttachments)
                                {
                                    <div style="margin: 4px 0; font-size: 0.85em;">
                                        <a href="/@jsonAttachment.LocalPath/@jsonAttachment.HashedFileName" target="_blank">
                                            @jsonAttachment.OriginalFileName
                                        </a>
                                        <span style="color: #666; margin-left: 8px;">(@FormatFileSize(jsonAttachment.FileSize))</span>
                                    </div>
                                }
                            </div>
                        }
                        @if (TxtAttachments.Any())
                        {
                            <div style="margin-top: 10px;">
                                <strong>Extracted TXT Files:</strong>
                                @foreach (var txtAttachment in TxtAttachments)
                                {
                                    <div style="margin: 4px 0; font-size: 0.85em;">
                                        <a href="/@txtAttachment.LocalPath/@txtAttachment.HashedFileName" target="_blank">
                                            @txtAttachment.OriginalFileName
                                        </a>
                                        <span style="color: #666; margin-left: 8px;">(@FormatFileSize(txtAttachment.FileSize))</span>
                                    </div>
                                }
                            </div>
                        }
                        @if (RefinedJsonAttachments.Any())
                        {
                            <div style="margin-top: 10px;">
                                <strong>Refined JSON Files:</strong>
                                @foreach (var refinedAttachment in RefinedJsonAttachments)
                                {
                                    <div style="margin: 4px 0; font-size: 0.85em;">
                                        <a href="/@refinedAttachment.LocalPath/@refinedAttachment.HashedFileName" target="_blank">
                                            @refinedAttachment.OriginalFileName
                                        </a>
                                        <span style="color: #666; margin-left: 8px;">(@FormatFileSize(refinedAttachment.FileSize))</span>
                                    </div>
                                }
                            </div>
                        }
                        @if (WordDocAttachments.Any())
                        {
                            <div style="margin-top: 10px;">
                                <strong>Generated Word Documents:</strong>
                                @foreach (var wordAttachment in WordDocAttachments)
                                {
                                    <div style="margin: 4px 0; font-size: 0.85em;">
                                        <a href="/@wordAttachment.LocalPath/@wordAttachment.HashedFileName" target="_blank">
                                            @wordAttachment.OriginalFileName
                                        </a>
                                        <span style="color: #666; margin-left: 8px;">(@FormatFileSize(wordAttachment.FileSize))</span>
                                    </div>
                                }
                            </div>
                        }

                    }

                    @if (WordDocAttachments.Any())
                    {
                        <hr />
                        <div class="settings">Proposler Packager</div>
                        
                        <div class="mb-2">
                            <FluentCheckbox @bind-Value="Proposal.IncludeSL2">Include SL-2 Form</FluentCheckbox>
                        </div>
                        

                        <div class="mb-2">
                            <FluentCheckbox @bind-Value="Proposal.IncludeD1">Include D1 Form</FluentCheckbox>
                        </div>

                        <div class="mb-2">
                            <FluentCheckbox @bind-Value="Proposal.IncludeD2">Include D2 Form</FluentCheckbox>
                        </div>

                        <div class="mb-2">
                            <input type="checkbox" value="Include TRIA" />
                        </div>

                        <FluentButton OnClick="CreateProposalPackage" Disabled="@isCreatingPackage" Appearance="Appearance.Accent" Style="width:100%; margin-top: 10px;">
                            @if (isCreatingPackage)
                            {
                                <span>Creating Package...</span>
                            }
                            else
                            {
                                <span>Create Proposler Package</span>
                            }
                        </FluentButton>

                        @if (PackageAttachment != null)
                        {
                            <div style="margin-top: 10px; padding: 8px; background-color: #f0f8ff; border-radius: 4px;">
                                <strong>Package Created:</strong><br />
                                <a href="/@PackageAttachment.LocalPath/@PackageAttachment.HashedFileName" target="_blank" style="color: #036ac4; text-decoration: none;">
                                    📄 @PackageAttachment.OriginalFileName
                                </a>
                                <span style="color: #666; margin-left: 8px;">(@FormatFileSize(PackageAttachment.FileSize))</span>
                            </div>
                        }
                    }
                }
                    else{
                        <div class="inst-me">
                    <span style="font-size: 1.15em; font-weight: 800; color: #454545;">Attach Quote and Update Status</span><br />To request a proposal be created, change the status to "New Request" and complete the basics section. Uploading a Quote, Supplemental and Acord that all have matching limits and coverages is also recommended. <br /><br />The working Word Doc for the proposal will also live here if you should need to change something.<br /><br />Click save after making any changes!
                </div>

                        <span>Please attach a quote to use Proposler.</span>
                }

                <div class="advancedbtn" @onclick="ToggleAdvancedMode">Advanced Mode</div>

            </FluentCard>
            
        </FluentStack>

    </FluentStack>
    
}
<style>.e-numsmall{font-size:.9em;height:27px}:root .e-re{background-color:#fefefe!important;overflow:hidden;box-sizing:border-box;margin-right:12px;margin-bottom:4px!important;padding:8px 8px 10px!important;min-width:220px!important;max-width:250px!important;border-radius:10px;box-shadow:0 2px 8px rgba(0,0,0,.04)}.e-upload{border:0!important}.e-upload .e-file-select-wrap{padding:0!important;margin:0!important}.e-upload .e-file-select-wrap .e-btn{top:-25px!important;left:0!important}.e-upload .e-upload-files .e-upload-file-list{position:unset!important;min-height:unset!important;height:unset!important}
</style>

<Surefire.Domain.Shared.Components.ConfirmationDialog
    DialogId="delete-attachment-dialog"
    Title="Delete Attachment"
    Message=@(deleteDialogMessage)
    ConfirmText="Delete"
    CancelText="Cancel"
    @bind-Hidden="isDeleteDialogHidden"
    OnConfirm="OnDeleteDialogConfirmed" />

@code {
    [Parameter] public int ClientId { get; set; }
    [Parameter] public int RenewalId { get; set; }
    private Proposal Proposal { get; set; }
    private List<ApplicationUser> AllUsers { get; set; } = new();
    private List<Attachment> Attachments { get; set; } = new();
    private bool isLoading = true;
    private bool advancedMode = false;
    private bool slerror = false;
    private List<SendInstructions> SendInstructionsList = Enum.GetValues(typeof(SendInstructions)).Cast<SendInstructions>().ToList();

    protected override async Task OnParametersSetAsync()
    {
        try
        {
            isLoading = true;
            AllUsers = await UserService.GetAllUsersAsync();
            Proposal = await ProposalService.GetProposalByRenewalIdAsync(RenewalId);

            // Ensure CreatedById is set
            if (string.IsNullOrEmpty(Proposal.CreatedById))
            {
                Proposal.CreatedById = StateService.CurrentUser?.Id ?? string.Empty;
            }

            if (Proposal.AttachmentGroupId.HasValue)
            {
                Attachments = await AttachmentService.GetAttachmentsByGroupIdAsync(Proposal.AttachmentGroupId.Value);
            }
            else
            {
                Attachments = new List<Attachment>();
            }

            isLoading = false;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error in OnParametersSetAsync: {ex.Message}", "ProposalDetails", ex);
            isLoading = false;
            StateHasChanged();
        }
    }
    public async Task OpenFile()
    {
        if (ProposalAttachment != null)
        {
            await EmberService.WindowsOpenFile(ProposalAttachment);
        }
    }
    // ------------------------------------------------- Save
    //----------------------------------------------------------
    private async Task SaveProposal(bool stayOnPage = false)
    {
        await ProposalService.SaveProposalAsync(Proposal);
        // Refresh Proposal from the database to get updated proposalid and attachmentgroupid
        var updatedProposal = await ProposalService.GetProposalByRenewalIdAsync(RenewalId);
        if (updatedProposal != null)
        {
            Proposal = updatedProposal;
        }
        if (!stayOnPage)
        {
            Navigation.NavigateTo($"/Renewals/Details/{RenewalId}");
        }
        StateHasChanged();
    }
    private async Task SaveProposalSimple(Microsoft.AspNetCore.Components.ChangeEventArgs args)
    {
        await ProposalService.SaveProposalAsync(Proposal);
    }
    private void ToggleAdvancedMode()
    {
        advancedMode = !advancedMode;
    }
    // ------------------------------------------------- Dialogs
    //----------------------------------------------------------
    private bool isDeleteDialogHidden = true;
    private Attachment? attachmentToDelete = null;
    private bool deleteFromFilesystem = false;
    private string deleteDialogMessage => attachmentToDelete == null
        ? "Are you sure you want to delete this attachment?"
        : $"Are you sure you want to delete '{attachmentToDelete.OriginalFileName}'?";
    private void OnDeleteAttachmentClicked(Attachment? attachment)
    {
        attachmentToDelete = attachment;
        deleteFromFilesystem = false;
        isDeleteDialogHidden = false;
    }
    private async Task OnDeleteDialogConfirmed(bool confirmed)
    {
        if (confirmed && attachmentToDelete != null)
        {
            if (deleteFromFilesystem)
            {
                await AttachmentService.DeleteAttachmentAndFileAsync(attachmentToDelete.AttachmentId);
            }
            else
            {
                await AttachmentService.DeleteAttachmentAsync(attachmentToDelete.AttachmentId);
            }
            await RefreshAttachments();
        }
        isDeleteDialogHidden = true;
        attachmentToDelete = null;
        deleteFromFilesystem = false;
        StateHasChanged();
    }

    // --------------------------------------- Images and Thumbs
    //----------------------------------------------------------
    // Returns true if the file format is an image
    private bool IsImage(string fileFormat)
    {
        if (string.IsNullOrEmpty(fileFormat)) return false;
        var ext = fileFormat.ToLower();
        return ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".gif";
    }
    // Returns the icon path for a given file format
    private string GetIconPath(string fileFormat)
    {
        if (string.IsNullOrEmpty(fileFormat)) return "/img/icons/document.png";
        var ext = fileFormat.ToLower();
        if (ext == ".pdf") return "/img/icons/pdf.png";
        if (ext == ".doc" || ext == ".docx") return "/img/icons/word.png";
        if (ext == ".xls" || ext == ".xlsx") return "/img/icons/excel.png";
        // add more as needed
        return "/img/icons/document.png";
    }
    // Returns the thumbnail path for an image attachment
    private string GetThumbnailPath(Attachment attachment)
    {
        if (attachment == null || string.IsNullOrEmpty(attachment.HashedFileName) || string.IsNullOrEmpty(attachment.LocalPath))
            return string.Empty;
        // Always jpg for thumbnails
        var baseName = attachment.HashedFileName;
        // Replace extension with _thumb.jpg
        var thumbName = baseName;
        var lastDot = baseName.LastIndexOf('.');
        if (lastDot >= 0)
        {
            thumbName = baseName.Substring(0, lastDot) + "_thumb.jpg";
        }
        else
        {
            thumbName = baseName + "_thumb.jpg";
        }
        // Ensure forward slashes
        var localPath = attachment.LocalPath.Replace("\\", "/").TrimEnd('/');
        return $"/{localPath}/{thumbName}";
    }

    // ---------------------------------------- Attachment Slots
    //----------------------------------------------------------
    private Attachment QuoteAttachment => Attachments?.Where(a => a.IsQuote).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment AcordAttachment => Attachments?.Where(a => a.IsAcord).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment SupplementalAttachment => Attachments?.Where(a => a.IsSupplemental).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment ProposalAttachment => Attachments?.Where(a => a.IsProposal).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment EnclosuresAttachment => Attachments?.Where(a => a.IsEnclosure).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment SL2Attachment => Attachments?.Where(a => a.IsSL2).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment BinderAttachment => Attachments?.Where(a => a.IsBinder).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment InvoiceAttachment => Attachments?.Where(a => a.IsInvoice).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private Attachment LossRunsAttachment => Attachments?.Where(a => a.Description != null && a.Description.ToLower().Contains("loss runs")).OrderByDescending(a => a.DateCreated).FirstOrDefault();

    private SfUploader UploaderQuote;
    private SfUploader UploaderAcord;
    private SfUploader UploaderSupplemental;
    private SfUploader UploaderProposal;
    private SfUploader UploaderEnclosures;
    private SfUploader UploaderSL2;
    private SfUploader UploaderBinder;
    private SfUploader UploaderInvoice;
    private SfUploader UploaderLossRuns;

    // Handles upload for each slot
    private async void OnUploadSlotSuccess(Syncfusion.Blazor.Inputs.ActionCompleteEventArgs args, string slotType)
    {
         if (!Proposal.AttachmentGroupId.HasValue)
        {
            await ProposalService.SaveProposalAsync(Proposal);
            Proposal = await ProposalService.GetProposalByRenewalIdAsync(RenewalId);
        }
        // Create new Attachment (simulate, real logic may differ depending on backend)
        var newAttachment = new Attachment
        {
            OriginalFileName = args.FileData.FirstOrDefault().Name,
            FileFormat = System.IO.Path.GetExtension(args.FileData.FirstOrDefault().Name),
            FileSize = args.FileData.FirstOrDefault().Size,
            DateCreated = DateTime.Now,
            AttachmentGroupId = Proposal.AttachmentGroupId ?? 0,
            IsQuote = slotType == "quote",
            IsAcord = slotType == "acord",
            IsSupplemental = slotType == "supplemental",
            IsProposal = slotType == "proposal",
            IsEnclosure = slotType == "enclosures",
            IsSL2 = slotType == "sl2",
            IsBinder = slotType == "binder",
            IsInvoice = slotType == "invoice",
            Description = slotType == "lossruns" ? "Loss Runs - " + args.FileData.FirstOrDefault().Name : args.FileData.FirstOrDefault().Name,
            RenewalId = RenewalId,
            ClientId = ClientId,
        };
        await AttachmentService.SaveDropZoneAttachmentAsync(newAttachment);
        await RefreshAttachments();
        
        StateHasChanged();
    }

    // Actually refresh the attachments list from the backend
    private async Task RefreshAttachments()
    {
        if (Proposal?.AttachmentGroupId != null)
        {
            Attachments = await AttachmentService.GetAttachmentsByGroupIdAsync(Proposal.AttachmentGroupId.Value);
        }
        StateHasChanged();
    }
    private List<CoverageCode> CoverageCodes = new()
    {
        new CoverageCode { Value = "2000", Description = "Property (2000)" },
        new CoverageCode { Value = "4000", Description = "Inland Marine (4000)" },
        new CoverageCode { Value = "5000", Description = "General Liability (5000)" },
        new CoverageCode { Value = "5900", Description = "Excess Liability (5900)" },
        new CoverageCode { Value = "6000", Description = "Errors & Omissions (6000)" },
        new CoverageCode { Value = "6010", Description = "Directors & Officers (6010)" },
        new CoverageCode { Value = "6020", Description = "Employment Practice Liability (6020)" },
        new CoverageCode { Value = "6200", Description = "Cyber Liability (6200)" },
        new CoverageCode { Value = "8000", Description = "Auto Liability (8000)" }
    };
    public class CoverageCode
    {
        public string Value { get; set; }
        public string Description { get; set; }
    }

    // ------------------------------------------ Status Options
    //----------------------------------------------------------
    private List<StatusOption> StatusOptions = new()
    {
        new StatusOption { Value = -1, Label = "(Not Started)" },
        new StatusOption { Value = 0, Label = "New Request" },
        new StatusOption { Value = 10, Label = "New Mailout" },
        new StatusOption { Value = 1, Label = "Pending Creation" },
        new StatusOption { Value = 2, Label = "Need Clarification" },
        new StatusOption { Value = 3, Label = "Sent" },
        new StatusOption { Value = 4, Label = "Signed" },
        new StatusOption { Value = 5, Label = "Rejected" },
        new StatusOption { Value = 6, Label = "Accepted" },
        new StatusOption { Value = 7, Label = "Pending Bind" },
        new StatusOption { Value = 8, Label = "Bound" }
        
    };
    private string StatusSaveString
    {
        get => Proposal.Status.ToString();
        set
        {
            if (int.TryParse(value, out var intVal))
            {
                Proposal.Status = intVal;
            }
        }
    }
    private async Task OnStatusChanged(object value)
    {
        // Save and stay on page
        await SaveProposal(true);
    }
    private async Task OnProposalStatusChanged(object value)
    {
        // Get the status description from the selected option
        var selectedStatus = StatusOptions.FirstOrDefault(s => s.Value.ToString() == value?.ToString());
        if (selectedStatus != null)
        {
            // Create a renewal note for the status change
            var user = StateService.CurrentUser;
            var userName = user != null ? user.FirstName : "System";
            var note = new RenewalNote
            {
                RenewalId = RenewalId,
                Note = $"Proposal status changed to <strong>{selectedStatus.Label}</strong> by {userName}.",
                DateCreated = DateTime.Now,
                CreatedById = user?.Id,
                NoteType = RenewalNoteType.SystemLog
            };

            // Add the note
            await RenewalService.AddRenewalNoteAsync(note);
        }

        // Save and stay on page
        await SaveProposal(true);
    }
    public class StatusOption
    {
        public int Value { get; set; }
        public string Label { get; set; }
    }
    public class EnumData<T>
    {
        public T Value { get; set; }
        public string Text { get; set; }
    }

    // ------------------------------------------------- Uploads
    //----------------------------------------------------------
    private double currentProgress = 0;
    private bool isProcessingAzure = false;
    private bool isCreatingWordDoc = false;
    private bool isRunningFullProcess = false;
    private bool isProcessingRefinedData = false;
    private bool isProcessingLLMWhisperer = false;
    private string azureProcessingStatus = string.Empty;

    private async Task OnChange(UploadChangeEventArgs args)
    {

        try
        {
            foreach (var file in args.Files)
            {
                await _log.LogAsync(LogLevel.Error, $"Uploading file: {file.FileInfo.Name}", "ProposalDetails");
                var path = $"wwwroot/uploads/temp/{file.FileInfo.Name}";
                var fileName = file.FileInfo.Name;
                FileStream filestream = new FileStream(path, FileMode.Create, FileAccess.Write);
                await file.File.OpenReadStream(long.MaxValue).CopyToAsync(filestream);
                filestream.Close();
                //Check if file exists
                if (System.IO.File.Exists(path))
                {
                    await _log.LogAsync(LogLevel.Error, $"File exists: {path}", "ProposalDetails");
                }
            }
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error uploading file: {ex.Message}", "DropzoneContainer", ex);
        }
    }
    private async Task OnAttachmentAdded(object args)
    {
        if (!Proposal.AttachmentGroupId.HasValue)
        {
            await ProposalService.SaveProposalAsync(Proposal);
        }

        if (Proposal.AttachmentGroupId.HasValue)
        {
            Attachments = await AttachmentService.GetAttachmentsByGroupIdAsync(Proposal.AttachmentGroupId.Value);
            StateHasChanged();
        }
    }
    private async Task ProcessQuoteWithAzure()
    {
        isProcessingAzure = true;
        if (!isRunningFullProcess) // Only reset if not part of full process
        {
            currentProgress = 0;
        }
        azureProcessingStatus = "Starting Azure Form Recognizer processing...";
        StateHasChanged();

        try
        {
            await _log.LogAsync(LogLevel.Information, $"Starting Azure Form Recognizer processing for quote: {QuoteAttachment.OriginalFileName}", "ProposalDetails");

            azureProcessingStatus = "Sending PDF to Azure Form Recognizer...";
            StateHasChanged();

            // Create a temporary attachment for filedataforai.pdf in the client's attachment directory
            var tempAttachment = new Attachment
                {
                    OriginalFileName = "filedataforai.pdf",
                    FileFormat = ".pdf",
                    LocalPath = $"uploads/clients/{ClientId}/{Proposal.AttachmentGroupId}",
                    HashedFileName = "filedataforai.pdf",
                    AttachmentGroupId = Proposal.AttachmentGroupId.Value,
                    RenewalId = RenewalId,
                    ClientId = ClientId
                };

            await AzureFormService.ProcessPdfAttachmentAsync(tempAttachment);

            azureProcessingStatus = "Refreshing attachments...";
            StateHasChanged();

            await RefreshAttachments();
            await _log.LogAsync(LogLevel.Information, "Successfully processed filedataforai.pdf with Azure Form Recognizer", "ProposalDetails");
            azureProcessingStatus = "Processing completed successfully! JSON files have been created.";
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error processing filedataforai.pdf with Azure: {ex.Message}", "ProposalDetails", ex);
            azureProcessingStatus = $"Error: {ex.Message}";
        }
        finally
        {
            isProcessingAzure = false;
            if (!isRunningFullProcess)
            {
                currentProgress = 100;
                StateHasChanged();
                await Task.Delay(2000); // Show completion for 2 seconds
                azureProcessingStatus = string.Empty;
                currentProgress = 0;
            }
            StateHasChanged();
        }
    }
    private async Task ProcessQuoteWithLLMWhisperer()
    {
        isProcessingLLMWhisperer = true;
        if (!isRunningFullProcess) // Only reset if not part of full process
        {
            currentProgress = 0;
        }
        azureProcessingStatus = "Starting LLM Whisperer processing...";
        StateHasChanged();

        try
        {
            await _log.LogAsync(LogLevel.Information, $"Starting LLM Whisperer processing for quote: {QuoteAttachment.OriginalFileName}", "ProposalDetails");

            azureProcessingStatus = "Sending PDF to LLM Whisperer...";
            StateHasChanged();

            // Create a temporary attachment for filedataforai.pdf in the client's attachment directory
            var tempAttachment = new Attachment
                {
                    OriginalFileName = "filedataforai.pdf",
                    FileFormat = ".pdf",
                    LocalPath = $"uploads/clients/{ClientId}/{Proposal.AttachmentGroupId}",
                    HashedFileName = "filedataforai.pdf",
                    AttachmentGroupId = Proposal.AttachmentGroupId.Value,
                    RenewalId = RenewalId,
                    ClientId = ClientId
                };

            await LLMWhispererService.ProcessPdfAttachmentAsync(tempAttachment);

            azureProcessingStatus = "Refreshing attachments...";
            StateHasChanged();

            await RefreshAttachments();
            await _log.LogAsync(LogLevel.Information, "Successfully processed filedataforai.pdf with LLM Whisperer", "ProposalDetails");
            azureProcessingStatus = "Processing completed successfully! TXT file has been created.";
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error processing filedataforai.pdf with LLM Whisperer: {ex.Message}", "ProposalDetails", ex);
            azureProcessingStatus = $"Error: {ex.Message}";
        }
        finally
        {
            isProcessingLLMWhisperer = false;
            if (!isRunningFullProcess)
            {
                currentProgress = 100;
                StateHasChanged();
                await Task.Delay(2000); // Show completion for 2 seconds
                azureProcessingStatus = string.Empty;
                currentProgress = 0;
            }
            StateHasChanged();
        }
    }
    private List<Attachment> JsonAttachments => Attachments?.Where(a => a.FileFormat?.ToLower() == ".json").ToList() ?? new List<Attachment>();
    private List<Attachment> TxtAttachments => Attachments?.Where(a => a.FileFormat?.ToLower() == ".txt").ToList() ?? new List<Attachment>();
    private List<Attachment> RefinedJsonAttachments => Attachments?.Where(a => a.IsRefinedProposal).ToList() ?? new List<Attachment>();
    private List<Attachment> WordDocAttachments => Attachments?.Where(a => a.FileFormat?.ToLower() == ".docx" && a.Description?.Contains("Generated Proposal") == true).ToList() ?? new List<Attachment>();
    private async Task ProcessRefinedData()
    {
        if (JsonAttachments.Count == 0 || TxtAttachments.Count == 0)
        {
            await _log.LogAsync(LogLevel.Error, "Missing required files for refinement", "ProposalDetails");
            azureProcessingStatus = "Error: Both JSON and TXT files are required for data refinement.";
            return;
        }

        isProcessingRefinedData = true;
        if (!isRunningFullProcess) // Only reset if not part of full process
        {
            currentProgress = 0;
        }
        azureProcessingStatus = "Starting data refinement with OpenAI...";
        StateHasChanged();

        try
        {
            // Get the latest JSON and TXT files - prefer minified JSON over raw
            var availableJsonFiles = JsonAttachments.Where(a => !a.IsRefinedProposal).OrderByDescending(a => a.DateCreated).ToList();

            // Prefer minified files over raw files
            var latestJsonAttachment = availableJsonFiles.FirstOrDefault(a => a.OriginalFileName.Contains("minified")) 
                                    ?? availableJsonFiles.FirstOrDefault();

            var latestTxtAttachment = TxtAttachments.OrderByDescending(a => a.DateCreated).FirstOrDefault();

            if (latestJsonAttachment == null || latestTxtAttachment == null)
            {
                azureProcessingStatus = "Error: Could not find latest JSON or TXT files.";
                return;
            }

            await _log.LogAsync(LogLevel.Information, $"Starting data refinement with JSON: {latestJsonAttachment.OriginalFileName}, TXT: {latestTxtAttachment.OriginalFileName}", "ProposalDetails");

            azureProcessingStatus = "Processing files with OpenAI...";
            StateHasChanged();

            var refinedAttachment = await OpenAIPro.RefineProposalDataAsync(
                latestJsonAttachment, 
                latestTxtAttachment, 
                Proposal.AttachmentGroupId.Value, 
                ClientId, 
                RenewalId);

            azureProcessingStatus = "Refreshing attachments...";
            StateHasChanged();

            await RefreshAttachments();
            await _log.LogAsync(LogLevel.Information, $"Successfully created refined proposal data: {refinedAttachment.OriginalFileName}", "ProposalDetails");
            azureProcessingStatus = "Data refinement completed successfully! Refined JSON file has been created.";
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error refining proposal data: {ex.Message}", "ProposalDetails", ex);
            azureProcessingStatus = $"Error: {ex.Message}";
        }
        finally
        {
            isProcessingRefinedData = false;
            if (!isRunningFullProcess)
            {
                currentProgress = 100;
                StateHasChanged();
                await Task.Delay(2000); // Show completion for 2 seconds
                azureProcessingStatus = string.Empty;
                currentProgress = 0;
            }
            StateHasChanged();
        }
    }
    private async Task CreateWordDocument()
    {
        if (RefinedJsonAttachments.Count == 0)
        {
            await _log.LogAsync(LogLevel.Error, "No refined JSON files found", "ProposalDetails");
            azureProcessingStatus = "Error: No refined JSON files found for creating a word document.";
            return;
        }

        isCreatingWordDoc = true;
        if (!isRunningFullProcess) // Only reset if not part of full process
        {
            currentProgress = 0;
        }
        azureProcessingStatus = "Starting word document creation...";
        StateHasChanged();

        try
        {
            await _log.LogAsync(LogLevel.Information, $"Starting word document creation with {RefinedJsonAttachments.Count} refined JSON files", "ProposalDetails");

            azureProcessingStatus = "Creating word document...";
            StateHasChanged();

            // Get the latest refined JSON attachment
            var latestRefinedAttachment = RefinedJsonAttachments.OrderByDescending(a => a.DateCreated).FirstOrDefault();

            if (latestRefinedAttachment == null)
            {
                azureProcessingStatus = "Error: No refined JSON attachment found.";
                return;
            }

            await ProposalWordDocumentService.CreateProposalWordDocumentAsync(
                latestRefinedAttachment, 
                ClientId, 
                RenewalId, 
                Proposal.AttachmentGroupId.Value);

            azureProcessingStatus = "Refreshing attachments...";
            StateHasChanged();

            await RefreshAttachments();
            await _log.LogAsync(LogLevel.Information, $"Successfully created word document with {RefinedJsonAttachments.Count} refined JSON files", "ProposalDetails");
            azureProcessingStatus = "Word document creation completed successfully!";
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error creating word document: {ex.Message}", "ProposalDetails", ex);
            azureProcessingStatus = $"Error: {ex.Message}";
        }
        finally
        {
            isCreatingWordDoc = false;
            if (!isRunningFullProcess)
            {
                currentProgress = 100;
                StateHasChanged();
                await Task.Delay(2000); // Show completion for 2 seconds
                azureProcessingStatus = string.Empty;
                currentProgress = 0;
            }
            StateHasChanged();
        }
    }
    private async Task RunFullProposalProcess()
    {
        if (QuoteAttachment == null)
        {
            await _log.LogAsync(LogLevel.Error, "Quote attachment is null", "ProposalDetails");
            azureProcessingStatus = "Error: No quote attachment found.";
            return;
        }

        if (string.IsNullOrEmpty(Proposal.ExtractPages))
        {
            await _log.LogAsync(LogLevel.Error, "No pages specified for extraction", "ProposalDetails");
            azureProcessingStatus = "Error: Please specify at least one page number to extract.";
            return;
        }

        isRunningFullProcess = true;
        currentProgress = 0;
        azureProcessingStatus = "Starting full proposal process...";
        StateHasChanged();

        try
        {
            await _log.LogAsync(LogLevel.Information, "Starting full proposal process", "ProposalDetails");

            // Step 1: Extract pages and create filedataforai.pdf (0% -> 25%)
            currentProgress = 10;
            azureProcessingStatus = "Extracting pages from quote...";
            StateHasChanged();

            // Get the full physical path to the quote attachment
            string quotePath = Path.Combine(
                Env.WebRootPath,
                QuoteAttachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()),
                QuoteAttachment.HashedFileName
            );

            // Extract the pages
            bool success = await ProposalService.ExtractPages(quotePath, Proposal.ExtractPages);

            if (!success)
            {
                throw new Exception("Failed to extract pages from quote");
            }

            currentProgress = 25;
            azureProcessingStatus = "Pages extracted successfully. Starting Azure and LLM processing...";
            StateHasChanged();

            // Step 2: Run ProcessQuoteWithAzure and ProcessQuoteWithLLMWhisperer in parallel (25% -> 50%)
            currentProgress = 30;
            azureProcessingStatus = "Running Azure Form Recognizer and LLM Whisperer in parallel...";
            StateHasChanged();

            var azureTask = ProcessQuoteWithAzure();
            var llmWhispererTask = ProcessQuoteWithLLMWhisperer();

            await Task.WhenAll(azureTask, llmWhispererTask);

            // Check if both processes completed successfully
            if (isProcessingAzure || isProcessingLLMWhisperer)
            {
                azureProcessingStatus = "Error: One or both initial processes failed to complete.";
                currentProgress = 0;
                StateHasChanged();
                return;
            }

            currentProgress = 50;
            StateHasChanged();

            // Step 3: Run ProcessRefinedData (50% -> 75%)
            currentProgress = 55;
            azureProcessingStatus = "Running data refinement process...";
            StateHasChanged();

            await ProcessRefinedData();

            // Check if refinement completed successfully
            if (isProcessingRefinedData)
            {
                azureProcessingStatus = "Error: Data refinement process failed to complete.";
                currentProgress = 0;
                StateHasChanged();
                return;
            }

            currentProgress = 75;
            StateHasChanged();

            // Step 4: Run CreateWordDocument (75% -> 100%)
            currentProgress = 80;
            azureProcessingStatus = "Creating Word document...";
            StateHasChanged();

            await CreateWordDocument();

            // Check if word document creation completed successfully
            if (isCreatingWordDoc)
            {
                azureProcessingStatus = "Error: Word document creation failed to complete.";
                currentProgress = 0;
                StateHasChanged();
                return;
            }

            currentProgress = 100;
            await _log.LogAsync(LogLevel.Information, "Full proposal process completed successfully", "ProposalDetails");
            azureProcessingStatus = "Full proposal process completed successfully! All files have been generated.";
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error in full proposal process: {ex.Message}", "ProposalDetails", ex);
            azureProcessingStatus = $"Error in full proposal process: {ex.Message}";
            currentProgress = 0;
            StateHasChanged();
        }
        finally
        {
            isRunningFullProcess = false;
            // Keep the progress and status visible for a moment after completion
            if (currentProgress == 100)
            {
                await Task.Delay(3000); // Show completion for 3 seconds
                azureProcessingStatus = string.Empty;
                currentProgress = 0;
            }
            StateHasChanged();
        }
    }
    private void ResetProgress()
    {
        currentProgress = 0;
        azureProcessingStatus = string.Empty;
        StateHasChanged();
    }
    private void OnUploadFailure(FailureEventArgs args)
    {
        _log.LogAsync(LogLevel.Error, $"Upload failure: {args.Response}", "DropzoneContainer");
        Console.WriteLine("File upload failed: " + args.File.Name);
    }
    private string FormatFileSize(double? fileSizeBytes)
    {
        if (!fileSizeBytes.HasValue) return "-";
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = fileSizeBytes.Value;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }


    // ------------------------------------------ Packager Properties and Methods
    // ---------------------------------------------------------------------------
    private bool isCreatingPackage = false;
    private Attachment? PackageAttachment => Attachments?.Where(a => a.Description?.Contains("Generated Proposal Package") == true).OrderByDescending(a => a.DateCreated).FirstOrDefault();
    private async Task CreateProposalPackage()
    {
        if (!WordDocAttachments.Any())
        {
            await _log.LogAsync(LogLevel.Error, "No Word document attachments found", "ProposalDetails");
            return;
        }

        isCreatingPackage = true;
        StateHasChanged();

        try
        {
            await _log.LogAsync(LogLevel.Information, "Starting proposal package creation", "ProposalDetails");

            // Get the latest Word document attachment
            var latestWordDoc = WordDocAttachments.OrderByDescending(a => a.DateCreated).FirstOrDefault();

            if (latestWordDoc == null)
            {
                await _log.LogAsync(LogLevel.Error, "No Word document found", "ProposalDetails");
                return;
            }

            // Save proposal to ensure all checkbox values are persisted
            await SaveProposal(true);

            // Create the package
            var packageAttachment = await PackagerService.CreateProposalPackageAsync(
                Proposal,
                latestWordDoc,
                AcordAttachment,
                SupplementalAttachment,
                Proposal.AttachmentGroupId.Value,
                ClientId,
                RenewalId);

            // Refresh attachments to show the new package
            await RefreshAttachments();

            await _log.LogAsync(LogLevel.Information, $"Successfully created proposal package: {packageAttachment.OriginalFileName}", "ProposalDetails");
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error creating proposal package: {ex.Message}", "ProposalDetails", ex);
        }
        finally
        {
            isCreatingPackage = false;
            StateHasChanged();
        }
    }
    private DateTime? SendDateProxy
    {
        get => Proposal.SendDate;
        set
        {
            if (Proposal.SendDate != value)
            {
                Proposal.SendDate = value;
                _ = SaveProposal(true);
            }
        }
    }
    private async Task CreateSL2()
    {
        try
        {
            if (Proposal == null || Proposal.Renewal?.Client == null)
            {
                slerror = true;
                await _log.LogAsync(LogLevel.Error, "Cannot create SL-2: Proposal or Client information is missing", "ProposalDetails");
                return;
            }

            // Get the coverage code value
            var coverageCode = CoverageCodes.FirstOrDefault(c => c.Value == Proposal.IncludeSL2Code)?.Value;
            if (string.IsNullOrEmpty(coverageCode))
            {
                slerror = true;
                await _log.LogAsync(LogLevel.Error, "Cannot create SL-2: Coverage code is missing", "ProposalDetails");
                return;
            }

            // Create and fill the SL-2 form
            var attachment = await FormService.CreateAndFillSL2(
                Proposal,
                Proposal.Renewal.Client.Name,
                Proposal.Renewal.Client.ClientId,
                Proposal.IncludeSL2BusinessDesc,
                coverageCode
            );
           
            // Save the attachment to the database
            //await AttachmentService.SaveAttachmentAsync(attachment);

            // Refresh attachments to show the new SL-2
            await RefreshAttachments();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error creating SL-2: {ex.Message}", "ProposalDetails", ex);
        }
    }
    private string[] pageNumbers = new string[5];
    private string GetPageValue(int index)
    {
        if (string.IsNullOrEmpty(Proposal.ExtractPages))
            return string.Empty;

        var pages = Proposal.ExtractPages.Split(',');
        return index < pages.Length ? pages[index] : string.Empty;
    }
    private string GetPageInputClass(int index)
    {
        return !string.IsNullOrEmpty(GetPageValue(index)) ? "filled" : "";
    }
    private async Task OnPageInput(int index, string value)
    {
        if (string.IsNullOrEmpty(value))
        {
            pageNumbers[index] = string.Empty;
        }
        else if (int.TryParse(value, out int pageNum))
        {
            pageNumbers[index] = pageNum.ToString();
        }

        // Rebuild the ExtractPages string
        var validPages = pageNumbers.Where(p => !string.IsNullOrEmpty(p)).ToList();
        Proposal.ExtractPages = string.Join(",", validPages);
        
        await SaveProposal(true);
    }
    private async Task OnPageKeyDown(int index, KeyboardEventArgs e)
    {
        if (e.Key == "Tab" && index < 4 && !string.IsNullOrEmpty(pageNumbers[index]))
        {
            // Focus next input
            await Task.Delay(50); // Small delay to ensure the DOM has updated
            var nextInput = $"page-input-{index + 1}";
            await JS.InvokeVoidAsync("eval", $"document.querySelector('.page-input:nth-child({index + 2})').focus()");
        }
    }
    private string GetProposlerButtonClass()
    {
        return $"proposler-button {(isRunningFullProcess ? "running" : "")}";
    }
    private bool HasAnyPagesEntered()
    {
        return !string.IsNullOrEmpty(Proposal.ExtractPages);
    }
    private async Task ExtractPages()
    {
        if (QuoteAttachment == null)
        {
            await _log.LogAsync(LogLevel.Error, "Quote attachment is null", "ProposalDetails");
            azureProcessingStatus = "Error: No quote attachment found.";
            return;
        }

        if (string.IsNullOrEmpty(Proposal.ExtractPages))
        {
            await _log.LogAsync(LogLevel.Error, "No pages specified for extraction", "ProposalDetails");
            azureProcessingStatus = "Error: Please specify at least one page number to extract.";
            return;
        }

        isProcessingAzure = true;
        if (!isRunningFullProcess) // Only reset if not part of full process
        {
            currentProgress = 0;
        }
        azureProcessingStatus = "Extracting pages from quote...";
        StateHasChanged();

        try
        {
            await _log.LogAsync(LogLevel.Information, $"Starting page extraction from quote: {QuoteAttachment.OriginalFileName}", "ProposalDetails");

            // Get the full physical path to the quote attachment
            string quotePath = Path.Combine(
                Env.WebRootPath,
                QuoteAttachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()),
                QuoteAttachment.HashedFileName
            );

            // Extract the pages
            bool success = await ProposalService.ExtractPages(quotePath, Proposal.ExtractPages);

            if (!success)
            {
                throw new Exception("Failed to extract pages from quote");
            }

            azureProcessingStatus = "Pages extracted successfully!";
            await _log.LogAsync(LogLevel.Information, "Successfully extracted pages from quote", "ProposalDetails");
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error extracting pages from quote: {ex.Message}", "ProposalDetails", ex);
            azureProcessingStatus = $"Error: {ex.Message}";
        }
        finally
        {
            isProcessingAzure = false;
            if (!isRunningFullProcess)
            {
                currentProgress = 100;
                StateHasChanged();
                await Task.Delay(2000); // Show completion for 2 seconds
                azureProcessingStatus = string.Empty;
                currentProgress = 0;
            }
            StateHasChanged();
        }
    }
    private async Task OpenWithWindows(Attachment attachment, bool openFolderOnly)
    {
        Console.WriteLine("Opening with windows...");
        try
        {
            await _log.LogAsync(LogLevel.Information, $"Opening folder: {attachment?.LocalPath}", "AttachmentIcons");

            if (!string.IsNullOrEmpty(attachment?.LocalPath))
            {
                if (openFolderOnly)
                {
                    List<string> mylistfiles = new List<string> { StringHelper.BuildWindowsPath(attachment, false) };
                    await EmberService.RunEmberFunction("Windows_OpenFolder", mylistfiles);
                }else{
                    List<string> mylistfiles = new List<string> { StringHelper.BuildWindowsPath(attachment, false) };
                    await EmberService.RunEmberFunction("Windows_OpenFile", mylistfiles);
                }
            }
        }
        catch(Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, ex.ToString(), "AttachmentIcons");
        }
    }
    private BillType? BillTypeProxy
    {
        get => Proposal.BillType;
        set
        {
            if (Proposal.BillType != value)
            {
                Proposal.BillType = value;
                _ = SaveProposal(true);
            }
        }
    }
    private async Task OpenD1Form()
    {
        // Manually create the Attachment object for the D1 form
        var d1Attachment = new Attachment
        {
            OriginalFileName = "d1-form-rev-01-01-2020.pdf",
            FileFormat = ".pdf",
            LocalPath = "accoutrements/",
            HashedFileName = "d1-form-rev-01-01-2020.pdf"
        };
        await OpenWithWindows(d1Attachment, true);
    }
    private async Task OpenD2Form()
    {
        // Manually create the Attachment object for the D2 form
        var d2Attachment = new Attachment
        {
            OriginalFileName = "d2-form-rev-01-01-2020.pdf",
            FileFormat = ".pdf",
            LocalPath = "accoutrements/",
            HashedFileName = "d2-form-rev-01-01-2020.pdf"
        };
        await OpenWithWindows(d2Attachment, true);
    }
} 