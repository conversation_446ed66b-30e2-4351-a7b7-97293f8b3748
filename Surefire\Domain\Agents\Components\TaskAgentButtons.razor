@using Surefire.Domain.Agents.Interfaces
@using Surefire.Domain.Agents.Models
@inject ITaskAgentOrchestrator AgentOrchestrator
@inject IJSRuntime JSRuntime
@inject ILogger<TaskAgentButtons> Logger
@inject IToastService ToastService

<div class="task-agent-buttons">
    @if (_availableButtons.Any())
    {
        <div class="agent-button-group">
            <h6 class="button-group-title">Quick Actions</h6>
            
            @foreach (var buttonConfig in _availableButtons)
            {
                <button class="btn @buttonConfig.CssClasses @(_executingAgents.Contains(buttonConfig.AgentId) ? "btn-loading" : "")" 
                        disabled="@(!buttonConfig.IsReady || _executingAgents.Contains(buttonConfig.AgentId))"
                        title="@buttonConfig.Description"
                        @onclick="() => ExecuteAgent(buttonConfig)">
                    
                    @if (_executingAgents.Contains(buttonConfig.AgentId))
                    {
                        <i class="fas fa-spinner fa-spin"></i>
                    }
                    else if (!string.IsNullOrEmpty(buttonConfig.Icon))
                    {
                        <i class="@buttonConfig.Icon"></i>
                    }
                    
                    @buttonConfig.ButtonText
                    
                    @if (!buttonConfig.IsReady)
                    {
                        <small class="text-muted d-block">
                            Missing: @string.Join(", ", buttonConfig.MissingParameters)
                        </small>
                    }
                </button>
            }
        </div>
    }
    else if (_loading)
    {
        <div class="text-center">
            <i class="fas fa-spinner fa-spin"></i>
            <span class="ms-2">Loading actions...</span>
        </div>
    }
</div>

<!-- Confirmation Modal -->
@if (_showConfirmation && _currentButtonConfig != null)
{
    <div class="modal fade show d-block" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Action</h5>
                    <button type="button" class="btn-close" @onclick="CancelConfirmation"></button>
                </div>
                <div class="modal-body">
                    <p>@_confirmationMessage</p>
                    @if (_previewMessage != null)
                    {
                        <div class="alert alert-info">
                            <strong>Preview:</strong> @_previewMessage
                        </div>
                    }
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="CancelConfirmation">Cancel</button>
                    <button type="button" class="btn btn-primary" @onclick="ConfirmExecution">
                        <i class="@_currentButtonConfig.Icon"></i>
                        Execute
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}



@code {
    [Parameter] public Dictionary<string, object> PageContext { get; set; } = new();
    [Parameter] public string Category { get; set; } = "";
    [Parameter] public string UserId { get; set; } = "";
    [Parameter] public EventCallback<TaskAgentResult> OnAgentExecuted { get; set; }

    private List<ActionButtonConfig> _availableButtons = new();
    private HashSet<string> _executingAgents = new();
    private bool _loading = true;
    
    // Confirmation modal state
    private bool _showConfirmation = false;
    private ActionButtonConfig? _currentButtonConfig = null;
    private string? _confirmationMessage = null;
    private string? _previewMessage = null;

    protected override async Task OnInitializedAsync()
    {
        await LoadAvailableButtons();
    }

    protected override async Task OnParametersSetAsync()
    {
        // Reload buttons when parameters change (e.g., different page context)
        await LoadAvailableButtons();
    }

    private async Task LoadAvailableButtons()
    {
        try
        {
            _loading = true;
            _availableButtons.Clear();
            StateHasChanged();

            // Get available agents for action buttons
            var agents = AgentOrchestrator.GetAvailableAgents(AgentExecutionContext.ActionButton, Category);
            
            // Generate button configurations for each agent
            foreach (var agent in agents)
            {
                var buttonConfig = await AgentOrchestrator.GenerateActionButtonConfigAsync(agent.AgentId, PageContext);
                if (buttonConfig != null)
                {
                    // Set default styling based on outcome type
                    if (string.IsNullOrEmpty(buttonConfig.CssClasses))
                    {
                        buttonConfig.CssClasses = agent.OutcomeType switch
                        {
                            AgentOutcomeType.Navigation => "btn-primary btn-sm",
                            AgentOutcomeType.Download => "btn-success btn-sm",
                            AgentOutcomeType.Message => "btn-info btn-sm",
                            _ => "btn-secondary btn-sm"
                        };
                    }

                    // Set default icon if not specified
                    if (string.IsNullOrEmpty(buttonConfig.Icon))
                    {
                        buttonConfig.Icon = agent.Category switch
                        {
                            "Loss Runs" => "fas fa-file-alt",
                            "Payments" => "fas fa-credit-card",
                            "Certificates" => "fas fa-certificate",
                            "Communications" => "fas fa-envelope",
                            _ => "fas fa-cog"
                        };
                    }

                    _availableButtons.Add(buttonConfig);
                }
            }

            Logger.LogInformation("Loaded {Count} task agent buttons for context {Context}", 
                _availableButtons.Count, PageContext);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading task agent buttons");
        }
        finally
        {
            _loading = false;
            StateHasChanged();
        }
    }

    private async Task ExecuteAgent(ActionButtonConfig buttonConfig)
    {
        try
        {
            // Check if confirmation is required
            if (buttonConfig.ShowConfirmation)
            {
                await ShowConfirmation(buttonConfig);
                return;
            }

            await ExecuteAgentInternal(buttonConfig);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error executing agent {AgentId}", buttonConfig.AgentId);
            await ShowResult($"Error executing {buttonConfig.ButtonText}: {ex.Message}", false);
        }
    }

    private async Task ShowConfirmation(ActionButtonConfig buttonConfig)
    {
        _currentButtonConfig = buttonConfig;
        _confirmationMessage = buttonConfig.ConfirmationMessage ?? $"Are you sure you want to {buttonConfig.ButtonText.ToLower()}?";
        
        // Get execution preview if available
        try
        {
            var agent = AgentOrchestrator.GetAvailableAgents(AgentExecutionContext.ActionButton)
                .FirstOrDefault(a => a.AgentId == buttonConfig.AgentId);
            
            if (agent != null)
            {
                var agentInstance = await GetAgentInstance(agent.AgentId);
                if (agentInstance != null)
                {
                    _previewMessage = await agentInstance.GetExecutionPreviewAsync(buttonConfig.PreFilledParameters);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Could not get execution preview for agent {AgentId}", buttonConfig.AgentId);
        }

        _showConfirmation = true;
        StateHasChanged();
    }

    private async Task ConfirmExecution()
    {
        if (_currentButtonConfig != null)
        {
            _showConfirmation = false;
            await ExecuteAgentInternal(_currentButtonConfig);
            _currentButtonConfig = null;
            _previewMessage = null;
        }
    }

    private void CancelConfirmation()
    {
        _showConfirmation = false;
        _currentButtonConfig = null;
        _previewMessage = null;
        StateHasChanged();
    }

    private async Task ExecuteAgentInternal(ActionButtonConfig buttonConfig)
    {
        var agentId = buttonConfig.AgentId;
        
        try
        {
            // Mark agent as executing
            _executingAgents.Add(agentId);
            StateHasChanged();

            // Execute the agent
            var result = await AgentOrchestrator.ExecuteFromButtonAsync(
                agentId,
                buttonConfig.PreFilledParameters,
                UserId,
                PageContext,
                skipConfirmation: true // We handled confirmation above
            );

            // Handle the result
            await HandleAgentResult(result);

            // Notify parent component
            if (OnAgentExecuted.HasDelegate)
            {
                await OnAgentExecuted.InvokeAsync(result);
            }
        }
        finally
        {
            // Remove from executing set
            _executingAgents.Remove(agentId);
            StateHasChanged();
        }
    }

    private async Task HandleAgentResult(TaskAgentResult result)
    {
        if (result.Success)
        {
            await ShowResult(result.Message, true);

            // Handle different outcome types
            switch (result.OutcomeType)
            {
                case AgentOutcomeType.Navigation when result.NavigationInfo != null:
                    await HandleNavigation(result.NavigationInfo);
                    break;

                case AgentOutcomeType.Download when result.DownloadUrls?.Any() == true:
                    await HandleDownloads(result.DownloadUrls);
                    break;

                case AgentOutcomeType.Data when result.Data != null:
                    await HandleDataResult(result.Data);
                    break;
            }
        }
        else
        {
            await ShowResult(result.ErrorMessage ?? "Agent execution failed", false);
        }
    }

    private async Task HandleNavigation(AgentNavigationInfo navigationInfo)
    {
        try
        {
            if (navigationInfo.OpenInNewTab)
            {
                await JSRuntime.InvokeVoidAsync("window.open", navigationInfo.NavigationUrl, "_blank");
            }
            else
            {
                var url = navigationInfo.NavigationUrl;
                
                // Add navigation parameters as query string if provided
                if (navigationInfo.NavigationParameters?.Any() == true)
                {
                    var queryString = string.Join("&", 
                        navigationInfo.NavigationParameters.Select(kvp => $"{kvp.Key}={Uri.EscapeDataString(kvp.Value?.ToString() ?? "")}"));
                    url += (url.Contains("?") ? "&" : "?") + queryString;
                }

                await JSRuntime.InvokeVoidAsync("window.location.assign", url);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error handling navigation to {Url}", navigationInfo.NavigationUrl);
        }
    }

    private async Task HandleDownloads(List<string> downloadUrls)
    {
        try
        {
            foreach (var url in downloadUrls)
            {
                await JSRuntime.InvokeVoidAsync("window.open", url, "_blank");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error handling downloads");
        }
    }

    private async Task HandleDataResult(object data)
    {
        // This could be customized based on the type of data
        // For now, just log it
        Logger.LogInformation("Agent returned data result: {Data}", data);
        await Task.CompletedTask;
    }

    private async Task ShowResult(string message, bool success)
    {
        if (success)
        {
            ToastService.ShowSuccess(message);
        }
        else
        {
            ToastService.ShowError(message);
        }

        await Task.CompletedTask;
    }

    private async Task<ITaskAgent?> GetAgentInstance(string agentId)
    {
        // This would typically be injected or resolved through DI
        // For now, return null - this is just for the preview functionality
        await Task.CompletedTask;
        return null;
    }
}

<style>
    .task-agent-buttons {
        margin: 1rem 0;
    }

    .agent-button-group {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        background-color: #f8f9fa;
    }

    .button-group-title {
        margin-bottom: 0.75rem;
        color: #6c757d;
        font-weight: 600;
    }

    .agent-button-group .btn {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        position: relative;
    }

    .btn-loading {
        pointer-events: none;
    }

    .modal-backdrop {
        background-color: rgba(0, 0, 0, 0.5);
    }

    .toast-container {
        z-index: 1060;
    }
</style> 