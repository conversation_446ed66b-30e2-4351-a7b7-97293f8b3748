﻿@using Surefire.Domain.Plugins
@using Surefire.Domain.Shared.Services
@using Surefire.Domain.Shared.Helpers
@using Surefire.Domain.Shared.Models
@using Surefire.Domain.Chat
@using Surefire.Domain.Chat.Services
@using Surefire.Domain.OpenAI.Simple
@using Newtonsoft.Json
@using RingCentral
@using Microsoft.EntityFrameworkCore
@inject ChatService _chatService
@inject TranscriptionService _transcriptionService
@inject IOpenAISimpleService _openAiService
@inject IJSRuntime JSRuntime
@inject IDbContextFactory<Surefire.Data.ApplicationDbContext> _dbFactory

@if (callLogs == null)
{
    <FluentProgressRing Width="30px" Color="#1b8ce3" />
}
else if (callLogs.Count == 0)
{
    <FluentIcon Value="@(new Icons.Regular.Size24.CallDismiss())" CustomColor="#b7b7b7" Color="Color.Custom" />
    @if (PhoneNumbers != null && PhoneNumbers.Any())
    {
        <span class="phone-none">No phone calls found for @string.Join(", ", PhoneNumbers).</span>
        
        @if (ClientId > 0 && !showingAllCalls)
        {
            <div class="mt-2">
                <FluentButton Appearance="Appearance.Outline" OnClick="FetchAllCalls">Show All Recent Calls</FluentButton>
                <p class="mt-2 text-sm">This will show all recent calls to help troubleshoot why no calls are showing for this client.</p>
            </div>
        }
    }
    else
    {
        <span class="phone-none">No recent phone conversations.</span>
    }
}
else
{
    @if (ClientId > 0)
    {
        <div class="mb-2 d-flex align-items-center justify-content-between">
            <h5>Found @callLogs.Count calls for Client #@ClientId</h5>
            <FluentButton Appearance="Appearance.Lightweight" 
                          IconStart="@(new Icons.Regular.Size20.ArrowClockwise())" 
                          Title="Refresh calls with latest data"
                          OnClick="RefreshCalls">Refresh</FluentButton>
        </div>
    }
    
    <FluentStack Orientation="Orientation.Vertical">
        @foreach (var log in callLogs)
        {
            <FluentStack>
                <div class="phone-icon-cell">
                    <span class="phone-icon">
                        @if (log.direction == "Inbound")
                        {
                            <FluentIcon Value="@(new Icons.Regular.Size28.CallInbound())" CustomColor="#0f6cbd" Color="Color.Custom" />
                        }
                        else if (log.direction == "Outbound")
                        {
                            <FluentIcon Value="@(new Icons.Regular.Size28.CallOutbound())" CustomColor="#e9776f" Color="Color.Custom" />
                        }else{
                            <FluentIcon Value="@(new Icons.Regular.Size28.CallMissed())" CustomColor="#1a4d16" Color="Color.Custom" />
                        }
                    </span>
                </div>
                <div class="phone-lognum" style="width:170px;">
                    @if (log.direction == "Inbound")
                    {
                        <span class="phone-lognum-in">@StringHelper.FormatPhoneNumber(log.from.phoneNumber)</span>
                    }
                    @if (log.direction == "Outbound")
                    {
                        <span class="phone-lognum-out">@StringHelper.FormatPhoneNumber(log.to.phoneNumber)</span>
                    }

                </div>
                <div class="phonecall-infotext">
                    <span class="phone-longago">@(StringHelper.FormatDateDifference(log.startTime))</span><br />
                    <span class="phonetxt">Call lasted @StringHelper.FormatSeconds(log.duration ?? 0).</span><br />

                </div>
             
                <div class="phone-call-buttons">
                    
                    @if (log.recording != null && log.recording.id != null)
                    {
                        @if (IsLoadingRecording(log.id))
                        {
                            <FluentProgressRing Width="16px" Color="#1b8ce3" />
                        }
                        else
                        {
                            <FluentButton Appearance="Appearance.Lightweight" 
                            IconStart="@(isCurrentlyPlaying.ContainsKey(log.id) && isCurrentlyPlaying[log.id] ? new Icons.Regular.Size20.Pause() : new Icons.Regular.Size20.Play())" 
                            Title="Play recording"
                            OnClick="@(() => PlayRecording(log.id, log.recording.id, log.recording.contentUri))" />

                            <FluentButton Appearance="Appearance.Lightweight" 
                            IconStart="@(new Icons.Regular.Size20.Bot())" 
                            Title="Transcribe and summarize call"
                            OnClick="@(() => TranscribeAndSummarize(log.id, log.recording.id, log.recording.contentUri))" />

                            @if (transcriptions.ContainsKey(log.id) && !string.IsNullOrEmpty(transcriptions[log.id]))
                            {
                                <FluentButton Appearance="Appearance.Lightweight" 
                                IconStart="@(new Icons.Regular.Size20.TextDescription())" 
                                Title="View transcription"
                                OnClick="@(() => ShowTranscription(log.id))" />
                            }
                            else
                            {
                                <FluentButton Appearance="Appearance.Lightweight" 
                                IconStart="@(new Icons.Regular.Size20.TextT())" 
                                Title="Transcribe recording"
                                OnClick="@(() => TranscribeRecording(log.id, log.recording.id, log.recording.contentUri))" />
                            }
                        }
                    }
                </div>
            </FluentStack>
        }
    </FluentStack>
}

@if (showTranscriptionDialog)
{
    <FluentDialog @bind-Visible="@showTranscriptionDialog" Title="Call Transcription" TrapFocus="true" Modal="true">
        <Body>
            <div class="transcription-content">
                @currentTranscription
            </div>
        </Body>
        <Footer>
            <FluentButton Appearance="Appearance.Accent" OnClick="@CloseTranscriptionDialog">Close</FluentButton>
        </Footer>
    </FluentDialog>
}

@if (showAiSummaryDialog)
{
    <FluentDialog @bind-Visible="@showAiSummaryDialog" Title="AI Call Summary" TrapFocus="true" Modal="true">
        <FluentDialogBody>
            <div class="dialog-content">
                <div class="dialog-header">
                    <div>
                        <strong>Phone:</strong> @StringHelper.FormatPhoneNumber(selectedTranscription?.PhoneNumber ?? "")
                    </div>
                    <div>
                        <strong>Date:</strong> @(selectedTranscription?.CallStartTime?.ToString("MM/dd/yyyy hh:mm tt") ?? "Unknown")
                    </div>
                    @if (selectedTranscription?.CallDuration.HasValue == true)
                    {
                        <div>
                            <strong>Duration:</strong> @StringHelper.FormatSeconds(selectedTranscription.CallDuration.Value)
                        </div>
                    }
                </div>
                
                @if (isGeneratingAiSummary)
                {
                    <div class="ai-summary-section">
                        <div class="loading-container">
                            <FluentProgressRing Width="30px" Color="#1b8ce3" />
                            <p>Generating AI summary...</p>
                        </div>
                    </div>
                }
                else if (!string.IsNullOrEmpty(aiSummary))
                {
                    <div class="ai-summary-section">
                        <div class="ai-summary-header">
                            <FluentIcon Value="@(new Icons.Regular.Size24.Bot())" CustomColor="#1b8ce3" Color="Color.Custom" />
                            <h4>AI Call Summary</h4>
                        </div>
                        <div class="ai-summary-content">
                            @((MarkupString)aiSummary)
                        </div>
                    </div>
                }
            </div>
        </FluentDialogBody>
        <FluentDialogFooter>
            @if (isGeneratingAiSummary)
            {
                <FluentButton Appearance="Appearance.Accent" Disabled="true">Close</FluentButton>
            }
            else
            {
                <FluentButton Appearance="Appearance.Accent" OnClick="@(() => CopyToClipboard(aiSummary))" IconStart="@(new Icons.Regular.Size20.Copy())">
                    Copy Summary
                </FluentButton>
                <FluentButton Appearance="Appearance.Accent" OnClick="CloseAiSummaryDialog">Close</FluentButton>
            }
        </FluentDialogFooter>
    </FluentDialog>
}

<audio id="audioPlayer" hidden></audio>

@code {
    [Parameter] public List<string>? PhoneNumbers { get; set; } = new();
    [Parameter] public bool ShowAll { get; set; } = false;
    [Parameter] public int ClientId { get; set; } = 0;

    private List<CallLogRecordFire> callLogs = new();
    private List<string> topCallTranscriptions = new List<string>();
    private CancellationTokenSource? _ctsCallLog;
    private Dictionary<string, bool> isLoadingRecording = new();
    private Dictionary<string, bool> isCurrentlyPlaying = new();
    private Dictionary<string, string> transcriptions = new();
    private bool showTranscriptionDialog = false;
    private string currentTranscription = "";
    private string? currentAudioCallId = null;
    private bool showingAllCalls = false;
    private bool showAiSummaryDialog = false;
    private bool isGeneratingAiSummary = false;
    private string aiSummary = "";
    private CallTranscription selectedTranscription;

    // MAIN FUNCTONS ----------------------------------------//
    protected override async Task OnParametersSetAsync()
    {
        try
        {
            showingAllCalls = false;

            _ctsCallLog?.Cancel();
            _ctsCallLog = new CancellationTokenSource();

            if (ClientId > 0)
            {
                var clientPhoneNumbers = await GetClientPhoneNumbersAsync(ClientId);

                // If we have phone numbers from the client, use those instead of the provided ones
                if (clientPhoneNumbers.Any())
                {
                    PhoneNumbers = clientPhoneNumbers;
                }
            }

            PhoneNumbers = PhoneNumbers?.Select(phoneNumber => StringHelper.FormatPhoneNumber(phoneNumber, "+1##########")).ToList() ?? new List<string>();

            // Uses the new method from ChatService instead of StateService or plugin
            callLogs = await _chatService.GetCallLogsByPhoneNumbersAsync(PhoneNumbers, ShowAll, _ctsCallLog.Token);

            await LoadTranscriptions();
        }
        catch (OperationCanceledException)
        {
            Console.WriteLine("Call log fetching was canceled.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"An error occurred while fetching call logs: {ex.Message}");
        }
    }
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JSRuntime.InvokeVoidAsync("eval", @"
                window.audioPlayerStatus = {
                    ended: function() {
                        DotNet.invokeMethodAsync('Surefire', 'AudioEnded');
                    }
                };
                document.getElementById('audioPlayer').addEventListener('ended', window.audioPlayerStatus.ended);
            ");
        }
    }

    // Transcriberizer --------------------------------------//
    private async Task TranscribeRecording(string callId, string recordingId, string contentUri = null)
    {
        try
        {
            isLoadingRecording[callId] = true;
            StateHasChanged();

            var recording = !string.IsNullOrEmpty(contentUri) 
                ? await _chatService.GetCallRecordingByUriAsync(contentUri)
                : await _chatService.GetCallRecordingAsync(recordingId);
            
            if (recording != null)
            {
                // Get phone number from the call log
                var log = callLogs.FirstOrDefault(l => l.id == callId);
                
                // Store both the from and to phone numbers to improve matching
                string fromPhoneNumber = NormalizePhoneNumber(log?.from?.phoneNumber ?? "");
                string toPhoneNumber = NormalizePhoneNumber(log?.to?.phoneNumber ?? "");
                
                // Use the appropriate phone number based on call direction
                string phoneNumber = log?.direction == "Inbound" ? fromPhoneNumber : toPhoneNumber;
                
                // Convert start time to DateTime if present
                DateTime? startTime = null;
                if (log?.startTime != null)
                {
                    if (DateTime.TryParse(log.startTime.ToString(), out var parsedTime))
                    {
                        startTime = parsedTime;
                    }
                }
                
                // Get the duration directly since it's already int?
                int? duration = log?.duration;
                
                // Transcribe and save, including the client ID and both phone numbers
                string transcriptionText = await _transcriptionService.TranscribeAndSaveAsync(
                    callId, 
                    recordingId, 
                    phoneNumber, 
                    recording.Value.Data,
                    startTime,
                    duration,
                    ClientId > 0 ? ClientId : null,
                    fromPhoneNumber,
                    toPhoneNumber
                );
                
                // Cache the transcription
                transcriptions[callId] = transcriptionText;
                
                // Refresh the top call transcriptions list to include the new transcription
                await LoadTopCallTranscriptions();
            }
            
            isLoadingRecording[callId] = false;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error transcribing recording: {ex.Message}");
            isLoadingRecording[callId] = false;
            StateHasChanged();
        }
    }
    private void ShowTranscription(string callId)
    {
        if (transcriptions.ContainsKey(callId))
        {
            currentTranscription = transcriptions[callId];
            showTranscriptionDialog = true;
            StateHasChanged();
        }
    }
    private async Task TranscribeAndSummarize(string callId, string recordingId, string contentUri = null)
    {
        try
        {
            // Check if transcription already exists
            if (!transcriptions.ContainsKey(callId) || string.IsNullOrEmpty(transcriptions[callId]))
            {
                // Transcribe the recording first
                await TranscribeRecording(callId, recordingId, contentUri);
            }
            
            // Get the transcription from the service
            var transcription = await _transcriptionService.GetTranscriptionAsync(callId);
            if (transcription != null && !string.IsNullOrEmpty(transcription.TranscriptionText))
            {
                // Refresh the top call transcriptions list to include the new transcription
                await LoadTopCallTranscriptions();
                
                selectedTranscription = transcription;
                showAiSummaryDialog = true;
                await GenerateAiSummary();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Unable to transcribe the call recording. Please try again.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in TranscribeAndSummarize: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Error processing call: {ex.Message}");
        }
    }
    private async Task GenerateAiSummary()
    {
        if (selectedTranscription == null || string.IsNullOrEmpty(selectedTranscription.TranscriptionText))
        {
            return;
        }
        
        isGeneratingAiSummary = true;
        StateHasChanged();
        
        try
        {
            // Create prompt for OpenAI
            string prompt = $@"The following is a transcription of a phone call between an insurance agent and a client. 
Please summarize the key points of this conversation in 3-5 bullet points. Format your response as HTML with <ul> and <li> tags.
Focus on identifying:
- The main purpose of the call
- Any specific insurance policies or coverages discussed
- Any action items or next steps mentioned
- Any questions or concerns from the client

Here is the transcription:
{CleanTranscriptionText(selectedTranscription.TranscriptionText)}";
            
            // Call OpenAI for summary
            var summaryText = await _openAiService.GenerateResponseAsync(prompt, "gpt-4o-mini");
            
            if (!string.IsNullOrEmpty(summaryText))
            {
                // Ensure the response has HTML formatting
                if (!summaryText.Contains("<ul>"))
                {
                    summaryText = $"<ul><li>{summaryText.Replace("\n", "</li><li>")}</li></ul>";
                    summaryText = summaryText.Replace("<li></li>", "");
                }
                
                aiSummary = summaryText;
            }
            else
            {
                aiSummary = "<p>Unable to generate summary. Please try again later.</p>";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error generating AI summary: {ex.Message}");
            aiSummary = "<p>Error generating summary. Please try again later.</p>";
        }
        finally
        {
            isGeneratingAiSummary = false;
            StateHasChanged();
        }
    }

    // Fetch Data ------------------------------------------//
    private async Task LoadTranscriptions()
    {
        try
        {
            // If we have a client ID, load all transcriptions for that client
            if (ClientId > 0)
            {
                var clientTranscriptions = await _transcriptionService.GetTranscriptionsForClientAsync(ClientId);
                foreach (var transcription in clientTranscriptions)
                {
                    if (!string.IsNullOrEmpty(transcription.TranscriptionText))
                    {
                        transcriptions[transcription.CallId] = transcription.TranscriptionText;
                    }
                }
            }
            else
            {
                // Otherwise, load transcriptions for visible call logs
                foreach (var log in callLogs)
                {
                    if (log.recording != null && log.recording.id != null)
                    {
                        var transcription = await _transcriptionService.GetTranscriptionAsync(log.id);
                        if (transcription != null && !string.IsNullOrEmpty(transcription.TranscriptionText))
                        {
                            transcriptions[log.id] = transcription.TranscriptionText;
                        }
                    }
                }
            }

            // Load the 5 most recent call transcriptions
            await LoadTopCallTranscriptions();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading transcriptions: {ex.Message}");
        }
    }
    private async Task LoadTopCallTranscriptions()
    {
        try
        {
            topCallTranscriptions.Clear();

            // Get transcriptions for the current call logs only
            var callIds = callLogs.Select(log => log.id).ToList();
            
            if (callIds.Any())
            {
                // Get transcriptions for these specific call IDs
                var recentTranscriptions = new List<CallTranscription>();
                
                foreach (var callId in callIds)
                {
                    var transcription = await _transcriptionService.GetTranscriptionAsync(callId);
                    if (transcription != null && !string.IsNullOrEmpty(transcription.TranscriptionText))
                    {
                        recentTranscriptions.Add(transcription);
                    }
                }

                // Sort by call start time (most recent first) and take the top 5
                var topTranscriptions = recentTranscriptions
                    .Where(t => t.CallStartTime.HasValue)
                    .OrderByDescending(t => t.CallStartTime)
                    .Take(5)
                    .ToList();

                foreach (var transcription in topTranscriptions)
                {
                    var formattedText = FormatTranscriptionForAI(transcription);
                    topCallTranscriptions.Add(formattedText);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading top call transcriptions: {ex.Message}");
        }
    }
    public List<string> GetTopCallTranscriptions()
    {
        // Return the cached list for performance, but this should be called after any transcription updates
        return topCallTranscriptions;
    }
    
    public async Task<List<string>> GetTopCallTranscriptionsAsync()
    {
        try
        {
            // Dynamically fetch the latest transcriptions instead of using cached list
            var latestTranscriptions = new List<string>();
            
            // Get transcriptions for the current call logs only
            var callIds = callLogs.Select(log => log.id).ToList();
            
            if (callIds.Any())
            {
                // Get transcriptions for these specific call IDs
                var recentTranscriptions = new List<CallTranscription>();
                
                foreach (var callId in callIds)
                {
                    var transcription = await _transcriptionService.GetTranscriptionAsync(callId);
                    if (transcription != null && !string.IsNullOrEmpty(transcription.TranscriptionText))
                    {
                        recentTranscriptions.Add(transcription);
                    }
                }

                // Sort by call start time (most recent first) and take the top 5
                var topTranscriptions = recentTranscriptions
                    .Where(t => t.CallStartTime.HasValue)
                    .OrderByDescending(t => t.CallStartTime)
                    .Take(5)
                    .ToList();

                foreach (var transcription in topTranscriptions)
                {
                    var formattedText = FormatTranscriptionForAI(transcription);
                    latestTranscriptions.Add(formattedText);
                }
            }
            
            return latestTranscriptions;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error getting top call transcriptions: {ex.Message}");
            return new List<string>();
        }
    }
    
    /// <summary>
    /// Gets the count of calls that have recordings but no transcriptions
    /// </summary>
    /// <returns>Number of un-transcribed calls</returns>
    public async Task<int> GetUntranscribedCallsCountAsync()
    {
        try
        {
            int untranscribedCount = 0;
            
            // Check each call log that has a recording
            foreach (var log in callLogs)
            {
                // Only count calls that have recordings
                if (log.recording != null && !string.IsNullOrEmpty(log.recording.id))
                {
                    // Check if transcription exists for this call
                    var transcription = await _transcriptionService.GetTranscriptionAsync(log.id);
                    if (transcription == null || string.IsNullOrEmpty(transcription.TranscriptionText))
                    {
                        untranscribedCount++;
                    }
                }
            }
            
            return untranscribedCount;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error getting un-transcribed calls count: {ex.Message}");
            return 0;
        }
    }
    
    /// <summary>
    /// Gets a list of call IDs that have recordings but no transcriptions
    /// </summary>
    /// <returns>List of call IDs that need transcription</returns>
    public async Task<List<string>> GetUntranscribedCallIdsAsync()
    {
        try
        {
            var untranscribedCallIds = new List<string>();
            
            // Check each call log that has a recording
            foreach (var log in callLogs)
            {
                // Only include calls that have recordings
                if (log.recording != null && !string.IsNullOrEmpty(log.recording.id))
                {
                    // Check if transcription exists for this call
                    var transcription = await _transcriptionService.GetTranscriptionAsync(log.id);
                    if (transcription == null || string.IsNullOrEmpty(transcription.TranscriptionText))
                    {
                        untranscribedCallIds.Add(log.id);
                    }
                }
            }
            
            return untranscribedCallIds;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error getting un-transcribed call IDs: {ex.Message}");
            return new List<string>();
        }
    }
    
    /// <summary>
    /// Gets a call log by its ID
    /// </summary>
    /// <param name="callId">The call ID to find</param>
    /// <returns>The call log record, or null if not found</returns>
    public CallLogRecordFire? GetCallLogById(string callId)
    {
        return callLogs.FirstOrDefault(log => log.id == callId);
    }
    
    /// <summary>
    /// Public method to transcribe a recording (for use by parent components)
    /// </summary>
    /// <param name="callId">The call ID</param>
    /// <param name="recordingId">The recording ID</param>
    /// <param name="contentUri">Optional content URI</param>
    /// <returns>Task</returns>
    public async Task TranscribeRecordingAsync(string callId, string recordingId, string contentUri = null)
    {
        await TranscribeRecording(callId, recordingId, contentUri);
    }
    
    public async Task RefreshTopCallTranscriptions()
    {
        await LoadTopCallTranscriptions();
    }
    
    private string CleanTranscriptionText(string transcriptionText)
    {
        if (string.IsNullOrEmpty(transcriptionText))
            return string.Empty;
            
        var cleanedText = transcriptionText;
        
        // Remove the Metro Insurance recording message if it appears at the start
        if (cleanedText.StartsWith("Metro Insurance. Connecting you now on a recorded line."))
        {
            cleanedText = cleanedText.Substring("Metro Insurance. Connecting you now on a recorded line.".Length).TrimStart();
        }
        
        return cleanedText;
    }
    
    private string FormatTranscriptionForAI(CallTranscription transcription)
    {
        var cleanedText = CleanTranscriptionText(transcription.TranscriptionText);
        
        // Truncate long transcriptions to keep the list manageable
        var truncatedText = cleanedText.Length > 200 
            ? cleanedText.Substring(0, 200) + "..." 
            : cleanedText;

        // Format the transcription with date and phone number
        return $"Date: {transcription.CallStartTime?.ToString("M/d/yyyy") ?? "Unknown"}   Originating Phone Number: {StringHelper.FormatPhoneNumber(transcription.PhoneNumber ?? "")} Call Transcription: {truncatedText}";
    }
    
    //Fetch Calls ------------------------------------------//
    private async Task RefreshCalls()
    {
        try
        {
            // Reset showing all calls flag when refreshing
            showingAllCalls = false;
            
            // Cancel any ongoing requests
            _ctsCallLog?.Cancel();
            _ctsCallLog = new CancellationTokenSource();
            
            // Invalidate the call logs cache
            _chatService.InvalidateCallLogsCache();
            
            // Use current phone numbers to filter
            callLogs = await _chatService.GetCallLogsByPhoneNumbersAsync(PhoneNumbers, ShowAll, _ctsCallLog.Token);
            
            Console.WriteLine($"Refreshingly retrieved {callLogs.Count} total call logs after refreshing");
            
            // Load transcriptions for these calls
            await LoadTranscriptions();
            
            // Refresh the top call transcriptions list
            await LoadTopCallTranscriptions();
            
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error refreshing calls: {ex.Message}");
        }
    }
    private async Task FetchAllCalls()
    {
        try 
        {
            showingAllCalls = true;
            
            // Cancel any ongoing requests
            _ctsCallLog?.Cancel();
            _ctsCallLog = new CancellationTokenSource();
            
            // Get all recent call logs regardless of phone number, but don't invalidate cache
            callLogs = await _chatService.GetCallLogsByPhoneNumbersAsync(null, true, _ctsCallLog.Token);
            
            // Load transcriptions for these calls
            await LoadTranscriptions();
            
            // Refresh the top call transcriptions list
            await LoadTopCallTranscriptions();
            
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error fetching all calls: {ex.Message}");
        }
    }
    
    // Player and UI ---------------------------------------//
    [JSInvokable]
    public static Task AudioEnded()
    {
        // This will be called when audio playback ends
        return Task.CompletedTask;
    }
    private async Task<List<string>> GetClientPhoneNumbersAsync(int clientId)
    {
        var phoneNumbers = new List<string>();

        try
        {
            using var context = await _dbFactory.CreateDbContextAsync();

            //Get Client from ClientId
            var client = await context.Clients
                .Where(c => c.ClientId == ClientId)
                .FirstOrDefaultAsync();
            if (client != null)
            {
                phoneNumbers.Add(client.PhoneNumber);
            }

            // Get phone numbers from contacts associated with this client
            var contactPhones = await context.PhoneNumbers
                .Where(p => p.Contact.ClientId == clientId)
                .Select(p => p.Number)
                .ToListAsync();
            phoneNumbers.AddRange(contactPhones);

            // Log the found phone numbers
            phoneNumbers.ForEach(p => Console.WriteLine($"  - {p}"));

            return phoneNumbers;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error getting client phone numbers: {ex.Message}");
            return phoneNumbers;
        }
    }
    private bool IsLoadingRecording(string callId)
    {
        return isLoadingRecording.ContainsKey(callId) && isLoadingRecording[callId];
    }
    private async Task PlayRecording(string callId, string recordingId, string contentUri = null)
    {
        try
        {
            // If already playing, toggle pause/play
            if (isCurrentlyPlaying.ContainsKey(callId) && isCurrentlyPlaying[callId])
            {
                await JSRuntime.InvokeVoidAsync("eval", "document.getElementById('audioPlayer').pause();");
                isCurrentlyPlaying[callId] = false;
                StateHasChanged();
                return;
            }

            // Reset any other playing states
            foreach (var key in isCurrentlyPlaying.Keys.ToList())
            {
                isCurrentlyPlaying[key] = false;
            }

            // If we already downloaded this recording before
            if (currentAudioCallId == callId)
            {
                await JSRuntime.InvokeVoidAsync("eval", "document.getElementById('audioPlayer').play();");
                isCurrentlyPlaying[callId] = true;
                StateHasChanged();
                return;
            }

            // Mark as loading
            isLoadingRecording[callId] = true;
            StateHasChanged();

            // Get recording from API - use contentUri if available
            var recording = !string.IsNullOrEmpty(contentUri) 
                ? await _chatService.GetCallRecordingByUriAsync(contentUri)
                : await _chatService.GetCallRecordingAsync(recordingId);
            
            if (recording != null)
            {
                // Convert byte array to base64 for audio element
                var base64 = Convert.ToBase64String(recording.Value.Data);
                var contentType = recording.Value.ContentType;
                
                // Set the audio source and play
                await JSRuntime.InvokeVoidAsync("eval", $@"
                    var audio = document.getElementById('audioPlayer');
                    audio.src = 'data:{contentType};base64,{base64}';
                    audio.play();
                ");
                
                currentAudioCallId = callId;
                isCurrentlyPlaying[callId] = true;
            }
            
            isLoadingRecording[callId] = false;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error playing recording: {ex.Message}");
            isLoadingRecording[callId] = false;
            StateHasChanged();
        }
    }
    private void CloseTranscriptionDialog()
    {
        showTranscriptionDialog = false;
        StateHasChanged();
    }
    private void CloseAiSummaryDialog()
    {
        showAiSummaryDialog = false;
        aiSummary = "";
        selectedTranscription = null;
        StateHasChanged();
    }
    private async Task CopyToClipboard(string text)
    {
        if (!string.IsNullOrEmpty(text))
        {
            await JSRuntime.InvokeVoidAsync("navigator.clipboard.writeText", text);
            await JSRuntime.InvokeVoidAsync("alert", "Summary copied to clipboard");
        }
    }
    
    // Misc -------------------------------------------------//
    private string NormalizePhoneNumber(string phoneNumber)
    {
        if (string.IsNullOrEmpty(phoneNumber))
            return string.Empty;
            
        // Remove all non-digit characters
        var digitsOnly = new string(phoneNumber.Where(char.IsDigit).ToArray());
        
        // Ensure 10-digit US numbers have country code
        if (digitsOnly.Length == 10)
            return "1" + digitsOnly;
            
        // If it already has country code (11 digits starting with 1)
        if (digitsOnly.Length == 11 && digitsOnly.StartsWith("1"))
            return digitsOnly;
            
        // Otherwise return as is
        return digitsOnly;
    }
    public void Dispose()
    {
        _ctsCallLog?.Cancel();
        _ctsCallLog?.Dispose();
    }
}