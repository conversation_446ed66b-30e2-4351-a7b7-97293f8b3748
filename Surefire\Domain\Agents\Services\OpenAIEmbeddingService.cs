using System;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Surefire.Domain.Agents.Interfaces;

namespace Surefire.Domain.Agents.Services
{
    public class OpenAIEmbeddingService : IEmbeddingService
    {
        private readonly HttpClient _httpClient;
        private readonly string _model;
        private readonly ILogger<OpenAIEmbeddingService> _logger;

        public OpenAIEmbeddingService(IHttpClientFactory httpClientFactory, IConfiguration configuration, ILogger<OpenAIEmbeddingService> logger)
        {
            var apiKey = configuration["OpenAi:ApiKey"] ?? throw new InvalidOperationException("OpenAI API Key missing");
            _httpClient = httpClientFactory.CreateClient("OpenAI");
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
            _model = "text-embedding-3-small";
            _logger = logger;
        }

        public async Task<float[]> GenerateEmbeddingAsync(string text, CancellationToken cancellationToken = default)
        {
            var req = new EmbeddingRequest { Model = _model, Input = text };
            _logger.LogInformation("Requesting embedding for text of length {Length}", text?.Length ?? 0);
            var json = JsonSerializer.Serialize(req);
            using var contentPayload = new StringContent(json, Encoding.UTF8, "application/json");
            var resp = await _httpClient.PostAsync("embeddings", contentPayload, cancellationToken);
            resp.EnsureSuccessStatusCode();
            var content = await resp.Content.ReadFromJsonAsync<EmbeddingResponse>(cancellationToken: cancellationToken);
            var embedding = content?.Data?.FirstOrDefault()?.Embedding;
            return embedding ?? Array.Empty<float>();
        }

        private class EmbeddingRequest
        {
            [JsonPropertyName("model")] public string Model { get; set; } = string.Empty;
            [JsonPropertyName("input")] public string Input { get; set; } = string.Empty;
        }
        private class EmbeddingResponse
        {
            [JsonPropertyName("data")] public Datum[]? Data { get; set; }
        }
        private class Datum
        {
            [JsonPropertyName("embedding")] public float[] Embedding { get; set; } = Array.Empty<float>();
        }
    }
}
