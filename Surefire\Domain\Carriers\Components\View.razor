@inherits AppComponentBase
@implements IDisposable
@using Microsoft.FluentUI.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.EntityFrameworkCore
@using Syncfusion.Blazor.Buttons
@using Surefire.Domain.Contacts.Models
@using Surefire.Domain.Policies.Models
@using Surefire.Domain.Policies.Services
@using Surefire.Domain.Carriers.Models
@using Surefire.Domain.Carriers.Services
@using Surefire.Domain.Shared.Components
@using Surefire.Domain.Shared.Models
@using Surefire.Data
@inject CarrierService CarrierService
@inject PolicyService PolicyService
@inject SurefireDialogService DialogService
@inject NavigationManager NavigationManager
@inject SearchService SearchService
@inject SharedService SharedService
@inject IDbContextFactory<ApplicationDbContext> DbContextFactory

@if (carrier is null)
{
    <p><em>Loading...</em></p>
}
else
{

    <div class="sfc-top">
        <div class="sfc-header-col-name">
            @if (!string.IsNullOrEmpty(carrier.CarrierName))
            {
            <span class="biz-name">@carrier.CarrierName</span><br />
            <span class="data-icon"><FluentIcon Value="@(new Icons.Filled.Size20.Location())" Slot="start" CustomColor="#454545" Color="Color.Custom" /></span>
            }

            @if (!string.IsNullOrEmpty(carrier.StreetAddress))
            {
                <a class="biz-data" href="https://www.google.com/maps/search/?api=1&query=@Uri.EscapeDataString($"{carrier.StreetAddress} {carrier.City}, {carrier.State} {carrier.Zip}")" target="_blank">
                    @carrier.StreetAddress @carrier.City @carrier.State @carrier.Zip
                </a>
            }

            @if (!string.IsNullOrEmpty(carrier.ServicingEmail))
            {
                <span class="data-icon"><FluentIcon Value="@(new Icons.Filled.Size20.Mail())" Slot="start" CustomColor="#454545" Color="Color.Custom" /></span>
                <Trigger Value="@carrier.ServicingEmail" Type="Trigger.ClickType.Email" Class="biz-data" />
            }

            @if (!string.IsNullOrEmpty(carrier.Website))
            {
                <span class="data-icon"><FluentIcon Value="@(new Icons.Filled.Size20.Globe())" Slot="start" CustomColor="#454545" Color="Color.Custom" /></span>
                <Trigger Value="@carrier.Website" Type="Trigger.ClickType.Url" Class="biz-data" />
            }
        </div>
        <div class="sfc-header-col-phone">
            <span class="client-phone">
                <span class="phone-icon">
                    <a href="tel:@StringHelper.FormatTelDialNumber(carrier.Phone)"><FluentIcon Value="@(new Icons.Regular.Size48.Phone())" Slot="start" CustomColor="#036ac4" Color="Color.Custom" /></a>
                </span>
                <Trigger Value="@StringHelper.FormatPhoneNumber(carrier.Phone)" Type="Trigger.ClickType.Phone" Class="client-phonenumber" />
            </span>
        </div>
    </div>

    <div class="page-content">
       

            <FluentStack>
                <div style="min-width:350px;">
                    @if (carrier.LogoFilename != null)
                    {
                        <img src="/uploads/logos/carrier/@carrier.LogoFilename" class="img-fluid" style="max-width:350px;" />
                    }
                    <div class="txt-section">Contacts</div>
                    <div class="carrier-contacts">
                        <ListContactsForParent Contacts="@carrier.Contacts" ParentType="Carrier" ParentId="@SelectedCarrierId" />
                    </div>
                </div>
                <div style="min-width:550px;">
                    <div class="txt-section">TOOLS</div>
                    <div class="div-section">
                        <div class="mf-flextwocolumn">
                            <div class="mf-flextwocolumn-col">
                                <div class="txt-section txt-sm">Service Centers</div>
                                @if (!string.IsNullOrEmpty(carrier.ServicingWebsite))
                                {
                                    <a href="@StringHelper.FormatUrl(carrier.ServicingWebsite)" target="_blank">
                                        <FluentIcon Value="@(new Icons.Regular.Size48.Globe())" CustomColor="#454545" Color="Color.Custom" />
                                    </a>
                                }
                                @if (!string.IsNullOrEmpty(carrier.ServicingEmail))
                                {
                                    <a href="mailto:@(carrier.ServicingEmail)">
                                        <FluentIcon Value="@(new Icons.Regular.Size48.Mail())" CustomColor="#454545" Color="Color.Custom" /><br />
                                    </a>
                                }
                            </div>
                            <div class="mf-flextwocolumn-col">
                                <div class="txt-section txt-sm">Quoting Links</div>

                                @if (!string.IsNullOrEmpty(carrier.QuotingWebsite))
                                {
                                    <a href="@StringHelper.FormatUrl(carrier.QuotingWebsite)" target="_blank">
                                        <FluentIcon Value="@(new Icons.Filled.Size48.Globe())" CustomColor="#454545" Color="Color.Custom" />
                                    </a>
                                }
                                @if (!string.IsNullOrEmpty(carrier.NewSubmissionEmail))
                                {
                                    <a href="@("mailto:" + carrier.NewSubmissionEmail)">
                                        <FluentIcon Value="@(new Icons.Filled.Size48.Mail())" CustomColor="#454545" Color="Color.Custom" /><br />
                                    </a>
                                }
                            </div>
                        </div>
                    </div>

                    <div class="txt-section spc">CREDENTIALS</div>
                    <div class="div-section">
                        @{
                            Console.WriteLine(carrier.Credentials.Count);
                        }
                        <CredentialList CarrierId="@SelectedCarrierId" Credentials="@carrier.Credentials" OnCredentialChanged="OnCredentialChanged" />
                    </div>

                    <div class="txt-section spc">NOTES</div>
                    <div class="div-section">
                        @if(string.IsNullOrEmpty(carrier.Notes))
                        {
                            <span class="txt-bold">(No notes found)</span>
                        }
                        else
                        {
                            <span class="txt-p">@carrier.Notes</span>
                        }
                    </div>
                </div>
                <div style="min-width:350px;">
                    <div class="txt-section">Specialty Lines</div>
                    <div class="div-section">
                        @if (allProducts != null && allProducts.Any())
                        {
                            <div class="products-checklist-compact">
                                @foreach (var product in allProducts)
                                {
                                    var isChecked = carrier?.CarrierProducts?.Any(cp => cp.ProductId == product.ProductId && cp.IsActive) ?? false;
                                    var isSpecialty = carrier?.CarrierProducts?.FirstOrDefault(cp => cp.ProductId == product.ProductId && cp.IsActive)?.ProductSpecialty ?? false;
                                
                                    <div class="product-item-compact">
                                        <SfCheckBox Checked="@GetProductCheckedProxy(product.ProductId)" 
                                                    ValueChange="@(async (Syncfusion.Blazor.Buttons.ChangeEventArgs<bool?> args) => await OnProductCheckboxChanged(product.ProductId, args))"
                                                    CssClass="compact-checkbox">
                                            <span class="product-label">
                                                @(!string.IsNullOrEmpty(product.LineNickname) ? product.LineNickname : product.LineName)
                                                @if (isSpecialty)
                                                {
                                                    <span class="specialty-indicator-compact" title="Specialty Line">★</span>
                                                }
                                            </span>
                                        </SfCheckBox>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <span class="txt-bold">(Loading products...)</span>
                        }
                    </div>

                    <div style="height:15px;"></div>

                    @* If the carrier is a wholesaler, list the carriers they can access here *@
                    <div class="txt-section">Carrier Access</div>
                    <div class="div-section">
                        @if (carrier.Wholesaler)
                        {
                            @if (wholesalerAccessCarriers.Count > 0)
                            {
                                <table class="flauxentTable mt-2">
                                    <thead class="sf-txt-column">
                                        <tr>
                                            <th>Carrier Name</th>
                                            <th>Notes</th>
                                            <th class="shmedium">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var accessCarrier in wholesalerAccessCarriers)
                                        {
                                            <tr>
                                                <td>@accessCarrier.CarrierName</td>
                                                <td>@(GetWholesalerCarrierNotes(accessCarrier.CarrierId))</td>
                                                <td>
                                                    <FluentButton Appearance="Appearance.Stealth" BackgroundColor="#fff0" OnClick="@(() => NavigateToCarrier(accessCarrier.CarrierId))" Title="View Carrier">
                                                        <FluentIcon Value="@(new Icons.Regular.Size16.Open())" />
                                                    </FluentButton>
                                                    <FluentButton Appearance="Appearance.Stealth" BackgroundColor="#fff0" OnClick="@(() => ShowRemoveCarrierConfirmation(accessCarrier))" Title="Remove Access">
                                                        <FluentIcon Value="@(new Icons.Regular.Size16.Delete())" />
                                                    </FluentButton>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            
                                <div style="margin-top: 10px;">
                                    <span class="sf-button-small" @onclick="OpenAddCarrierDialog">
                                        <FluentIcon Value="@(new Icons.Regular.Size20.AddCircle())" Slot="start" Color="Color.Custom" CustomColor="#0473ce" /> Associate Carrier
                                    </span>
                                </div>
                            }
                            else
                            {
                                <div class="empty-message">No carrier associations available.</div>
                                <span class="sf-button-small" @onclick="OpenAddCarrierDialog">
                                    <FluentIcon Value="@(new Icons.Regular.Size20.AddCircle())" Slot="start" Color="Color.Custom" CustomColor="#0473ce" /> Associate Carrier
                                </span>
                            }
                        }
                        else
                        {
                            <div class="empty-message">Carrier access is only available for wholesalers.</div>
                        }
                    </div>

                </div>
                <div>
                    
                    <div class="txt-section">Policies</div>
                    <div class="div-section">
                        <PolicyListSmall policyList="@carrierPolicies" />
                    </div>
                </div>
            </FluentStack>
        </div>
   
}

<!-- Add Carrier Access Dialog -->
<FluentDialog @bind-Hidden="addCarrierDialogHidden" @ref="addCarrierDialogRef" TrapFocus="true" Modal="true" Title="Add Carrier Access" Style="min-width: 550px;">
    <div class="dialog-content">
        @if (selectedCarrierToAdd == null)
        {
            <div class="form-group">
                <span class="txt-label mb-2">Search for Carrier</span>
                <div class="search-box">
                    <FluentTextField @bind-Value="carrierSearchTerm" Placeholder="Start typing to search carriers..." @oninput="HandleCarrierSearchInput" />
                    @if (isSearchingCarriers)
                    {
                        <div class="search-spinner">
                            <FluentProgressRing Size="ProgressRingSize.Tiny" />
                        </div>
                    }
                </div>
            </div>
            
            @if (availableCarriers.Count > 0)
            {
                <div class="search-results-container">
                    <table class="search-results-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Location</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var availableCarrier in availableCarriers.Take(10))
                            {
                                <tr class="search-result-row" @onclick="() => SelectCarrierToAdd(availableCarrier)">
                                    <td>@availableCarrier.CarrierName</td>
                                    <td>@(availableCarrier.Wholesaler ? "Wholesaler" : "Carrier")</td>
                                    <td>@($"{availableCarrier.City}, {availableCarrier.State}".Trim(' ', ','))</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else if (!string.IsNullOrWhiteSpace(carrierSearchTerm) && !isSearchingCarriers)
            {
                <div class="empty-search-results">No matching carriers found. Try a different search term.</div>
            }
        }
        
        @if (selectedCarrierToAdd != null)
        {
            <div class="selected-result">
                <div class="selected-result-header">Selected Carrier:</div>
                <div class="selected-result-name">@selectedCarrierToAdd.CarrierName</div>
                @if (!string.IsNullOrEmpty(selectedCarrierToAdd.City) || !string.IsNullOrEmpty(selectedCarrierToAdd.State))
                {
                    <div class="selected-result-parent">@($"{selectedCarrierToAdd.City}, {selectedCarrierToAdd.State}".Trim(' ', ','))</div>
                }
            </div>
            
            <div class="form-group mt-4">
                <span class="txt-label mb-2">Notes (Optional)</span>
                <FluentTextField @bind-Value="carrierAccessNotes" Placeholder="Additional notes about this carrier access" />
            </div>
        }
    </div>
    <FluentDialogFooter>
        <FluentButton Appearance="Appearance.Accent" OnClick="AddCarrierAccess" Disabled="@(selectedCarrierToAdd == null)">Add Access</FluentButton>
        <FluentButton Appearance="Appearance.Outline" OnClick="CancelAddCarrierDialog">Cancel</FluentButton>
    </FluentDialogFooter>
</FluentDialog>

<!-- Remove Carrier Access Confirmation Dialog -->
<FluentDialog @bind-Hidden="removeCarrierDialogHidden" TrapFocus="true" Modal="true" Title="Confirm Remove Carrier Access">
    <div class="dialog-content">
        <p>Are you sure you want to remove access to this carrier?</p>
        @if (carrierToRemove != null)
        {
            <p><strong>@carrierToRemove.CarrierName</strong></p>
        }
    </div>
    <FluentDialogFooter>
        <FluentButton Appearance="Appearance.Accent" OnClick="ConfirmRemoveCarrierAccess">Remove Access</FluentButton>
        <FluentButton Appearance="Appearance.Outline" OnClick="CancelRemoveCarrierAccess">Cancel</FluentButton>
    </FluentDialogFooter>
</FluentDialog>

@code {
    [Parameter] public int SelectedCarrierId { get; set; }
    [Parameter] public EventCallback<int> SelectedCarrierIdChanged { get; set; }

    Carrier? carrier;
    private List<Policy> carrierPolicies = new();
    private List<Carrier> wholesalerAccessCarriers = new();
    private List<WholesalerCarrier> wholesalerCarrierRelationships = new();
    private List<Product> allProducts = new();
    private Dictionary<int, bool> productCheckStates = new();

    // Add Carrier Dialog Properties
    private bool addCarrierDialogHidden = true;
    private FluentDialog? addCarrierDialogRef;
    private bool isSearchingCarriers = false;
    private string carrierSearchTerm = string.Empty;
    private List<Carrier> availableCarriers = new();
    private Carrier? selectedCarrierToAdd;
    private string carrierAccessNotes = string.Empty;
    private System.Timers.Timer? carrierSearchTimer;

    // Remove Carrier Dialog Properties
    private bool removeCarrierDialogHidden = true;
    private Carrier? carrierToRemove;

    protected override async Task OnInitializedAsync()
    {
        // Load products and carrier data in parallel
        await Task.WhenAll(LoadProductsData(), LoadCarrierData());
        
        // Initialize search timer
        carrierSearchTimer = new System.Timers.Timer(500); // 500ms debounce
        carrierSearchTimer.AutoReset = false;
        carrierSearchTimer.Elapsed += async (sender, e) => await SearchAvailableCarriers();
    }

    private async Task LoadCarrierData()
    {
        carrier = await CarrierService.GetCarrierByIdAsync(SelectedCarrierId);
        if (carrier is null)
        {
            NavigationManager.NavigateTo("notfound");
            return;
        }
        carrierPolicies = await PolicyService.GetCurrentPoliciesByCarrierIdAsync(SelectedCarrierId);
        
        // Load wholesaler access carriers if this is a wholesaler
        if (carrier.Wholesaler)
        {
            wholesalerAccessCarriers = await CarrierService.GetWholesalerAccessCarriersAsync(SelectedCarrierId);
            // Load the relationships to get notes
            await LoadWholesalerCarrierRelationships();
        }
        
        // Initialize product check states
        InitializeProductCheckStates();
    }

    private async Task LoadProductsData()
    {
        allProducts = await SharedService.GetAllProductsAsync();
    }

    private void InitializeProductCheckStates()
    {
        productCheckStates.Clear();
        if (carrier?.CarrierProducts != null)
        {
            foreach (var product in allProducts)
            {
                productCheckStates[product.ProductId] = carrier.CarrierProducts.Any(cp => cp.ProductId == product.ProductId && cp.IsActive);
            }
        }
    }

    private bool GetProductCheckedProxy(int productId)
    {
        return productCheckStates.GetValueOrDefault(productId, false);
    }

    private async Task OnProductCheckboxChanged(int productId, Syncfusion.Blazor.Buttons.ChangeEventArgs<bool?> args)
    {
        var isChecked = args.Checked ?? false;
        
        // Update the UI state immediately
        productCheckStates[productId] = isChecked;
        StateHasChanged();

        try
        {
            if (isChecked)
            {
                await CarrierService.AddCarrierProductAsync(SelectedCarrierId, productId);
            }
            else
            {
                await CarrierService.RemoveCarrierProductAsync(SelectedCarrierId, productId);
            }
            
            // Reload carrier data to refresh the underlying data model
            await LoadCarrierData();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error updating carrier product: {ex.Message}");
            // Revert the checkbox state on error
            productCheckStates[productId] = !isChecked;
            StateHasChanged();
        }
    }

    private async Task LoadWholesalerCarrierRelationships()
    {
        using var context = DbContextFactory.CreateDbContext();
        wholesalerCarrierRelationships = await context.WholesalerCarriers
            .Where(wc => wc.WholesalerId == SelectedCarrierId && wc.IsActive)
            .ToListAsync();
    }

    private string GetWholesalerCarrierNotes(int carrierId)
    {
        var relationship = wholesalerCarrierRelationships.FirstOrDefault(wc => wc.CarrierId == carrierId);
        return relationship?.Notes ?? string.Empty;
    }

    // Dialog Management Methods
    private async Task OpenAddCarrierDialog()
    {
        selectedCarrierToAdd = null;
        carrierSearchTerm = string.Empty;
        carrierAccessNotes = string.Empty;
        availableCarriers.Clear();
        addCarrierDialogHidden = false;
        StateHasChanged();
    }

    private void CancelAddCarrierDialog()
    {
        addCarrierDialogHidden = true;
        selectedCarrierToAdd = null;
        carrierSearchTerm = string.Empty;
        carrierAccessNotes = string.Empty;
        availableCarriers.Clear();
        StateHasChanged();
    }

    private void ShowRemoveCarrierConfirmation(Carrier carrierToRemoveAccess)
    {
        carrierToRemove = carrierToRemoveAccess;
        removeCarrierDialogHidden = false;
        StateHasChanged();
    }

    private void CancelRemoveCarrierAccess()
    {
        removeCarrierDialogHidden = true;
        carrierToRemove = null;
        StateHasChanged();
    }

    // Search and Selection Methods
    private async Task HandleCarrierSearchInput(ChangeEventArgs e)
    {
        carrierSearchTerm = e.Value?.ToString() ?? string.Empty;
        selectedCarrierToAdd = null; // Clear selection when search changes
        
        carrierSearchTimer?.Stop();
        carrierSearchTimer?.Start();
        StateHasChanged();
    }

    private async Task SearchAvailableCarriers()
    {
        if (string.IsNullOrWhiteSpace(carrierSearchTerm) || carrierSearchTerm.Length < 2)
        {
            await InvokeAsync(() =>
            {
                availableCarriers.Clear();
                isSearchingCarriers = false;
                StateHasChanged();
            });
            return;
        }

        await InvokeAsync(() =>
        {
            isSearchingCarriers = true;
            StateHasChanged();
        });

        try
        {
            var results = await CarrierService.GetAvailableCarriersForWholesalerAsync(SelectedCarrierId, carrierSearchTerm);
            
            await InvokeAsync(() =>
            {
                availableCarriers = results;
                isSearchingCarriers = false;
                StateHasChanged();
            });
        }
        catch (Exception ex)
        {
            await InvokeAsync(() =>
            {
                availableCarriers.Clear();
                isSearchingCarriers = false;
                StateHasChanged();
            });
            Console.WriteLine($"Error searching carriers: {ex.Message}");
        }
    }

    private void SelectCarrierToAdd(Carrier carrier)
    {
        selectedCarrierToAdd = carrier;
        StateHasChanged();
    }

    // Action Methods
    private async Task AddCarrierAccess()
    {
        if (selectedCarrierToAdd == null) return;

        try
        {
            await CarrierService.AddWholesalerCarrierAccessAsync(
                SelectedCarrierId, 
                selectedCarrierToAdd.CarrierId, 
                string.IsNullOrWhiteSpace(carrierAccessNotes) ? null : carrierAccessNotes);
            
            await LoadCarrierData(); // Refresh the data
            CancelAddCarrierDialog();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error adding carrier access: {ex.Message}");
            // You might want to show a toast notification here
        }
    }

    private async Task ConfirmRemoveCarrierAccess()
    {
        if (carrierToRemove == null) return;

        try
        {
            await CarrierService.RemoveWholesalerCarrierAccessAsync(SelectedCarrierId, carrierToRemove.CarrierId);
            await LoadCarrierData(); // Refresh the data
            CancelRemoveCarrierAccess();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error removing carrier access: {ex.Message}");
            // You might want to show a toast notification here
        }
    }

    // Navigation Methods
    private void NavigateToCarrier(int carrierId)
    {
        NavigationManager.NavigateTo($"/Carriers/{carrierId}");
    }

    private async Task OnCredentialChanged()
    {
        await LoadCarrierData();
        StateHasChanged();
    }
    
    private void SaveContact(Contact contact)
    {
        //carrier.Contacts.Add(contact);
    }

    public void Dispose()
    {
        carrierSearchTimer?.Dispose();
    }
}