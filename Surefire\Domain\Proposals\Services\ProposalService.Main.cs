﻿using System;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Surefire.Data;
using Surefire.Domain.Proposals;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Surefire.Domain.Forms.Models;
using Surefire.Domain.Logs;
using Surefire.Domain.Shared.Services;
using Microsoft.Extensions.Logging;

namespace Surefire.Domain.Proposals
{
    public partial class ProposalService
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly ILoggingService _log;
        private readonly StateService _stateService;
        protected readonly string _formRecognizerEndpoint;
        protected readonly string _formRecognizerKey;
        protected readonly string _openAiApiKey;
        protected readonly HttpClient _httpClient;

        public ProposalService(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            IHttpContextAccessor httpContextAccessor,
            ILoggingService log,
            IDbContextFactory<ApplicationDbContext> dbContextFactory,
            StateService stateService)
        {
            _context = context;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
            _dbContextFactory = dbContextFactory;
            _log = log;
            _stateService = stateService;

            _formRecognizerEndpoint = Environment.GetEnvironmentVariable("AZURE_FORM_RECOGNIZER_ENDPOINT")
                ?? throw new Exception("Missing AZURE_FORM_RECOGNIZER_ENDPOINT");
            _formRecognizerKey = Environment.GetEnvironmentVariable("AZURE_FORM_RECOGNIZER_KEY")
                ?? throw new Exception("Missing AZURE_FORM_RECOGNIZER_KEY");
            _openAiApiKey = Environment.GetEnvironmentVariable("OPENAI")
                ?? throw new Exception("Missing OPENAI");

            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _openAiApiKey);
            
            // Initialize the ExtractorService
            InitializeExtractorService();
        }

        /// <summary>
        /// Returns all proposals for a given client.
        /// </summary>
        public async Task<List<Proposal>> GetProposalsForClientAsync(int clientId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            return await context.Proposals
                .Where(p => p.ClientId == clientId)
                .OrderByDescending(p => p.DateCreated)
                .ToListAsync();
        }

        /// <summary>
        /// Returns a filtered list of proposals for the homepage display.
        /// Shows all proposals with status 0-2, status 10 (renewal mailouts), and status 3 proposals from the last 15 days.
        /// </summary>
        public async Task<List<Proposal>> GetProposalHomepageListAsync()
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            var cutoffDate = DateTime.UtcNow.AddDays(-15);
            
            return await context.Proposals
                .Include(p => p.Renewal)
                    .ThenInclude(r => r.Client)
                .Include(p => p.CreatedBy)
                .Include(p => p.Policy)
                    .ThenInclude(policy => policy.Product)
                .Where(p => 
                    // Always show status 0-2
                    p.Status <= 2 ||
                    // Always show status 10 (renewal mailouts)
                    p.Status == 10 ||
                    // Show status 3 only if within last 15 days
                    (p.Status == 3 && p.DateCreated >= cutoffDate)
                )
                .OrderByDescending(p => p.DateCreated)
                .ToListAsync();
        }

        /// <summary>
        /// Creates a new proposal for the specified client and returns its ID.
        /// </summary>
        public async Task<int> CreateProposalForClientAsync(int clientId)
        {
            await _log.LogAsync(LogLevel.Information, $"Creating new proposal for client: {clientId}", "ProposalService");
            
            // Get the current user
            var userId = _userManager.GetUserId(_httpContextAccessor.HttpContext.User);
            if (string.IsNullOrEmpty(userId))
            {
                throw new UnauthorizedAccessException("No user is currently logged in");
            }

            using var context = await _dbContextFactory.CreateDbContextAsync();
            var proposal = new Proposal
            {
                ClientId = clientId,
                RawJson = "{}",
                ApprovedJson = "{}",
                DateCreated = DateTime.UtcNow,
                DateModified = DateTime.UtcNow,
                CreatedById = userId,
                ModifiedById = userId,
                IsApproved = false
            };
            
            context.Proposals.Add(proposal);
            await context.SaveChangesAsync();
            
            await _log.LogAsync(LogLevel.Information, $"Created proposal ID: {proposal.ProposalId} for client: {clientId}", "ProposalService");
            return proposal.ProposalId;
        }

        /// <summary>
        /// Orchestrates the complete process:
        /// - Extract selected pages from the original PDF.
        /// - Extract raw data via Azure Form Recognizer.
        /// - Digest the raw data using OpenAI.
        /// - Generate a proposal DOCX from the cleaned JSON and template.
        /// Returns a tuple containing the cleaned JSON and the DOCX bytes.
        /// </summary>
        //public async Task<(string cleanedJson, byte[] proposalDocBytes)> CreateProposalAsync(byte[] originalPdfBytes, int[] selectedPages, string templatePath)
        //{
        //    // 1. Extract selected pages (handled in the PDF partial)
        //    byte[] selectedPdf = ExtractSelectedPages(originalPdfBytes, selectedPages);

        //    // 2. Extract raw data using Azure Form Recognizer (handled in FormRecognizer partial)
        //    string rawDataJson = await ExtractDataAsync(selectedPdf);

        //    // 3. Digest the raw data via OpenAI (handled in the Digestor partial)
        //    string cleanedJson = await DigestDataAsync(rawDataJson);

        //    // 4. Generate the proposal document (DOCX) using the cleaned JSON (handled in the DOCX partial)
        //    byte[] proposalDoc = GenerateProposalDocument(cleanedJson, templatePath);

        //    return (cleanedJson, proposalDoc);
        //}

        // Placeholder for additional main methods (e.g. listing proposals, editing names, etc.)
        public void ListProposals()
        {
            // TODO: Implement method to list proposals from the database.
            throw new NotImplementedException();
        }

        public void EditProposalName(int proposalId, string newName)
        {
            // TODO: Implement method to edit the proposal name and update the database.
            throw new NotImplementedException();
        }

        /// <summary>
        /// Returns the proposal for a given renewalId, or null if not found.
        /// </summary>
        public async Task<Proposal> GetProposalByRenewalIdAsync(int renewalId)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            var proposal = await context.Proposals
                .Include(p => p.Renewal)
                .Include(p => p.Renewal.Client)
                .FirstOrDefaultAsync(p => p.RenewalId == renewalId);

            if (proposal?.Renewal == null)
            {
                var renewal = await context.Renewals.FindAsync(renewalId);
                // Create a new proposal
                proposal = new Proposal
                {
                    RenewalId = renewalId,
                    CreatedById = _userManager.GetUserId(_httpContextAccessor.HttpContext.User),
                    Status = -1,
                    SendDate = renewal.RenewalDate.AddDays(-15)
                };
                context.Proposals.Add(proposal);
                await context.SaveChangesAsync();
            }

            return proposal;
        }

        /// <summary>
        /// Saves (inserts or updates) a Proposal entity.
        /// </summary>
        public async Task SaveProposalAsync(Proposal proposal)
        {
            using var context = await _dbContextFactory.CreateDbContextAsync();
            var userId = _userManager.GetUserId(_httpContextAccessor.HttpContext.User);
            proposal.DateModified = DateTime.UtcNow;
            proposal.ModifiedById = userId;

            // Ensure proposal has an attachment group
            await EnsureProposalHasAttachmentGroupAsync(proposal, context);

            if (proposal.ProposalId == 0)
            {
                proposal.DateCreated = DateTime.UtcNow;
                proposal.CreatedById = userId;
                context.Proposals.Add(proposal);
            }
            else
            {
                context.Proposals.Update(proposal);
            }
            await context.SaveChangesAsync();
            
            // Invalidate homepage cache since proposal data changed
            _stateService.InvalidateHomepageCache();
        }

        /// <summary>
        /// Ensures a proposal has an attachment group. Creates one if it doesn't exist.
        /// </summary>
        private async Task EnsureProposalHasAttachmentGroupAsync(Proposal proposal, ApplicationDbContext context)
        {
            if (!proposal.AttachmentGroupId.HasValue)
            {
                var attachmentGroup = new Surefire.Domain.Attachments.Models.AttachmentGroup
                {
                    CreatedAt = DateTime.UtcNow,
                    Name = proposal.ProposalId == 0 
                        ? $"New Proposal Attachments" 
                        : $"Proposal {proposal.ProposalId} Attachments"
                };
                context.AttachmentGroups.Add(attachmentGroup);
                await context.SaveChangesAsync();
                proposal.AttachmentGroupId = attachmentGroup.AttachmentGroupId;
            }
        }
    }
}
