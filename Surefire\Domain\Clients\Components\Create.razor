﻿@page "/Clients/Create"
@using Surefire.Domain.OpenAI
@using Microsoft.EntityFrameworkCore
@using Surefire.Domain.Clients.Models
@using Surefire.Domain.Contacts.Models
@using Surefire.Domain.Clients.Services
@using Surefire.Domain.Shared.Helpers
@using System.ComponentModel.DataAnnotations
@using Syncfusion.Blazor.Inputs
@inject ClientService ClientService
@inject NavigationManager Navigation

<_toolbar ClientLoaded="true" OnShowSmartPaste="ShowSmartPasteDialog" PageName="Create" />

<div class="page-content">
    <div class="page-content-max">
        <div class="mh1" style="font-weight:600; padding-left:5px; color:#6b6b6bff !important;">Create New Client</div>

        <EditForm Model="clientForm" OnValidSubmit="CreateClient" OnInvalidSubmit="OnInvalidSubmit" FormName="create">
            <DataAnnotationsValidator />
            <div class="mf-flex mf-flex-row mf-row-container">
                <div class="mf-item-lg">
                    <div class="txt-section">Company</div>

                    <SfTextBox id="clientName" @bind-Value="clientForm.Name" Placeholder="Client Name" FloatLabelType="FloatLabelType.Always" />
                    <ValidationMessage For="() => clientForm.Name" class="text-danger" />

                    <SfTextBox id="lookupCode" @bind-Value="clientForm.LookupCode" Placeholder="Lookup Code" FloatLabelType="FloatLabelType.Always" />
                    <ValidationMessage For="() => clientForm.LookupCode" class="text-danger" />

                    <SfTextBox id="phoneNumber" @bind-Value="clientForm.PhoneNumber" Placeholder="Phone Number" FloatLabelType="FloatLabelType.Always" @onblur="FormatClientPhoneNumber" />
                    <ValidationMessage For="() => clientForm.PhoneNumber" class="text-danger" />
                    @if (!string.IsNullOrEmpty(clientPhoneValidationError))
                    {
                        <div class="text-danger" style="font-size: 13px; padding-top: 4px;">
                            <span>@clientPhoneValidationError</span>
                        </div>
                    }

                    <SfTextBox id="email" @bind-Value="clientForm.Email" Placeholder="Email" FloatLabelType="FloatLabelType.Always" CssClass="e-custom" />
                    <ValidationMessage For="() => clientForm.Email" class="text-danger" />

                    <SfTextBox id="website" @bind-Value="clientForm.Website" Placeholder="Website" FloatLabelType="FloatLabelType.Always" CssClass="e-custom" />
                    <ValidationMessage For="() => clientForm.Website" class="text-danger" />
                </div>

                <div class="mf-item-lg mf-pad-left">
                    <div class="txt-section">Address</div>
                    <SfTextBox id="addressLine1" @bind-Value="clientForm.AddressLine1" Placeholder="Address Line 1" FloatLabelType="FloatLabelType.Always" CssClass="e-custom" />
                    <ValidationMessage For="() => clientForm.AddressLine1" class="text-danger" />

                    <SfTextBox id="addressLine2" @bind-Value="clientForm.AddressLine2" Placeholder="Address Line 2" FloatLabelType="FloatLabelType.Always" CssClass="e-custom" />

                    <SfTextBox id="city" @bind-Value="clientForm.City" Placeholder="City" FloatLabelType="FloatLabelType.Always" CssClass="e-custom" />
                    <ValidationMessage For="() => clientForm.City" class="text-danger" />

                    <SfTextBox id="state" @bind-Value="clientForm.State" Placeholder="State" FloatLabelType="FloatLabelType.Always" />
                    <ValidationMessage For="() => clientForm.State" class="text-danger" />

                    <SfTextBox id="postalCode" @bind-Value="clientForm.PostalCode" Placeholder="Postal Code" FloatLabelType="FloatLabelType.Always" CssClass="e-custom" />
                    <ValidationMessage For="() => clientForm.PostalCode" class="text-danger" />

                </div>

                <div class="mf-item-lg mf-pad-left">
                    <div class="txt-section">Contact</div>
                    <SfTextBox id="firstname" @bind-Value="clientForm.ContactFirstName" Placeholder="First Name" FloatLabelType="FloatLabelType.Always" />
                    <ValidationMessage For="() => clientForm.ContactFirstName" class="text-danger" />

                    <SfTextBox id="lastname" @bind-Value="clientForm.ContactLastName" Placeholder="Last Name" FloatLabelType="FloatLabelType.Always" CssClass="e-custom" />
                    <ValidationMessage For="() => clientForm.ContactLastName" class="text-danger" />

                    <SfTextBox id="contactemail" @bind-Value="clientForm.ContactEmail" Placeholder="Email" FloatLabelType="FloatLabelType.Always" CssClass="e-custom" />
                    <ValidationMessage For="() => clientForm.ContactEmail" class="text-danger" />

                    <SfTextBox id="contactphone" @bind-Value="clientForm.ContactPhone" Placeholder="Phone" FloatLabelType="FloatLabelType.Always" CssClass="e-custom" @onblur="FormatContactPhoneNumber" />
                    <ValidationMessage For="() => clientForm.ContactPhone" class="text-danger" />
                    @if (!string.IsNullOrEmpty(contactPhoneValidationError))
                    {
                        <div class="text-danger" style="font-size: 13px; padding-top: 4px;">
                            <span>@contactPhoneValidationError</span>
                        </div>
                    }
                </div>
            </div>

            <br />

            <FluentStack HorizontalAlignment="HorizontalAlignment.SpaceBetween">
                <SfButton Content="Cancel" CssClass="e-secondary" type="button" @onclick="CancelCreate"></SfButton>
                <SfButton Content="Create Client" CssClass="e-primary" type="submit"></SfButton>
            </FluentStack>
        
        </EditForm>
        @* <SmartPaste @ref="smartPaste" OnDataExtracted="OnDataExtracted" /> *@
    </div>
</div>

<style>
    :root .e-custom {
        background-color: #ffffff99 !important;
    }
    .page-content-max {
        max-width:900px;
    }
</style>
@code {
    [CascadingParameter] public Action<string> UpdateHeader { get; set; }

    //private SmartPaste smartPaste;
    private EditContext _editContext;
    private ValidationMessageStore _messageStore;
    private NewClientForm clientForm = new NewClientForm();
    private string clientPhoneValidationError = "";
    private string contactPhoneValidationError = "";

    protected override async Task OnInitializedAsync()
    {
        UpdateHeader?.Invoke("Clients");
        _editContext = new EditContext(clientForm);
        _editContext.OnValidationRequested += (sender, args) => ValidateFields();
        _messageStore = new ValidationMessageStore(_editContext);
    }
   
    private void ValidateFields()
    {
        _messageStore.Clear();

        // Check required fields using data annotations first
        var validationResults = new List<ValidationResult>();
        var context = new ValidationContext(clientForm);
        bool isValid = Validator.TryValidateObject(clientForm, context, validationResults, true);

        // Add any data annotation validation errors
        foreach (var validationResult in validationResults)
        {
            foreach (var memberName in validationResult.MemberNames)
            {
                _messageStore.Add(_editContext.Field(memberName), validationResult.ErrorMessage);
            }
        }

        // Add custom phone validation errors
        if (!string.IsNullOrEmpty(clientPhoneValidationError))
        {
            _messageStore.Add(() => clientForm.PhoneNumber, clientPhoneValidationError);
        }

        if (!string.IsNullOrEmpty(contactPhoneValidationError))
        {
            _messageStore.Add(() => clientForm.ContactPhone, contactPhoneValidationError);
        }

        _editContext.NotifyValidationStateChanged();
    }
    
    private void FormatClientPhoneNumber()
    {
        clientPhoneValidationError = "";
        
        if (!string.IsNullOrEmpty(clientForm.PhoneNumber))
        {
            var digits = new string(clientForm.PhoneNumber.Where(char.IsDigit).ToArray());
            
            if (digits.Length < 10)
            {
                clientPhoneValidationError = "Phone number must have at least 10 digits";
            }
            else if (digits.Length > 10 && !digits.StartsWith("1"))
            {
                clientPhoneValidationError = "Phone number cannot have more than 10 digits";
            }
            else
            {
                // Only format if validation passes
                clientForm.PhoneNumber = StringHelper.FormatPhoneNumber(clientForm.PhoneNumber);
            }
        }
        
        StateHasChanged();
    }
    
    private void FormatContactPhoneNumber()
    {
        contactPhoneValidationError = "";
        
        if (!string.IsNullOrEmpty(clientForm.ContactPhone))
        {
            var digits = new string(clientForm.ContactPhone.Where(char.IsDigit).ToArray());
            
            if (digits.Length < 10)
            {
                contactPhoneValidationError = "Phone number must have at least 10 digits";
            }
            else if (digits.Length > 10 && !digits.StartsWith("1"))
            {
                contactPhoneValidationError = "Phone number cannot have more than 10 digits";
            }
            else
            {
                // Only format if validation passes
                clientForm.ContactPhone = StringHelper.FormatPhoneNumber(clientForm.ContactPhone);
            }
        }
        
        StateHasChanged();
    }

    private void OnInvalidSubmit()
    {
        // Handle what happens when the form is invalid.
        Console.WriteLine("Form is invalid - validation errors present");
        ValidateFields(); // Ensure all validation messages are shown
    }
    
    private async Task CreateClient()
    {
        Console.WriteLine("CreateClient method called");
        
        // Clear any previous phone validation errors and re-validate
        ValidateFields();
        
        if (_editContext.Validate()) // Proceed only if validation passes
        {
            Console.WriteLine("Validation passed, creating client...");
            try
            {
                // Map NewClientForm to entities
                var newClient = new Client
                    {
                        Name = clientForm.Name,
                        PhoneNumber = clientForm.PhoneNumber,
                        Email = clientForm.Email,
                        Website = clientForm.Website,
                        LookupCode = ValidateLookupCode(clientForm.LookupCode),
                        Address = new Address
                        {
                            AddressLine1 = clientForm.AddressLine1,
                            AddressLine2 = clientForm.AddressLine2,
                            City = clientForm.City,
                            State = clientForm.State,
                            PostalCode = clientForm.PostalCode
                        },
                        PrimaryContact = new Contact
                        {
                            FirstName = clientForm.ContactFirstName,
                            LastName = clientForm.ContactLastName,
                            DateCreated = DateTime.UtcNow,
                            DateModified = DateTime.UtcNow,
                            EmailAddresses = !string.IsNullOrEmpty(clientForm.ContactEmail) ? new List<EmailAddress>
                            {
                                new EmailAddress
                                {
                                    Email = clientForm.ContactEmail,
                                    IsPrimary = true,
                                    DateCreated = DateTime.UtcNow,
                                    DateModified = DateTime.UtcNow
                                }
                            } : null,
                            PhoneNumbers = !string.IsNullOrEmpty(clientForm.ContactPhone) ? new List<PhoneNumber>
                            {
                                new PhoneNumber
                                {
                                    Number = clientForm.ContactPhone,
                                    Type = PhoneType.Office,
                                    IsPrimary = true,
                                    DateCreated = DateTime.UtcNow,
                                    DateModified = DateTime.UtcNow
                                }
                            } : null
                        }
                    };

                Console.WriteLine($"About to call CreateNewClientAsync for client: {newClient.Name}");
                int clientId = await ClientService.CreateNewClientAsync(newClient);
                Console.WriteLine($"Client created successfully with ID: {clientId}");
                Navigation.NavigateTo($"/Clients/{clientId}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating client: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
        else
        {
            Console.WriteLine("Validation failed - form has errors");
        }
    }

    private async Task ShowSmartPasteDialog()
    {
        // smartPaste.Show();
    }
    private void OnDataExtracted(LeadData data)
    {
        if (data != null)
        {
            if (!string.IsNullOrEmpty(data.CompanyName))
                clientForm.Name = data.CompanyName;

            if (data.PhoneNumbers != null && !string.IsNullOrEmpty(StringHelper.GetBestPhoneNumber(data.PhoneNumbers)))
                clientForm.PhoneNumber = StringHelper.GetBestPhoneNumber(data.PhoneNumbers);

            if (!string.IsNullOrEmpty(data.Email))
                clientForm.Email = data.Email;

            if (!string.IsNullOrEmpty(data.Website))
                clientForm.Website = data.Website;

            if (!string.IsNullOrEmpty(data.Address))
                clientForm.AddressLine1 = data.Address;

            if (!string.IsNullOrEmpty(data.City))
                clientForm.City = data.City;

            if (!string.IsNullOrEmpty(data.State))
                clientForm.State = data.State;

            if (!string.IsNullOrEmpty(data.Zip))
                clientForm.PostalCode = data.Zip;

            if (!string.IsNullOrEmpty(data.FirstName))
                clientForm.ContactFirstName = data.FirstName;

            if (!string.IsNullOrEmpty(data.LastName))
                clientForm.ContactLastName = data.LastName;

            if (!string.IsNullOrEmpty(data.Email))
                clientForm.ContactEmail = data.Email;

            if (data.PhoneNumbers != null && !string.IsNullOrEmpty(StringHelper.GetBestMobileNumber(data.PhoneNumbers)))
                clientForm.ContactPhone = StringHelper.GetBestMobileNumber(data.PhoneNumbers);
        }
    }
    private void CancelCreate()
    {
        Navigation.NavigateTo("/");
    }
    private string ValidateLookupCode(string lookupCode)
    {
        if (string.IsNullOrWhiteSpace(lookupCode))
        {
            if (!string.IsNullOrWhiteSpace(clientForm.Name) && clientForm.Name.Length >= 5)
            {
                return clientForm.Name.Substring(0, 5).ToUpper() + "-01";
            }
            else if (!string.IsNullOrWhiteSpace(clientForm.Name))
            {
                // If the name is shorter than 5 characters, use the whole name
                return clientForm.Name.ToUpper() + "-01";
            }
            else
            {
                // Fallback for missing name
                throw new InvalidOperationException("Client Name is required to generate a Lookup Code.");
            }
        }
        return lookupCode;
    }
}
