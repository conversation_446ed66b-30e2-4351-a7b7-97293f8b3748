//using Surefire.Domain.Clients.Services;
//using Surefire.Domain.Policies.Services;
//using Surefire.Domain.Agents.Handlers;

//namespace Surefire.Domain.Agents.Definitions
//{
//    public class AdHocLossRunAgent : IAgent
//    {
//        private readonly ClientService _clientService;
//        private readonly PolicyService _policyService;
//        private readonly LossRunRequestHandler _handler;

//        public AdHocLossRunAgent(
//            ClientService clientService,
//            PolicyService policyService,
//            LossRunRequestHandler handler)
//        {
//            _clientService = clientService;
//            _policyService = policyService;
//            _handler = handler;
//        }

//        public async Task<AgentResult> ExecuteAsync(AgentContext context)
//        {
//            string clientName = context.Data.TryGetValue("ClientName", out var val) ? val?.ToString() : "Pacific Security";
//            string productName = context.Data.TryGetValue("ProductName", out var val2) ? val2?.ToString() : "Work Comp";
//            int years = context.Data.TryGetValue("Years", out var y) && int.TryParse(y?.ToString(), out var n) ? n : 5;

//            // 1a. Lookup client
//            var client = await _clientService.GetClientByNameAsync(clientName);
//            if (client == null)
//                return new AgentResult { Success = false, Message = $"Client '{clientName}' not found." };

//            //1b. Get product by name
//            var product = await _policyService.GetProductByNameAsync(productName);
//            if (product == null)
//                return new AgentResult { Success = false, Message = $"Product '{productName}' not found." };


//            // 2. Query policies for last N years using the found product's line name
//            var policies = await _policyService.GetPoliciesForClientAndLineAsync(client.ClientId, product.ProductId, years);
//            if (policies == null || !policies.Any())
//                return new AgentResult { Success = false, Message = $"No {product.LineName} policies found for {clientName} in last {years} years." };

//            // 3. Identify carriers and their loss-run request emails
//            var carrierEmailMap = await _handler.GetCarriersAndEmailsAsync(policies);

//            // 4. Draft and send emails to carriers missing runs
//            var emailResults = await _handler.DraftAndSendLossRunRequestsAsync(client, policies, carrierEmailMap);

//            // 5. Provide fallback guidance (if any carriers missing emails)
//            var fallback = await _handler.GetFallbackGuidanceAsync(carrierEmailMap);

//            return new AgentResult
//            {
//                Success = true,
//                Message = $"Loss run requests sent for {clientName}.",
//                Payload = new
//                {
//                    EmailsSent = emailResults,
//                    Fallback = fallback
//                }
//            };
//        }
//    }
//}
