﻿using Microsoft.AspNetCore.Components;
using Surefire.Domain.Shared.Services;
using Microsoft.AspNetCore.Components.Authorization;

public class AppComponentBase : ComponentBase
{
    [Inject]
    protected StateService StateService { get; set; } = default!;

    [Inject]
    protected AuthenticationStateProvider AuthStateProvider { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        // Use proper event-driven initialization instead of polling
        if (!StateService.IsInitialized)
        {
            try
            {
                // Wait for initialization with a reasonable timeout
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                await StateService.InitializationTask.WaitAsync(cts.Token);
            }
            catch (TimeoutException)
            {
                Console.WriteLine("StateService initialization timed out after 30 seconds");
                // Continue anyway - app might still be partially functional
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("StateService initialization was cancelled");
                // Continue anyway - app might still be partially functional
            }
        }
    }
}
