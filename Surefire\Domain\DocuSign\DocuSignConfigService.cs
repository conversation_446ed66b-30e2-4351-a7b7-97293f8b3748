﻿using System;
using Microsoft.Extensions.Configuration;

namespace Surefire.Domain.DocuSign
{
    /// <summary>
    /// Service for accessing and managing DocuSign configuration settings
    /// </summary>
    public class DocuSignConfigService : IDocuSignConfigService
    {
        private readonly IConfiguration _configuration;
        private DocuSignConfig _cachedConfig;

        /// <summary>
        /// Creates a new instance of the DocuSign config service
        /// </summary>
        /// <param name="configuration">The application configuration</param>
        public DocuSignConfigService(IConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        /// <summary>
        /// Gets the current DocuSign configuration settings
        /// </summary>
        /// <returns>The current DocuSign configuration</returns>
        public DocuSignConfig GetDocuSignConfig()
        {
            // Return cached config if available
            if (_cachedConfig != null)
            {
                return _cachedConfig.Clone();
            }

            // Load config from settings (prioritize environment variables over configuration)
            var config = new DocuSignConfig
            {
                ApiKey = Environment.GetEnvironmentVariable("DOCUSIGN_API_KEY") ?? 
                         _configuration["DocuSign:ApiKey"] ?? 
                         string.Empty,
                         
                IntegratorKey = Environment.GetEnvironmentVariable("DOCUSIGN_INTEGRATOR_KEY") ?? 
                                 _configuration["DocuSign:IntegratorKey"] ?? 
                                 string.Empty,
                                 
                AccountId = Environment.GetEnvironmentVariable("DOCUSIGN_ACCOUNT_ID") ?? 
                            _configuration["DocuSign:AccountId"] ?? 
                            string.Empty,
                            
                UserId = Environment.GetEnvironmentVariable("DOCUSIGN_USER_ID") ?? 
                         _configuration["DocuSign:UserId"] ?? 
                         string.Empty,
                         
                BaseUrl = Environment.GetEnvironmentVariable("DOCUSIGN_BASE_URL") ?? 
                          _configuration["DocuSign:BaseUrl"] ??
                          "na3.docusign.net",
                          
                AuthServer = Environment.GetEnvironmentVariable("DOCUSIGN_AUTH_SERVER") ?? 
                             _configuration["DocuSign:AuthServer"] ?? 
                             "account.docusign.com",
                             
                PrivateKeyPath = Environment.GetEnvironmentVariable("DOCUSIGN_PRIVATE_KEY_PATH") ?? 
                                 _configuration["DocuSign:PrivateKeyPath"] ?? 
                                 string.Empty
            };

            // If ApiKey is not set, use the IntegratorKey (they're usually the same)
            if (string.IsNullOrEmpty(config.ApiKey) && !string.IsNullOrEmpty(config.IntegratorKey))
            {
                config.ApiKey = config.IntegratorKey;
            }

            // Cache the config
            _cachedConfig = config.Clone();

            return config;
        }

        /// <summary>
        /// Updates the DocuSign configuration settings
        /// </summary>
        /// <param name="config">The new configuration settings</param>
        public void UpdateDocuSignConfig(DocuSignConfig config)
        {
            if (config == null)
            {
                throw new ArgumentNullException(nameof(config), "Cannot update DocuSign configuration with null value");
            }

            // Update the cached config
            _cachedConfig = config.Clone();
        }

        /// <summary>
        /// Determines if the current configuration is for a production environment
        /// </summary>
        /// <returns>True if the current configuration is for production, false for demo/test</returns>
        public bool IsProduction()
        {
            var config = GetDocuSignConfig();

            // Check if the base URL contains "demo" or the auth server contains "-d"
            bool isDemoBaseUrl = config.BaseUrl.Contains("demo", StringComparison.OrdinalIgnoreCase);
            bool isDemoAuthServer = config.AuthServer.Contains("-d", StringComparison.OrdinalIgnoreCase);

            return !isDemoBaseUrl && !isDemoAuthServer;
        }
    }
}