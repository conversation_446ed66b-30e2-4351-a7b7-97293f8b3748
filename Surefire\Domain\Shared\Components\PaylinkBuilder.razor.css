.alink {
    width: 100%;
    font-size: 12px;
    word-break: break-all;
    white-space: normal;
    color: #bbbbbb;
    font-weight:bold;
    line-height:14px;
}

.blink {
    width:80px;
    white-space:nowrap;
    overflow:hidden;
}

.acol {
    color:#c5403b;
}

.bcol {
    color: #0f6cbd;
}
.gurl {
    height:20px;

}
.gurl2 {
    font-size: 12px;
    word-break: break-all;
    white-space: normal;
    color:#0f6cbd;
}

.div-section {
    padding: 10px;
    border-top: 1px solid #ccc;
    border-left: 5px solid #ccc;
    /*background-color: #ffffff43;*/
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#e5e5e5+0,ffffff+26,000000+55,e5e5e5+100&0.65+0,1+26,0+55,0.65+100 */
    /*background: linear-gradient(135deg, rgba(229,229,229,0.65) 0%,rgba(255,255,255,1) 26%,rgba(0,0,0,0) 55%,rgba(229,229,229,0.65) 100%);*/ /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#cccccc+0,f2f2f2+13,000000+100&1+0,1+13,0+100 */
    background: linear-gradient(165deg, rgba(204,204,204,1) 0%,rgba(242,242,242,1) 13%,rgba(0,0,0,0) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.txt-section-bar {
    background-color: #ccc;
    font-family: "montserrat", sans-serif;
    font-size: .9rem;
    font-weight: 400;
    color: #454545;
    text-transform: uppercase;
    padding: 4px 15px 4px 12px;
    border-top-right-radius: 10px;
    letter-spacing: .2em;
    position: relative;
    top: -2px;
}