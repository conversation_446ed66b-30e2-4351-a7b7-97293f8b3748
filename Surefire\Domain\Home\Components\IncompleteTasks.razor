﻿@using Surefire.Domain.Renewals.ViewModels
@using Surefire.Domain.Renewals.Services
@using Surefire.Domain.Home.Models
@using Surefire.Domain.Shared.Services
@using Syncfusion.Blazor.Notifications
@inject RenewalService RenewalService
@inject StateService StateService
@inject HomeService HomeService
@implements IDisposable

<div class="sectiontitletab">Incomplete Tasks</div>
<div class="incomplete-container">
    <table id="incomplete-table" cellspacing="0">
        <tbody>
            @if (incompleteTasks == null || isLoading)
            {
                for (var i = 0; i < 7; i++)
                {
                    <tr>
                        <td><SfSkeleton Shape=SkeletonType.Rectangle Width="100%" Height="15px" CssClass="e-customize" Visible="true"></SfSkeleton></td>
                    </tr>
                }
            }
            else if (!incompleteTasks.Any())
            {
                <tr>
                    <td class="nothingfound">Nothing found here.</td>
                </tr>
            }
            else
            {
                @foreach (var task in incompleteTasks)
                {
                    <tr class="trow2 ellipsis">
                        <td class="intd"><span class="prodmeme">@StringHelper.GetSafeSubstring(task.PolicyProduct, 0, 3)</span><span class="hexp2">@task.RenewalDate.ToString("MM/dd")</span></td>
                        <td class="ellipsis">
                            <FluentStack>
                                <div class="inco infill"><a class="rentask-link" @onclick="() => HandleRenewalClick(task.RenewalId)">@task.ClientName</a></div>
                                <div class="inco">@task.TaskName</div>
                            </FluentStack>
                            
                        
                        </td>
                    </tr>
                }
            }
        </tbody>
    </table>
</div>

@code {
    private List<HomePageTasksViewModel> incompleteTasks = new();
    private bool isLoading = true;

    protected override async Task OnInitializedAsync()
    {
        // Subscribe to homepage data updates to refresh when data changes
        StateService.OnHomepageDataUpdated += HandleHomepageDataUpdated;
        
        await LoadIncompleteTasksData();
    }

    private async Task LoadIncompleteTasksData()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            // Try to get data from cache first
            if (StateService.CachedIncompleteTasks.Any())
            {
                LoadDataFromCache();
            }
            else
            {
                // Load data directly if cache is empty
                await LoadDataDirectly();
            }
        }
        catch (Exception ex)
        {
            // Handle error gracefully - initialize empty list
            incompleteTasks = new();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void LoadDataFromCache()
    {
        incompleteTasks = StateService.CachedIncompleteTasks.ToList();
    }

    private async Task LoadDataDirectly()
    {
        incompleteTasks = await HomeService.GetIncompleteTasksForCurrentUserAsync();
    }

    private void HandleHomepageDataUpdated()
    {
        InvokeAsync(() =>
        {
            LoadDataFromCache();
            StateHasChanged();
        });
    }

    protected async Task HandleRenewalClick(int renewalId)
    {
        // Set the state service to remember which renewal was selected
        StateService.HtmlRenewalId = renewalId;
        StateService.HtmlView = "details";
        StateService.HtmlTab = "tab-1";
        
        var ren = await RenewalService.GetRenewalByIdAsync(renewalId);
        Navigation.NavigateTo($"/Renewals/Details/{renewalId}");
    }

    public void Dispose()
    {
        StateService.OnHomepageDataUpdated -= HandleHomepageDataUpdated;
    }
}