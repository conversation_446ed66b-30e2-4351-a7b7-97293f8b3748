﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Surefire.Data;

#nullable disable

namespace Surefire.Data.MigrationsLocal
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250123092205_InitialSQLiteMigrationSnappers2")]
    partial class InitialSQLiteMigrationSnappers2
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.0");

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<string>("RoleId")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasColumnType("TEXT");

                    b.Property<string>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("Surefire.Data.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<string>("DesktopUsername")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FirstName")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("LastLogin")
                        .HasColumnType("TEXT");

                    b.Property<string>("LastLookupClient")
                        .HasColumnType("TEXT");

                    b.Property<string>("LastLookupPerson")
                        .HasColumnType("TEXT");

                    b.Property<string>("LastName")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int?>("LastRenewalId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("LastRenewalMonth")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LastRenewalPerson")
                        .HasColumnType("TEXT");

                    b.Property<string>("LastRenewalScreen")
                        .HasColumnType("TEXT");

                    b.Property<int?>("LastRenewalYear")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("TEXT");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("PictureUrl")
                        .HasColumnType("TEXT");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("TEXT");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Surefire.Domain.Accounting.Models.Settlement", b =>
                {
                    b.Property<int>("SettlementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AccountingBillingCompany")
                        .HasColumnType("TEXT");

                    b.Property<string>("AccountingCarrier")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("AccountingIssueDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("AccountingPaidOnDate")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("AccountingPaidToCarrierAmount")
                        .HasColumnType("TEXT");

                    b.Property<string>("AccountingPolicyNumber")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("AccountingStatementDueDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("AccountingStatementNumber")
                        .HasColumnType("TEXT");

                    b.Property<string>("BillType")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("BrokerFee")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("CommissionAmount")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("CommissionPercentage")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("DownPaymentAmount")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("DownPaymentPercentage")
                        .HasColumnType("TEXT");

                    b.Property<string>("FinanceAccountNumber")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("FinanceAmount")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("FinanceChargePercent")
                        .HasColumnType("TEXT");

                    b.Property<int?>("FinanceMonths")
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("FullGrandTotalPayment")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsFinanced")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsFullPayment")
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("MinEarnedPercentage")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("PayAmount")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("PayAmountNeededToBind")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("PayAmountNet")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("PayDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("PayDepositDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("PayDone")
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("PayFees")
                        .HasColumnType("TEXT");

                    b.Property<string>("PayNumber")
                        .HasColumnType("TEXT");

                    b.Property<string>("PayType")
                        .HasColumnType("TEXT");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("Premium")
                        .HasColumnType("TEXT");

                    b.Property<int?>("RenewalId")
                        .HasColumnType("INTEGER");

                    b.HasKey("SettlementId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("PolicyId");

                    b.HasIndex("RenewalId");

                    b.ToTable("Settlements");
                });

            modelBuilder.Entity("Surefire.Domain.Accounting.Models.SettlementItem", b =>
                {
                    b.Property<int>("SettlementItemId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<decimal?>("Amount")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<string>("ItemCode")
                        .HasColumnType("TEXT");

                    b.Property<int>("SettlementId")
                        .HasColumnType("INTEGER");

                    b.HasKey("SettlementItemId");

                    b.HasIndex("SettlementId");

                    b.ToTable("SettlementItems");
                });

            modelBuilder.Entity("Surefire.Domain.Attachments.Models.Attachment", b =>
                {
                    b.Property<int>("AttachmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("AttachmentGroupId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("CarrierId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ClaimId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Comments")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateLastOpened")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("FileFormat")
                        .HasColumnType("TEXT");

                    b.Property<double?>("FileSize")
                        .HasColumnType("REAL");

                    b.Property<int?>("FolderId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("HashedFileName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsBinder")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsClientAccessible")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsEndorsement")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsPolicyCopy")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsProposal")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsQuote")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LocalPath")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("OriginalFileName")
                        .HasColumnType("TEXT");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("RenewalId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Status")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("SubmissionId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("UploadedById")
                        .HasColumnType("TEXT");

                    b.HasKey("AttachmentId");

                    b.HasIndex("AttachmentGroupId");

                    b.HasIndex("CarrierId");

                    b.HasIndex("ClaimId");

                    b.HasIndex("ClientId");

                    b.HasIndex("FolderId");

                    b.HasIndex("PolicyId");

                    b.HasIndex("RenewalId");

                    b.HasIndex("SubmissionId");

                    b.HasIndex("UploadedById");

                    b.ToTable("Attachments");
                });

            modelBuilder.Entity("Surefire.Domain.Attachments.Models.AttachmentGroup", b =>
                {
                    b.Property<int>("AttachmentGroupId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("AttachmentGroupId");

                    b.ToTable("AttachmentGroups");
                });

            modelBuilder.Entity("Surefire.Domain.Attachments.Models.Folder", b =>
                {
                    b.Property<int>("FolderId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("FolderId");

                    b.ToTable("Folders");
                });

            modelBuilder.Entity("Surefire.Domain.Carriers.Models.Carrier", b =>
                {
                    b.Property<int>("CarrierId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("AddressId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("AppetiteJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("CarrierName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CarrierNickname")
                        .HasColumnType("TEXT");

                    b.Property<string>("City")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IssuingCarrier")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LogoFilename")
                        .HasColumnType("TEXT");

                    b.Property<string>("LookupCode")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("LossRunsEmail")
                        .HasColumnType("TEXT");

                    b.Property<string>("NewSubmissionEmail")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<string>("Phone")
                        .HasColumnType("TEXT");

                    b.Property<bool>("QuickLink")
                        .HasColumnType("INTEGER");

                    b.Property<string>("QuotelinesJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("QuotingWebsite")
                        .HasColumnType("TEXT");

                    b.Property<string>("ServicingEmail")
                        .HasColumnType("TEXT");

                    b.Property<string>("ServicingWebsite")
                        .HasColumnType("TEXT");

                    b.Property<string>("State")
                        .HasColumnType("TEXT");

                    b.Property<string>("StreetAddress")
                        .HasColumnType("TEXT");

                    b.Property<string>("Website")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Wholesaler")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Zip")
                        .HasColumnType("TEXT");

                    b.HasKey("CarrierId");

                    b.HasIndex("AddressId");

                    b.HasIndex("CreatedById");

                    b.ToTable("Carriers");
                });

            modelBuilder.Entity("Surefire.Domain.Carriers.Models.Credential", b =>
                {
                    b.Property<int>("CredentialId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("CarrierId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<string>("Password")
                        .HasColumnType("TEXT");

                    b.Property<string>("Username")
                        .HasColumnType("TEXT");

                    b.Property<string>("Website")
                        .HasColumnType("TEXT");

                    b.HasKey("CredentialId");

                    b.HasIndex("CarrierId");

                    b.HasIndex("CreatedById");

                    b.ToTable("Credentials");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.BusinessDetails", b =>
                {
                    b.Property<int>("BusinessDetailsId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<double?>("AnnualGrossSalesRevenueReceipts")
                        .HasColumnType("REAL");

                    b.Property<double?>("AnnualPayrollHazardExposure")
                        .HasColumnType("REAL");

                    b.Property<bool?>("BuildingLocationMonitoredSecurity")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("BuildingLocationNumberOfStories")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("BuildingLocationSprinklered")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("BuildingLocationSquareFootage")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("BuildingLocationYearBuilt")
                        .HasColumnType("INTEGER");

                    b.Property<string>("BusinessIndustry")
                        .HasColumnType("TEXT");

                    b.Property<double?>("BusinessPersonalPropertyBPP")
                        .HasColumnType("REAL");

                    b.Property<string>("BusinessSpecialty")
                        .HasColumnType("TEXT");

                    b.Property<int?>("BusinessType")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateStarted")
                        .HasColumnType("TEXT");

                    b.Property<double?>("EstimatedAnnualPayroll0")
                        .HasColumnType("REAL");

                    b.Property<double?>("EstimatedAnnualPayroll1")
                        .HasColumnType("REAL");

                    b.Property<double?>("EstimatedAnnualPayroll2")
                        .HasColumnType("REAL");

                    b.Property<double?>("EstimatedAnnualPayroll3")
                        .HasColumnType("REAL");

                    b.Property<double?>("EstimatedAnnualPayroll4")
                        .HasColumnType("REAL");

                    b.Property<double?>("EstimatedGrossSales0")
                        .HasColumnType("REAL");

                    b.Property<double?>("EstimatedGrossSales1")
                        .HasColumnType("REAL");

                    b.Property<double?>("EstimatedGrossSales2")
                        .HasColumnType("REAL");

                    b.Property<double?>("EstimatedGrossSales3")
                        .HasColumnType("REAL");

                    b.Property<double?>("EstimatedGrossSales4")
                        .HasColumnType("REAL");

                    b.Property<double?>("EstimatedSubcontractingExpenses")
                        .HasColumnType("REAL");

                    b.Property<string>("FEIN")
                        .HasColumnType("TEXT");

                    b.Property<string>("InsuranceHistory")
                        .HasColumnType("TEXT");

                    b.Property<string>("LapseHistory")
                        .HasColumnType("TEXT");

                    b.Property<int?>("LegalEntityType")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LicenseNumber")
                        .HasColumnType("TEXT");

                    b.Property<int?>("LicenseType")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LongDescription")
                        .HasColumnType("TEXT");

                    b.Property<int?>("NumClaims")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("NumFullTimeEmployees")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("NumPartTimeEmployees")
                        .HasColumnType("INTEGER");

                    b.Property<string>("RecordType")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ShortDescription")
                        .HasColumnType("TEXT");

                    b.Property<int?>("YearsExperience")
                        .HasColumnType("INTEGER");

                    b.HasKey("BusinessDetailsId");

                    b.HasIndex("ClientId");

                    b.ToTable("BusinessDetails");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.Client", b =>
                {
                    b.Property<int>("ClientId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("AddressId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("CSRId")
                        .HasColumnType("TEXT");

                    b.Property<string>("Comments")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedById")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateOpened")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .HasColumnType("TEXT");

                    b.Property<string>("LogoFilename")
                        .HasColumnType("TEXT");

                    b.Property<string>("LookupCode")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("TEXT");

                    b.Property<int>("PrimaryContactId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ProducerId")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Website")
                        .HasColumnType("TEXT");

                    b.Property<string>("eClientId")
                        .HasColumnType("TEXT");

                    b.HasKey("ClientId");

                    b.HasIndex("AddressId");

                    b.HasIndex("CSRId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("PrimaryContactId");

                    b.HasIndex("ProducerId");

                    b.ToTable("Clients");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.ClientNote", b =>
                {
                    b.Property<int>("ClientNoteId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Deleted")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("ClientNoteId");

                    b.HasIndex("ClientId");

                    b.ToTable("ClientNotes");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.Lead", b =>
                {
                    b.Property<int>("LeadId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Address")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("BindDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("City")
                        .HasColumnType("TEXT");

                    b.Property<int?>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("CompanyName")
                        .HasColumnType("TEXT");

                    b.Property<string>("ContactName")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("LastOpened")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<string>("Operations")
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("TEXT");

                    b.Property<int?>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("Source")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("Stage")
                        .HasColumnType("INTEGER");

                    b.Property<string>("State")
                        .HasColumnType("TEXT");

                    b.Property<string>("Title")
                        .HasColumnType("TEXT");

                    b.Property<string>("Website")
                        .HasColumnType("TEXT");

                    b.Property<string>("Zip")
                        .HasColumnType("TEXT");

                    b.HasKey("LeadId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ProductId");

                    b.ToTable("Leads");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.LeadNote", b =>
                {
                    b.Property<int>("LeadNoteId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Deleted")
                        .HasColumnType("INTEGER");

                    b.Property<int>("LeadId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("LeadNoteId");

                    b.HasIndex("LeadId");

                    b.ToTable("LeadNotes");
                });

            modelBuilder.Entity("Surefire.Domain.Contacts.Models.Contact", b =>
                {
                    b.Property<int>("ContactId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("AddressId")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("Billing")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("CarrierId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .HasColumnType("TEXT");

                    b.Property<string>("EmailAlternate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Fax")
                        .HasColumnType("TEXT");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("HeadshotFilename")
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsInactive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsStarred")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LastName")
                        .HasColumnType("TEXT");

                    b.Property<string>("MiddleName")
                        .HasColumnType("TEXT");

                    b.Property<string>("Mobile")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Owner")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Phone")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Representative")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("Service")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Title")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Underwriter")
                        .HasColumnType("INTEGER");

                    b.HasKey("ContactId");

                    b.HasIndex("AddressId");

                    b.HasIndex("CarrierId");

                    b.HasIndex("ClientId");

                    b.ToTable("Contacts");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.Certificate", b =>
                {
                    b.Property<int>("CertificateId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("AttachGLAI")
                        .HasColumnType("INTEGER");

                    b.Property<string>("AttachGLAIfilename")
                        .HasColumnType("TEXT");

                    b.Property<bool?>("AttachGLWOS")
                        .HasColumnType("INTEGER");

                    b.Property<string>("AttachGLWOSfilename")
                        .HasColumnType("TEXT");

                    b.Property<bool?>("AttachPNC")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("AttachWCWOS")
                        .HasColumnType("INTEGER");

                    b.Property<string>("AttachWCWOSfilename")
                        .HasColumnType("TEXT");

                    b.Property<bool?>("BlockAttachments")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("HolderName")
                        .HasColumnType("TEXT");

                    b.Property<string>("JSONData")
                        .HasColumnType("TEXT");

                    b.Property<string>("JSONDataTemp")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProjectName")
                        .HasColumnType("TEXT");

                    b.HasKey("CertificateId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("Certificates");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.FormDoc", b =>
                {
                    b.Property<int>("FormDocId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<int>("FormPdfId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("JSONData")
                        .HasColumnType("TEXT");

                    b.Property<int?>("LeadId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("TEXT");

                    b.Property<string>("Title")
                        .HasColumnType("TEXT");

                    b.HasKey("FormDocId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FormPdfId");

                    b.HasIndex("LeadId");

                    b.HasIndex("ModifiedById");

                    b.ToTable("FormDocs");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.FormDocRevision", b =>
                {
                    b.Property<int>("FormDocRevisionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<int>("FormDocId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("JSONData")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("TEXT");

                    b.Property<string>("RevisionName")
                        .HasColumnType("TEXT");

                    b.HasKey("FormDocRevisionId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FormDocId");

                    b.HasIndex("ModifiedById");

                    b.ToTable("FormDocRevisions");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.FormPdf", b =>
                {
                    b.Property<int>("FormPdfId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<string>("Filepath")
                        .HasColumnType("TEXT");

                    b.Property<string>("JSONFields")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("TEXT");

                    b.Property<string>("Title")
                        .HasColumnType("TEXT");

                    b.HasKey("FormPdfId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("FormPdf");
                });

            modelBuilder.Entity("Surefire.Domain.Logs.Log", b =>
                {
                    b.Property<int>("LogId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("EntityId")
                        .HasColumnType("TEXT");

                    b.Property<string>("EntityType")
                        .HasColumnType("TEXT");

                    b.Property<string>("Exception")
                        .HasColumnType("TEXT");

                    b.Property<int>("LogLevel")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Source")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.HasKey("LogId");

                    b.HasIndex("UserId");

                    b.ToTable("Logs");
                });

            modelBuilder.Entity("Surefire.Domain.OpenAI.OpenAIPrompt", b =>
                {
                    b.Property<int>("OpenAIPromptId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<string>("note")
                        .HasColumnType("TEXT");

                    b.Property<string>("prompt")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("OpenAIPromptId");

                    b.ToTable("OpenAIPrompt");
                });

            modelBuilder.Entity("Surefire.Domain.OpenAI.OpenAIResponse", b =>
                {
                    b.Property<int>("OpenAIResponseId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<string>("promptId")
                        .HasColumnType("TEXT");

                    b.Property<string>("response")
                        .HasColumnType("TEXT");

                    b.HasKey("OpenAIResponseId");

                    b.ToTable("OpenAIResponse");
                });

            modelBuilder.Entity("Surefire.Domain.Plugins.Plugin", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("BaseUri")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClientId")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClientSecret")
                        .HasColumnType("TEXT");

                    b.Property<string>("DeveloperName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("GrantType")
                        .HasColumnType("TEXT");

                    b.Property<string>("HashId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Jwt")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PluginWebsite")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("RedirectUri")
                        .HasColumnType("TEXT");

                    b.Property<string>("Scope")
                        .HasColumnType("TEXT");

                    b.Property<string>("ShortDescription")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("TokenUri")
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Plugins");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.AutoCoverage", b =>
                {
                    b.Property<int>("AutoCoverageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("AdditionalAttachments")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("AdditionalAttachmentsAttachmentAttachmentId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("AdditionalCoverageLimit")
                        .HasColumnType("INTEGER");

                    b.Property<string>("AdditionalCoverageName")
                        .HasColumnType("TEXT");

                    b.Property<bool?>("AdditionalInsured")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("AdditionalInsuredAttachmentAttachmentId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("BodilyInjuryPerAccident")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("BodilyInjuryPerPerson")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("CombinedLimit")
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<bool?>("ForAny")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("ForHired")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("ForNonOwned")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("ForOwned")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("ForScheduled")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("TEXT");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("PropertyDamage")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("WaiverOfSub")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("WaiverOfSubAttachmentAttachmentId")
                        .HasColumnType("INTEGER");

                    b.HasKey("AutoCoverageId");

                    b.HasIndex("AdditionalAttachmentsAttachmentAttachmentId");

                    b.HasIndex("AdditionalInsuredAttachmentAttachmentId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PolicyId")
                        .IsUnique();

                    b.HasIndex("WaiverOfSubAttachmentAttachmentId");

                    b.ToTable("AutoCoverages");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Claim", b =>
                {
                    b.Property<int>("ClaimId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<double>("AmountPaid")
                        .HasColumnType("REAL");

                    b.Property<string>("ClaimNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("DateOfLoss")
                        .HasColumnType("TEXT");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("INTEGER");

                    b.HasKey("ClaimId");

                    b.HasIndex("PolicyId");

                    b.ToTable("Claims");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.GeneralLiabilityCoverage", b =>
                {
                    b.Property<int>("GeneralLiabilityCoverageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("AdditionalAttachments")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("AdditionalAttachmentsAttachmentAttachmentId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("AdditionalCoverageLimit")
                        .HasColumnType("INTEGER");

                    b.Property<string>("AdditionalCoverageName")
                        .HasColumnType("TEXT");

                    b.Property<bool?>("AdditionalInsured")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("AdditionalInsuredAttachmentAttachmentId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("AdditionalInsuredFormNumber")
                        .HasColumnType("TEXT");

                    b.Property<int?>("AggregateAppliesPer")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("ClaimsMade")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT");

                    b.Property<int?>("DamageToPremises")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<int?>("EachOccurrence")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("GeneralAggregate")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("MedicalExpenses")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("TEXT");

                    b.Property<bool?>("Occurence")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("PersonalInjury")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("INTEGER");

                    b.Property<double?>("Premium")
                        .HasColumnType("REAL");

                    b.Property<bool?>("PrimaryWording")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ProductsAggregate")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("WaiverOfSub")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("WaiverOfSubAttachmentAttachmentId")
                        .HasColumnType("INTEGER");

                    b.HasKey("GeneralLiabilityCoverageId");

                    b.HasIndex("AdditionalAttachmentsAttachmentAttachmentId");

                    b.HasIndex("AdditionalInsuredAttachmentAttachmentId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PolicyId")
                        .IsUnique();

                    b.HasIndex("WaiverOfSubAttachmentAttachmentId");

                    b.ToTable("GeneralLiabilityCoverages");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Loss", b =>
                {
                    b.Property<int>("LossId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<double?>("AmountPaid")
                        .HasColumnType("REAL");

                    b.Property<double?>("AmountReserved")
                        .HasColumnType("REAL");

                    b.Property<DateTime?>("DateClaimSubmitted")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateOccurred")
                        .HasColumnType("TEXT");

                    b.Property<string>("LongDescription")
                        .HasColumnType("TEXT");

                    b.Property<bool?>("Open")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ShortDescription")
                        .HasColumnType("TEXT");

                    b.Property<bool?>("Subgrogated")
                        .HasColumnType("INTEGER");

                    b.Property<string>("UserModifiedId")
                        .HasColumnType("TEXT");

                    b.HasKey("LossId");

                    b.HasIndex("PolicyId");

                    b.HasIndex("UserModifiedId");

                    b.ToTable("Losses");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Policy", b =>
                {
                    b.Property<int>("PolicyId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ApplicationId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("CSRId")
                        .HasColumnType("TEXT");

                    b.Property<int?>("CarrierId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("EffectiveDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("ExpirationDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<string>("PolicyNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Premium")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ProducerId")
                        .HasColumnType("TEXT");

                    b.Property<int?>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Status")
                        .HasColumnType("TEXT");

                    b.Property<int?>("WholesalerId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ePolicyId")
                        .HasColumnType("TEXT");

                    b.Property<string>("ePolicyLineId")
                        .HasColumnType("TEXT");

                    b.Property<string>("eType")
                        .HasColumnType("TEXT");

                    b.Property<string>("eTypeCode")
                        .HasColumnType("TEXT");

                    b.HasKey("PolicyId");

                    b.HasIndex("ApplicationId");

                    b.HasIndex("CSRId");

                    b.HasIndex("CarrierId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ProducerId");

                    b.HasIndex("ProductId");

                    b.HasIndex("WholesalerId");

                    b.ToTable("Policies");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.PropertyCoverage", b =>
                {
                    b.Property<int>("PropertyCoverageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("BusinessPersonalProperty")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<int?>("Equipment")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("TEXT");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("INTEGER");

                    b.HasKey("PropertyCoverageId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PolicyId")
                        .IsUnique();

                    b.ToTable("PropertyCoverage");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.RatingBasis", b =>
                {
                    b.Property<int>("RatingBasisId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<double?>("BaseRate")
                        .HasColumnType("REAL");

                    b.Property<string>("Basis")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClassCode")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClassDescription")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("Exposure")
                        .HasColumnType("TEXT");

                    b.Property<int?>("LocationId")
                        .HasColumnType("INTEGER");

                    b.Property<double?>("NetRate")
                        .HasColumnType("REAL");

                    b.Property<double?>("Payroll")
                        .HasColumnType("REAL");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("INTEGER");

                    b.Property<double?>("Premium")
                        .HasColumnType("REAL");

                    b.Property<int?>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("UserModifiedId")
                        .HasColumnType("TEXT");

                    b.HasKey("RatingBasisId");

                    b.HasIndex("LocationId");

                    b.HasIndex("PolicyId");

                    b.HasIndex("ProductId");

                    b.HasIndex("UserModifiedId");

                    b.ToTable("RatingBases");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.UmbrellaCoverage", b =>
                {
                    b.Property<int>("UmbrellaCoverageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("ClaimsMade")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<int?>("DeductibleRetentionAmount")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("EachOccurrence")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("GeneralAggregate")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("HasDeductible")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("HasRetention")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("IsExcess")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("IsUmbrella")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("TEXT");

                    b.Property<bool?>("Occurrence")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("INTEGER");

                    b.HasKey("UmbrellaCoverageId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PolicyId")
                        .IsUnique();

                    b.ToTable("UmbrellaCoverage");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.WorkCompCoverage", b =>
                {
                    b.Property<int>("WorkCompCoverageId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("CreatedById")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<int?>("DiseaseEachEmployee")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("DiseasePolicyLimit")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("EachAccident")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("TEXT");

                    b.Property<bool?>("OwnersOfficersExcluded")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("PerOther")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("PerStatute")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("WaiverOfSub")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("WaiverOfSubAttachmentAttachmentId")
                        .HasColumnType("INTEGER");

                    b.HasKey("WorkCompCoverageId");

                    b.HasIndex("ClientId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PolicyId")
                        .IsUnique();

                    b.HasIndex("WaiverOfSubAttachmentAttachmentId");

                    b.ToTable("WorkCompCoverages");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.Renewal", b =>
                {
                    b.Property<int>("RenewalId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AssignedToId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int?>("CarrierId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<string>("ExpiringPolicyNumber")
                        .HasColumnType("TEXT");

                    b.Property<decimal?>("ExpiringPremium")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("RenewalDate")
                        .HasColumnType("TEXT");

                    b.Property<int?>("WholesalerId")
                        .HasColumnType("INTEGER");

                    b.HasKey("RenewalId");

                    b.HasIndex("AssignedToId");

                    b.HasIndex("CarrierId");

                    b.HasIndex("ClientId");

                    b.HasIndex("PolicyId");

                    b.HasIndex("ProductId");

                    b.HasIndex("WholesalerId");

                    b.ToTable("Renewals");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.Submission", b =>
                {
                    b.Property<int>("SubmissionId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("CarrierId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateDeleted")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<int?>("LeadId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<int?>("Premium")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("PrimaryCarrierContactId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("PrimaryWholesalerContactId")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ProductId")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("RenewalId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Status")
                        .HasColumnType("TEXT");

                    b.Property<int>("StatusInt")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("SubmissionDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("SubmissionStatus")
                        .HasColumnType("TEXT");

                    b.Property<int?>("WholesalerId")
                        .HasColumnType("INTEGER");

                    b.HasKey("SubmissionId");

                    b.HasIndex("CarrierId");

                    b.HasIndex("LeadId");

                    b.HasIndex("ProductId");

                    b.HasIndex("RenewalId");

                    b.HasIndex("WholesalerId");

                    b.ToTable("Submissions");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.SubmissionNote", b =>
                {
                    b.Property<int>("SubmissionNoteId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Deleted")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("SubmissionId")
                        .HasColumnType("INTEGER");

                    b.HasKey("SubmissionNoteId");

                    b.HasIndex("SubmissionId");

                    b.ToTable("SubmissionNotes");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TaskMaster", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int?>("DaysBeforeExpiration")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<string>("ForType")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Important")
                        .HasColumnType("INTEGER");

                    b.Property<int>("OrderNumber")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ParentTaskId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("TaskName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("ParentTaskId");

                    b.ToTable("TaskMasters");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TrackTask", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AssignedToId")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Completed")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateModified")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("GoalDate")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Hidden")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("Highlighted")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasColumnType("TEXT");

                    b.Property<int>("OrderNumber")
                        .HasColumnType("INTEGER");

                    b.Property<int>("RenewalId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("TaskName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("AssignedToId");

                    b.HasIndex("RenewalId");

                    b.ToTable("TrackTasks");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Address", b =>
                {
                    b.Property<int>("AddressId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AddressLine1")
                        .HasColumnType("TEXT");

                    b.Property<string>("AddressLine2")
                        .HasColumnType("TEXT");

                    b.Property<string>("City")
                        .HasColumnType("TEXT");

                    b.Property<string>("Country")
                        .HasColumnType("TEXT");

                    b.Property<string>("PostalCode")
                        .HasColumnType("TEXT");

                    b.Property<string>("State")
                        .HasColumnType("TEXT");

                    b.HasKey("AddressId");

                    b.ToTable("Address");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Application", b =>
                {
                    b.Property<int>("ApplicationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("ApplicationDate")
                        .HasColumnType("TEXT");

                    b.HasKey("ApplicationId");

                    b.ToTable("Application");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.DailyTask", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AssignedToId")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Completed")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("TEXT");

                    b.Property<DateTime?>("DateCreated")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Highlighted")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("Order")
                        .HasColumnType("INTEGER");

                    b.Property<string>("TaskName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("AssignedToId");

                    b.ToTable("DailyTasks");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Driver", b =>
                {
                    b.Property<int>("DriverId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("DateOfBirth")
                        .HasColumnType("TEXT");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsPrimaryDriver")
                        .HasColumnType("INTEGER");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("LicenseExpiryDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("LicenseNumber")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("INTEGER");

                    b.HasKey("DriverId");

                    b.HasIndex("PolicyId");

                    b.ToTable("Drivers");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.FireSearchResultViewModel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("AddressType")
                        .HasColumnType("TEXT");

                    b.Property<string>("DataType")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Parent")
                        .HasColumnType("TEXT");

                    b.Property<string>("Primary")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("FireSearchResultViewModel");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Location", b =>
                {
                    b.Property<int>("LocationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("AddressId")
                        .HasColumnType("INTEGER");

                    b.Property<string>("BuildingName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int?>("BuildingTotalSquareFootage")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("ClientId")
                        .HasColumnType("INTEGER");

                    b.Property<double?>("GrossSales")
                        .HasColumnType("REAL");

                    b.Property<int?>("NumFullTimeEmployees")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("NumPartTimeEmployees")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("NumStories")
                        .HasColumnType("INTEGER");

                    b.Property<int?>("OccupiedSquareFootage")
                        .HasColumnType("INTEGER");

                    b.Property<bool?>("Owner")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SquareFootage")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool?>("Tenant")
                        .HasColumnType("INTEGER");

                    b.Property<string>("YearBuilt")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("LocationId");

                    b.HasIndex("AddressId");

                    b.HasIndex("ClientId");

                    b.ToTable("Locations");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Product", b =>
                {
                    b.Property<int>("ProductId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("Description")
                        .HasColumnType("TEXT");

                    b.Property<string>("LineCode")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("LineName")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("LineNickname")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("ProductId");

                    b.ToTable("Products");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Vehicle", b =>
                {
                    b.Property<int>("VehicleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("CountryOfRegistration")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("LicensePlate")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Make")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Model")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int?>("PolicyId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("RegistrationDate")
                        .HasColumnType("TEXT");

                    b.Property<string>("VIN")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Year")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("VehicleId");

                    b.HasIndex("PolicyId");

                    b.ToTable("Vehicles");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Data.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Surefire.Domain.Accounting.Models.Settlement", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithMany("Settlements")
                        .HasForeignKey("PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Renewals.Models.Renewal", "Renewal")
                        .WithMany("Settlements")
                        .HasForeignKey("RenewalId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedBy");

                    b.Navigation("Policy");

                    b.Navigation("Renewal");
                });

            modelBuilder.Entity("Surefire.Domain.Accounting.Models.SettlementItem", b =>
                {
                    b.HasOne("Surefire.Domain.Accounting.Models.Settlement", "Settlement")
                        .WithMany("SettlementItems")
                        .HasForeignKey("SettlementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Settlement");
                });

            modelBuilder.Entity("Surefire.Domain.Attachments.Models.Attachment", b =>
                {
                    b.HasOne("Surefire.Domain.Attachments.Models.AttachmentGroup", "AttachmentGroup")
                        .WithMany("Attachments")
                        .HasForeignKey("AttachmentGroupId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Carrier")
                        .WithMany("Attachments")
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Claim", null)
                        .WithMany("Attachments")
                        .HasForeignKey("ClaimId");

                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany("Attachments")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Surefire.Domain.Attachments.Models.Folder", "Folder")
                        .WithMany("Attachments")
                        .HasForeignKey("FolderId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithMany("Attachments")
                        .HasForeignKey("PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Renewals.Models.Renewal", "Renewal")
                        .WithMany("Attachments")
                        .HasForeignKey("RenewalId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Renewals.Models.Submission", "Submission")
                        .WithMany("Attachments")
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "UploadedBy")
                        .WithMany()
                        .HasForeignKey("UploadedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AttachmentGroup");

                    b.Navigation("Carrier");

                    b.Navigation("Client");

                    b.Navigation("Folder");

                    b.Navigation("Policy");

                    b.Navigation("Renewal");

                    b.Navigation("Submission");

                    b.Navigation("UploadedBy");
                });

            modelBuilder.Entity("Surefire.Domain.Carriers.Models.Carrier", b =>
                {
                    b.HasOne("Surefire.Domain.Shared.Models.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId");

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Address");

                    b.Navigation("CreatedBy");
                });

            modelBuilder.Entity("Surefire.Domain.Carriers.Models.Credential", b =>
                {
                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Carrier")
                        .WithMany("Credentials")
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.Navigation("Carrier");

                    b.Navigation("CreatedBy");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.BusinessDetails", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Client");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.Client", b =>
                {
                    b.HasOne("Surefire.Domain.Shared.Models.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Data.ApplicationUser", "CSR")
                        .WithMany()
                        .HasForeignKey("CSRId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Contacts.Models.Contact", "PrimaryContact")
                        .WithMany()
                        .HasForeignKey("PrimaryContactId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Surefire.Data.ApplicationUser", "Producer")
                        .WithMany()
                        .HasForeignKey("ProducerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Address");

                    b.Navigation("CSR");

                    b.Navigation("CreatedBy");

                    b.Navigation("PrimaryContact");

                    b.Navigation("Producer");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.ClientNote", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany("ClientNotes")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Client");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.Lead", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Shared.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.LeadNote", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Lead", "Lead")
                        .WithMany("LeadNotes")
                        .HasForeignKey("LeadId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Lead");
                });

            modelBuilder.Entity("Surefire.Domain.Contacts.Models.Contact", b =>
                {
                    b.HasOne("Surefire.Domain.Shared.Models.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId");

                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Carrier")
                        .WithMany("Contacts")
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany("Contacts")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Address");

                    b.Navigation("Carrier");

                    b.Navigation("Client");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.Certificate", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany("Certificates")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.FormDoc", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany("FormDocs")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Forms.Models.FormPdf", "FormPdf")
                        .WithMany()
                        .HasForeignKey("FormPdfId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Clients.Models.Lead", "Lead")
                        .WithMany("FormDocs")
                        .HasForeignKey("LeadId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("FormPdf");

                    b.Navigation("Lead");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.FormDocRevision", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Forms.Models.FormDoc", "FormDoc")
                        .WithMany("FormDocRevisions")
                        .HasForeignKey("FormDocId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedBy");

                    b.Navigation("FormDoc");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.FormPdf", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("Surefire.Domain.Logs.Log", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.AutoCoverage", b =>
                {
                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "AdditionalAttachmentsAttachment")
                        .WithMany()
                        .HasForeignKey("AdditionalAttachmentsAttachmentAttachmentId");

                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "AdditionalInsuredAttachment")
                        .WithMany()
                        .HasForeignKey("AdditionalInsuredAttachmentAttachmentId");

                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithOne("AutoCoverage")
                        .HasForeignKey("Surefire.Domain.Policies.Models.AutoCoverage", "PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "WaiverOfSubAttachment")
                        .WithMany()
                        .HasForeignKey("WaiverOfSubAttachmentAttachmentId");

                    b.Navigation("AdditionalAttachmentsAttachment");

                    b.Navigation("AdditionalInsuredAttachment");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Policy");

                    b.Navigation("WaiverOfSubAttachment");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Claim", b =>
                {
                    b.HasOne("Surefire.Domain.Policies.Models.Policy", null)
                        .WithMany("Claims")
                        .HasForeignKey("PolicyId");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.GeneralLiabilityCoverage", b =>
                {
                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "AdditionalAttachmentsAttachment")
                        .WithMany()
                        .HasForeignKey("AdditionalAttachmentsAttachmentAttachmentId");

                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "AdditionalInsuredAttachment")
                        .WithMany()
                        .HasForeignKey("AdditionalInsuredAttachmentAttachmentId");

                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithOne("GeneralLiabilityCoverage")
                        .HasForeignKey("Surefire.Domain.Policies.Models.GeneralLiabilityCoverage", "PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "WaiverOfSubAttachment")
                        .WithMany()
                        .HasForeignKey("WaiverOfSubAttachmentAttachmentId");

                    b.Navigation("AdditionalAttachmentsAttachment");

                    b.Navigation("AdditionalInsuredAttachment");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Policy");

                    b.Navigation("WaiverOfSubAttachment");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Loss", b =>
                {
                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithMany("Losses")
                        .HasForeignKey("PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "UserModified")
                        .WithMany()
                        .HasForeignKey("UserModifiedId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Policy");

                    b.Navigation("UserModified");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Policy", b =>
                {
                    b.HasOne("Surefire.Domain.Shared.Models.Application", "Application")
                        .WithMany()
                        .HasForeignKey("ApplicationId");

                    b.HasOne("Surefire.Data.ApplicationUser", "CSR")
                        .WithMany()
                        .HasForeignKey("CSRId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Carrier")
                        .WithMany()
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany("Policies")
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "Producer")
                        .WithMany()
                        .HasForeignKey("ProducerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Shared.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Wholesaler")
                        .WithMany()
                        .HasForeignKey("WholesalerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Application");

                    b.Navigation("CSR");

                    b.Navigation("Carrier");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("Producer");

                    b.Navigation("Product");

                    b.Navigation("Wholesaler");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.PropertyCoverage", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithOne("PropertyCoverage")
                        .HasForeignKey("Surefire.Domain.Policies.Models.PropertyCoverage", "PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Policy");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.RatingBasis", b =>
                {
                    b.HasOne("Surefire.Domain.Shared.Models.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithMany("RatingBases")
                        .HasForeignKey("PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Shared.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "UserModified")
                        .WithMany()
                        .HasForeignKey("UserModifiedId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Location");

                    b.Navigation("Policy");

                    b.Navigation("Product");

                    b.Navigation("UserModified");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.UmbrellaCoverage", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithOne("UmbrellaCoverage")
                        .HasForeignKey("Surefire.Domain.Policies.Models.UmbrellaCoverage", "PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Policy");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.WorkCompCoverage", b =>
                {
                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Data.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithOne("WorkCompCoverage")
                        .HasForeignKey("Surefire.Domain.Policies.Models.WorkCompCoverage", "PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Attachments.Models.Attachment", "WaiverOfSubAttachment")
                        .WithMany()
                        .HasForeignKey("WaiverOfSubAttachmentAttachmentId");

                    b.Navigation("Client");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Policy");

                    b.Navigation("WaiverOfSubAttachment");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.Renewal", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Carrier")
                        .WithMany()
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Clients.Models.Client", "Client")
                        .WithMany()
                        .HasForeignKey("ClientId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Policies.Models.Policy", "Policy")
                        .WithMany("Renewals")
                        .HasForeignKey("PolicyId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Shared.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Wholesaler")
                        .WithMany()
                        .HasForeignKey("WholesalerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("AssignedTo");

                    b.Navigation("Carrier");

                    b.Navigation("Client");

                    b.Navigation("Policy");

                    b.Navigation("Product");

                    b.Navigation("Wholesaler");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.Submission", b =>
                {
                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Carrier")
                        .WithMany()
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Clients.Models.Lead", "Lead")
                        .WithMany("Submissions")
                        .HasForeignKey("LeadId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Shared.Models.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Renewals.Models.Renewal", "Renewal")
                        .WithMany("Submissions")
                        .HasForeignKey("RenewalId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Carriers.Models.Carrier", "Wholesaler")
                        .WithMany()
                        .HasForeignKey("WholesalerId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Carrier");

                    b.Navigation("Lead");

                    b.Navigation("Product");

                    b.Navigation("Renewal");

                    b.Navigation("Wholesaler");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.SubmissionNote", b =>
                {
                    b.HasOne("Surefire.Domain.Renewals.Models.Submission", "Submission")
                        .WithMany("SubmissionNotes")
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Submission");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TaskMaster", b =>
                {
                    b.HasOne("Surefire.Domain.Renewals.Models.TaskMaster", null)
                        .WithMany()
                        .HasForeignKey("ParentTaskId")
                        .OnDelete(DeleteBehavior.Restrict);
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.TrackTask", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Surefire.Domain.Renewals.Models.Renewal", "Renewal")
                        .WithMany("TrackTasks")
                        .HasForeignKey("RenewalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssignedTo");

                    b.Navigation("Renewal");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.DailyTask", b =>
                {
                    b.HasOne("Surefire.Data.ApplicationUser", "AssignedTo")
                        .WithMany()
                        .HasForeignKey("AssignedToId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("AssignedTo");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Driver", b =>
                {
                    b.HasOne("Surefire.Domain.Policies.Models.Policy", null)
                        .WithMany("Drivers")
                        .HasForeignKey("PolicyId");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Location", b =>
                {
                    b.HasOne("Surefire.Domain.Shared.Models.Address", "Address")
                        .WithMany()
                        .HasForeignKey("AddressId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Surefire.Domain.Clients.Models.Client", null)
                        .WithMany("Locations")
                        .HasForeignKey("ClientId");

                    b.Navigation("Address");
                });

            modelBuilder.Entity("Surefire.Domain.Shared.Models.Vehicle", b =>
                {
                    b.HasOne("Surefire.Domain.Policies.Models.Policy", null)
                        .WithMany("Vehicles")
                        .HasForeignKey("PolicyId");
                });

            modelBuilder.Entity("Surefire.Domain.Accounting.Models.Settlement", b =>
                {
                    b.Navigation("SettlementItems");
                });

            modelBuilder.Entity("Surefire.Domain.Attachments.Models.AttachmentGroup", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("Surefire.Domain.Attachments.Models.Folder", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("Surefire.Domain.Carriers.Models.Carrier", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Contacts");

                    b.Navigation("Credentials");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.Client", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Certificates");

                    b.Navigation("ClientNotes");

                    b.Navigation("Contacts");

                    b.Navigation("FormDocs");

                    b.Navigation("Locations");

                    b.Navigation("Policies");
                });

            modelBuilder.Entity("Surefire.Domain.Clients.Models.Lead", b =>
                {
                    b.Navigation("FormDocs");

                    b.Navigation("LeadNotes");

                    b.Navigation("Submissions");
                });

            modelBuilder.Entity("Surefire.Domain.Forms.Models.FormDoc", b =>
                {
                    b.Navigation("FormDocRevisions");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Claim", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("Surefire.Domain.Policies.Models.Policy", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("AutoCoverage");

                    b.Navigation("Claims");

                    b.Navigation("Drivers");

                    b.Navigation("GeneralLiabilityCoverage");

                    b.Navigation("Losses");

                    b.Navigation("PropertyCoverage");

                    b.Navigation("RatingBases");

                    b.Navigation("Renewals");

                    b.Navigation("Settlements");

                    b.Navigation("UmbrellaCoverage");

                    b.Navigation("Vehicles");

                    b.Navigation("WorkCompCoverage");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.Renewal", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Settlements");

                    b.Navigation("Submissions");

                    b.Navigation("TrackTasks");
                });

            modelBuilder.Entity("Surefire.Domain.Renewals.Models.Submission", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("SubmissionNotes");
                });
#pragma warning restore 612, 618
        }
    }
}
