# Custom Fonts Directory

This directory contains TTF font files that will be used by the Syncfusion PDF conversion process.

## Aptos Font Family

To use Aptos fonts in your proposal PDFs, place the following TTF files in this directory:

- `aptos.ttf` - Aptos Regular
- `aptos-light.ttf` - Aptos Light  
- `aptos-bold.ttf` - Aptos Bold
- `aptos-black.ttf` - Aptos Black
- `aptos-display.ttf` - Aptos Display

## How to Obtain Aptos Fonts

### Option 1: From Windows 11
If you have Windows 11, Aptos fonts are included by default. You can find them at:
```
C:\Windows\Fonts\
```
Look for files starting with "aptos" and copy the TTF versions to this directory.

### Option 2: From Microsoft Office
If you have a recent version of Microsoft Office, Aptos fonts may be installed with Office.

### Option 3: Download from Microsoft
Aptos fonts may be available for download from Microsoft's official font repositories.

## File Naming Convention

The PackagerService expects these exact filenames:
- `aptos.ttf` (Regular weight)
- `aptos-light.ttf` (Light weight)
- `aptos-bold.ttf` (Bold weight)
- `aptos-black.ttf` (Black weight)
- `aptos-display.ttf` (Display variant)

## Notes

- Only TTF format is supported
- Font files are loaded automatically when creating proposal packages
- If font files are not found, the system will log warnings but continue processing
- Make sure you have proper licensing rights to use these fonts in your application

## Testing

After adding font files, create a new proposal package to test that the fonts are being embedded correctly in the PDF output. 