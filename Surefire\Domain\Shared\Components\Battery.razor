﻿<div class="batteryContainer">
    <div style="width: @GetAdjustedWidth(); background-color: @GetFillColor()" class="batteryFill @(PerComplete == 100 ? "shimmer" : "")">
    </div>
</div>

@code {
    [Parameter]
    public int PerComplete { get; set; } = 0;

    private string GetAdjustedWidth()
    {
        if (PerComplete == 100)
        {
            return "100%";
        }
        return $"{Math.Max(10, Math.Min(90, PerComplete))}%";
    }

    private string GetFillColor()
    {
        return PerComplete == 100 ? "green" : "#4d4d4d";
    }
}