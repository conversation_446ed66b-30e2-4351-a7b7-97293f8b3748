@page "/firebar-demo"
@using Domain.Shared.Components

<PageTitle>FireBar Demo</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">🔥 FireBar Component Demo</h1>
            <p class="lead">A unique progress bar component using the Surefire logo with animated fire effects!</p>
        </div>
    </div>

    <!-- Interactive Demo -->
    <div class="row mb-5">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3>Interactive Demo</h3>
                </div>
                <div class="card-body text-center">
                    <div style="width: 200px; height: 200px; margin: 0 auto;">
                        <Sicks Progress="@currentProgress" 
                                Width="200px" 
                                Height="200px"
                                SolidColor="@selectedSolidColor"
                                SolidColorEnd="@selectedSolidColorEnd"
                                ShowPercentage="@showPercentage" />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3>Controls</h3>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Progress: @currentProgress.ToString("F0")%</label>
                        <input type="range" class="form-range" min="0" max="100" step="1" 
                               @bind="currentProgress" @bind:event="oninput" />
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" @bind="showPercentage" id="showPercentage">
                            <label class="form-check-label" for="showPercentage">
                                Show Percentage
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Color Theme:</label>
                        <select class="form-select" @onchange="OnColorThemeChanged">
                            <option value="green">Green (Success)</option>
                            <option value="blue">Blue (Info)</option>
                            <option value="purple">Purple (Primary)</option>
                            <option value="orange">Orange (Warning)</option>
                            <option value="red">Red (Danger)</option>
                        </select>
                    </div>

                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" @onclick="StartAutoProgress">
                            @(isAutoRunning ? "Stop" : "Start") Auto Progress
                        </button>
                        <button class="btn btn-secondary" @onclick="ResetProgress">Reset</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Size Variations -->
    <div class="row mb-5">
        <div class="col-12">
            <h2>Size Variations</h2>
            <div class="row text-center">
                <div class="col-md-3 mb-3">
                    <h5>Small (75px)</h5>
                    <FireBar Progress="75" Width="75px" Height="75px" ShowPercentage="false" />
                </div>
                <div class="col-md-3 mb-3">
                    <h5>Medium (100px)</h5>
                    <FireBar Progress="50" Width="100px" Height="100px" />
                </div>
                <div class="col-md-3 mb-3">
                    <h5>Large (150px)</h5>
                    <FireBar Progress="25" Width="150px" Height="150px" />
                </div>
                <div class="col-md-3 mb-3">
                    <h5>Extra Large (200px)</h5>
                    <FireBar Progress="90" Width="200px" Height="200px" />
                </div>
            </div>
        </div>
    </div>

    <!-- Color Themes -->
    <div class="row mb-5">
        <div class="col-12">
            <h2>Color Themes</h2>
            <div class="row text-center">
                <div class="col-md-2 mb-3">
                    <h6>Success</h6>
                    <FireBar Progress="80" Width="100px" Height="100px" 
                            SolidColor="#28a745" SolidColorEnd="#20c997" ShowPercentage="false" />
                </div>
                <div class="col-md-2 mb-3">
                    <h6>Info</h6>
                    <FireBar Progress="60" Width="100px" Height="100px" 
                            SolidColor="#17a2b8" SolidColorEnd="#6f42c1" ShowPercentage="false" />
                </div>
                <div class="col-md-2 mb-3">
                    <h6>Warning</h6>
                    <FireBar Progress="40" Width="100px" Height="100px" 
                            SolidColor="#ffc107" SolidColorEnd="#fd7e14" ShowPercentage="false" />
                </div>
                <div class="col-md-2 mb-3">
                    <h6>Danger</h6>
                    <FireBar Progress="20" Width="100px" Height="100px" 
                            SolidColor="#dc3545" SolidColorEnd="#e83e8c" ShowPercentage="false" />
                </div>
                <div class="col-md-2 mb-3">
                    <h6>Primary</h6>
                    <FireBar Progress="70" Width="100px" Height="100px" 
                            SolidColor="#007bff" SolidColorEnd="#6610f2" ShowPercentage="false" />
                </div>
                <div class="col-md-2 mb-3">
                    <h6>Dark</h6>
                    <FireBar Progress="90" Width="100px" Height="100px" 
                            SolidColor="#343a40" SolidColorEnd="#6c757d" ShowPercentage="false" />
                </div>
            </div>
        </div>
    </div>

    <!-- Usage Examples -->
    <div class="row">
        <div class="col-12">
            <h2>Usage Examples</h2>
            <div class="card">
                <div class="card-body">
                    <h5>Basic Usage</h5>
                    <pre><code>&lt;FireBar Progress="75" /&gt;</code></pre>
                    
                    <h5 class="mt-4">Custom Size and Colors</h5>
                    <pre><code>&lt;FireBar Progress="50" 
         Width="200px" 
         Height="200px"
         SolidColor="#007bff" 
         SolidColorEnd="#6610f2"
         ShowPercentage="true" /&gt;</code></pre>

                    <h5 class="mt-4">Available Parameters</h5>
                    <ul>
                        <li><strong>Progress</strong> (double): Progress value from 0 to 100</li>
                        <li><strong>Width</strong> (string): Component width (default: "150px")</li>
                        <li><strong>Height</strong> (string): Component height (default: "150px")</li>
                        <li><strong>SolidColor</strong> (string): Start color for filled portion (default: "#28a745")</li>
                        <li><strong>SolidColorEnd</strong> (string): End color for filled portion (default: "#20c997")</li>
                        <li><strong>BorderColor</strong> (string): Border color (default: "#333")</li>
                        <li><strong>BorderWidth</strong> (string): Border width (default: "2")</li>
                        <li><strong>BorderOpacity</strong> (string): Border opacity (default: "0.3")</li>
                        <li><strong>TextColor</strong> (string): Percentage text color (default: "#fff")</li>
                        <li><strong>ShowPercentage</strong> (bool): Show percentage text (default: true)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private double currentProgress = 45;
    private bool showPercentage = true;
    private bool isAutoRunning = false;
    private string selectedSolidColor = "#28a745";
    private string selectedSolidColorEnd = "#20c997";
    private Timer? autoProgressTimer;

    private void OnColorThemeChanged(ChangeEventArgs e)
    {
        var theme = e.Value?.ToString();
        (selectedSolidColor, selectedSolidColorEnd) = theme switch
        {
            "blue" => ("#17a2b8", "#6f42c1"),
            "purple" => ("#6f42c1", "#e83e8c"),
            "orange" => ("#ffc107", "#fd7e14"),
            "red" => ("#dc3545", "#e83e8c"),
            _ => ("#28a745", "#20c997") // green (default)
        };
        StateHasChanged();
    }

    private void StartAutoProgress()
    {
        if (isAutoRunning)
        {
            autoProgressTimer?.Dispose();
            isAutoRunning = false;
        }
        else
        {
            isAutoRunning = true;
            currentProgress = 0;
            autoProgressTimer = new Timer(async _ =>
            {
                await InvokeAsync(() =>
                {
                    currentProgress += 1;
                    if (currentProgress >= 100)
                    {
                        currentProgress = 0;
                    }
                    StateHasChanged();
                });
            }, null, TimeSpan.Zero, TimeSpan.FromMilliseconds(100));
        }
    }

    private void ResetProgress()
    {
        autoProgressTimer?.Dispose();
        isAutoRunning = false;
        currentProgress = 0;
    }

    public void Dispose()
    {
        autoProgressTimer?.Dispose();
    }
} 