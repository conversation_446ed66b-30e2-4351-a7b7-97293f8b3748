using Surefire.Domain.Agents.Interfaces;
using Surefire.Domain.Agents.Models;
using Surefire.Domain.Agents.Services;
using Surefire.Domain.Agents.TaskAgents;

namespace Surefire.Domain.Agents.Services
{
    /// <summary>
    /// Orchestrates task agent execution and coordination
    /// </summary>
    public class TaskAgentOrchestrator : ITaskAgentOrchestrator
    {
        private readonly ITaskAgentRegistry _agentRegistry;
        private readonly IParameterExtractionService _parameterExtraction;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TaskAgentOrchestrator> _logger;

        public TaskAgentOrchestrator(
            ITaskAgentRegistry agentRegistry,
            IParameterExtractionService parameterExtraction,
            IServiceProvider serviceProvider,
            ILogger<TaskAgentOrchestrator> logger)
        {
            _agentRegistry = agentRegistry ?? throw new ArgumentNullException(nameof(agentRegistry));
            _parameterExtraction = parameterExtraction ?? throw new ArgumentNullException(nameof(parameterExtraction));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Get available agents for a specific context and category
        /// </summary>
        public IEnumerable<TaskAgentDefinition> GetAvailableAgents(AgentExecutionContext context, string? category = null)
        {
            try
            {
                var agents = _agentRegistry.GetAvailableAgents();
                
                return agents.Where(agent => 
                    (context == AgentExecutionContext.AIChat && agent.SupportsAIChat) ||
                    (context == AgentExecutionContext.ActionButton && agent.SupportsActionButton))
                    .Where(agent => string.IsNullOrEmpty(category) || agent.Category.Equals(category, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available agents for context {Context} and category {Category}", context, category);
                return new List<TaskAgentDefinition>();
            }
        }

        /// <summary>
        /// Process a natural language request from AI chat
        /// </summary>
        public async Task<TaskAgentIntentResult> ProcessChatRequestAsync(
            string userInput,
            string userId,
            string sessionId,
            Dictionary<string, object>? context = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Processing AI chat input: {Input}", userInput);

                // 1. Find matching agent
                var matchResult = _agentRegistry.FindBestMatch(userInput, AgentExecutionContext.AIChat);
                if (matchResult.Agent == null)
                {
                    return new TaskAgentIntentResult
                    {
                        AgentIdentified = false,
                        Confidence = 0.0,
                        ReadyForExecution = false,
                        AlternativeSuggestions = new List<string> { "Try rephrasing your request", "Check available actions" }
                    };
                }

                _logger.LogInformation("Found matching agent: {AgentId} with confidence {Confidence}", 
                    matchResult.Agent.AgentId, matchResult.Confidence);

                // 2. Extract parameters from user input
                var extractionResult = await _parameterExtraction.ExtractParametersAsync(
                    userInput, matchResult.Agent.Parameters, context, cancellationToken);

                // 3. Return intent result with parameter validation
                return new TaskAgentIntentResult
                {
                    AgentIdentified = true,
                    AgentId = matchResult.Agent.AgentId,
                    Confidence = matchResult.Confidence,
                    ParameterValidation = extractionResult,
                    ReadyForExecution = extractionResult.IsComplete
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing chat request");
                return new TaskAgentIntentResult
                {
                    AgentIdentified = false,
                    Confidence = 0.0,
                    ReadyForExecution = false
                };
            }
        }

        /// <summary>
        /// Continue execution after user provides clarification for missing parameters
        /// </summary>
        public async Task<TaskAgentIntentResult> ContinueWithClarificationAsync(
            string agentId,
            string clarificationInput,
            Dictionary<string, object> existingParameters,
            string userId,
            string sessionId,
            Dictionary<string, object>? context = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var agentDefinition = _agentRegistry.GetAgentDefinition(agentId);
                if (agentDefinition == null)
                {
                    return new TaskAgentIntentResult
                    {
                        AgentIdentified = false,
                        Confidence = 0.0,
                        ReadyForExecution = false
                    };
                }

                // Extract additional parameters from clarification
                var clarificationResult = await _parameterExtraction.ExtractParametersAsync(
                    clarificationInput, agentDefinition.Parameters, context, cancellationToken);

                // Merge with existing parameters
                var mergedParameters = new Dictionary<string, object>(existingParameters);
                foreach (var param in clarificationResult.ValidatedParameters)
                {
                    mergedParameters[param.Key] = param.Value;
                }

                // Re-validate all parameters
                var finalValidation = await _parameterExtraction.ValidateParametersAsync(
                    mergedParameters, agentDefinition.Parameters);

                return new TaskAgentIntentResult
                {
                    AgentIdentified = true,
                    AgentId = agentId,
                    Confidence = 1.0, // High confidence since agent was already identified
                    ParameterValidation = finalValidation,
                    ReadyForExecution = finalValidation.IsComplete
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error continuing with clarification for agent {AgentId}", agentId);
                return new TaskAgentIntentResult
                {
                    AgentIdentified = false,
                    Confidence = 0.0,
                    ReadyForExecution = false
                };
            }
        }

        /// <summary>
        /// Execute an agent that has been fully validated and is ready for execution
        /// </summary>
        public async Task<TaskAgentResult> ExecuteValidatedAgentAsync(
            string agentId,
            Dictionary<string, object> validatedParameters,
            AgentExecutionContext executionContext,
            string userId,
            string? sessionId = null,
            Dictionary<string, object>? pageContext = null,
            CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;
            
            try
            {
                _logger.LogInformation("=== EXECUTING VALIDATED AGENT ===");
                _logger.LogInformation("Agent ID: {AgentId}", agentId);
                _logger.LogInformation("User ID: {UserId}", userId);
                _logger.LogInformation("Execution Context: {ExecutionContext}", executionContext);
                _logger.LogInformation("Session ID: {SessionId}", sessionId ?? "None");
                _logger.LogInformation("Validated Parameters Count: {ParameterCount}", validatedParameters?.Count ?? 0);
                _logger.LogInformation("Page Context Count: {PageContextCount}", pageContext?.Count ?? 0);

                // Log parameters
                if (validatedParameters != null)
                {
                    foreach (var param in validatedParameters)
                    {
                        _logger.LogInformation("  Parameter: {Key} = {Value} (Type: {Type})", 
                            param.Key, param.Value, param.Value?.GetType().Name ?? "null");
                    }
                }

                // Log page context
                if (pageContext != null)
                {
                    foreach (var context in pageContext)
                    {
                        _logger.LogInformation("  Context: {Key} = {Value} (Type: {Type})", 
                            context.Key, context.Value, context.Value?.GetType().Name ?? "null");
                    }
                }

                _logger.LogInformation("Attempting to get agent instance...");
                var agent = await GetAgentInstanceAsync(agentId);
                if (agent == null)
                {
                    _logger.LogError("❌ Agent {AgentId} not found or could not be instantiated", agentId);
                    return new TaskAgentResult
                    {
                        Success = false,
                        OutcomeType = AgentOutcomeType.Error,
                        ErrorMessage = $"Agent {agentId} not found or could not be instantiated",
                        ExecutionTime = DateTime.UtcNow - startTime
                    };
                }

                _logger.LogInformation("✅ Agent instance obtained: {AgentType}", agent.GetType().Name);
                _logger.LogInformation("Agent Definition - Name: {Name}, Category: {Category}", 
                    agent.Definition?.Name ?? "Unknown", agent.Definition?.Category ?? "Unknown");

                // Create the agent request
                var request = new TaskAgentRequest
                {
                    AgentId = agentId,
                    Parameters = validatedParameters,
                    Context = executionContext,
                    UserId = userId,
                    SessionId = sessionId ?? string.Empty,
                    PageContext = pageContext ?? new Dictionary<string, object>()
                };

                _logger.LogInformation("Created agent request for agent: {AgentId}", request.AgentId);
                _logger.LogInformation("Executing agent...");

                // Execute the agent
                var result = await agent.ExecuteAsync(request, cancellationToken);

                _logger.LogInformation("=== AGENT EXECUTION COMPLETED ===");
                _logger.LogInformation("Success: {Success}", result.Success);
                _logger.LogInformation("Outcome Type: {OutcomeType}", result.OutcomeType);
                _logger.LogInformation("Execution Time: {ExecutionTime}ms", result.ExecutionTime.TotalMilliseconds);
                _logger.LogInformation("Message Length: {MessageLength}", result.Message?.Length ?? 0);
                _logger.LogInformation("Error Message: {ErrorMessage}", result.ErrorMessage ?? "None");
                _logger.LogInformation("Suggestions Count: {SuggestionsCount}", result.Suggestions?.Count ?? 0);
                _logger.LogInformation("Has Data: {HasData}", result.Data != null);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "=== CRITICAL ERROR DURING AGENT EXECUTION ===");
                _logger.LogError("Agent ID: {AgentId}", agentId);
                _logger.LogError("Error: {ErrorMessage}", ex.Message);
                _logger.LogError("Stack trace: {StackTrace}", ex.StackTrace);
                
                return new TaskAgentResult
                {
                    Success = false,
                    OutcomeType = AgentOutcomeType.Error,
                    ErrorMessage = $"Agent execution failed: {ex.Message}",
                    ExecutionTime = DateTime.UtcNow - startTime
                };
            }
        }

        /// <summary>
        /// Execute agent from action button
        /// </summary>
        public async Task<TaskAgentResult> ExecuteFromButtonAsync(
            string agentId,
            Dictionary<string, object> parameters,
            string userId,
            Dictionary<string, object>? pageContext = null,
            bool skipConfirmation = false,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Executing agent {AgentId} from button with parameters: {Parameters}", agentId, parameters);
                
                return await ExecuteValidatedAgentAsync(agentId, parameters, AgentExecutionContext.ActionButton, userId, null, pageContext, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing agent {AgentId} from button", agentId);
                return new TaskAgentResult
                {
                    Success = false,
                    OutcomeType = AgentOutcomeType.Error,
                    ErrorMessage = $"Error executing agent: {ex.Message}",
                    ExecutionTime = TimeSpan.Zero
                };
            }
        }

        /// <summary>
        /// Execute agent from AI chat input (for backward compatibility)
        /// </summary>
        public async Task<TaskAgentResult> ExecuteFromChatAsync(
            string userInput, 
            string userId, 
            Dictionary<string, object>? context = null,
            CancellationToken cancellationToken = default)
        {
            var sessionId = Guid.NewGuid().ToString();
            var intentResult = await ProcessChatRequestAsync(userInput, userId, sessionId, context, cancellationToken);
            
            if (!intentResult.ReadyForExecution || intentResult.AgentId == null || intentResult.ParameterValidation == null)
            {
                return new TaskAgentResult
                {
                    Success = false,
                    OutcomeType = AgentOutcomeType.Confirmation,
                    Message = "Additional information needed",
                    Data = new
                    {
                        ExtractedParameters = intentResult.ParameterValidation?.ValidatedParameters,
                        MissingParameters = intentResult.ParameterValidation?.MissingParameters,
                        AmbiguousParameters = intentResult.ParameterValidation?.AmbiguousParameters,
                        ClarificationMessage = intentResult.ParameterValidation?.ClarificationMessage
                    },
                    ExecutionTime = TimeSpan.Zero
                };
            }

            return await ExecuteValidatedAgentAsync(
                intentResult.AgentId, 
                intentResult.ParameterValidation.ValidatedParameters, 
                AgentExecutionContext.AIChat, 
                userId, 
                sessionId, 
                context, 
                cancellationToken);
        }

        /// <summary>
        /// Generate action button configuration for an agent
        /// </summary>
        public async Task<ActionButtonConfig?> GenerateActionButtonConfigAsync(
            string agentId, 
            Dictionary<string, object>? pageContext = null)
        {
            try
            {
                var agentDefinition = _agentRegistry.GetAgentDefinition(agentId);
                if (agentDefinition == null || !agentDefinition.SupportsActionButton)
                {
                    return null;
                }

                var agent = await GetAgentInstanceAsync(agentId);
                if (agent == null)
                {
                    return null;
                }

                // Extract and validate parameters from page context
                var extractedParams = new Dictionary<string, object>();
                var missingParams = new List<string>();

                foreach (var paramDef in agentDefinition.Parameters.Where(p => p.IsRequired))
                {
                    if (pageContext?.TryGetValue(paramDef.Name, out var value) == true && value != null)
                    {
                        extractedParams[paramDef.Name] = value;
                    }
                    else
                    {
                        missingParams.Add(paramDef.Name);
                    }
                }

                // Add optional parameters from context
                foreach (var paramDef in agentDefinition.Parameters.Where(p => !p.IsRequired))
                {
                    if (pageContext?.TryGetValue(paramDef.Name, out var value) == true && value != null)
                    {
                        extractedParams[paramDef.Name] = value;
                    }
                    else if (paramDef.DefaultValue != null)
                    {
                        extractedParams[paramDef.Name] = paramDef.DefaultValue;
                    }
                }

                var buttonConfig = new ActionButtonConfig
                {
                    AgentId = agentId,
                    ButtonText = GetButtonText(agentDefinition),
                    Description = agentDefinition.Description,
                    PreFilledParameters = extractedParams,
                    MissingParameters = missingParams,
                    IsReady = missingParams.Count == 0,
                    ShowConfirmation = agentDefinition.RequiresConfirmation && missingParams.Count == 0,
                    ConfirmationMessage = missingParams.Count == 0 ? 
                        $"Are you sure you want to {agentDefinition.Name.ToLower()}?" : 
                        null
                };

                return buttonConfig;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating action button config for agent {AgentId}", agentId);
                return null;
            }
        }

        /// <summary>
        /// Get an instance of the specified agent
        /// </summary>
        private async Task<ITaskAgent?> GetAgentInstanceAsync(string agentId)
        {
            try
            {
                var agentDefinition = _agentRegistry.GetAgentDefinition(agentId);
                if (agentDefinition == null)
                {
                    return null;
                }

                // Try to resolve the agent from DI container
                var agentType = _agentRegistry.GetAgentType(agentId);
                if (agentType != null)
                {
                    var agent = _serviceProvider.GetService(agentType) as ITaskAgent;
                    return agent;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting agent instance for {AgentId}", agentId);
                return null;
            }
        }

        /// <summary>
        /// Get clarification questions for missing/ambiguous parameters
        /// </summary>
        private List<string> GetClarificationQuestions(TaskAgentDefinition agent, AgentParameterValidation extractionResult)
        {
            var questions = new List<string>();

            foreach (var missingParam in extractionResult.MissingParameters)
            {
                var paramDef = agent.Parameters.FirstOrDefault(p => p.Name == missingParam);
                if (paramDef?.ClarificationQuestion != null)
                {
                    questions.Add(paramDef.ClarificationQuestion);
                }
            }

            foreach (var ambiguousParam in extractionResult.AmbiguousParameters)
            {
                var paramDef = agent.Parameters.FirstOrDefault(p => p.Name == ambiguousParam);
                if (paramDef?.ClarificationQuestion != null)
                {
                    questions.Add($"Could you clarify: {paramDef.ClarificationQuestion}");
                }
            }

            return questions;
        }

        /// <summary>
        /// Generate appropriate button text for an agent
        /// </summary>
        private string GetButtonText(TaskAgentDefinition agent)
        {
            return agent.OutcomeType switch
            {
                AgentOutcomeType.Navigation => $"Open {agent.Name}",
                AgentOutcomeType.Download => $"Download {agent.Name}",
                AgentOutcomeType.Message => $"Send {agent.Name}",
                _ => agent.Name
            };
        }
    }
} 