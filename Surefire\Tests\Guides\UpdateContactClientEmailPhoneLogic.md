# Contact and Client Email/Phone Migration Documentation

## Overview
This document tracks the migration from legacy email/phone fields to normalized tables in the Contacts and Clients systems.

## Model Changes

### Contact Model Changes
- Removed legacy fields:
  - `Email` (string)
  - `EmailAlternate` (string)
  - `Phone` (string)
  - `Fax` (string)
  - `Mobile` (string)
- Kept new normalized collections:
  - `ICollection<PhoneNumber> PhoneNumbers`
  - `ICollection<EmailAddress> EmailAddresses`
- Kept primary relationship fields:
  - `int? PrimaryPhoneId`
  - `PhoneNumber? PrimaryPhone`
  - `int? PrimaryEmailId`
  - `EmailAddress? PrimaryEmail`

### Client Model Changes
- Removed unused collections:
  - `ICollection<PhoneNumber> PhoneNumbers`
  - `ICollection<EmailAddress> EmailAddresses`
- Removed primary relationship fields:
  - `int? PrimaryPhoneId`
  - `PhoneNumber? PrimaryPhone`
  - `int? PrimaryEmailId`
  - `EmailAddress? PrimaryEmail`
- Kept legacy fields (for backward compatibility):
  - `PhoneNumber` (string)
  - `Email` (string)

## Database Context Changes
- Removed Client's PhoneNumbers and EmailAddresses relationships from ModelBuilderGroups.cs
- No explicit configuration was needed for PrimaryPhone/PrimaryEmail relationships in Client model

## Phone Type Mapping
When updating code, ensure the following mappings are used consistently:

### Legacy to New Phone Type Mapping
| Legacy Field | New PhoneType Enum | Description |
|--------------|-------------------|-------------|
| `Phone`      | `PhoneType.Office` | Main office/business phone |
| `Mobile`     | `PhoneType.Mobile` | Mobile/cell phone |
| `Fax`        | `PhoneType.Fax`    | Fax machine number |
| N/A          | `PhoneType.Home`   | Home phone number |
| N/A          | `PhoneType.Other`  | Any other type (requires TypeOther description) |

### Additional Legacy Text Mappings
When encountering these terms in code or UI, map them as follows:
- "Phone" → `PhoneType.Office` (0)
- "MainOffice" → `PhoneType.Office` (0)
- "Cell" → `PhoneType.Mobile` (1)

### UI Display Text
When displaying phone types in the UI, use these exact strings:
- "Office"
- "Mobile"
- "Home"
- "Fax"
- "Other"

## Updated Methods

### GetAllEmailAddresses()
```csharp
public List<string> GetAllEmailAddresses()
{
    var emailAddresses = new List<string>();

    // Add client's legacy email if it exists
    if (!string.IsNullOrEmpty(selectedClient?.Email))
    {
        emailAddresses.Add(selectedClient.Email);
    }

    // Add all contact email addresses
    if (selectedClient?.Contacts != null)
    {
        foreach (var contact in selectedClient.Contacts)
        {
            // Add primary email if it exists
            if (contact.PrimaryEmail != null)
            {
                emailAddresses.Add(contact.PrimaryEmail.Email);
            }

            // Add all other email addresses
            if (contact.EmailAddresses != null)
            {
                emailAddresses.AddRange(
                    contact.EmailAddresses
                        .Where(e => !e.IsPrimary) // Skip primary as it's already added
                        .Select(e => e.Email)
                );
            }
        }
    }

    return emailAddresses.Where(email => !string.IsNullOrEmpty(email)).Distinct().ToList();
}
```

### GetClientAndContactPhoneNumbers()
```csharp
public List<string> GetClientAndContactPhoneNumbers(Surefire.Domain.Clients.Models.Client? selectedClient)
{
    var phoneNumbers = new List<string>();

    // Add client's legacy phone number if it exists
    if (selectedClient != null && !string.IsNullOrEmpty(selectedClient.PhoneNumber))
    {
        phoneNumbers.Add(selectedClient.PhoneNumber);
    }

    // Add contact phone numbers
    if (selectedClient?.Contacts != null)
    {
        foreach (var contact in selectedClient.Contacts)
        {
            // Add primary phone if it exists
            if (contact.PrimaryPhone != null)
            {
                phoneNumbers.Add(contact.PrimaryPhone.Number);
            }

            // Add all other phone numbers
            if (contact.PhoneNumbers != null)
            {
                phoneNumbers.AddRange(
                    contact.PhoneNumbers
                        .Where(p => !p.IsPrimary) // Skip primary as it's already added
                        .Select(p => p.Number)
                );
            }
        }
    }

    return phoneNumbers.Where(phone => !string.IsNullOrEmpty(phone)).Distinct().ToList();
}
```

## PhoneNumber Model Structure
```csharp
public enum PhoneType
{
    Office = 0,
    Mobile = 1,
    Home = 2,
    Fax = 3,
    Other = 4
}

public class PhoneNumber
{
    public int PhoneNumberId { get; set; }
    public string Number { get; set; }
    public string? Extension { get; set; }
    public PhoneType Type { get; set; }
    public string? TypeOther { get; set; }
    public bool IsPrimary { get; set; }
    public bool SMS { get; set; }
    public DateTime DateCreated { get; set; }
    public DateTime? DateModified { get; set; }
    
    // Navigation properties
    public int? ContactId { get; set; }
    public Contact? Contact { get; set; }
    public int? ClientId { get; set; }
    public Client? Client { get; set; }
}
```

## EmailAddress Model Structure
```csharp
public class EmailAddress
{
    public int EmailAddressId { get; set; }
    public string Email { get; set; }
    public string? Label { get; set; }
    public bool IsPrimary { get; set; }
    public DateTime DateCreated { get; set; }
    public DateTime? DateModified { get; set; }
    
    // Navigation properties
    public int? ContactId { get; set; }
    public Contact? Contact { get; set; }
    public int? ClientId { get; set; }
    public Client? Client { get; set; }
}
```

## Migration Notes
- Data has been migrated ensuring:
  - No duplicate phone numbers or emails are inserted
  - Correct type codes are used for each phone number
  - Primary phone/email is set to matching number/email if it exists
  - Primary phone/email is set to the only number/email if there is just one
  - All data is clean and validated

## Code Update Guidelines
1. When updating code that references old phone fields:
   - Document the old field being replaced
   - Map it to the correct new PhoneType enum value
   - Ensure UI text matches the standardized display text
2. When adding new phone number handling:
   - Use the PhoneType enum values
   - Include proper validation for TypeOther when Type is Other
   - Maintain consistent UI text display

## TODO
- [ ] Review and update any services that use the removed fields
- [ ] Update any DTOs that might reference the removed fields
- [ ] Update any UI components that display or edit these fields
- [ ] Create database migration for the model changes
- [ ] Test the changes in development environment
- [ ] Plan production deployment strategy
- [ ] Verify all UI text matches standardized phone type display text

## Important Considerations
1. The Client model still maintains legacy PhoneNumber and Email string fields for backward compatibility
2. The Contact model now fully relies on the normalized PhoneNumbers and EmailAddresses collections
3. Both models use IsPrimary flag in the PhoneNumber and EmailAddress entities to track primary contact methods
4. The migration ensures data integrity while moving from the old structure to the new one
5. Phone type consistency is critical - always use the enum values and standardized UI text
6. When encountering legacy terms "Phone", "MainOffice", or "Cell" in code, map them to Office (0) and Mobile (1) respectively 