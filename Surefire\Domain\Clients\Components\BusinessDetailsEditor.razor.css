﻿.pol-section-title {
    font-family: "montserrat", sans-serif;
    font-size: 1.25em;
    font-weight: 400;
    color: #6b6b6b;
    text-transform: uppercase;
    letter-spacing: 2px;
    background-color: #e9e9e9;
    padding: 3px 20px 3px 5px;
    border-top-right-radius: 20px;
    text-shadow: 1px 1px 1px #fff;
}

.pol-name {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: .8em;
    display: inline-block;
    font-weight: bold;
    color: #484848;
    text-align: right;
    position: relative;
    top: 0px;
}
.pol-name-2 {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: .8em;
    display: inline-block;
    font-weight: bold;
    color: #484848;
    text-align: right;
    position: relative;
    top: 0px;
    width:25px;
}

.pol-value {
    width: 200px;
    display: inline-block;
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: 14px;
    color: #000;
    height: 30px;
}
.pol-value-2 {
    width: 200px;
    display: inline-block;
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: 14px;
    color: #000;
    height: 30px;
    overflow:hidden;
}
.pol-row {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: .8em;
    font-weight: bold;
    color: #484848;
    text-align: right;
    position: relative;
    top: 0px;
    width:300px;
    height:14px;
}
.pol-row span {
    position: relative;
    top:5px;
    margin-right:5px;
    margin-left: 5px;
}
.business-details {
    font-size: 1em;
}

.pol-section {
}

.pol-section-container {
    border-left: 5px solid #dedede;
    padding-top: 10px;
    padding-bottom: 20px;
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#e5e5e5+0,ffffff+35&1+0,0+35 */
    background: linear-gradient(165deg, rgba(229,229,229,1) 0%,rgba(255,255,255,0) 35%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.pol-column-spacer {
    height: 0px;
}

.copy-spcr {
    position: relative;
    display: inline-block;
    top: 6px;
    left: 0px;
    width: 17px;
    height: 17px;
}

.copy-btn {
    opacity: .3;
    position: relative;
    display: inline-block;
    top: 6px;
    left: 0px;
    width: 17px;
    height: 17px;
}

    .copy-btn:hover {
        cursor: pointer;
        opacity: 1;
    }

    .copy-btn:active {
        cursor: pointer;
        opacity: 1;
        top: 7px;
        left: 1px;
    }

.flat-card {
    font-family: "montserrat", sans-serif;
    font-size: 1.6em;
    font-weight: 300;
    color: #828282;
    position:relative;
    top:25px;
}

.flat-class-container {
    position: relative;top: -25px;
    margin-left:10px;
}

.flat-up {
    /* Used for vertical spacing in ProposalDetails, keep for consistency */
}

.drop-zone-acord {
    outline: 0px;
    outline-offset: 0px !important;
    padding: 0 !important;
    background: none !important;
}

.attachment-thumbnail {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    margin-bottom: 0;
    margin-top:30px;
}

.thumb-icon {
    max-width: 160px;
    box-shadow: 1px 1px 9px #ccc;
}

.attachment-actions {
    display: flex;
    flex-direction: row;
    gap: 6px;
    margin-top: 5px;
    margin-bottom: 2px;
    justify-content: flex-end !important;
    width: 100%;
}

.delbtn, .folderbtn, .openbtn {
    cursor: pointer;
    font-size: 14px !important;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    transition: background 0.15s;
}

.delbtn:hover, .folderbtn:hover, .openbtn:hover {
    background: #e8f0fe;
}


.copy-btn {
    cursor: pointer;
    margin-left: 5px;
}

.copy-spcr {
    margin-left: 20px; /* Adjust as needed for alignment */
}

.pol-column-spacer {
    margin-bottom: 20px; /* Adjust as needed */
}

.txt-section {
    font-weight: bold;
    margin-bottom: 5px;
}

.key {
    color: #cf4740;
}

.string {
    color: #593294;
}

.number {
    color: #b5cea8;
}

.brace {
    color: #bdbdbd;
}

.colon {
    color: #1b8ce3;
}

.boolean {
    color: #1b8ce3;
}

.null {
    color: #bdbdbd;
}

.bracket {
    color: #bdbdbd;
}

.comma {
    color: #bdbdbd;
}