using Surefire.Domain.Agents.Interfaces;

namespace Surefire.Domain.Agents.Services
{
    /// <summary>
    /// Placeholder entity extraction service
    /// This will be replaced with the embedding-based entity extraction system
    /// </summary>
    public class PlaceholderEntityExtractionService : IEntityExtractionService
    {
        private readonly ILogger<PlaceholderEntityExtractionService> _logger;

        // Mock data for demonstration - this will be replaced with database/embedding queries
        private readonly List<string> _mockClients = new()
        {
            "Acme Corp", "Pacific Security", "Baron's Heating", "TWS", "Smith Industries", "Johnson & Associates"
        };

        private readonly List<string> _mockCarriers = new()
        {
            "State Farm", "Allstate", "Liberty Mutual", "Progressive", "Travelers", "Hartford"
        };

        private readonly List<string> _mockPolicyTypes = new()
        {
            "Workers Compensation", "Work Comp", "General Liability", "Commercial Auto", "Property", "Cyber Liability"
        };

        public PlaceholderEntityExtractionService(ILogger<PlaceholderEntityExtractionService>? logger = null)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Extract client names from user input
        /// PLACEHOLDER: This will be replaced with the embedding-based extraction system
        /// </summary>
        public async Task<List<(string ClientName, double Confidence)>> ExtractClientNamesAsync(string userInput, int topResults = 5)
        {
            _logger.LogInformation("PLACEHOLDER: Extracting client names from: {UserInput}", userInput);
            
            // Simple fuzzy matching for now
            var results = FindBestMatches(userInput, _mockClients, topResults);
            
            _logger.LogInformation("Found {Count} client matches", results.Count);
            return results;
        }

        /// <summary>
        /// Extract carrier names from user input
        /// PLACEHOLDER: This will be replaced with the embedding-based extraction system
        /// </summary>
        public async Task<List<(string CarrierName, double Confidence)>> ExtractCarrierNamesAsync(string userInput, int topResults = 5)
        {
            _logger.LogInformation("PLACEHOLDER: Extracting carrier names from: {UserInput}", userInput);
            
            var results = FindBestMatches(userInput, _mockCarriers, topResults);
            
            _logger.LogInformation("Found {Count} carrier matches", results.Count);
            return results;
        }

        /// <summary>
        /// Extract policy types from user input
        /// PLACEHOLDER: This will be replaced with the embedding-based extraction system
        /// </summary>
        public async Task<List<(string PolicyType, double Confidence)>> ExtractPolicyTypesAsync(string userInput, int topResults = 5)
        {
            _logger.LogInformation("PLACEHOLDER: Extracting policy types from: {UserInput}", userInput);
            
            var results = FindBestMatches(userInput, _mockPolicyTypes, topResults);
            
            _logger.LogInformation("Found {Count} policy type matches", results.Count);
            return results;
        }

        /// <summary>
        /// Generic entity extraction for custom entity types
        /// PLACEHOLDER: This will be replaced with the embedding-based extraction system
        /// </summary>
        public async Task<List<(string Entity, double Confidence)>> ExtractEntitiesAsync(string userInput, string entityType, int topResults = 5)
        {
            _logger.LogInformation("PLACEHOLDER: Extracting {EntityType} entities from: {UserInput}", entityType, userInput);
            
            // For now, just return empty results for unknown entity types
            var results = new List<(string, double)>();
            
            _logger.LogInformation("Found {Count} {EntityType} matches", results.Count, entityType);
            return results;
        }

        /// <summary>
        /// Check if entity extraction is available for a given entity type
        /// </summary>
        public bool SupportsEntityType(string entityType)
        {
            var supportedTypes = new[] { "clientname", "carriername", "policytype" };
            return supportedTypes.Contains(entityType.ToLowerInvariant());
        }

        /// <summary>
        /// Find best matches using simple string similarity
        /// This will be replaced with embedding similarity search
        /// </summary>
        private List<(string Entity, double Confidence)> FindBestMatches(string userInput, List<string> candidates, int topResults)
        {
            var userInputLower = userInput.ToLowerInvariant();
            var matches = new List<(string Entity, double Confidence)>();

            foreach (var candidate in candidates)
            {
                var confidence = CalculateStringMatch(userInputLower, candidate.ToLowerInvariant());
                if (confidence > 0.1) // Only include if there's some similarity
                {
                    matches.Add((candidate, confidence));
                }
            }

            return matches
                .OrderByDescending(m => m.Confidence)
                .Take(topResults)
                .ToList();
        }

        /// <summary>
        /// Calculate string match confidence
        /// This is a simple implementation that will be replaced with embedding similarity
        /// </summary>
        private double CalculateStringMatch(string input, string candidate)
        {
            // Exact match gets highest score
            if (input.Contains(candidate) || candidate.Contains(input))
            {
                return 0.9;
            }

            // Check for partial word matches
            var inputWords = input.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            var candidateWords = candidate.Split(' ', StringSplitOptions.RemoveEmptyEntries);

            var matchingWords = inputWords.Intersect(candidateWords, StringComparer.OrdinalIgnoreCase).Count();
            if (matchingWords > 0)
            {
                var score = (double)matchingWords / Math.Max(inputWords.Length, candidateWords.Length);
                return score * 0.8; // Scale down for partial matches
            }

            // Check for abbreviation matches (e.g., "work comp" for "Workers Compensation")
            if (IsAbbreviationMatch(input, candidate))
            {
                return 0.7;
            }

            return 0.0;
        }

        /// <summary>
        /// Check if input might be an abbreviation of the candidate
        /// </summary>
        private bool IsAbbreviationMatch(string input, string candidate)
        {
            // Simple check for common abbreviations
            var abbreviations = new Dictionary<string, string[]>
            {
                ["work comp"] = new[] { "workers compensation", "workers comp" },
                ["wc"] = new[] { "workers compensation", "workers comp" },
                ["gl"] = new[] { "general liability" },
                ["auto"] = new[] { "commercial auto", "automobile" }
            };

            foreach (var abbrev in abbreviations)
            {
                if (input.Contains(abbrev.Key))
                {
                    foreach (var fullForm in abbrev.Value)
                    {
                        if (candidate.Contains(fullForm))
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }
    }
} 