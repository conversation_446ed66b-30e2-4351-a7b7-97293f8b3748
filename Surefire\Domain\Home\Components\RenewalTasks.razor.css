﻿:root tr td {
    padding: 0px 0px;
    height: 20px;
}
.sectiontitletab {
    font-family: "montserrat", sans-serif;
    font-size: 1.5em;
    padding-top: 5px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    color: #fff;
    text-align: center;
    z-index:101;
    position:relative;
}

.background-default {
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#e35049+1,201e60+49,3d3d3d+100 */
    background: linear-gradient(to right, #767676 1%,#201e60 49%,#3d3d3d 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.background-darkpurple {
    background-color: #4e1359;
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#4e1359+0,3d3d3d+100 */
    background: linear-gradient(to right, rgba(78,19,89,1) 0%,rgba(61,61,61,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.home-box {
    overflow:hidden;
    box-shadow:0px 0px 12px #0000005f;
}
#tasktable {
    padding-top: 10px;
    text-align: left;
    width: 100%;
    margin: 0;
    padding: 0;
    border-collapse: collapse;
    z-index:99;
}
.ttable {
    text-align: left;
    width: 100%;
    margin: 0;
    padding: 0;
    border-collapse: collapse;
    font-size: 1em;
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#dbdbdb+0,ffffff+45,dbdbdb+100&0.99+0,0.99+100 */
    background: linear-gradient(to bottom, rgba(219,219,219,0.99) 0%,rgba(255,255,255,0.99) 45%,rgba(219,219,219,0.99) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
tr .trow {
    display: block;
    margin-top: 5px !important;
    padding: 20px;
}
.tcname {
    max-width: 150px !important;
    height: 10px;
    font-size: .9em;
}
.tbg tr {
    background-color: #767676 !important;
}
.hprod {
    color: #e6e6e6;
    background-color: #767676 !important;
    padding-left: 4px;
    padding-top: 2px;
    padding-bottom: 2px;
    padding-right: 4px;
    font-size: .9em;
    font-weight: bold;
    min-width: 90px;
    height: 100%;
    font-family: "mono45-headline", monospace;
    font-weight: 500;
    font-style: normal;
    letter-spacing: 1.5px;
}
.hprodbg-default {
    background-color: #6858c7;
}
.hprodbg-darkpurple {
    background-color: #4e1359;
}
.ttname a {
    text-decoration:none;
}

.hexp {
    background-color: #ffffffa7;
    color: #424242;
    padding-left: 3px;
    padding-top: 1px;
    padding-bottom: 1px;
    padding-right: 4px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    font-size: .9em;
    font-family: "mono45-headline", monospace;
    font-weight: 500;
    font-style: normal;
}
.ttname {
    height: 10px;
    font-size: .9em;
    font-weight: bold;
}
.ttpri {
    height: 10px;
    font-size: .7em;
}
.rentask-link a:hover {
    cursor:pointer;
}
.rentask-link:hover {
    cursor: pointer;
}