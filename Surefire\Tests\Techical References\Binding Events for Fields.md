﻿# Field Change Events & Binding While Saving to Database via Entity Frameworks – A **Surefire** Rulebook  
*File: `FieldChangeEventsBindingInstructions.md`*  
*Version 1.0 – Last updated 2025‑06‑08*

These conventions standardize **how to wire up change / value events** for every UI field type used in Surefire’s Blazor (.NET 8) components. Follow them *exactly* so models update correctly and the project still compiles.

---
## 1 · Universal rules
1. **Always two things:**  
   * A **bind** directive (`@bind-Value`, `@bind-Checked`) so the UI shows the current value.  
   * An explicit **event callback** so changes persist (`SaveProposal`, `OnStatusChanged`, etc.).
2. **Async first.** If the callback touches the database or service layer, declare it `async Task` and `await` inside.
3. Attribute names are **PascalCase** (`OnChange`, not `onchange`), except for the standard Blazor DOM event `@onchange`.
4. Keep lambdas *tiny*. If you need more than one line, create a dedicated method.

---
## 2 · Control‑by‑control cheat‑sheet


### **Syncfusion SfTextBox**
#### Binding directive: @bind-Value="…"
#### Correct Change Hookup:
```razor 
<SfTextBox @bind-Value="Proposal.SpecialInstructions" OnChange="@(args => SaveProposal(true))" />
```
#### Bad Form:
• Missing `Multiline="true"` when storing long text

---

### **Syncfusion SfDropDownList**
#### Binding directive: @bind-Value="…"
#### Correct Change Hookup:
```razor 
<SfDropDownList TItem="StatusOption" TValue="string" @bind-Value="StatusSaveString" DataSource="@StatusOptions">
    <DropDownListEvents TItem="StatusOption" TValue="string" ValueChange="OnStatusChanged" />
    <DropDownListFieldSettings Text="Label" Value="Value" />
</SfDropDownList>
```
#### Bad Form:
• Omitting `<DropDownListEvents>`  
• Putting `ValueChange` on the *root* tag  
• Leaving out generics

---

### **Syncfusion SfNumericTextBox**
#### Binding directive: @bind-Value="…"
#### Correct Change Hookup:
```razor 
<SfNumericTextBox TValue="int?" @bind-Value="Proposal.BrokerFee" Min="0" OnChange="@(args => SaveProposal(true))" />
```
#### Bad Form:
• Setting `ValueChange` – wrong event name

---

### **Syncfusion SfDatePicker**
#### Binding directive: @bind-Value="…"
#### Correct Change Hookup:
```razor 
<SfDatePicker TValue="DateTime?" @bind-Value="Proposal.SendDate" OnChange="@(args => SaveProposal(true))" />
```
#### **CRITICAL:** You must use lambda syntax with ignored args parameter
• **Method reference only:** `OnChange="MethodName"` will NOT work - causes EventCallback errors
• **Correct lambda:** `OnChange="@(args => MethodName())"` - args parameter is ignored but required
• **Create separate method handlers if needed** - keep methods parameterless since @bind-Value handles data binding

#### Bad Form:
• Using `@onchange` (DOM) for DatePicker
• Using method reference without lambda: `OnChange="SaveMethod"`
• Using `ValueChange` or `<DatePickerEvents>` - wrong event pattern

---

### **Syncfusion SfCheckBox**
#### Binding directive: @bind-Checked="…"
#### Correct Change Hookup:
```razor 
<SfCheckBox @bind-Checked="Proposal.IncludeSL2" @onchange="@SaveProposalSimple" Label="SL2"></SfCheckBox>
```
#### Bad Form:
• **Self‑closing tag** (`<SfCheckBox … />`) 
• `CheckedChange` (wrong event) 

---

### **FluentRadio**
#### Binding directive: @bind-Value="…" (on group) 
#### Correct Change Hookup:
```razor 
<FluentRadioGroup @bind-Value="Proposal.BillType" OnChange="BillTypeChanged">
```
#### Bad Form:
• Attaching events to individual `<FluentRadio>` elements

---

### **FluentCheckbox**
#### Binding directive: @bind-Value="…"
#### Correct Change Hookup:
```razor 
<FluentCheckbox @bind-Value="Proposal.IncludeSL2" OnChange="SaveProposalSimple">Include SL‑2</FluentCheckbox>
```
#### Bad Form:
• Using `@bind-Checked` instead of `@bind-Value`

---

## 3 · Callback signatures

|       Control           |                          Suggested signature                                       |
|-------------------------|------------------------------------------------------------------------------------|
| **SfDropDownList**      | `async Task OnStatusChanged(object value)`                                         |
| **SfNumericTextBox**    | `async Task OnBrokerFeeChanged(ChangeEventArgs<int?> args)`                        |
| **SfDatePicker**        | `async Task OnSendDateChanged()` *(parameterless - @bind-Value handles data)*       |
| **SfCheckBox**          | `async Task SaveProposalSimple(ChangeEventArgs<bool> args)` (args optional)        |
| **FluentCheckbox**      | `async Task SaveProposalSimple(ChangeEventArgs<bool> args)` (args optional)        |

*Tip:* Use `object` for dropdown value if the list mixes int and string; cast inside.

---

## 4· SfDropDownList events element
* Must be the **first child** (before templates/fields).  
* Generic arguments **must** mirror the parent.  
* Supported attributes: `ValueChange`, `Blur`, `Focus`. Only wire what you need.

---
## 5· Typical pattern for saving on change

```razor
<SfNumericTextBox TValue="int?" Min="0" Max="100"
        @bind-Value="Proposal.DownPaymentPercent"
        OnChange="@(args => SaveProposal(true))" />

<SfDatePicker TValue="DateTime?" @bind-Value="Policy.EffectiveDate"
        OnChange="@(args => OnEffectiveDateChanged())" />

@code {
    private async Task SaveProposal(bool stayOnPage = false)
    {
        await ProposalService.SaveProposalAsync(Proposal);
        if (!stayOnPage)
            Navigation.NavigateTo($"/Renewals/Details/{RenewalId}");
    }
    
    private async Task OnEffectiveDateChanged()
    {
        // @bind-Value already updated Policy.EffectiveDate
        await PolicyService.UpdatePolicyAsync(Policy);
    }
}
```

---
## 6 · Common pitfalls (🚫 = never do this)
🚫 **Event duplication:** Wiring both `OnChange` *and* `ValueChange` on the same control will fire twice. Pick one.  
🚫 **Mismatched generic parameters** between `SfDropDownList` and `DropDownListEvents`.  
🚫 **Camel‑case attributes** (`onChange`) – Blazor treats them as plain HTML attributes.  
🚫 **Async‑void** callbacks—return `Task` instead.  
🚫 **Heavy logic inside lambdas** – extract into a method.

---
## 7· Pre‑commit checklist
- [ ] Correct bind directive present?  
- [ ] Exactly **one** change callback wired?  
- [ ] `DropDownListEvents` inside every `SfDropDownList`?  
- [ ] `<SfCheckBox>` has explicit closing tag?  
- [ ] Callback marked `async` and returns `Task` if awaiting?  
- [ ] No misspelled attribute names?  

If any box is unchecked, fix before merging. Consistency here prevents nearly **all** runtime bugs we’ve been hunting.
---

### Additional Notes

- Domain/Proposal/Components/ProposalDetails.razor has many examples of different fields
``` razor
<SfCheckBox @bind-Checked="@BusinessDetails.BuildingLocationMonitoredSecurity" @onchange="@UpdateBusinessDetails"></SfCheckBox>
```
uses: private void onChange(Microsoft.AspNetCore.Components.ChangeEventArgs args)

``` razor
<SfCheckBox @bind-Checked="@AutoCoverage.ForAny" ValueChange="@(async (Syncfusion.Blazor.Buttons.ChangeEventArgs<bool?> args) => await UpdateCoverageBool(args))"></SfCheckBox>
```
uses: private void ValueChange(ChangeEventArgs<bool> args)

- When all else fails, use a proxy property!