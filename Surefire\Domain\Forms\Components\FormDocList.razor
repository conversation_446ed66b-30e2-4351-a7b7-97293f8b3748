﻿@namespace Surefire.Domain.Forms.Components
@using Surefire.Domain.Clients.Models
@using Surefire.Domain.Forms.Models
@using Surefire.Domain.Forms.Services
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Buttons
@using Microsoft.AspNetCore.Components
@using System.Threading.Tasks
@using Microsoft.FluentUI.AspNetCore.Components
@inject NavigationManager NavigationManager
@inject FormService FormService

<div class="form-doc-list-container">
    <div class="form-doc-list-header">
        @if (!string.IsNullOrEmpty(Title))
        {
            <h3>@Title</h3>
        }
        
        @if (!string.IsNullOrEmpty(EntityType) && EntityId > 0)
        {
            <SfButton CssClass="e-primary" IconCss="e-icons e-plus-icon" OnClick="OpenNewFormDialog">New Form</SfButton>
        }
    </div>

    @if (formDocList is null)
    {
        <p><em>Loading...</em></p>
    }
    else if (formDocList.Count == 0 && !IsSearching)
    {
        <p><em>No forms found...</em></p>
    }
    else
    {
        <SfGrid @ref="Grid" DataSource="@formDocList"
                AllowPaging="true" 
                AllowSorting="true"
                AllowFiltering="true"
                Height="@(Height)"
                Width="@(Width)"
                TValue="FormDoc">
            <GridEvents TValue="FormDoc" OnActionBegin="ActionBeginHandler"></GridEvents>
            <GridPageSettings PageSize="@PageSize" PageSizes="true"></GridPageSettings>
            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Menu"></GridFilterSettings>
            <GridColumns>
                <GridColumn Field=@nameof(FormDoc.FormDocId) HeaderText="ID" Width="70"></GridColumn>
                <GridColumn Field=@nameof(FormDoc.Title) HeaderText="Application" Width="200">
                    <Template>
                        @{
                            var doc = (FormDoc)context;
                            <a href="/Forms/Editor/@doc.FormDocId">@doc.Title</a>
                        }
                    </Template>
                </GridColumn>
                <GridColumn Field=@nameof(FormDoc.Description) HeaderText="Description" Width="250"></GridColumn>
                <GridColumn Field=@nameof(FormDoc.DateCreated) HeaderText="Created" Width="120" Format="MM/dd/yyyy"></GridColumn>
                <GridColumn Field="CreatedBy.FirstName" HeaderText="Created By" Width="120">
                    <Template>
                        @{
                            var doc = (FormDoc)context;
                            @(doc.CreatedBy?.FirstName ?? "-")
                        }
                    </Template>
                </GridColumn>
                <GridColumn Field=@nameof(FormDoc.DateModified) HeaderText="Modified" Width="120" Format="MM/dd/yyyy"></GridColumn>
                <GridColumn Field="ModifiedBy.FirstName" HeaderText="Modified By" Width="120">
                    <Template>
                        @{
                            var doc = (FormDoc)context;
                            @(doc.ModifiedBy?.FirstName ?? "-")
                        }
                    </Template>
                </GridColumn>
                <GridColumn HeaderText="Actions" Width="150">
                    <Template>
                        @{
                            var doc = (FormDoc)context;
                            <div class="action-buttons">
                                <FluentButton Appearance="Appearance.Lightweight" OnClick="@(()=>ShowRevisions(doc))" Title="Show Revisions">
                                    <FluentIcon Value="@(new Icons.Regular.Size16.History())" />
                                </FluentButton>
                                <FluentButton Appearance="Appearance.Lightweight" OnClick="@(()=>DuplicateForm(doc))" Title="Duplicate Form">
                                    <FluentIcon Value="@(new Icons.Regular.Size16.Copy())" />
                                </FluentButton>
                                <FluentButton Appearance="Appearance.Lightweight" OnClick="@(()=>OpenForm(doc))" Title="Open Form">
                                    <FluentIcon Value="@(new Icons.Regular.Size16.Open())" />
                                </FluentButton>
                                <FluentButton Appearance="Appearance.Lightweight" OnClick="@(()=>ShowDeleteConfirmation(doc))" Title="Delete Form">
                                    <FluentIcon Value="@(new Icons.Regular.Size16.Delete())" />
                                </FluentButton>
                            </div>
                        }
                    </Template>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    }

    <!-- Revision History Dialog -->
    <SfDialog @bind-Visible="RevisionDialogVisible" Width="600px" IsModal="true" ShowCloseIcon="true">
        <DialogEvents Closed="@OnRevisionDialogClose"></DialogEvents>
        <DialogTemplates>
            <Header>Revision History for @SelectedFormTitle</Header>
            <Content>
                @if (Revisions == null || Revisions.Count == 0)
                {
                    <p>No revision history available.</p>
                }
                else
                {
                    <div class="revision-list">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Revision Name</th>
                                    <th>Created Date</th>
                                    <th>Created By</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var revision in Revisions)
                                {
                                    <tr>
                                        <td>@revision.RevisionName</td>
                                        <td>@revision.DateCreated.ToString("MM/dd/yyyy HH:mm")</td>
                                        <td>@(revision.CreatedBy?.FirstName ?? "System")</td>
                                        <td>
                                            <SfButton CssClass="e-info" OnClick="@(() => OpenRevision(revision.FormDocId, revision.FormDocRevisionId))">Open</SfButton>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
            </Content>
        </DialogTemplates>
        <DialogButtons>
            <DialogButton OnClick="@OnRevisionDialogClose" Content="Close" IsPrimary="false"></DialogButton>
        </DialogButtons>
    </SfDialog>

    <!-- New Form Dialog -->
    <SfDialog @bind-Visible="NewFormDialogVisible" Width="500px" IsModal="true" ShowCloseIcon="true">
        <DialogEvents Closed="@OnNewFormDialogClose"></DialogEvents>
        <DialogTemplates>
            <Header>Create New Form</Header>
            <Content>
                @if (AvailableForms == null || AvailableForms.Count == 0)
                {
                    <p>Loading available form templates...</p>
                }
                else
                {
                    <div class="form-group">
                        <label for="formSelect">Select Form Template:</label>
                        <select id="formSelect" class="form-control" @bind="SelectedFormPdfId">
                            <option value="0">-- Select a Form --</option>
                            @foreach (var form in AvailableForms)
                            {
                                <option value="@form.FormPdfId">@form.Title</option>
                            }
                        </select>
                    </div>
                }
            </Content>
        </DialogTemplates>
        <DialogButtons>
            <DialogButton OnClick="@CreateNewForm" Content="Create" IsPrimary="true" Disabled="@(SelectedFormPdfId <= 0)"></DialogButton>
            <DialogButton OnClick="@OnNewFormDialogClose" Content="Cancel" IsPrimary="false"></DialogButton>
        </DialogButtons>
    </SfDialog>

    <!-- Delete Confirmation Dialog -->
    <SfDialog @bind-Visible="@DeleteDialogVisible" Width="400px" IsModal="true" ShowCloseIcon="true" Header="Delete Form">
        <DialogTemplates>
            <Header>Delete Form</Header>
        </DialogTemplates>
        <DialogPositionData X="center" Y="center"></DialogPositionData>
        <DialogButtons>
            <DialogButton Content="Delete" IsPrimary="true" OnClick="@DeleteForm" CssClass="e-flat e-primary"></DialogButton>
            <DialogButton Content="Cancel" OnClick="@(() => DeleteDialogVisible = false)" CssClass="e-flat"></DialogButton>
        </DialogButtons>
        <div class="dialog-content">
            <p>Are you sure you want to delete this form? This action cannot be undone.</p>
            <p><strong>Form Title:</strong> @SelectedFormTitle</p>
        </div>
    </SfDialog>
</div>

<style>
    .form-doc-list-container {
        margin-bottom: 20px;
    }
    .form-doc-list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    .action-buttons {
        display: flex;
        gap: 5px;
    }
    .revision-list {
        max-height: 300px;
        overflow-y: auto;
    }
</style>

@code {
    private SfGrid<FormDoc> Grid;
    
    [Parameter]
    public List<FormDoc> formDocList { get; set; }

    [Parameter]
    public string EntityType { get; set; }

    [Parameter]
    public int EntityId { get; set; }

    [Parameter]
    public string Title { get; set; }

    [Parameter]
    public bool AllowAddNew { get; set; } = true;

    [Parameter]
    public bool AllowEdit { get; set; } = true;

    [Parameter]
    public string Height { get; set; } = "450px";

    [Parameter]
    public string Width { get; set; } = "100%";

    [Parameter]
    public int PageSize { get; set; } = 10;
    
    [Parameter]
    public EventCallback OnFormAdded { get; set; }

    private bool RevisionDialogVisible { get; set; } = false;
    private bool NewFormDialogVisible { get; set; } = false;
    private bool IsSearching { get; set; } = false;
    private int SelectedFormDocId { get; set; }
    private string SelectedFormTitle { get; set; }
    private List<FormDocRevision> Revisions { get; set; } = new List<FormDocRevision>();
    private List<FormPdf> AvailableForms { get; set; } = new List<FormPdf>();
    private int SelectedFormPdfId { get; set; } = 0;
    private int TotalRecords { get; set; }
    private int CurrentPage { get; set; } = 1;
    private string SortField { get; set; } = "DateModified";
    private bool SortAscending { get; set; } = false;
    private bool DeleteDialogVisible { get; set; } = false;
    private FormDoc FormToDelete { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await LoadFormDocsAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (!string.IsNullOrEmpty(EntityType) && EntityId > 0)
        {
            await LoadFormDocsAsync();
        }
    }

    private async Task LoadFormDocsAsync()
    {
        if (!string.IsNullOrEmpty(EntityType) && EntityId > 0)
        {
            IsSearching = true;
            try
            {
                formDocList = await FormService.GetFormDocsByEntityAsync(
                    EntityType, 
                    EntityId,
                    CurrentPage,
                    PageSize,
                    SortField,
                    SortAscending);
                
                TotalRecords = await FormService.GetFormDocsCountByEntityAsync(EntityType, EntityId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading form docs: {ex.Message}");
                formDocList = new List<FormDoc>();
            }
            IsSearching = false;
        }
    }

    private async Task ActionBeginHandler(ActionEventArgs<FormDoc> args)
    {
        if (args.RequestType == Syncfusion.Blazor.Grids.Action.Paging)
        {
            CurrentPage = args.CurrentPage;
            await LoadFormDocsAsync();
        }
        else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Sorting)
        {
            SortField = args.ColumnName;
            SortAscending = args.Direction == Syncfusion.Blazor.Grids.SortDirection.Ascending;
            await LoadFormDocsAsync();
        }
    }

    private async Task ShowRevisions(FormDoc formDoc)
    {
        SelectedFormDocId = formDoc.FormDocId;
        SelectedFormTitle = formDoc.Title;
        Revisions = await FormService.GetFormDocRevisionsAsync(formDoc.FormDocId);
        RevisionDialogVisible = true;
    }

    private void OnRevisionDialogClose()
    {
        RevisionDialogVisible = false;
    }

    private void OpenForm(FormDoc formDoc)
    {
        NavigationManager.NavigateTo($"/Forms/Editor/{formDoc.FormDocId}");
    }

    private void OpenRevision(int formDocId, int revisionId)
    {
        // Navigate to the form editor with a query parameter for the revision
        NavigationManager.NavigateTo($"/Forms/Editor/{formDocId}?revisionId={revisionId}");
    }

    private async Task DuplicateForm(FormDoc formDoc)
    {
        var newFormDocId = await FormService.DuplicateFormDocAsync(formDoc);
        NavigationManager.NavigateTo($"/Forms/Editor/{newFormDocId}");
    }

    private async Task OpenNewFormDialog()
    {
        // Load available form templates
        AvailableForms = await FormService.GetAllFormPdfs();
        SelectedFormPdfId = 0;
        NewFormDialogVisible = true;
    }

    private void OnNewFormDialogClose()
    {
        NewFormDialogVisible = false;
    }

    private async Task CreateNewForm()
    {
        if (SelectedFormPdfId <= 0)
        {
            return;
        }

        int newFormId = 0;

        // Create a new form based on the selected template and entity
        switch (EntityType.ToLower())
        {
            case "client":
                newFormId = await FormService.CreateFormDoc(SelectedFormPdfId, clientId: EntityId);
                break;
            case "lead":
                newFormId = await FormService.CreateFormDoc(SelectedFormPdfId, leadId: EntityId);
                break;
            case "submission":
                newFormId = await FormService.CreateFormDoc(SelectedFormPdfId, submissionId: EntityId);
                break;
            case "policy":
                newFormId = await FormService.CreateFormDoc(SelectedFormPdfId, policyId: EntityId);
                break;
            case "renewal":
                newFormId = await FormService.CreateFormDoc(SelectedFormPdfId, renewalId: EntityId);
                break;
        }

        NewFormDialogVisible = false;

        if (newFormId > 0)
        {
            // Notify parent if needed
            if (OnFormAdded.HasDelegate)
            {
                await OnFormAdded.InvokeAsync();
            }
            
            // Navigate to the form editor
            NavigationManager.NavigateTo($"/Forms/Editor/{newFormId}");
        }
    }

    private void ShowDeleteConfirmation(FormDoc formDoc)
    {
        FormToDelete = formDoc;
        SelectedFormTitle = formDoc.Title;
        DeleteDialogVisible = true;
    }

    private async Task DeleteForm()
    {
        if (FormToDelete == null) return;

        try
        {
            await FormService.DeleteFormDocAsync(FormToDelete.FormDocId);
            await LoadFormDocsAsync(); // Refresh the list
            DeleteDialogVisible = false;
        }
        catch (Exception ex)
        {
            // You might want to show an error message to the user here
            Console.WriteLine($"Error deleting form: {ex.Message}");
        }
    }
}
