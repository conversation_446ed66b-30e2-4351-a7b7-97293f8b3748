# Surefire To-Do List


## Tasks Logic: Enhancements
- **Subtasks:**
  - Implement logic to allow tasks to have subtasks, supporting hierarchical task structures.
  - Update the data model and UI to display and manage subtasks under each main task.
- **New Task/Subtask Menu Items:**
  - Add "New Task" and "New Subtask" options under the "New" button in the renewal details page for quick creation.
- **MasterTask Assignment:**
  - Add the ability to assign a MasterTask to a user so that when a renewal is created, the default tasks are already assigned to the appropriate user.
  - Update the renewal creation logic to support this assignment.
- **Notes Field Redesign:**
  - Remove the notes fields from each tracktask row.
  - Add an "Add Note" button that opens a dialog for entering a note for that task, which is then added to the renewal's activity log.
  - Provide a UI/UX for both entering and viewing notes for a specific tracktask item, possibly as a modal or expandable section.
- **Renewals Tab UI/UX:**
  - The middle column of the renewals first tab should display todo items/tasks.
  - Clicking a tracktask row should display that item's subtasks.
  - Add tabs or buttons to display tasks for submissions.
  - Add a dropdown to filter and display all pending tasks for that renewal for a specific user, defaulting to the current user. 
	- 
	- 
	- 
	- HIGHLIGHT or bring to light any unfinished tasks that are within one week of the renewals expiration date
	- Need a "master task" browser on the homepage
  
## Renewals Manager and Clients Pages: Sync View/Tab
- Ensure that when navigating between Renewals Manager and Clients pages, the user is returned to the same view and tab.
- Store the current tab/view state in `StateService` or a similar shared service.
- Update navigation logic to restore the last active tab when returning to these pages.

## Graph Fetched Emails: Add Caching
- Implement caching for the emails list fetched via the Graph API.
- Store the loaded emails in memory (e.g., using `IMemoryCache` or a scoped service) to avoid redundant API calls.
- Invalidate or refresh the cache as needed (e.g., on manual refresh or after sending/receiving new emails).

## Submission Button: Improve Clarity for First Submission
- Make the "Create Submission" button more prominent and user-friendly, especially for the first submission.
- Consider automatically creating the first submission when a renewal is created, reducing user friction.
- Update UI/UX to guide users through the submission process.

## Underwriter Email: Pre-fill Subject
- When clicking the underwriter's email, pre-fill the email subject with the client name, policy type, renewal number, etc.
- Update the email link generation logic to include these details in the `mailto:` subject parameter.

## Renewal Task Submenu: Add "Postpone" Button
- Add a "Postpone" button to the renewal task submenu.
- When clicked, move the task item off the `renewalTasks` and daily task lists, but do not change the `goalDate`.
- Ensure postponed tasks are tracked and can be restored or reviewed later.

## Policy List: Use `<trigger>` Component for Policy Number
- Integrate or enhance the custom `<trigger>` component for policy numbers in the policy list.
- Enable features like click-to-copy, interop search, and other quick actions via the trigger.

## Contacts Page: Default Contact Method
- When adding a new email to the contacts page, auto-select the default contact method if available.
- If no default exists, default to the main slot.
- Update the logic in the contacts form to handle these defaults.

## Title Bar: Consistent Window Name
- Standardize the window title bar name across all pages and dialogs.
- Update the `<title>` or `<PageTitle>` elements to ensure consistency.

## Contact Form: Cleanup
- Refactor and clean up the contact form for better usability and maintainability.
- Address any UI inconsistencies or redundant fields.

## Save Button: Ajax Spinner for Async Submissions
- Update the save button on forms to display an Ajax spinner or loading indicator when submitting asynchronously.
- Disable the button during submission to prevent duplicate requests.

## Keyboard Shortcuts: Control+Enter to Submit
- Enable Control+Enter as a shortcut to submit new forms.
- Add event listeners or Blazor event handlers to capture this key combination.

## New Client Screen: Remove Contact Requirement
- Allow creation of a new client without requiring a contact to be added immediately.
- Update validation logic to make contact optional during client creation.

## Keyboard Shortcuts: Common Functions
- Implement keyboard shortcuts for frequently used actions (e.g., navigation, save, search).
- Document available shortcuts in the UI or help section for user reference.
