/* RecentPaymentsList.razor.css - Matches DocuSignEnvelopeList styling */

.sectiontitletab {
    font-family: "montserrat", sans-serif;
    font-size: 1.3em;
    padding-top: 6px;
    padding-bottom: 6px;
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#595959+0,aaaaaa+87,bfbfbf+94,aaaaaa+100 */
    background: linear-gradient(to right, #595959 0%,#aaaaaa 87%,#bfbfbf 94%,#aaaaaa 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    padding-left: 10px;
    color: #e6e6e6;
    text-align: left;
    border-left: 15px solid #1b1b1b;
    z-index: 20;
    position: relative;
    text-shadow: 1px 1px 3px #2b2b2b;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #ffffff7e;
}

.leads-box-inner {
    box-shadow: inset 0px 5px 7px #a1a1a1;
    background-color: #dfdfdf87;
    z-index: 10;
    border-bottom-right-radius: 15px;
    border-bottom-left-radius: 15px;
}
.lcname {
    font-weight: bold;
    color: #595959;
    cursor: pointer;
}

    .lcname:hover {
        color: #006bb7;
    }

.lccon {
    color: #423838;
    font-size: .8em;
}

/*standard mid table*/
.mid-a {
    width: auto;
    padding-left: 15px;
}
.mid-b {
    width: 20%;
}
.mid-c {
    width: 15%;
}
.mid-d {
    width: 60px;
}
.mid-name {
    font-weight: bold;
    color: #595959;
    cursor: pointer;
}
    .mid-name:hover {
        color: #006bb7;
    }
.mid-color {
    color: #20874b;
    font-weight: bold;
    font-size: .8em;
}
.mid-date {
    color: #7c7c7c;
    font-size: .8em;
}
.mid-dotname {
    text-decoration: none;
    color: #2ca55e;
    font-size: .75em;
    position: relative;
    left: -4px;
    top: -2px;
}
/*standard mid table*/
.ltable {
    text-align: left;
    width: 100%;
    margin: 0;
    padding: 0;
    border-collapse: collapse;
    font-size: 1em;
}

.lbg {
    height: 20px;
    color: #ffffff64;
    text-align: left;
    font-size: .7em;
}
    .lbg th {
        color: #979797;
    }
.lbody {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: .9em;
}
    .lbody tr td {
        /*padding: 2px 0px 2px 15px;*/
    }
/*standard mid table*/
.main {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

    .main:hover {
        background-color: #fff;
        cursor: pointer;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
.dot {
    height: 8px;
    width: 8px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
    position: relative;
    top: -0px;
    margin-right: 8px;
    margin-left: 6px;
}

.dot-2 {
    background-color: #2ca55e;
    box-shadow: 0px 0px 7px #2ca55e;
    border: 1px solid #ffffffaa;
}

.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

.no-taskssub {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 20px;
}

.ctweak {
    /*margin-left: 11px;*/
}

/* Modal styles */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 1001;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #ddd;
    background: linear-gradient(to right, rgba(61,61,61,1) 0%,rgba(32,30,96,1) 49%,rgba(61,61,61,1) 100%);
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
}

    .modal-header h3 {
        margin: 0;
        color: #b8adef;
        font-family: "montserrat", sans-serif;
    }

.close-btn {
    background: none;
    border: none;
    font-size: 1.5em;
    color: #b8adef;
    cursor: pointer;
}

.modal-body {
    padding: 20px;
}

.details-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

    .details-table tr {
        border-bottom: 1px solid #eee;
    }

        .details-table tr:last-child {
            border-bottom: none;
        }

    .details-table td {
        padding: 10px 5px;
    }

.detail-label {
    font-weight: bold;
    color: #555;
    width: 30%;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    gap: 10px;
}

.action-btn {
    padding: 8px 15px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-size: 0.9em;
    transition: background-color 0.2s ease;
}

.close-modal-btn {
    background-color: #f0f0f0;
    color: #333;
}

    .close-modal-btn:hover {
        background-color: #ddd;
    }

.caret-container {
    display: flex;
    align-items: center;
    margin-left: auto;
}
.caret {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: rotate(0deg);
    color: #b8adef;
}
.caret.collapsed {
    transform: rotate(-90deg);
}

.slider {
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
    overflow: hidden;
    max-height: 2000px;
    opacity: 1;
}
.slider.collapsed {
    max-height: 0;
    opacity: 0;
    pointer-events: none;
}

/* Enhanced Loading Animations */
.skeleton-row {
    animation: skeleton-pulse 1.5s ease-in-out infinite;
}

@keyframes skeleton-pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.skeleton-shimmer {
    position: relative;
    overflow: hidden;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Slide-in animation for data rows */
.slide-in-row {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.27s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-row.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Fade-in for no data message */
.fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Improved skeleton loading appearance */
.skeleton-row td {
    padding: 8px 9px;
}

/* Staggered animation delays for skeleton rows */
.skeleton-row:nth-child(1) { animation-delay: 0ms; }
.skeleton-row:nth-child(2) { animation-delay: 100ms; }
.skeleton-row:nth-child(3) { animation-delay: 200ms; }
.skeleton-row:nth-child(4) { animation-delay: 300ms; }
.skeleton-row:nth-child(5) { animation-delay: 400ms; }
.skeleton-row:nth-child(6) { animation-delay: 500ms; }
.skeleton-row:nth-child(7) { animation-delay: 600ms; }
.skeleton-row:nth-child(8) { animation-delay: 700ms; } 