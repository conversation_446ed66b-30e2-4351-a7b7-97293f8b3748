using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Surefire.Domain.Chat
{
    public class CallTranscription
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// The call ID from RingCentral
        /// </summary>
        public string CallId { get; set; }

        /// <summary>
        /// The recording ID from RingCentral
        /// </summary>
        public string RecordingId { get; set; }

        /// <summary>
        /// The phone number of the caller
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// The raw text of the transcription
        /// </summary>
        public string TranscriptionText { get; set; }

        /// <summary>
        /// The date and time the transcription was created
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// The start time of the call
        /// </summary>
        public DateTime? CallStartTime { get; set; }

        /// <summary>
        /// The duration of the call in seconds
        /// </summary>
        public int? CallDuration { get; set; }

        /// <summary>
        /// The language of the transcription (e.g., "en" for English)
        /// </summary>
        public string Language { get; set; } = "en";

        /// <summary>
        /// Whether the transcription has been reviewed by a human
        /// </summary>
        public bool IsReviewed { get; set; } = false;

        /// <summary>
        /// Optional notes about the transcription
        /// </summary>
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// Associated client ID (if any)
        /// </summary>
        public int? ClientId { get; set; }

        /// <summary>
        /// The "from" phone number in the call
        /// </summary>
        public string FromPhoneNumber { get; set; }

        /// <summary>
        /// The "to" phone number in the call
        /// </summary>
        public string ToPhoneNumber { get; set; }
    }

    /// <summary>
    /// Request model for OpenAI transcription API
    /// </summary>
    public class TranscriptionRequest
    {
        /// <summary>
        /// The audio file to transcribe, in one of these formats: mp3, mp4, mpeg, mpga, m4a, wav, or webm
        /// </summary>
        public byte[] AudioData { get; set; }
        
        /// <summary>
        /// ID of the model to use. Only whisper-1 is currently available.
        /// </summary>
        public string Model { get; set; } = "whisper-1";
        
        /// <summary>
        /// The language of the input audio. Supplying the input language in ISO-639-1 format
        /// will improve accuracy and latency.
        /// </summary>
        public string Language { get; set; } = "en";
        
        /// <summary>
        /// The format of the transcript output, in one of these options: json, text, srt, verbose_json, or vtt
        /// </summary>
        public string ResponseFormat { get; set; } = "text";
        
        /// <summary>
        /// The sampling temperature, between 0 and 1. Higher values like 0.8 will make the output more random,
        /// while lower values like 0.2 will make it more focused and deterministic
        /// </summary>
        public float Temperature { get; set; } = 0.0f;
    }
    
    /// <summary>
    /// Response model from OpenAI transcription API
    /// </summary>
    public class TranscriptionResponse
    {
        /// <summary>
        /// The transcribed text
        /// </summary>
        public string Text { get; set; }
    }
} 