﻿using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Surefire.Domain.Agents.Models;

namespace Surefire.Domain.Agents.Utilities
{
    /// <summary>
    /// Provides SQL injection prevention and query sanitization
    /// </summary>
    public class SqlSanitizer
    {
        private readonly ILogger<SqlSanitizer> _logger;

        // SQL injection prevention patterns
        private static readonly Regex DangerousPatterns = new(
            @"(DROP\s+TABLE|DELETE\s+FROM|TRUNCATE|ALTER\s+TABLE|CREATE\s+TABLE|INSERT\s+INTO|UPDATE\s+SET|EXEC|EXECUTE|xp_|sp_|DECLARE\s+@|SET\s+@|WAITFOR\s+DELAY|BENCHMARK|SLEEP|LOAD_FILE|INTO\s+OUTFILE|INTO\s+DUMPFILE|SHUTDOWN|KILL|REVOKE|GRANT|DENY|BACKUP|RESTORE|DBCC|CHECKPOINT|RECONFIGURE|RECOVERY|RESTORE|SHUTDOWN|KILL|REVOKE|GRANT|DENY|BACKUP|RESTORE|DBCC|CHECKPOINT|RECONFIGURE|RECOVERY)",
            RegexOptions.IgnoreCase | RegexOptions.Compiled);

        // Potentially dangerous SQL comments
        private static readonly Regex CommentPatterns = new(
            @"(--.*$|/\*.*\*/)",
            RegexOptions.IgnoreCase | RegexOptions.Compiled | RegexOptions.Multiline);

        // Query complexity indicators
        private static readonly string[] ComplexityIndicators = new[]
        {
            "WITH", "RECURSIVE", "UNION", "INTERSECT", "EXCEPT", "CROSS APPLY", "OUTER APPLY",
            "PIVOT", "UNPIVOT", "ROLLUP", "CUBE", "GROUPING SETS", "OVER", "PARTITION BY"
        };

        // Query cost indicators
        private static readonly string[] CostIndicators = new[]
        {
            "ORDER BY", "GROUP BY", "HAVING", "DISTINCT", "TOP", "OFFSET", "FETCH",
            "CASE", "WHEN", "THEN", "ELSE", "END", "LIKE", "IN", "EXISTS", "NOT EXISTS"
        };

        public SqlSanitizer(ILogger<SqlSanitizer> logger)
        {
            _logger = logger;
        }

        public SqlValidationResult Validate(string sql)
        {
            var result = new SqlValidationResult { IsValid = true, IsReadOnly = true };

            if (string.IsNullOrWhiteSpace(sql))
            {
                result.IsValid = false;
                result.Errors.Add("SQL query is empty");
                return result;
            }

            // Check for dangerous patterns
            if (DangerousPatterns.IsMatch(sql) || CommentPatterns.IsMatch(sql))
            {
                result.IsValid = false;
                result.Errors.Add("Query contains potentially dangerous operations or comments.");
                result.IsReadOnly = false;
            }

            // Ensure it's a SELECT statement
            var trimmedSql = sql.Trim();
            if (!trimmedSql.StartsWith("SELECT", StringComparison.OrdinalIgnoreCase))
            {
                result.IsValid = false;
                result.Errors.Add("Only SELECT queries are allowed.");
                result.IsReadOnly = false;
            }

            // Analyze query complexity
            var complexityScore = AnalyzeQueryComplexity(sql);
            result.Complexity = complexityScore.Complexity;
            //result.EstimatedCost = complexityScore.EstimatedCost;
            //result.EstimatedTimeout = complexityScore.EstimatedTimeout;
            result.Warnings.AddRange(complexityScore.Warnings);

            return result;
        }

        private (string Complexity, int EstimatedCost, int EstimatedTimeout, List<string> Warnings) AnalyzeQueryComplexity(string sql)
        {
            var warnings = new List<string>();
            var complexityScore = 0;
            var timeoutScore = 0;

            // Count joins
            var joinCount = Regex.Matches(sql, @"\bJOIN\b", RegexOptions.IgnoreCase).Count;
            complexityScore += joinCount * 2;
            timeoutScore += joinCount * 100;

            // Count subqueries
            var subqueryCount = Regex.Matches(sql, @"\(\s*SELECT", RegexOptions.IgnoreCase).Count;
            complexityScore += subqueryCount * 3;
            timeoutScore += subqueryCount * 200;

            // Check for complex operations
            foreach (var indicator in ComplexityIndicators)
            {
                if (sql.Contains(indicator, StringComparison.OrdinalIgnoreCase))
                {
                    complexityScore += 5;
                    timeoutScore += 300;
                    warnings.Add($"Query uses complex operation: {indicator}");
                }
            }

            // Check for expensive operations
            foreach (var indicator in CostIndicators)
            {
                if (sql.Contains(indicator, StringComparison.OrdinalIgnoreCase))
                {
                    complexityScore += 2;
                    timeoutScore += 150;
                }
            }

            // Determine complexity level
            string complexity;
            if (complexityScore > 20)
            {
                complexity = "High";
                warnings.Add("Query has high complexity and may impact performance");
            }
            else if (complexityScore > 10)
            {
                complexity = "Medium";
            }
            else
            {
                complexity = "Low";
            }

            // Estimate timeout in milliseconds
            var estimatedTimeout = Math.Min(timeoutScore, 30000); // Cap at 30 seconds

            return (complexity, complexityScore, estimatedTimeout, warnings);
        }

        /// <summary>
        /// Checks if a SQL query contains potentially dangerous operations
        /// </summary>
        /// <param name="sql">The SQL query to check</param>
        /// <returns>True if the query is safe, false otherwise</returns>
        public bool IsSafeSql(string sql)
        {
            if (string.IsNullOrWhiteSpace(sql))
            {
                return false;
            }

            // Check for dangerous patterns
            if (DangerousPatterns.IsMatch(sql))
            {
                _logger.LogWarning("Potentially dangerous SQL detected: {SQL}", sql);
                return false;
            }

            // Check for comment patterns that might be used to hide malicious code
            if (CommentPatterns.IsMatch(sql))
            {
                _logger.LogWarning("SQL with comments detected, potential SQL injection: {SQL}", sql);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Sanitizes a SQL query by removing dangerous operations
        /// </summary>
        /// <param name="sql">The SQL query to sanitize</param>
        /// <returns>A sanitized SQL query</returns>
        public string SanitizeSql(string sql)
        {
            if (string.IsNullOrWhiteSpace(sql))
            {
                return string.Empty;
            }

            // Remove comments
            var sanitized = CommentPatterns.Replace(sql, string.Empty);

            // Log if dangerous patterns were found
            if (DangerousPatterns.IsMatch(sanitized))
            {
                _logger.LogWarning("Sanitizing potentially dangerous SQL: {SQL}", sql);
            }

            // Replace dangerous patterns with empty strings
            sanitized = DangerousPatterns.Replace(sanitized, string.Empty);

            return sanitized.Trim();
        }

        /// <summary>
        /// Converts a natural language query to a parameterized SQL query
        /// </summary>
        /// <param name="sql">The SQL query</param>
        /// <param name="parameters">Dictionary of parameter names and values</param>
        /// <returns>A tuple containing the parameterized SQL and whether it's safe</returns>
        public (string SqlQuery, bool IsSafe) ParameterizeSql(string sql, Dictionary<string, object> parameters)
        {
            if (string.IsNullOrWhiteSpace(sql))
            {
                return (string.Empty, false);
            }

            var sanitized = SanitizeSql(sql);
            var isSafe = IsSafeSql(sanitized);

            // Replace parameter placeholders with @paramName format
            foreach (var param in parameters)
            {
                sanitized = sanitized.Replace($"{{{param.Key}}}", $"@{param.Key}");
            }

            return (sanitized, isSafe);
        }

        /// <summary>
        /// Estimates the potential result size of a query
        /// </summary>
        /// <param name="sql">The SQL query to analyze</param>
        /// <returns>Estimated result size in rows</returns>
        public int EstimateResultSize(string sql)
        {
            if (string.IsNullOrWhiteSpace(sql))
            {
                return 0;
            }

            var sizeScore = 0;

            // Check for TOP/LIMIT clauses
            var topMatch = Regex.Match(sql, @"TOP\s+(\d+)", RegexOptions.IgnoreCase);
            if (topMatch.Success && int.TryParse(topMatch.Groups[1].Value, out var topLimit))
            {
                return topLimit;
            }

            // Check for pagination
            var offsetMatch = Regex.Match(sql, @"OFFSET\s+(\d+)\s+ROWS", RegexOptions.IgnoreCase);
            var fetchMatch = Regex.Match(sql, @"FETCH\s+NEXT\s+(\d+)\s+ROWS", RegexOptions.IgnoreCase);
            if (offsetMatch.Success && fetchMatch.Success && int.TryParse(fetchMatch.Groups[1].Value, out var fetchLimit))
            {
                return fetchLimit;
            }

            // Estimate based on complexity
            var complexityScore = AnalyzeQueryComplexity(sql);
            sizeScore += complexityScore.EstimatedCost * 100;

            // Check for aggregations
            if (sql.Contains("GROUP BY", StringComparison.OrdinalIgnoreCase))
            {
                sizeScore = (int)(sizeScore * 0.1); // Aggregations typically reduce result size
            }

            // Check for DISTINCT
            if (sql.Contains("DISTINCT", StringComparison.OrdinalIgnoreCase))
            {
                sizeScore = (int)(sizeScore * 0.5); // DISTINCT typically reduces result size
            }

            return Math.Min(sizeScore, 10000); // Cap at 10,000 rows
        }

        /// <summary>
        /// Analyzes a query plan for potential performance issues
        /// </summary>
        /// <param name="sql">The SQL query to analyze</param>
        /// <returns>A list of potential performance issues</returns>
        public List<string> AnalyzeQueryPlan(string sql)
        {
            var issues = new List<string>();

            if (string.IsNullOrWhiteSpace(sql))
            {
                return issues;
            }

            // Check for missing indexes
            var joinPatterns = new[]
            {
                @"JOIN\s+\w+\s+ON\s+\w+\.\w+\s*=\s*\w+\.\w+",
                @"WHERE\s+\w+\.\w+\s*=\s*\w+\.\w+"
            };

            foreach (var pattern in joinPatterns)
            {
                var matches = Regex.Matches(sql, pattern, RegexOptions.IgnoreCase);
                foreach (Match match in matches)
                {
                    issues.Add($"Potential missing index for join condition: {match.Value}");
                }
            }

            // Check for table scans
            if (sql.Contains("FROM", StringComparison.OrdinalIgnoreCase) &&
                !sql.Contains("WHERE", StringComparison.OrdinalIgnoreCase))
            {
                issues.Add("Query may perform a table scan - consider adding WHERE clause");
            }

            // Check for expensive operations
            if (sql.Contains("LIKE '%", StringComparison.OrdinalIgnoreCase))
            {
                issues.Add("Leading wildcard in LIKE may prevent index usage");
            }

            if (sql.Contains("OR", StringComparison.OrdinalIgnoreCase))
            {
                issues.Add("OR conditions may prevent index usage - consider UNION");
            }

            if (sql.Contains("NOT IN", StringComparison.OrdinalIgnoreCase))
            {
                issues.Add("NOT IN may be expensive - consider NOT EXISTS");
            }

            // Check for implicit conversions
            var implicitConversionPatterns = new[]
            {
                @"WHERE\s+\w+\s*=\s*'[^']*'",  // String comparison
                @"WHERE\s+\w+\s*=\s*\d+"       // Number comparison
            };

            foreach (var pattern in implicitConversionPatterns)
            {
                var matches = Regex.Matches(sql, pattern, RegexOptions.IgnoreCase);
                foreach (Match match in matches)
                {
                    issues.Add($"Potential implicit conversion in condition: {match.Value}");
                }
            }

            return issues;
        }
    }
}