﻿@inherits AppComponentBase
@using Surefire.Domain.Policies.Models
@using Microsoft.AspNetCore.Components
@implements IDialogContentComponent<Product>
@inject SharedService SharedService

<!-- Header -->
<FluentDialogHeader ShowDismiss="true">
    <FluentStack VerticalAlignment="VerticalAlignment.Center">
        <FluentIcon Value="@(new Icons.Regular.Size24.WindowApps())" />
        <FluentLabel Typo="Typography.PaneHeader">
            @Dialog.Instance.Parameters.Title
        </FluentLabel>
    </FluentStack>
</FluentDialogHeader>

<!-- Body -->
<FluentDialogBody>
    <EditForm Model="Content">
        <DataAnnotationsValidator />

        <FluentTextField @bind-Value="Content.LineName" Placeholder="Line Name" Label="Line Name" />
        <FluentTextField @bind-Value="Content.LineNickname" Placeholder="Line Nickname" Label="Line Nickname" />
        <FluentTextField @bind-Value="Content.LineCode" Placeholder="Line Code" Label="Line Code" />
        <FluentTextArea @bind-Value="Content.Description" Placeholder="Description" Label="Description" />

        <div style="color: var(--error);">
            <FluentValidationSummary />
        </div>
    </EditForm>
</FluentDialogBody>

<!-- Footer -->
<FluentDialogFooter>
    <FluentButton Appearance="Appearance.Accent" OnClick="@SaveAsync">Save</FluentButton>
    <FluentButton Appearance="Appearance.Neutral" OnClick="@CancelAsync">Cancel</FluentButton>
</FluentDialogFooter>

@code {
    [CascadingParameter] public FluentDialog Dialog { get; set; } = default!;
    [Parameter] public Product Content { get; set; } = default!;

    protected override void OnInitialized()
    {
        //Nothing
    }

    private async Task SaveAsync()
    {
        if (string.IsNullOrWhiteSpace(Content.LineName))
        {
            // Validation error: LineName is required
            return;
        }

        await Dialog.CloseAsync(Content);
    }

    private async Task CancelAsync()
    {
        await Dialog.CancelAsync();
    }
}
