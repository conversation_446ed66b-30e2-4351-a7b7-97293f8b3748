using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;

namespace Surefire.Domain.OpenAI.Simple
{
    public class OpenAISimpleService : IOpenAISimpleService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<OpenAISimpleService> _logger;
        private const string OpenAiChatCompletionsEndpoint = "https://api.openai.com/v1/chat/completions";

        // Recommended: Use IHttpClientFactory by injecting it
        public OpenAISimpleService(IHttpClientFactory httpClientFactory, IConfiguration configuration, ILogger<OpenAISimpleService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _httpClient = httpClientFactory.CreateClient("OpenAI"); // Use a named client if configuring elsewhere

            // --- API Key Handling ---
            // Best practice: Get API key from configuration (appsettings.json, user secrets, environment variables, Key Vault etc.)
            string? apiKey = configuration["OpenAI:ApiKey"] // Example: expects section "OpenAI" with key "ApiKey"
                          ?? Environment.GetEnvironmentVariable("OPENAI_API_KEY"); // Fallback to environment variable

            if (string.IsNullOrWhiteSpace(apiKey))
            {
                _logger.LogError("OpenAI API Key is missing. Configure it in IConfiguration (e.g., 'OpenAI:ApiKey') or the 'OPENAI_API_KEY' environment variable.");
                throw new InvalidOperationException("OpenAI API Key is missing.");
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
            // No "OpenAI-Beta" header needed for standard chat completions
        }

        /// <inheritdoc/>
        public async Task<string?> GenerateResponseAsync(
            string prompt,
            string model = "gpt-4o-mini",
            CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(prompt))
            {
                _logger.LogWarning("GenerateResponseAsync called with empty prompt.");
                return null; // Or throw ArgumentException
            }
            if (string.IsNullOrWhiteSpace(model))
            {
                _logger.LogWarning("GenerateResponseAsync called with empty model name.");
                return null; // Or throw ArgumentException
            }

            var requestPayload = new ChatCompletionRequest
            {
                Model = model,
                Messages = new[] { new ChatMessage("user", prompt) },
                // Add other parameters like Temperature, MaxTokens if needed
                // Temperature = 0.7,
                // MaxTokens = 150
            };

            try
            {
                _logger.LogInformation("Sending prompt to OpenAI model {Model}...", model);
                using var response = await _httpClient.PostAsJsonAsync(
                    OpenAiChatCompletionsEndpoint,
                    requestPayload,
                    options: (JsonSerializerOptions?)null, // Explicitly pass null for options
                    cancellationToken: cancellationToken); // Pass cancellationToken by name too for clarit

                if (response.IsSuccessStatusCode)
                {
                    var completionResponse = await response.Content.ReadFromJsonAsync<ChatCompletionResponse>(cancellationToken: cancellationToken);
                    string? resultText = completionResponse?.Choices?.FirstOrDefault()?.Message?.Content?.Trim();

                    if (string.IsNullOrEmpty(resultText))
                    {
                        _logger.LogWarning("OpenAI response was successful but contained no text content.");
                        return null;
                    }

                    _logger.LogInformation("Received response from OpenAI.");
                    return resultText;
                }
                else
                {
                    string errorBody = await response.Content.ReadAsStringAsync(cancellationToken);
                    _logger.LogError("OpenAI API request failed with status code {StatusCode}. Reason: {ReasonPhrase}. Body: {ErrorBody}",
                        response.StatusCode, response.ReasonPhrase, errorBody);
                    // Consider throwing a specific exception here if needed
                    // throw new OpenAIApiException($"OpenAI request failed: {response.StatusCode}", errorBody);
                    return null;
                }
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "HTTP request to OpenAI API failed.");
                return null;
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Failed to serialize request or deserialize OpenAI response.");
                return null;
            }
            catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
            {
                _logger.LogError(ex, "Request to OpenAI API timed out.");
                return null;
            }
            catch (TaskCanceledException ex) when (cancellationToken.IsCancellationRequested)
            {
                _logger.LogInformation("Request to OpenAI API was canceled.");
                // Don't return null here, let the cancellation propagate if required by caller
                throw;
            }
            catch (Exception ex) // Catch broader exceptions
            {
                _logger.LogError(ex, "An unexpected error occurred while communicating with OpenAI.");
                return null;
            }
        }

        // --- Internal DTOs for Request/Response ---
        // Use records for concise, immutable DTOs (C# 9+) or classes

        private record ChatCompletionRequest
        {
            [JsonPropertyName("model")]
            public string Model { get; init; } = string.Empty;

            [JsonPropertyName("messages")]
            public ChatMessage[] Messages { get; init; } = Array.Empty<ChatMessage>();

            // Optional parameters:
            // [JsonPropertyName("temperature")]
            // [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
            // public double? Temperature { get; init; }

            // [JsonPropertyName("max_tokens")]
            // [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
            // public int? MaxTokens { get; init; }
        }

        private record ChatMessage(
            [property: JsonPropertyName("role")] string Role,
            [property: JsonPropertyName("content")] string Content
        );

        private record ChatCompletionResponse
        {
            [JsonPropertyName("id")]
            public string? Id { get; init; }

            [JsonPropertyName("object")]
            public string? ObjectType { get; init; } // e.g., "chat.completion"

            [JsonPropertyName("created")]
            public long CreatedUnixTime { get; init; }

            [JsonPropertyName("model")]
            public string? Model { get; init; }

            [JsonPropertyName("choices")]
            public Choice[]? Choices { get; init; }

            // Add Usage property if needed
            // [JsonPropertyName("usage")]
            // public UsageStats? Usage { get; init; }
        }

        private record Choice
        {
            [JsonPropertyName("index")]
            public int Index { get; init; }

            [JsonPropertyName("message")]
            public ChatMessage? Message { get; init; }

            // Could also be "stop", "length", "content_filter", "tool_calls"
            [JsonPropertyName("finish_reason")]
            public string? FinishReason { get; init; }
        }

        // Optional: Define a custom exception class
        // public class OpenAIApiException : Exception
        // {
        //     public string? ErrorBody { get; }
        //     public OpenAIApiException(string message, string? errorBody = null) : base(message)
        //     {
        //         ErrorBody = errorBody;
        //     }
        //     public OpenAIApiException(string message, Exception innerException, string? errorBody = null) : base(message, innerException)
        //     {
        //         ErrorBody = errorBody;
        //     }
        // }
    }
}