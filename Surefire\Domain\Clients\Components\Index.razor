﻿@page "/Clients/List"
@using Surefire.Domain.Clients.Models
@using Surefire.Domain.Clients.Services
@using Microsoft.EntityFrameworkCore
@inject ClientService ClientService

<_toolbar ClientId="@ClientId" ClientName="@SelectedClientName" PageName="List" ClientLoaded="true" />

<div class="page-content-datagrid">
    <div class="fluent-data-grid">
        <FluentDataGrid Items="@FilteredClients" ResizableColumns="true" Pagination="@pagination" ShowHover="true"
                        OnRowClick="@((FluentDataGridRow<Client> row) => HandleRowClick(row))">
            <PropertyColumn Property="@(p => p.ClientId)" Title="Client Id" Width="60px" Sortable="true" />
            <PropertyColumn Property="@(p => p.Name)" Title="Name" Width="350px" Sortable="true" />
            <PropertyColumn Property="@(p => p.PhoneNumber)" Title="Phone Number" Sortable="true" />
            <PropertyColumn Property="@(p => p.Email)" Title="Email" Sortable="true" />
            <PropertyColumn Property="@(p => p.Website)" Title="Website" Sortable="true" />
            <PropertyColumn Property="@(p => p.LookupCode)" Title="Lookup Code" Sortable="true" />
        </FluentDataGrid>
        <div class="fluent-data-grid__bottombar">
            <div class="fluent-data-grid__pagination">
                <FluentPaginator State="@pagination" />
            </div>

            <div class="fluent-data-grid__search">
                <FluentSearch Placeholder="Search..." @bind-Value="SearchTerm" @oninput="HandleSearchInput" />
            </div>
        </div>
    </div>
</div>

@code {
    public IQueryable<Client> ClientData { get; set; }
    public IQueryable<Client> FilteredClients { get; set; }
    public int ClientId { get; set; }
    public string SelectedClientName { get; set; } = "";
    public string SearchTerm { get; set; } = string.Empty;
    PaginationState pagination = new PaginationState { ItemsPerPage = 18 };
    private System.Timers.Timer debounceTimer;
    bool SelectFromEntireRow = true;
    [CascadingParameter] public Action<string> UpdateHeader { get; set; }

    protected override async Task OnInitializedAsync()
    {
        UpdateHeader?.Invoke("Clients");
        ClientData = ClientService.GetAllClients().OrderBy(c => c.Name);
        FilteredClients = ClientData;
        debounceTimer = new System.Timers.Timer(300); // Debounce for 300ms
        debounceTimer.AutoReset = false; // Prevent multiple triggers
        debounceTimer.Elapsed += async (sender, e) =>
        {
            await InvokeAsync(ApplySearchFilter); // Apply the search filter after the debounce period
        };
    }

    private void HandleSearchInput(Microsoft.AspNetCore.Components.ChangeEventArgs e)
    {
        if (e.Value is string searchTerm)
        {
            SearchTerm = searchTerm;
            debounceTimer.Stop();
            debounceTimer.Start(); // Start the debounce timer
        }
    }

    private void ApplySearchFilter()
    {
        FilteredClients = (string.IsNullOrWhiteSpace(SearchTerm)
        ? ClientData
        : ClientData.Where(c =>
              EF.Functions.Like(c.Name.ToLower(), $"%{SearchTerm.ToLower()}%")
           || EF.Functions.Like(c.PhoneNumber.ToLower(), $"%{SearchTerm.ToLower()}%")
           || EF.Functions.Like(c.Email.ToLower(), $"%{SearchTerm.ToLower()}%")
           || EF.Functions.Like(c.Website.ToLower(), $"%{SearchTerm.ToLower()}%")
           || EF.Functions.Like(c.LookupCode.ToLower(), $"%{SearchTerm.ToLower()}%")))
           .OrderBy(c => c.Name);

        InvokeAsync(StateHasChanged); // Trigger UI refresh after filtering
    }

    private void HandleRowDoubleClick(FluentDataGridRow<Client> row)
    {
        if (row != null && row.Item != null)
        {
            var selectedClient = row.Item;
            row.Class = "selected-row";
            SelectedClientName = row.Item.Name + ": ";
            ClientId = selectedClient.ClientId;
            StateHasChanged();
        }
    }

    private void HandleRowSelected(FluentDataGridRow<Client> row)
    {
        if (row != null && row.Item != null)
        {
            var selectedClient = row.Item;
            ClientId = selectedClient.ClientId;
            Navigation.NavigateTo($"/Clients/{ClientId}");
        }
    }

    private void HandleRowClick(FluentDataGridRow<Client> row)
    {
        if (row != null && row.Item != null)
        {
            var selectedClient = row.Item;
            ClientId = selectedClient.ClientId;
            Navigation.NavigateTo($"/Clients/{ClientId}");
        }
    }
    private void Dispose()
    {
        ClientData = null;
        FilteredClients = null;
        debounceTimer.Dispose();
    }
}
