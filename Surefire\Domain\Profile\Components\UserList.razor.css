﻿.user-list-container {
    margin: 20px 0;
    overflow-x: auto;
}

.user-list-table {
    width: 100%;
    border-collapse: collapse;
    font-family: Arial, sans-serif;
    font-size: 14px;
    color: #333;
}

    .user-list-table th, .user-list-table td {
        border: 1px solid #ccc;
        padding: 10px;
        text-align: left;
    }

    .user-list-table th {
        background-color: #f7f7f7;
        font-weight: bold;
    }

    .user-list-table tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    .user-list-table tr:hover {
        background-color: #f1f1f1;
    }

    .user-list-table td {
        padding: 10px;
        vertical-align: middle;
    }

    .user-list-table .e-inplaceeditor {
        width: 100%; /* Ensure editor fits table cell */
    }

.user-list-container {
    overflow-x: auto; /* Prevent overflow issues */
}
.e-inplaceeditor .e-editable-value-container .e-editable-value {
    border-bottom: none;
}
/* To change background color and border radius of in-place editor container */
.e-inplaceeditor .e-editable-value-container {
    background: #e9ecef;
    border-radius: 4px;
}
    /* To show the edit icon */
    .e-inplaceeditor .e-editable-value-container .e-editable-overlay-icon {
        visibility: visible;
    }