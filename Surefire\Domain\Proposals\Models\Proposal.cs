using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Surefire.Domain.Clients.Models;
using Surefire.Data;
using Surefire.Domain.Policies.Models;
using Surefire.Domain.Attachments.Models;
using Microsoft.AspNetCore.Identity;

namespace Surefire.Domain.Proposals
{
    public class Proposal
    {
        [Key]
        public int ProposalId { get; set; }
        public string? RawJson { get; set; } = "{}";
        public string? CleanedJson { get; set; } = "{}";
        public string? ApprovedJson { get; set; } = "{}";
        [Required]
        public DateTime DateCreated { get; set; } = DateTime.UtcNow;
        public DateTime DateModified { get; set; } = DateTime.UtcNow;
        public int Status { get; set; } = 0;

        // Foreign keys and navigation properties
        public int? ClientId { get; set; }
        public Client? Client { get; set; }
        public int? AttachmentId { get; set; }
        public Attachment? Attachment { get; set; }
        public int? RawJsonAttachmentId { get; set; }
        public Attachment? RawJsonAttachment { get; set; }
        public int? CleanedJsonAttachmentId { get; set; }
        public Attachment? CleanedJsonAttachment { get; set; }
        public int? PolicyId { get; set; }
        public Policy? Policy { get; set; }
        public bool IsApproved { get; set; }

        // Numbers
        public int? BrokerFee { get; set; }
        public int? DownPaymentPercent { get; set; }
        public int? MinimumEarnedPercent { get; set; }
        public decimal? PurePremium { get; set; }
        public decimal? TotalTaxesAndFees { get; set; }
        public decimal? TotalDeposit { get; set; }
        public decimal? TotalCost { get; set; }

        // Proposal Setup
        public bool IsFinanced { get; set; }
        public bool SendASAP { get; set; }
        public DateTime? SendDate { get; set; }
        public SendInstructions? SendInstructions { get; set; }
        public DocuSignOptions? DocuSignOptions { get; set; }
        public BillType? BillType { get; set; }        
        [Column(TypeName = "text")]
        public string? SpecialInstructions { get; set; }

        // Packager
        public bool IncludeSL2 { get; set; }
        public string? IncludeSL2Code { get; set; }
        public string? IncludeSL2BusinessDesc { get; set; }
        public bool IncludeD1 { get; set; }
        public bool IncludeD2 { get; set; }
        public string? ExtractPages { get; set; }


        // Foreign keys for users
        [Required]
        public string? CreatedById { get; set; }
        public ApplicationUser? CreatedBy { get; set; }
        public string? ModifiedById { get; set; }
        public ApplicationUser? ModifiedBy { get; set; }

        // Fee Items
        public ICollection<ProposalFeeItem>? ProposalFeeItems { get; set; }

        public int? RenewalId { get; set; }
        public Surefire.Domain.Renewals.Models.Renewal? Renewal { get; set; }
        public int? AttachmentGroupId { get; set; }
        public Surefire.Domain.Attachments.Models.AttachmentGroup? AttachmentGroup { get; set; }
        
    }

    public enum SendInstructions
    {
        DocuSign,
        DocuSignDraft,
        WordDocEpicAttachment,
        WordDocEmail,
        PdfEmail
    }

    public enum DocuSignOptions
    {
        NoReminders,
        DailyReminders,
        BiWeeklyReminders
    }
    public enum BillType
    {
        Direct,
        Agency
    }
}
