@using Microsoft.FluentUI.AspNetCore.Components
@using Surefire.Domain.Contacts.Models
@using Surefire.Domain.Clients.Services
@using Surefire.Domain.Renewals.Services
@using System.Globalization
@inject ClientService ClientService
@inject RenewalService RenewalService
@inject ILogger<PayLinkDialog> Logger

<FluentDialog @bind-Hidden="Hidden" Modal="true" PreventScroll="true">
   <FluentDialogHeader>
        <FluentLabel Typo="Typography.PaneHeader">
            <FluentIcon Value="@(new Icons.Regular.Size24.Payment())" Color="Color.Accent" />
            Create Payment Link
        </FluentLabel>
    </FluentDialogHeader> 

    <FluentDialogBody>
        @if (_loading)
        {
            <div class="loading-container">
                <FluentProgressRing />
                <FluentLabel Typo="Typography.Body">Loading client information...</FluentLabel>
            </div>
        }
        else if (_loadError != null)
        {
            <FluentMessageBar Intent="MessageIntent.Error">
                <FluentIcon Value="@(new Icons.Regular.Size20.ErrorCircle())" Color="Color.Custom" CustomColor="var(--error)" Slot="start" />
                <FluentLabel Typo="Typography.Body" Color="Color.Accent">
                    Error loading data: @_loadError
                </FluentLabel>
            </FluentMessageBar>
        }
        else
        {
            <FluentStack Orientation="Orientation.Vertical" VerticalGap="16">
                <!-- Client Information Display -->
                <FluentCard>
                    <FluentStack Orientation="Orientation.Vertical" VerticalGap="8">
                        <FluentLabel Typo="Typography.Subject" Weight="FontWeight.Bold">
                            Client: @_clientName
                        </FluentLabel>
                        @if (!string.IsNullOrEmpty(_productLineName))
                        {
                            <FluentLabel Typo="Typography.Body" Color="Color.Accent">
                                Product: @_productLineName
                            </FluentLabel>
                        }
                    </FluentStack>
                </FluentCard>

                <!-- Contact Selection -->
                <FluentStack Orientation="Orientation.Vertical" VerticalGap="4">
                    <FluentLabel Typo="Typography.Body" Weight="FontWeight.Bold">
                        Select Contact *
                    </FluentLabel>
                    <FluentSelect @bind-SelectedOption="@selectedContact"
                                  Items="@_contacts"
                                  OptionText="@(c => GetContactDisplayText(c))"
                                  OptionValue="@(c => c.ContactId.ToString())"
                                  TOption="Contact"
                                  Width="100%" />



                    @if (selectedContact != null && selectedContact.ContactId > 0)
                    {
                        <FluentLabel Typo="Typography.Body" Color="Color.Accent">
                            Email: @GetPrimaryEmail(selectedContact)
                        </FluentLabel>
                    }
                </FluentStack>

                <!-- Payment Amount -->
                <FluentStack Orientation="Orientation.Vertical" VerticalGap="4">
                    <FluentLabel Typo="Typography.Body" Weight="FontWeight.Bold">
                        Payment Amount *
                    </FluentLabel>
                    <FluentNumberField @bind-Value="@_paymentAmount"
                                       Format="C2"
                                       Step="1"
                                       Min="0.01"
                                       Width="100%"
                                       Placeholder="Enter payment amount" />
                </FluentStack>

                <!-- Payment Description -->
                <FluentStack Orientation="Orientation.Vertical" VerticalGap="4">
                    <FluentLabel Typo="Typography.Body" Weight="FontWeight.Bold">
                        Payment Description *
                    </FluentLabel>
                    <FluentTextArea @bind-Value="@_paymentDescription"
                                    Rows="3"
                                    Resize="TextAreaResize.Vertical"
                                    Width="100%"
                                    Placeholder="Enter what this payment is for" />
                </FluentStack>

                <!-- Validation Messages -->
                @if (_validationErrors.Any())
                {
                    <FluentMessageBar Intent="MessageIntent.Warning">
                        <FluentIcon Value="@(new Icons.Regular.Size20.Warning())" Color="Color.Custom" CustomColor="var(--warning)" Slot="start" />
                        <FluentStack Orientation="Orientation.Vertical" VerticalGap="2">
                            @foreach (var error in _validationErrors)
                            {
                                <FluentLabel Typo="Typography.Body">• @error</FluentLabel>
                            }
                        </FluentStack>
                    </FluentMessageBar>
                }
            </FluentStack>
        }
    </FluentDialogBody>

    <FluentDialogFooter>
        <FluentButton Appearance="Appearance.Neutral" 
                      OnClick="@OnCancel"
                      Disabled="@_processing">
            Cancel
        </FluentButton>
        <FluentButton Appearance="Appearance.Accent" 
                      OnClick="@OnSendPayLink"
                      Disabled="@(_processing || _loading || _loadError != null || !IsValid())"
                      Loading="@_processing">
            <FluentIcon Value="@(new Icons.Regular.Size20.Send())" Slot="start" />
            Send Payment Link
        </FluentButton>
    </FluentDialogFooter>
</FluentDialog>

@code {
    [Parameter] public bool Hidden { get; set; }
    [Parameter] public EventCallback<bool> HiddenChanged { get; set; }
    [Parameter] public int RenewalId { get; set; }
    [Parameter] public EventCallback OnClose { get; set; }
    [Parameter] public EventCallback<PayLinkDialogResult> OnPaymentLinkSent { get; set; }

    // Private fields

    // Private fields
    private bool _loading = true;
    private bool _processing = false;
    private string? _loadError = null;
    private string _clientName = "";
    private string _productLineName = "";
    private int _clientId = 0;
    
    private List<Contact> _contacts = new();
    private Contact? selectedContact = null;
    private decimal _paymentAmount = 400.00m;
    private string _paymentDescription = "";
    
    private List<string> _validationErrors = new();

    protected override async Task OnParametersSetAsync()
    {
        if (!Hidden && RenewalId > 0 && _clientId == 0) // Only load once, when dialog is about to be shown
        {
            await LoadRenewalData();
        }
    }

    private async Task LoadRenewalData()
    {
        try
        {
            _loading = true;
            _loadError = null;
            StateHasChanged();

            // Get renewal data to identify client
            var renewalData = await RenewalService.GetRenewalAgentDataAsync(RenewalId);
            
            if (renewalData.ClientId == null)
            {
                _loadError = "Unable to load client information from renewal";
                return;
            }

            _clientId = renewalData.ClientId.Value;
            _clientName = renewalData.ClientName ?? $"Client {_clientId}";
            _productLineName = renewalData.ProductType ?? "";

            // Set default payment description
            if (!string.IsNullOrEmpty(_productLineName))
            {
                _paymentDescription = $"Payment for {_productLineName}";
            }
            else
            {
                _paymentDescription = "Insurance payment";
            }

            // Load client contacts
            _contacts = await ClientService.GetContactsByClientIdAsync(_clientId);
            
            if (!_contacts.Any())
            {
                _loadError = "No contacts found for this client";
                return;
            }

            // Pre-select the primary contact (if there's one with a primary email or just the first one)
            selectedContact = _contacts.FirstOrDefault(c => 
                c.PrimaryEmail != null || 
                c.EmailAddresses?.Any(e => e.IsPrimary) == true ||
                c.EmailAddresses?.Any() == true) ?? _contacts.FirstOrDefault();
            
            Logger.LogInformation("Loaded {ContactCount} contacts for client {ClientId}", _contacts.Count, _clientId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading renewal data for renewal {RenewalId}", RenewalId);
            _loadError = $"Error loading data: {ex.Message}";
        }
        finally
        {
            _loading = false;
            StateHasChanged();
        }
    }

    private string GetContactDisplayText(Contact contact)
    {
        if (contact == null) return "";
        
        var name = $"{contact.FirstName} {contact.LastName}".Trim();
        var email = GetPrimaryEmail(contact);
        
        if (!string.IsNullOrEmpty(email))
        {
            return $"{name} ({email})";
        }
        
        return name;
    }

    private string GetPrimaryEmail(Contact contact)
    {
        if (contact == null) return "";
        
        // First check if there's a PrimaryEmail navigation property
        if (contact.PrimaryEmail != null)
        {
            return contact.PrimaryEmail.Email;
        }
        
        // Then look for an email marked as primary
        var primaryEmail = contact.EmailAddresses?.FirstOrDefault(e => e.IsPrimary);
        if (primaryEmail != null)
        {
            return primaryEmail.Email;
        }
        
        // Finally, fall back to the first email address
        return contact.EmailAddresses?.FirstOrDefault()?.Email ?? "";
    }

    private bool IsValid()
    {
        _validationErrors.Clear();
        
        if (selectedContact == null || selectedContact.ContactId <= 0)
        {
            _validationErrors.Add("Please select a contact");
        }
        else if (string.IsNullOrEmpty(GetPrimaryEmail(selectedContact)))
        {
            _validationErrors.Add("Selected contact has no email address");
        }
        
        if (_paymentAmount <= 0)
        {
            _validationErrors.Add("Payment amount must be greater than $0");
        }
        
        if (string.IsNullOrWhiteSpace(_paymentDescription))
        {
            _validationErrors.Add("Payment description is required");
        }
        
        return !_validationErrors.Any();
    }

    private async Task OnSendPayLink()
    {
        if (!IsValid()) return;

        try
        {
            _processing = true;
            StateHasChanged();

            var result = new PayLinkDialogResult
            {
                ClientId = _clientId,
                ContactId = selectedContact!.ContactId,
                PaymentAmount = _paymentAmount,
                PaymentDescription = _paymentDescription.Trim(),
                ClientName = _clientName,
                ContactName = $"{selectedContact.FirstName} {selectedContact.LastName}",
                ContactEmail = GetPrimaryEmail(selectedContact)
            };

            await OnPaymentLinkSent.InvokeAsync(result);
            await OnClose.InvokeAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error sending payment link");
            _loadError = $"Error sending payment link: {ex.Message}";
        }
        finally
        {
            _processing = false;
            StateHasChanged();
        }
    }

    private async Task OnCancel()
    {
        await CloseDialog();
    }

    private async Task CloseDialog()
    {
        if (HiddenChanged.HasDelegate)
            await HiddenChanged.InvokeAsync(true);
        if (OnClose.HasDelegate)
            await OnClose.InvokeAsync();
    }

    // Reset dialog state when closed
    protected override void OnParametersSet()
    {
        if (Hidden)
        {
            // Reset state when dialog is closed
            _clientId = 0;
            selectedContact = null;
            _paymentAmount = 400.00m;
            _paymentDescription = "";
            _validationErrors.Clear();
            _loadError = null;
        }
    }

    public class PayLinkDialogResult
    {
        public int ClientId { get; set; }
        public int ContactId { get; set; }
        public decimal PaymentAmount { get; set; }
        public string PaymentDescription { get; set; } = "";
        public string ClientName { get; set; } = "";
        public string ContactName { get; set; } = "";
        public string ContactEmail { get; set; } = "";
    }
}

<style>
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        padding: 24px;
    }
</style> 