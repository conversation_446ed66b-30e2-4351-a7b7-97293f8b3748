﻿.auto-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 16px;
}

.coverage-section {
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--neutral-foreground-rest);
}

.field-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.field-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.field-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.field-label {
    font-weight: 500;
    color: var(--neutral-foreground-rest);
}

.switch-container {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--neutral-stroke-divider-rest);
}

    .switch-container:last-child {
        border-bottom: none;
    }

.attachment-section {
    margin-top: 16px;
    padding: 16px;
    border: 2px dashed var(--neutral-stroke-accessible-rest);
    border-radius: 8px;
    transition: all 0.2s ease;
}

    .attachment-section.has-file {
        border-style: solid;
        background-color: var(--neutral-layer-2);
    }

.file-display {
    display: flex;
    align-items: center;
    gap: 12px;
}

.file-icon {
    color: var(--accent-foreground-rest);
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 500;
    color: var(--neutral-foreground-rest);
}

.file-size {
    font-size: 12px;
    color: var(--neutral-foreground-hint);
}

.file-action-btn {
    min-width: auto;
}

.upload-area {
    text-align: center;
    padding: 24px;
}

.upload-icon {
    color: var(--accent-foreground-rest);
    margin-bottom: 12px;
}

.upload-text {
    font-weight: 500;
    color: var(--neutral-foreground-rest);
    margin-bottom: 4px;
}

.upload-hint {
    font-size: 12px;
    color: var(--neutral-foreground-hint);
}

.drivers-section, .vehicles-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.drivers-table-header, .vehicles-table-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 20px 24px;
    background-color: var(--neutral-layer-2);
    font-size: 18px;
    font-weight: 600;
    color: var(--neutral-foreground-rest);
    border-bottom: 1px solid var(--neutral-stroke-divider-rest);
}

.drivers-table, .vehicles-table {
    width: 100%;
    border-collapse: collapse;
}

    .drivers-table th, .vehicles-table th {
        text-align: left;
        padding: 12px 16px;
        background-color: var(--neutral-layer-1);
        border-bottom: 1px solid var(--neutral-stroke-divider-rest);
        font-weight: 600;
        color: var(--neutral-foreground-rest);
        font-size: 14px;
    }

    .drivers-table td, .vehicles-table td {
        padding: 12px 16px;
        border-bottom: 1px solid var(--neutral-stroke-divider-rest);
        color: var(--neutral-foreground-rest);
        font-size: 14px;
    }

    .drivers-table tbody tr:hover, .vehicles-table tbody tr:hover {
        background-color: var(--neutral-layer-1);
    }

.code-cell {
    background-color: var(--accent-fill-rest);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}
.switch-stack {
    align-items: end !important;
}
.switch-container span {
    font-size: .85em;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin-left: 6px;
}
.vin-cell {
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

.empty-state {
    text-align: center;
    padding: 48px 24px;
    color: var(--neutral-foreground-hint);
}

.empty-state-icon {
    margin-bottom: 16px;
    opacity: 0.5;
}

.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 48px;
    color: var(--neutral-foreground-hint);
}
