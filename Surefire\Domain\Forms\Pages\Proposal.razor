﻿@page "/Proposals/{ClientId:int}"
@using System.IO
@using Newtonsoft.Json
@using Newtonsoft.Json.Linq
@using Syncfusion.Pdf
@using Syncfusion.Pdf.Parsing
@using Syncfusion.Blazor.SfPdfViewer
@using Surefire.Domain.Proposals

@inject ProposalService ProposalService
@inject IWebHostEnvironment Environment
@inject IHttpClientFactory _http
@inject IJSRuntime JS

<div class="page-toolbar">
    <SfButton CssClass="e-primary" IconCss="e-icons e-plus-icon">New Certificate</SfButton>

    <span class="sf-verthr2"></span>

    <a id="Reset" class="toolbar-link">
        <FluentIcon Value="@(new Icons.Regular.Size24.Eraser())" />
        <span class="toolbar-text">Reset</span>
    </a>

    <a id="Duplicate" class="toolbar-link">
        <FluentIcon Value="@(new Icons.Regular.Size24.DocumentCopy())" />
        <span class="toolbar-text">Duplicate</span>
    </a>
</div>

PROPOSALS
<Proposals ClientId="ClientId" />

@code {
    [Parameter]
    public int ClientId { get; set; }

    protected override async Task OnInitializedAsync()
    {
        //Get proposal
        //proposal = await ProposalService.GetProposal(ProposalId);
    }
}
