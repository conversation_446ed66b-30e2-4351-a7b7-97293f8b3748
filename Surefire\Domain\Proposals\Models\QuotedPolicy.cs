﻿using System.Collections.Generic;

namespace Surefire.Domain.Proposals.Models
{
    public class QuotedPolicy
    {
        // Root-level properties
        public string ClientName { get; set; }
        public string TodaysDate { get; set; }
        public string ClientAddressLine1 { get; set; }
        public string ClientAddressLine2 { get; set; }
        public string ClientAddressLine3 { get; set; }
        public string ContactFirstName { get; set; }
        public string PrimaryCoverage { get; set; }
        public string EffectiveDate { get; set; }
        public string ExpirationDate { get; set; }
        public string CarrierName { get; set; }
        public string CarrierRating { get; set; }
        public string RenewalOfPolicyNumber { get; set; }
        public string PolicyTermLength { get; set; }
        public string QuoteValidUntil { get; set; }
        public string PrerequisitesNeededToBind { get; set; }
        public string AdditionalTermsAndConditions { get; set; }
        public string AdditionalDisclaimers { get; set; }

        // Collections for nested elements
        public List<Coverage> Coverages { get; set; } = new List<Coverage>();
        public List<RatingBasis> RatingBasises { get; set; } = new List<RatingBasis>();
        public List<Location> Locations { get; set; } = new List<Location>();
        public List<Endorsement> Endorsements { get; set; } = new List<Endorsement>();
        public List<TaxFeeItem> TaxFeeItems { get; set; } = new List<TaxFeeItem>();

        // Additional root-level financial data
        public string TotalTaxesAndFeesDollarAmount { get; set; }
        public string MinimumEarnedPercentage { get; set; }
        public string MinimumEarnedDollarAmount { get; set; }
        public string TotalDepositDollarAmount { get; set; }
        public string TotalDownPayment { get; set; }
        public string PurePremiumDollarAmount { get; set; }
        public string TotalPolicyCost { get; set; }
    }

    public class Coverage
    {
        public string CoverageName { get; set; }
        public string CoverageType { get; set; }
        public string CoverageRetroActiveDate { get; set; }
        public List<Limit> Limits { get; set; } = new List<Limit>();
        public List<Deductible> Deductibles { get; set; } = new List<Deductible>();
    }

    public class Limit
    {
        public string LimitName { get; set; }
        public string LimitDollarAmount { get; set; }
    }

    public class Deductible
    {
        public string DeductibleName { get; set; }
        public string DeductibleDollarAmount { get; set; }
    }

    public class RatingBasis
    {
        public string LocationNumber { get; set; }
        public string RateClassCode { get; set; }
        public string RateDescription { get; set; }
        public string RateExposure { get; set; }
        public string RateBasis { get; set; }
        public string NetRate { get; set; }
        public string GrossRate { get; set; }
        public string RatePremium { get; set; }
        public string NumberOfFullTimeEmployees { get; set; }
        public string NumberOfPartTimeEmployees { get; set; }
        // If needed, you can include SubLimitAmount as well:
        public string SubLimitAmount { get; set; }
    }

    public class Location
    {
        public string LocationNumber { get; set; }
        public string LocationDescription { get; set; }
        public string LocationFullAddress { get; set; }
    }

    public class Endorsement
    {
        public string EndorsementNumber { get; set; }
        public string EndorsementName { get; set; }
    }

    public class TaxFeeItem
    {
        public string TaxFeeItemName { get; set; }
        public string TaxFeeItemDollarAmount { get; set; }
    }
}
