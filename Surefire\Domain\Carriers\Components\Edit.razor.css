﻿/* General styles for the carrier edit page container */
.carrier-edit-page h1 {
    font-family: "montserrat", sans-serif; /* Keep existing font if desired */
    font-weight: 200;
    font-size: 1.8em; /* Slightly smaller? */
    font-style: normal;
    color: #888; /* Lighter gray */
    padding: 0;
    margin: 0 0 20px 0; /* Bottom margin */
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/* Main flex container for the form columns */
.sfpage-carrier-edit {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
    gap: 20px; /* Consistent spacing between columns */
    max-width: 1200px; /* Adjust max-width as needed */
    margin: 0 auto; /* Center the form area if needed */
}

/* Style for each column */
.sf-col {
    flex: 1; /* Each column takes equal space */
    min-width: 280px; /* Minimum width before wrapping */
    display: flex;
    flex-direction: column;
    gap: 15px; /* Space between cards within a column */
}

/* Use FluentCard or similar container for grouping */
.fluent-card {
    /* Add default padding/margin to cards if needed via Fluent UI theming or here */
    padding: 15px; /* Example padding */
}

/* Spacing for form elements inside cards/columns */
.sf-col .mb-3 {
    margin-bottom: 1rem; /* Standard bootstrap spacing, adjust if needed */
}

/* Ensure Syncfusion textboxes take full width */
.sf-col .e-control-wrapper.e-input-group {
    width: 100%;
    /* margin-bottom: 0; */ /* Remove default margin if .mb-3 handles it */
}

.sf-col .e-control-wrapper.e-text-area {
    width: 100%;
}


/* Styling for validation messages */
.validation-message { /* Target the Blazor validation component */
    font-size: 0.8rem; /* Slightly smaller */
    color: #dc3545; /* Standard danger color */
    display: block; /* Ensure it takes its own line */
    margin-top: 4px;
}

/* Checkbox layout adjustments if needed */
.sf-col .d-flex.flex-column .fluent-checkbox {
    margin-bottom: 10px; /* Space between stacked checkboxes */
}

.sf-col .d-flex.flex-column .validation-message {
    margin-bottom: 10px; /* Add space below validation messages for checkboxes */
}

/* Logo preview */
.logo-preview img {
    border: 1px solid #ddd;
    border-radius: 4px;
    object-fit: contain; /* Ensure logo fits well */
}

/* Action buttons area */
.form-actions {
    display: flex;
    gap: 10px; /* Space between buttons */
    margin-top: 25px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

/* Alert message styling */
.alert {
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;
}

.alert-success {
    color: #0f5132;
    background-color: #d1e7dd;
    border-color: #badbcc;
}

.alert-danger {
    color: #842029;
    background-color: #f8d7da;
    border-color: #f5c2c7;
}

.alert-warning {
    color: #664d03;
    background-color: #fff3cd;
    border-color: #ffecb5;
}

.alert-info {
    color: #055160;
    background-color: #cff4fc;
    border-color: #b6effb;
}
