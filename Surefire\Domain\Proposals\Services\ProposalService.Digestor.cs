﻿using System;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Surefire.Domain.Proposals
{
    public partial class ProposalService
    {
        /// <summary>
        /// Calls OpenAI’s API with the raw JSON and the combined prompt (using digester_prompt.txt and digester_prompt.xml)
        /// to return cleaned JSON.
        /// </summary>
        public async Task<string> DigestDataAsync(string rawJson)
        {
            // Load your prompt files (update paths as needed)
            string promptText = File.ReadAllText("path/to/digester_prompt.txt");
            string promptXml = File.ReadAllText("path/to/digester_prompt.xml");
            string fullPrompt = $"{promptText}\n{promptXml}\nHere is the raw data:\n{rawJson}";

            var requestBody = new
            {
                model = "gpt-4-turbo",
                messages = new object[]
                {
                    new { role = "system", content = "You are an expert insurance analyst. Respond only with valid JSON." },
                    new { role = "user", content = fullPrompt }
                },
                temperature = 0.3,
                max_tokens = 4000
            };

            string requestJson = JsonConvert.SerializeObject(requestBody);
            var content = new StringContent(requestJson, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("https://api.openai.com/v1/chat/completions", content);
            string responseJson = await response.Content.ReadAsStringAsync();
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"OpenAI API call failed: {response.StatusCode}, {responseJson}");
            }
            JObject parsedResponse = JObject.Parse(responseJson);
            string cleanedData = parsedResponse["choices"]?[0]?["message"]?["content"]?.ToString();
            if (string.IsNullOrEmpty(cleanedData))
            {
                throw new Exception("No cleaned JSON data returned from OpenAI");
            }
            return cleanedData;
        }
    }
}
