﻿@namespace Surefire.Domain.Utilities
@using Surefire.Domain.Shared.Components
@using Surefire.Domain.Shared.Models
@using Surefire.Domain.Shared.Helpers
@using Surefire.Domain.Shared.Services
@using Icons = Microsoft.FluentUI.AspNetCore.Components.Icons
@using Surefire.Domain.Forms.Components
@using Surefire.Domain.Clients.Models
@using Surefire.Domain.Policies.Models
@using Surefire.Domain.Contacts.Models
@using Surefire.Domain.Renewals.Models
@using Surefire.Domain.Utilities
@using Surefire.Domain.Utilities.Components
@inject IJSRuntime JS
@inject NavigationManager Navigation

<div class="utilities-container">
    <div class="utilities-sidebar">
        <FluentSearch @bind-Value="@searchQuery" 
                     Placeholder="Search utilities..." 
                     @onclick="() => selectedTool = null" />
        <div class="utilities-list">
            @foreach (var tool in FilteredTools)
            {
                <div class="utility-item @(selectedTool?.Id == tool.Id ? "selected" : "")" 
                     @onclick="() => SelectTool(tool)">
                    <FluentIcon Value="@(tool.Icon())" />
                    <span>@tool.Name</span>
                </div>
            }
        </div>
    </div>
    
    <div class="utilities-main">
        @if (selectedTool == null)
        {
            <div class="utilities-home">
                <h2>Utility Tools</h2>
                <div class="recent-tools">
                    <h3>Recently Used</h3>
                    <div class="tool-grid">
                        @foreach (var tool in recentTools)
                        {
                            <div class="tool-card" @onclick="() => SelectTool(tool)">
                                <FluentIcon Value="@(tool.Icon())" />
                                <span>@tool.Name</span>
                            </div>
                        }
                    </div>
                </div>
                <div class="frequent-tools">
                    <h3>Frequently Used</h3>
                    <div class="tool-grid">
                        @foreach (var tool in frequentTools)
                        {
                            <div class="tool-card" @onclick="() => SelectTool(tool)">
                                <FluentIcon Value="@(tool.Icon())" />
                                <span>@tool.Name</span>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }
        else
        {
            @* <div class="tool-header">
                <h2>@selectedTool.Name</h2>
                <FluentButton Appearance="Appearance.Lightweight" OnClick="() => selectedTool = null">
                    <FluentIcon Value="@(new Icons.Filled.Size24.Dismiss())" />
                </FluentButton>
            </div> *@
          
            @switch (selectedTool.Id)
            {
                case "paylink":
                    <Paylink ClientId="@ClientId" PolicyId="@PolicyId" RenewalId="@RenewalId" />
                    break;
                case "email":
                    <EmailFunctionsCombined 
                        CurrentPolicies="@CurrentPolicies"
                        PastPolicies="@PastPolicies"
                        Contacts="@Contacts"
                        ClientName="@ClientName"
                        ClientId="@ClientId" />
                    break;
                case "client-debug":
                    <ClientDebugScreen 
                        ClientId="@ClientId"
                        PolicyId="@PolicyId"
                        ContactId="@ContactId" />
                    break;
                case "loss-runs":
                    <RequestLossRuns 
                        ClientId="@ClientId"
                        RenewalId="@RenewalId" />
                    break;
                case "data-import":
                    <DataImport 
                        ClientId="@ClientId"
                        PolicyId="@PolicyId"
                        RenewalId="@RenewalId" />
                    break;
                case "epic-sync":
                    <EpicClientPolicySync 
                        ClientId="@ClientId" />
                    break;
            }
         
        }
    </div>
</div>

@code {
    [Parameter]
    public int? ClientId { get; set; }
    
    [Parameter]
    public int? PolicyId { get; set; }
    
    [Parameter]
    public int? ContactId { get; set; }
    
    [Parameter]
    public int? RenewalId { get; set; }
    
    [Parameter]
    public int? CarrierId { get; set; }

    private string searchQuery = "";
    private UtilityTool? selectedTool;
    private List<UtilityTool> allTools = new()
    {
        new UtilityTool 
        { 
            Id = "paylink", 
            Name = "Quick PayLink Email", 
            Icon = () => new Icons.Filled.Size24.Payment()
        },
        new UtilityTool 
        { 
            Id = "email", 
            Name = "Send Smart Email", 
            Icon = () => new Icons.Filled.Size24.MailEdit()
        },
        new UtilityTool 
        { 
            Id = "loss-runs", 
            Name = "Loss Runs Requester", 
            Icon = () => new Icons.Filled.Size24.Document()
        },
        new UtilityTool 
        { 
            Id = "client-debug", 
            Name = "Client Debug Screen", 
            Icon = () => new Icons.Filled.Size24.Bug()
        },
        new UtilityTool 
        { 
            Id = "data-import", 
            Name = "Data Import", 
            Icon = () => new Icons.Filled.Size24.ArrowUpload()
        },
        new UtilityTool 
        { 
            Id = "epic-sync", 
            Name = "Epic Policy Sync", 
            Icon = () => new Icons.Filled.Size24.DocumentSync()
        }
    };

    private List<UtilityTool> recentTools = new();
    private List<UtilityTool> frequentTools = new();

    private IEnumerable<UtilityTool> FilteredTools => 
        string.IsNullOrWhiteSpace(searchQuery) 
            ? allTools 
            : allTools.Where(t => t.Name.Contains(searchQuery, StringComparison.OrdinalIgnoreCase));

    private void SelectTool(UtilityTool tool)
    {
        selectedTool = tool;
        UpdateRecentTools(tool);
    }

    private void UpdateRecentTools(UtilityTool tool)
    {
        recentTools.Remove(tool);
        recentTools.Insert(0, tool);
        if (recentTools.Count > 5)
        {
            recentTools.RemoveAt(recentTools.Count - 1);
        }
    }

    // These properties would be populated from your services
    private Client? CurrentClient { get; set; }
    private Policy? CurrentPolicy { get; set; }
    private Renewal? CurrentRenewal { get; set; }
    private List<Policy>? CurrentPolicies { get; set; }
    private List<Policy>? PastPolicies { get; set; }
    private List<Contact>? Contacts { get; set; }
    private string? ClientName { get; set; }

    protected override async Task OnInitializedAsync()
    {
        // Initialize with some default frequent tools
        frequentTools = allTools.Take(3).ToList();
    }
}