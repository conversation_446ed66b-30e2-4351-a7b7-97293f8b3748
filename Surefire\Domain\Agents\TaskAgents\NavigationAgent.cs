using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Surefire.Data;
using Surefire.Domain.Agents.Models;
using Surefire.Domain.Shared.Services;
using Surefire.Domain.Agents.Interfaces;

using System.Text.Json;
using System.Text.RegularExpressions;

namespace Surefire.Domain.Agents.Services
{
    /// <summary>
    /// Agent for handling navigation requests within the application
    /// </summary>
    public class NavigationAgent : INavigationAgent
    {
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly IChatCompletionService _chatService;
        private readonly StateService _stateService;
        private readonly ILogger<NavigationAgent> _logger;
        private readonly IEmbeddingService _embeddingService;
        private readonly IVectorStore _vectorStore;

        // Navigation keywords that indicate navigation intent
        private readonly string[] _navigationKeywords = {
            "open", "show", "view", "navigate", "go to", "display", "see",
            "client screen", "policy list", "policies", "renewal", "proposal",
            "carrier", "transcription", "transcriptions", "details"
        };

        public NavigationAgent(
            IDbContextFactory<ApplicationDbContext> dbContextFactory,
            IConfiguration configuration,
            StateService stateService,
            IEmbeddingService embeddingService,
            IVectorStore vectorStore,
            ILogger<NavigationAgent> logger)
        {
            _dbContextFactory = dbContextFactory;
            _stateService = stateService;
            _embeddingService = embeddingService;
            _vectorStore = vectorStore;
            _logger = logger;

            // Initialize Semantic Kernel with OpenAI (same as OpenAIAgent)
            var builder = Kernel.CreateBuilder();
            var openAiApiKey = configuration["OpenAi:ApiKey"] ?? Environment.GetEnvironmentVariable("OPENAI") ?? throw new InvalidOperationException("OpenAI API key not configured");
            builder.AddOpenAIChatCompletion(
                modelId: "gpt-4.1-mini",
                apiKey: openAiApiKey);

            var kernel = builder.Build();
            _chatService = kernel.GetRequiredService<IChatCompletionService>();
        }

        public bool IsNavigationRequest(string input)
        {
            var lowerInput = input.ToLower();
            return _navigationKeywords.Any(keyword => lowerInput.Contains(keyword));
        }

        public async Task<NavigationResponse> ProcessNavigationRequestAsync(NavigationRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("[Navigation] Processing navigation request: {Input}", request.Input);

                // Parse the navigation intent using AI
                var parsedIntent = await ParseNavigationIntentAsync(request.Input, cancellationToken);
                
                _logger.LogInformation("[Navigation] Parsed intent: Type={Type}, Confidence={Confidence}, Entities={Entities}", 
                    parsedIntent.Type, parsedIntent.Confidence, 
                    string.Join(", ", parsedIntent.Entities.Select(e => $"{e.Key}='{e.Value}'")));
                
                if (parsedIntent.Type == NavigationType.Unknown)
                {
                    return new NavigationResponse
                    {
                        Success = false,
                        ErrorMessage = "Unable to understand the navigation request",
                        Message = "I couldn't determine what you want to navigate to. Try being more specific."
                    };
                }

                // Look up entities in the database
                var entityLookups = await PerformEntityLookupsAsync(parsedIntent, cancellationToken);
                
                // Build the navigation response
                var response = await BuildNavigationResponseAsync(parsedIntent, entityLookups, cancellationToken);
                
                _logger.LogInformation("[Navigation] Navigation response: {Success}, URL: {Url}", response.Success, response.NavigationUrl);
                
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[Navigation] Error processing navigation request: {Input}", request.Input);
                return new NavigationResponse
                {
                    Success = false,
                    ErrorMessage = "An error occurred while processing the navigation request",
                    Message = "Sorry, I encountered an error while trying to navigate. Please try again."
                };
            }
        }

        private async Task<ParsedNavigationIntent> ParseNavigationIntentAsync(string input, CancellationToken cancellationToken)
        {
            try
            {
                var prompt = CreateNavigationParsingPrompt(input);
                var chatHistory = new ChatHistory();
                chatHistory.AddSystemMessage("You are an AI that parses navigation requests for an insurance application. Always respond with valid JSON.");
                chatHistory.AddUserMessage(prompt);

                var result = await _chatService.GetChatMessageContentAsync(chatHistory, cancellationToken: cancellationToken);
                var parsedResult = ParseNavigationIntentResult(result.Content ?? "");
                
                return parsedResult;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[Navigation] Failed to parse navigation intent, using fallback");
                return FallbackNavigationParsing(input);
            }
        }

        private string CreateNavigationParsingPrompt(string input)
        {
            return $@"Parse this navigation request and extract the intent and entities:

Input: ""{input}""

**CRITICAL PRIORITIZATION RULES:**
- **Client navigation is 5x more common than carrier navigation**
- **Always try to interpret names as CLIENT names first, then carriers**
- **Only classify as CarrierDetails if explicitly mentioned as a carrier or insurance company**
- **Business names like ""Cobian Electric"", ""ABC Corp"", ""Metro Services"" are almost always CLIENTS, not carriers**

**CARRIER vs CLIENT NAME PATTERNS:**
- **Carrier names typically contain**: insurance, surety, casualty, underwriters, specialty, indemnity, mutual
- **Client names are typically**: business names without insurance terms (e.g., ""Electric Company"", ""Construction"", ""Services"", ""Corp"", ""LLC"")
- **Examples of CARRIERS**: ""AmWINS Access Insurance Services"", ""Hartford Casualty"", ""Eastern Underwriters"", ""Liberty Mutual"", ""RT Specialty""
- **Examples of CLIENTS**: ""Cobian Electric"", ""Metro Construction"", ""ABC Services"", ""Pacific Security""

Navigation Types Available (in order of priority):

1. **ClientDetails**: Open client information page (MOST COMMON - 80% of requests)
   - Patterns: ""open [business name]"", ""show [company]"", ""view [client name]""
   - Entities: ClientName
   - Examples: ""Open Cobian Electric"", ""Show ABC Corp"", ""View Metro Services""

2. **ClientPolicies**: Show client's policy list
   - Patterns: ""show policies for"", ""list policies"", ""policies for [client]""
   - Entities: ClientName

3. **ClientTranscriptions**: View call transcriptions for client
   - Patterns: ""view transcriptions"", ""call transcriptions for""
   - Entities: ClientName

4. **RenewalDetails**: Open renewal details page
   - Patterns: ""open renewal"", ""show [product] renewal""
   - Entities: ClientName, ProductLineName

5. **RenewalProposal**: Open renewal proposal page
   - Patterns: ""proposal for"", ""show proposal""
   - Entities: ClientName, ProductLineName

6. **CarrierDetails**: Open carrier information page (RARE - only 5% of requests)
   - Patterns: ""view carrier [name]"", ""show carrier [name]"", ""open insurance company [name]""
   - Entities: CarrierName
   - **Only use if explicitly mentioned as a carrier/insurance company**
   - Examples: ""View carrier State Fund"", ""Show insurance company Travelers""

**Entity Extraction Guidelines:**
- Assume business names are CLIENT names unless explicitly stated otherwise
- Look for words like ""carrier"", ""insurance company"", ""insurer"" to identify carriers
- **Use name patterns to distinguish**: Names with ""insurance"", ""surety"", ""casualty"", ""underwriters"", ""indemnity"" are likely carriers
- **Business names without insurance terms** (like ""Electric"", ""Construction"", ""Services"") are almost always clients
- Most navigation requests are for clients, not carriers

Respond with ONLY raw JSON (no markdown, no code blocks):
{{
  ""type"": ""ClientDetails|ClientPolicies|ClientTranscriptions|CarrierDetails|RenewalDetails|RenewalProposal|Unknown"",
  ""entities"": {{
    ""clientName"": ""extracted client name or null"",
    ""carrierName"": ""extracted carrier name or null"",
    ""productLineName"": ""extracted product line name or null""
  }},
  ""confidence"": 0.0-1.0,
  ""intent"": ""brief description of what user wants""
}}";
        }

        private ParsedNavigationIntent ParseNavigationIntentResult(string result)
        {
            try
            {
                var jsonStart = result.IndexOf('{');
                var jsonEnd = result.LastIndexOf('}');
                
                string jsonContent = result;
                if (jsonStart >= 0 && jsonEnd > jsonStart)
                {
                    jsonContent = result.Substring(jsonStart, jsonEnd - jsonStart + 1);
                }

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                var jsonResult = JsonSerializer.Deserialize<JsonElement>(jsonContent, options);
                
                var intent = new ParsedNavigationIntent
                {
                    Type = ParseNavigationType(jsonResult.GetProperty("type").GetString() ?? "Unknown"),
                    Confidence = jsonResult.GetProperty("confidence").GetDouble(),
                    Intent = jsonResult.GetProperty("intent").GetString() ?? ""
                };

                if (jsonResult.TryGetProperty("entities", out var entitiesElement))
                {
                    foreach (var prop in entitiesElement.EnumerateObject())
                    {
                        var value = prop.Value.GetString();
                        if (!string.IsNullOrEmpty(value))
                        {
                            intent.Entities[prop.Name] = value;
                        }
                    }
                }

                return intent;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[Navigation] Failed to parse navigation intent result: {Result}", result);
                return new ParsedNavigationIntent { Type = NavigationType.Unknown, Confidence = 0 };
            }
        }

        private NavigationType ParseNavigationType(string typeString)
        {
            return typeString.ToLower() switch
            {
                "clientdetails" => NavigationType.ClientDetails,
                "clientpolicies" => NavigationType.ClientPolicies,
                "clienttranscriptions" => NavigationType.ClientTranscriptions,
                "carrierdetails" => NavigationType.CarrierDetails,
                "renewaldetails" => NavigationType.RenewalDetails,
                "renewalproposal" => NavigationType.RenewalProposal,
                _ => NavigationType.Unknown
            };
        }

        private ParsedNavigationIntent FallbackNavigationParsing(string input)
        {
            var lowerInput = input.ToLower();
            
            // Simple pattern matching fallback with client prioritization
            if (lowerInput.Contains("transcription"))
                return new ParsedNavigationIntent { Type = NavigationType.ClientTranscriptions, Confidence = 0.6 };
            if (lowerInput.Contains("policies") || lowerInput.Contains("policy list"))
                return new ParsedNavigationIntent { Type = NavigationType.ClientPolicies, Confidence = 0.6 };
            if (lowerInput.Contains("proposal"))
                return new ParsedNavigationIntent { Type = NavigationType.RenewalProposal, Confidence = 0.6 };
            if (lowerInput.Contains("renewal"))
                return new ParsedNavigationIntent { Type = NavigationType.RenewalDetails, Confidence = 0.6 };
            
            // Only classify as carrier if explicitly mentioned as carrier/insurance company OR contains carrier-specific terms
            if (lowerInput.Contains("carrier") || lowerInput.Contains("insurance company") || lowerInput.Contains("insurer") ||
                lowerInput.Contains("insurance") || lowerInput.Contains("surety") || lowerInput.Contains("casualty") || 
                lowerInput.Contains("underwriters") || lowerInput.Contains("indemnity") || lowerInput.Contains("mutual"))
                return new ParsedNavigationIntent { Type = NavigationType.CarrierDetails, Confidence = 0.6 };
            
            // Default to client details for any "open" or "show" commands
            if (lowerInput.Contains("client") || lowerInput.Contains("open") || lowerInput.Contains("show") || lowerInput.Contains("view"))
                return new ParsedNavigationIntent { Type = NavigationType.ClientDetails, Confidence = 0.7 };
                
            return new ParsedNavigationIntent { Type = NavigationType.Unknown, Confidence = 0 };
        }

        private async Task<Dictionary<string, NavigationEntityLookup>> PerformEntityLookupsAsync(ParsedNavigationIntent intent, CancellationToken cancellationToken)
        {
            var lookups = new Dictionary<string, NavigationEntityLookup>();

            using var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken);

            // Look up client if specified using vector search
            if (intent.Entities.TryGetValue("clientName", out var clientName))
            {
                _logger.LogInformation("[Navigation] Looking up client with name: '{ClientName}' using vector search", clientName);
                
                var client = await FindClientByVectorSearchAsync(clientName, cancellationToken);

                _logger.LogInformation("[Navigation] Client lookup result: Found={Found}, Name='{Name}'", client != null, client?.Name ?? "None");

                lookups["client"] = new NavigationEntityLookup
                {
                    Found = client != null,
                    Id = client?.ClientId ?? 0,
                    Name = client?.Name ?? clientName,
                    Type = "Client"
                };
            }

            // Look up carrier if specified
            if (intent.Entities.TryGetValue("carrierName", out var carrierName))
            {
                // Extract first word for more flexible matching
                var firstWord = carrierName.Split(' ', StringSplitOptions.RemoveEmptyEntries).FirstOrDefault() ?? carrierName;
                
                _logger.LogInformation("[Navigation] Looking up carrier with name: '{CarrierName}', first word: '{FirstWord}'", carrierName, firstWord);
                
                // Try first word match first
                var carrier = await context.Carriers
                    .Where(c => c.CarrierName.Contains(firstWord))
                    .Select(c => new { c.CarrierId, c.CarrierName })
                    .FirstOrDefaultAsync(cancellationToken);

                // If no match with first word, try the full extracted name
                if (carrier == null && firstWord != carrierName)
                {
                    carrier = await context.Carriers
                        .Where(c => c.CarrierName.Contains(carrierName))
                        .Select(c => new { c.CarrierId, c.CarrierName })
                        .FirstOrDefaultAsync(cancellationToken);
                }

                _logger.LogInformation("[Navigation] Carrier lookup result: Found={Found}, Name='{Name}'", carrier != null, carrier?.CarrierName ?? "None");

                lookups["carrier"] = new NavigationEntityLookup
                {
                    Found = carrier != null,
                    Id = carrier?.CarrierId ?? 0,
                    Name = carrier?.CarrierName ?? carrierName,
                    Type = "Carrier"
                };
            }

            // Look up renewal if we have both client and product line
            if (intent.Entities.TryGetValue("clientName", out var renewalClientName) && 
                intent.Entities.TryGetValue("productLineName", out var productLine) &&
                lookups.TryGetValue("client", out var clientLookup) && clientLookup.Found)
            {
                // Extract first word for more flexible product line matching
                var productFirstWord = productLine.Split(' ', StringSplitOptions.RemoveEmptyEntries).FirstOrDefault() ?? productLine;
                
                _logger.LogInformation("[Navigation] Looking up renewal for client {ClientId} with product line: '{ProductLine}', first word: '{FirstWord}'", 
                    clientLookup.Id, productLine, productFirstWord);
                
                // Try first word match for product line
                var renewal = await context.Renewals
                    .Where(r => r.ClientId == clientLookup.Id && 
                               r.RenewalDate > DateTime.Now &&
                               context.Products.Any(p => p.ProductId == r.ProductId && 
                                                        p.LineName.Contains(productFirstWord)))
                    .Select(r => new { r.RenewalId, r.RenewalDate, ProductLineName = r.Product.LineName })
                    .FirstOrDefaultAsync(cancellationToken);

                // If no match with first word, try the full product line name
                if (renewal == null && productFirstWord != productLine)
                {
                    renewal = await context.Renewals
                        .Where(r => r.ClientId == clientLookup.Id && 
                                   r.RenewalDate > DateTime.Now &&
                                   context.Products.Any(p => p.ProductId == r.ProductId && 
                                                            p.LineName.Contains(productLine)))
                        .Select(r => new { r.RenewalId, r.RenewalDate, ProductLineName = r.Product.LineName })
                        .FirstOrDefaultAsync(cancellationToken);
                }

                _logger.LogInformation("[Navigation] Renewal lookup result: Found={Found}, ProductLine='{ProductLine}'", 
                    renewal != null, renewal?.ProductLineName ?? "None");

                lookups["renewal"] = new NavigationEntityLookup
                {
                    Found = renewal != null,
                    Id = renewal?.RenewalId ?? 0,
                    Name = renewal?.ProductLineName ?? productLine,
                    Type = "Renewal",
                    AdditionalData = renewal != null ? new Dictionary<string, object> { ["RenewalDate"] = renewal.RenewalDate } : new()
                };
            }

            return lookups;
        }

        private async Task<NavigationResponse> BuildNavigationResponseAsync(
            ParsedNavigationIntent intent, 
            Dictionary<string, NavigationEntityLookup> lookups, 
            CancellationToken cancellationToken)
        {
            var response = new NavigationResponse
            {
                Type = intent.Type,
                ExtractedEntities = intent.Entities.Values.ToList()
            };

            switch (intent.Type)
            {
                case NavigationType.ClientDetails:
                    return BuildClientDetailsResponse(lookups, response);
                
                case NavigationType.ClientPolicies:
                    return BuildClientPoliciesResponse(lookups, response);
                
                case NavigationType.ClientTranscriptions:
                    return BuildClientTranscriptionsResponse(lookups, response);
                
                case NavigationType.CarrierDetails:
                    return BuildCarrierDetailsResponse(lookups, response);
                
                case NavigationType.RenewalDetails:
                    return BuildRenewalDetailsResponse(lookups, response);
                
                case NavigationType.RenewalProposal:
                    return BuildRenewalProposalResponse(lookups, response);
                
                default:
                    response.Success = false;
                    response.ErrorMessage = "Navigation type not supported";
                    response.Message = "I don't know how to navigate to that location yet.";
                    return response;
            }
        }

        private NavigationResponse BuildClientDetailsResponse(Dictionary<string, NavigationEntityLookup> lookups, NavigationResponse response)
        {
            if (!lookups.TryGetValue("client", out var clientLookup) || !clientLookup.Found)
            {
                response.Success = false;
                response.ErrorMessage = "Client not found";
                response.Message = "I couldn't find that client in the system.";
                return response;
            }

            response.Success = true;
            response.NavigationUrl = $"/Clients/{clientLookup.Id}";
            response.Message = $"Opening client details for {clientLookup.Name}";
            return response;
        }

        private NavigationResponse BuildClientPoliciesResponse(Dictionary<string, NavigationEntityLookup> lookups, NavigationResponse response)
        {
            if (!lookups.TryGetValue("client", out var clientLookup) || !clientLookup.Found)
            {
                response.Success = false;
                response.ErrorMessage = "Client not found";
                response.Message = "I couldn't find that client in the system.";
                return response;
            }

            response.Success = true;
            response.NavigationUrl = $"/Clients/{clientLookup.Id}";
            response.StateChanges["ClientTab"] = "tab-2";
            response.Message = $"Opening policy list for {clientLookup.Name}";
            return response;
        }

        private NavigationResponse BuildClientTranscriptionsResponse(Dictionary<string, NavigationEntityLookup> lookups, NavigationResponse response)
        {
            if (!lookups.TryGetValue("client", out var clientLookup) || !clientLookup.Found)
            {
                response.Success = false;
                response.ErrorMessage = "Client not found";
                response.Message = "I couldn't find that client in the system.";
                return response;
            }

            response.Success = true;
            response.NavigationUrl = $"/Clients/{clientLookup.Id}/transcriptions";
            response.Message = $"Opening call transcriptions for {clientLookup.Name}";
            return response;
        }

        private NavigationResponse BuildCarrierDetailsResponse(Dictionary<string, NavigationEntityLookup> lookups, NavigationResponse response)
        {
            if (!lookups.TryGetValue("carrier", out var carrierLookup) || !carrierLookup.Found)
            {
                response.Success = false;
                response.ErrorMessage = "Carrier not found";
                response.Message = "I couldn't find that carrier in the system.";
                return response;
            }

            response.Success = true;
            response.NavigationUrl = $"/Carriers/{carrierLookup.Id}";
            response.Message = $"Opening carrier details for {carrierLookup.Name}";
            return response;
        }

        private NavigationResponse BuildRenewalDetailsResponse(Dictionary<string, NavigationEntityLookup> lookups, NavigationResponse response)
        {
            if (!lookups.TryGetValue("renewal", out var renewalLookup) || !renewalLookup.Found)
            {
                response.Success = false;
                response.ErrorMessage = "Renewal not found";
                response.Message = "I couldn't find that renewal in the system.";
                return response;
            }

            response.Success = true;
            response.NavigationUrl = $"/Renewals/Details/{renewalLookup.Id}";
            response.Message = $"Opening renewal details for {renewalLookup.Name}";
            return response;
        }

        private NavigationResponse BuildRenewalProposalResponse(Dictionary<string, NavigationEntityLookup> lookups, NavigationResponse response)
        {
            if (!lookups.TryGetValue("renewal", out var renewalLookup) || !renewalLookup.Found)
            {
                response.Success = false;
                response.ErrorMessage = "Renewal not found";
                response.Message = "I couldn't find that renewal in the system.";
                return response;
            }

            response.Success = true;
            response.NavigationUrl = $"/Renewals/Details/{renewalLookup.Id}";
            response.StateChanges["HtmlTab"] = "tab-5";
            response.Message = $"Opening proposal screen for {renewalLookup.Name} renewal";
            return response;
        }

        /// <summary>
        /// Find client using vector search for better fuzzy matching
        /// </summary>
        private async Task<Domain.Clients.Models.Client?> FindClientByVectorSearchAsync(string clientName, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(clientName)) return null;

            try
            {
                _logger.LogInformation("🔍 [Navigation] Performing vector search for client: '{ClientName}'", clientName);
                
                // Generate embedding for the client name
                var queryVector = await _embeddingService.GenerateEmbeddingAsync(clientName, cancellationToken);
                
                // Search for similar clients in the vector store
                var searchResults = await _vectorStore.SearchAsync(queryVector, 5, cancellationToken);
                
                _logger.LogInformation("[Navigation] Vector search found {ResultCount} potential client matches", searchResults.Count);
                
                // Filter for Client entities and find the best match
                var clientMatches = searchResults
                    .Where(r => GetMetadataString(r.Metadata, "Entity") == "Client")
                    .Where(r => r.Score > 0.4f) // Threshold for good matches
                    .ToList();

                if (!clientMatches.Any())
                {
                    _logger.LogWarning("[Navigation] No client matches found above threshold (0.4) for '{ClientName}'", clientName);
                    return null;
                }

                // Log all matches for debugging
                foreach (var match in clientMatches)
                {
                    var entityId = GetMetadataInt(match.Metadata, "EntityId");
                    var entityName = GetMetadataString(match.Metadata, "Name");
                    _logger.LogInformation("[Navigation] Client match: ID={EntityId}, Name='{EntityName}', Score={Score:F3}", 
                        entityId, entityName, match.Score);
                }

                // Get the best match
                var bestMatch = clientMatches.First();
                var clientId = GetMetadataInt(bestMatch.Metadata, "EntityId");
                
                if (clientId == 0)
                {
                    _logger.LogError("[Navigation] Best client match has invalid EntityId");
                    return null;
                }

                _logger.LogInformation("✅ [Navigation] Selected best client match: ID={ClientId}, Score={Score:F3}", 
                    clientId, bestMatch.Score);

                // Fetch the actual client from database
                using var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
                var client = await context.Clients.FirstOrDefaultAsync(c => c.ClientId == clientId, cancellationToken);
                
                if (client != null)
                {
                    _logger.LogInformation("✅ [Navigation] Client retrieved from database: {ClientName}", client.Name);
                }
                else
                {
                    _logger.LogError("❌ [Navigation] Client with ID {ClientId} not found in database", clientId);
                }

                return client;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[Navigation] Error in vector search for client '{ClientName}'", clientName);
                return null;
            }
        }

        /// <summary>
        /// Helper method to safely get string values from metadata
        /// </summary>
        private string? GetMetadataString(IDictionary<string, object> metadata, string key)
        {
            if (!metadata.TryGetValue(key, out var value)) return null;
            
            return value switch
            {
                string s => s,
                System.Text.Json.JsonElement je when je.ValueKind == System.Text.Json.JsonValueKind.String => je.GetString(),
                System.Text.Json.JsonElement je when je.ValueKind == System.Text.Json.JsonValueKind.Null => null,
                null => null,
                _ => value.ToString()
            };
        }

        /// <summary>
        /// Helper method to safely get integer values from metadata
        /// </summary>
        private int GetMetadataInt(IDictionary<string, object> metadata, string key)
        {
            if (!metadata.TryGetValue(key, out var value)) return 0;
            
            return value switch
            {
                int i => i,
                long l => (int)l,
                string s when int.TryParse(s, out var parsed) => parsed,
                System.Text.Json.JsonElement je when je.ValueKind == System.Text.Json.JsonValueKind.Number => je.GetInt32(),
                _ => 0
            };
        }
    }
} 