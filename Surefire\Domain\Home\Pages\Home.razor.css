﻿.page-content {
    background: none;
    position: relative;
    padding-top: 105px;
    z-index: 100;
    height: calc(100vh - 179px);
    padding-left: 20px;
}

.home-content {
    padding-left: 15px;
    padding-right: 15px;
    position: relative;
    z-index: 100;
}
:root h1 {
    font-family: "montserrat", sans-serif;
    font-weight: 100;
    font-size:3em;
    color: #8a8a8a;
    padding-top:0px;
    padding-bottom:0px;
    margin-top:0px;
    margin-bottom:0px;
}
i {
    font-family: "montserrat-italic";
    font-weight:100;
    font-size: 2em;
    margin-top: 0px;
    padding-top: 0px;
    color: #b0b0b0;
}
.img-fluid {
    width:100%;
}
.bottom-right {
    position: sticky;
    bottom: 100px;
    right: 0;
    z-index: 1;
    opacity: .25;
    background: none;
    pointer-events: none;
}
.pasdu {
    color: #ff0000 !important;
}
.ttnote {
}
.hexp {
    background-color: #ffffffa7;
    color: #424242;
    padding: 2px 5px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    font-size: .9em;
    font-family: "mono45-headline", monospace;
    font-weight: 500;
    font-style: normal;
}
.hnum {
    background-color: #ffffffa7;
    color: #000000;
    padding: 2px 5px;
    border:1px solid #ccc;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    font-size: .9em;
    font-family: "mono45-headline", monospace;
    font-weight: 500;
    font-style: normal;
}
.sectiontitle {
    font-family: "montserrat", sans-serif;
    font-size:2em;
    padding-left:7px;
    border-bottom:1px solid #ccc;
}
.sectiontitle2 {
    font-family: "montserrat", sans-serif;
    font-size: 1.5em;
    padding-left: 7px;
    border-bottom: 1px solid #ccc;
}

.home-content {
    display: flex;
    flex-wrap: wrap; /* Allow wrapping when necessary */
    gap: 20px; /* Optional spacing between columns */
}

    /* First column: Flexible width with max-width of 600px and min-width of 300px */
    .home-content > div:nth-child(1) {
        flex: 1 1 40%; /* Takes 40% of the available space */
        max-width: 650px;
        min-width: 300px;
        box-sizing: border-box;
    }

    /* Second column: Flexible width with max-width of 600px and min-width of 300px */
    .home-content > div:nth-child(2) {
        flex: 1 1 40%; /* Takes 40% of the available space */
        max-width: 650px;
        min-width: 300px;
        box-sizing: border-box;
    }

    /* Third column: Flexible width with max-width of 600px and min-width of 300px */
    .home-content > div:nth-child(3) {
        flex: 1 1 20%; /* Takes 20% of the available space */
        max-width: 650px;
        min-width: 300px;
        box-sizing: border-box;
    }

@media(max-width:1400px) {
    
}

/* Main flex container */
.flex-container {
    display: flex;
    box-sizing: border-box;
    flex-wrap: nowrap;
    z-index:200;
}

/* Left column (40%) */
.left-column {
    flex: 1 1 40%;
    box-sizing: border-box;
    min-width:650px;
}
.left-column__pad {
    padding-right: 10px
}
/* Right column (60%) */
.right-column {
    flex: 1 1 60%;
    box-sizing: border-box;
}
.right-column__pad {
    padding-left: 14px
}
/* Nested flex container inside the right column */
.nested-flex-container {
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
}

/* Nested left column (60%) */
.nested-left-column {
    flex: 0 0 60%;
    min-width: 200px;
    max-width: 800px;
    box-sizing: border-box;
}
    .nested-left-column__pad {
        padding-right:10px;
    }
.middle-container {
    box-shadow: 3px 0px 8px #ffffffd7;
    overflow: hidden;
    border-radius: 15px;
    border: 1px solid #0000004c;
}
    /* Nested right column (40%) */
.nested-right-column {
    flex: 0 0 40%;
    min-width: 200px;
    max-width: 800px;
    box-sizing: border-box;
}
.nested-right-column__pad {
    padding-left: 15px
}
/* Media query for responsive behavior */
@media(max-width:1500px) {
    .nested-left-column {
        flex: 0 0 100%;
        min-width: 400px;
        max-width: 800px;
        box-sizing: border-box;
    }
    .nested-right-column__pad {
        padding-left: 0px;
        padding-right: 10px;
        padding-top:20px;
    }
    /* Nested right column (40%) */
    .nested-right-column {
        flex: 0 0 100%;
        min-width: 400px;
        max-width: 800px;
        box-sizing: border-box;
    }
}