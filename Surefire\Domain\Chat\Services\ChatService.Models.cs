﻿using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using System.Linq;
using System.IO;

namespace Surefire.Domain.Chat
{

    // Helper classes for deserializing API responses
    internal class TokenResponse
    {
        public string access_token { get; set; }
        public string token_type { get; set; }
        public int expires_in { get; set; }
        public string refresh_token { get; set; }
        public int refresh_token_expires_in { get; set; }
        public string scope { get; set; }
        public string owner_id { get; set; }
    }

    internal class MessageStoreResponse
    {
        public MessageRecord[] records { get; set; }
        public Paging paging { get; set; }
        public Navigation navigation { get; set; }
    }

    internal class MessageRecord
    {
        public long id { get; set; }
        public string uri { get; set; }
        public MessageType type { get; set; }
        public string subject { get; set; }
        public string text { get; set; }
        public string direction { get; set; }
        public DateTime creation_time { get; set; }
        public DateTime last_modified_time { get; set; }
        public MessageStatus status { get; set; }
        public ReadStatus read_status { get; set; }
        public int priority { get; set; }
        public CallerInfo from { get; set; }
        public CallerInfo[] to { get; set; }
    }

    internal class Paging
    {
        public int page { get; set; }
        public int per_page { get; set; }
        public int page_start { get; set; }
        public int page_end { get; set; }
        public int total_pages { get; set; }
        public int total_elements { get; set; }
    }

    internal class Navigation
    {
        public string first_page { get; set; }
        public string next_page { get; set; }
        public string previous_page { get; set; }
        public string last_page { get; set; }
    }

    internal class CallerInfo
    {
        public string phone_number { get; set; }
        public string name { get; set; }
        public Location location { get; set; }
    }

    internal class Location
    {
        public string City { get; set; }
        public string State { get; set; }
        public string Country { get; set; }
        public string ZipCode { get; set; }
    }

    internal enum MessageType
    {
        Fax,
        SMS,
        VoiceMail,
        Pager,
        Text
    }

    internal enum MessageStatus
    {
        Received,
        Queued,
        Sent,
        Delivered,
        DeliveryFailed,
        SendingFailed,
        Sending
    }

    internal enum ReadStatus
    {
        Read,
        Unread
    }

    internal class RecordingMetadata
    {
        public string id { get; set; }
        public string contentUri { get; set; }
        public string contentType { get; set; }
        public long duration { get; set; }
    }

    // --- Call Log DTOs ---
    internal class CallLogResponse
    {
        public CallLogRecordFire[] records { get; set; }
        public Paging paging { get; set; } // Re-use existing Paging DTO
        public Navigation navigation { get; set; } // Re-use existing Navigation DTO
    }

    // Represents a single call log record from RingCentral API
    // Made public to be accessible by the Razor component
    public class CallLogRecordFire
    {
        public string id { get; set; }
        public string uri { get; set; }
        public string sessionId { get; set; }
        public DateTime startTime { get; set; }
        public int? duration { get; set; } // Nullable int for duration
        public string type { get; set; } // Voice, Fax
        public string direction { get; set; } // Inbound, Outbound
        public string action { get; set; } // e.g., "Phone Call", "FindMe"
        public string result { get; set; } // e.g., "Accepted", "Missed", "Voicemail"
        public CallPartyInfo from { get; set; }
        public CallPartyInfo to { get; set; }
        public CallRecordingInfo recording { get; set; }
        public string reason { get; set; }
        public string reasonDescription { get; set; }
        public MessageInfo message { get; set; } // For Fax/SMS related calls
        public BillingInfo billing { get; set; }
        public string transport { get; set; } // PSTN, VoIP
        public string legType { get; set; } // e.g. Accept, Transfer, ParkRetrieval, etc.
        public string delegationType { get; set; } // Added based on original Razor component
        public ExtensionInfo extension { get; set; } // Added based on original Razor component

        // Properties that might not directly map but were in the original grid
        // public string internalType { get; set; } // Need to verify API source
        // public string partyId { get; set; } // Need to verify API source
    }

    public class CallPartyInfo
    {
        public string phoneNumber { get; set; }
        public string name { get; set; }
        public string deviceId { get; set; }
        public string extensionId { get; set; }
        public string location { get; set; }
        public string dialerPhoneNumber { get; set; } // Added based on original Razor component
        public string dialedPhoneNumber { get; set; } // Added based on original Razor component
    }

    public class CallRecordingInfo
    {
        public string id { get; set; }
        public string uri { get; set; }
        public string type { get; set; } // Automatic, OnDemand
        public string contentUri { get; set; }
        public long? duration { get; set; }
    }

    public class MessageInfo
    {
        public string id { get; set; }
        public string type { get; set; } // Fax, SMS etc.
        public string uri { get; set; }
    }

    public class BillingInfo
    {
        public decimal? costIncluded { get; set; }
        public decimal? costPurchased { get; set; }
    }

    public class ExtensionInfo
    {
        public long? id { get; set; }
        public string uri { get; set; }
    }

    // Message class for chat components
    public class MessageItem
    {
        public string Content { get; set; } = "";
        public DateTime Timestamp { get; set; }
        public bool IsFromCurrentUser { get; set; }
        public string? UserId { get; set; }
        public string? UserName { get; set; }
        public string? UserFullName { get; set; }
        public string? UserPictureUrl { get; set; }
    }
    
    // ConversationInfo class to represent a conversation in the list
    public class ConversationInfo
    {
        public string PhoneNumber { get; set; }
        public DateTime LastMessageTime { get; set; }
        public string LastMessageText { get; set; }
        public int UnreadCount { get; set; }
        
        // Contact information from phone lookup
        public string? ContactName { get; set; }
        public string? ContactTitle { get; set; }
        public int? ContactId { get; set; }
        public string? ClientName { get; set; }
        public int? ClientId { get; set; }
        public string? CarrierName { get; set; }
        public int? CarrierId { get; set; }
        public bool IsContactFound { get; set; }
        
        public string DisplayName
        {
            get
            {
                if (!string.IsNullOrEmpty(ContactName))
                {
                    var name = ContactName;
                    if (!string.IsNullOrEmpty(ClientName))
                    {
                        name += $" ({ClientName})";
                    }
                    else if (!string.IsNullOrEmpty(CarrierName))
                    {
                        name += $" ({CarrierName})";
                    }
                    return name;
                }
                return PhoneNumber;
            }
        }
    }
}
