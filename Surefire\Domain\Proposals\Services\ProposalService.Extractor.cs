﻿using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Azure;
using Azure.AI.FormRecognizer.DocumentAnalysis;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Surefire.Domain.Proposals
{
    public partial class ProposalService
    {
        // Extractor service property
        public ExtractorService ExtractorService { get; private set; }

        // Constructor to initialize the ExtractorService
        private void InitializeExtractorService()
        {
            ExtractorService = new ExtractorService(_formRecognizerEndpoint, _formRecognizerKey, _log);
        }

        /// <summary>
        /// Updates a proposal with extracted data and attachments
        /// </summary>
        public async Task<bool> UpdateProposalWithExtractedDataAsync(int proposalId, int pdfAttachmentId, int jsonAttachmentId, string jsonData)
        {
            await _log.LogAsync(LogLevel.Information, $"Updating proposal {proposalId} with extracted data", "ProposalService");
            
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                
                var proposal = await context.Proposals
                    .FirstOrDefaultAsync(p => p.ProposalId == proposalId);
                
                if (proposal == null)
                {
                    throw new Exception($"Proposal with ID {proposalId} not found");
                }
                
                // Update the proposal with the attachment IDs and JSON data
                proposal.AttachmentId = pdfAttachmentId;
                proposal.RawJsonAttachmentId = jsonAttachmentId;
                proposal.RawJson = jsonData;
                proposal.DateModified = DateTime.UtcNow;
                
                // Get the current user ID for the modified by field
                var userId = _userManager.GetUserId(_httpContextAccessor.HttpContext.User);
                if (!string.IsNullOrEmpty(userId))
                {
                    proposal.ModifiedById = userId;
                }
                
                // Save the changes to the database
                await context.SaveChangesAsync();
                
                await _log.LogAsync(LogLevel.Information, $"Successfully updated proposal {proposalId} with extracted data", "ProposalService");
                
                return true;
            }
            catch (Exception ex)
            {
                await _log.LogAsync(LogLevel.Error, $"Error updating proposal {proposalId} with extracted data: {ex.Message}", "ProposalService");
                throw;
            }
        }

        /// <summary>
        /// Calls Azure Form Recognizer with the provided PDF bytes and processes the result.
        /// Returns the processed data as a formatted JSON string.
        /// </summary>
        public async Task<string> ExtractDataAsync(byte[] pdfBytes)
        {
            var client = new DocumentAnalysisClient(new Uri(_formRecognizerEndpoint), new AzureKeyCredential(_formRecognizerKey));
            using var stream = new MemoryStream(pdfBytes);
            // Pass WaitUntil.Completed, then the model id and document stream.
            var operation = await client.AnalyzeDocumentAsync(WaitUntil.Completed, "prebuilt-document", stream);
            var result = await operation.WaitForCompletionAsync();
            string jsonResult = JsonConvert.SerializeObject(result, Formatting.Indented);
            return jsonResult;
        }

        /// <summary>
        /// Cleans up extracted string values by removing extra whitespace and common prefixes.
        /// </summary>
        private string CleanValue(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return value;
            // Remove extra whitespace
            value = string.Join(" ", value.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries));
            // Remove common prefixes
            string[] prefixes = { "Phone:", "Email:", "Website:", "Address:", "Tel:", "Fax:" };
            foreach (var prefix in prefixes)
            {
                if (value.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
                {
                    value = value.Substring(prefix.Length).Trim();
                }
            }
            return value;
        }

        /// <summary>
        /// Processes the AnalyzeResult from Form Recognizer and returns a JObject with:
        /// - "text_content": all lines of text from the pages.
        /// - "key_value_pairs": a dictionary of cleaned key-value pairs.
        /// - "tables": an array of tables (each table as a 2D array of strings).
        /// </summary>
        private JObject ProcessExtractedData(AnalyzeResult result)
        {
            var extractedData = new JObject();
            var keyValuePairs = new JObject();
            var textContent = new JArray();
            var tablesArray = new JArray();

            // Process key-value pairs
            if (result.KeyValuePairs != null)
            {
                foreach (var kv in result.KeyValuePairs)
                {
                    if (kv.Key != null && kv.Value != null)
                    {
                        string key = CleanValue(kv.Key.Content);
                        string value = CleanValue(kv.Value.Content);
                        keyValuePairs[key] = value;
                    }
                }
            }

            // Process text content from pages
            if (result.Pages != null)
            {
                foreach (var page in result.Pages)
                {
                    if (page.Lines != null)
                    {
                        foreach (var line in page.Lines)
                        {
                            string content = line.Content?.Trim();
                            if (!string.IsNullOrEmpty(content))
                            {
                                textContent.Add(content);
                                // Look for simple key-value patterns in the line
                                if (content.Contains(":") &&
                                    !content.StartsWith("http:", StringComparison.OrdinalIgnoreCase) &&
                                    !content.StartsWith("https:", StringComparison.OrdinalIgnoreCase))
                                {
                                    var parts = content.Split(new[] { ':' }, 2);
                                    if (parts.Length == 2)
                                    {
                                        string k = CleanValue(parts[0]);
                                        string v = CleanValue(parts[1]);
                                        if (!string.IsNullOrEmpty(k) && !string.IsNullOrEmpty(v))
                                        {
                                            keyValuePairs[k] = v;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Process tables
            if (result.Tables != null)
            {
                foreach (var table in result.Tables)
                {
                    // Determine dimensions (assuming cell.RowIndex and cell.ColumnIndex are zero-indexed)
                    int rows = table.Cells.Max(c => c.RowIndex) + 1;
                    int cols = table.Cells.Max(c => c.ColumnIndex) + 1;
                    var tableData = new JArray();

                    // Initialize table rows with empty cells
                    for (int i = 0; i < rows; i++)
                    {
                        var rowArray = new JArray(Enumerable.Repeat("", cols).ToArray());
                        tableData.Add(rowArray);
                    }

                    // Fill the cells with cleaned content
                    foreach (var cell in table.Cells)
                    {
                        int rowIndex = cell.RowIndex;
                        int colIndex = cell.ColumnIndex;
                        var rowArray = (JArray)tableData[rowIndex];
                        rowArray[colIndex] = CleanValue(cell.Content);
                    }
                    tablesArray.Add(tableData);
                }
            }

            extractedData["text_content"] = textContent;
            extractedData["key_value_pairs"] = keyValuePairs;
            extractedData["tables"] = tablesArray;

            return extractedData;
        }
    }
}
