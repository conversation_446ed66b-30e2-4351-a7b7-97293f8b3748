﻿using Surefire.Data;
using Surefire.Domain.Forms.Models;
using Microsoft.EntityFrameworkCore;
using Syncfusion.Pdf.Parsing;
using Surefire.Domain.Shared.Services;
using Surefire.Domain.Shared.Helpers;
using Surefire.Domain.Attachments.Models;
using Surefire.Domain.Proposals;
using Surefire.Domain.Shared.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using System.Linq;
using System.IO;

namespace Surefire.Domain.Forms.Services
{
    public partial class FormService
    {
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly StateService _stateService;
        private readonly ExternalPortalService _externalPortalService;
        private readonly ILogger<FormService> _logger;

        public FormService(IDbContextFactory<ApplicationDbContext> dbContextFactory, StateService stateService, ExternalPortalService externalPortalService, ILogger<FormService> logger)
        {
            _dbContextFactory = dbContextFactory;
            _stateService = stateService;
            _externalPortalService = externalPortalService;
            _logger = logger;
        }

        // [GET] ----------------------------------------------------------------------//
        public async Task<List<FormPdf>> GetAllFormPdfs()
        {
            using var context = _dbContextFactory.CreateDbContext();
            var formpdflist = await context.FormPdf.ToListAsync();
            return formpdflist;
        }
        public async Task<Certificate> GetCertificateByIdAsync(int certid)
        {
            using var context = _dbContextFactory.CreateDbContext();
            var certificate = await context.Certificates.FirstOrDefaultAsync(p => p.CertificateId == certid);

            return certificate;
        }
        public async Task<FormDoc> GetFormDocByIdAsync(int formDocId)
        {
            using var context = _dbContextFactory.CreateDbContext();
            var formDoc = await context.FormDocs
                .Include(fd => fd.Client)      // Include the Client entity
                .Include(fd => fd.Lead)      // Include the Client entity
                .Include(fd => fd.CreatedBy)   // Include the CreatedBy entity
                .Include(fd => fd.ModifiedBy)  // Include the ModifiedBy entity
                .Include(fd => fd.FormPdf)     // Include the FormPdf entity
                .Include(fd => fd.Submission)  // Include Submission
                .Include(fd => fd.Policy)      // Include Policy
                .Include(fd => fd.Renewal)     // Include Renewal
                .FirstOrDefaultAsync(p => p.FormDocId == formDocId);

            return formDoc;
        }

        // New method to get form docs by entity type and ID
        public async Task<List<FormDoc>> GetFormDocsByEntityAsync(string entityType, int entityId, int page = 1, int pageSize = 10, string sortField = "DateModified", bool sortAscending = false)
        {
            using var context = _dbContextFactory.CreateDbContext();
            IQueryable<FormDoc> query = context.FormDocs
                .Include(fd => fd.FormPdf)
                .Include(fd => fd.CreatedBy)
                .Include(fd => fd.ModifiedBy);

            // Filter by entity type and ID
            switch (entityType.ToLower())
            {
                case "client":
                    query = query.Where(fd => fd.ClientId == entityId);
                    break;
                case "lead":
                    query = query.Where(fd => fd.LeadId == entityId);
                    break;
                case "submission":
                    query = query.Where(fd => fd.SubmissionId == entityId);
                    break;
                case "policy":
                    query = query.Where(fd => fd.PolicyId == entityId);
                    break;
                case "renewal":
                    query = query.Where(fd => fd.RenewalId == entityId);
                    break;
                default:
                    throw new ArgumentException("Invalid entity type", nameof(entityType));
            }

            // Apply sorting
            query = ApplySorting(query, sortField, sortAscending);

            // Apply paging
            var skip = (page - 1) * pageSize;
            return await query.Skip(skip).Take(pageSize).ToListAsync();
        }

        // Helper method to apply sorting
        private IQueryable<FormDoc> ApplySorting(IQueryable<FormDoc> query, string sortField, bool sortAscending)
        {
            switch (sortField.ToLower())
            {
                case "title":
                    return sortAscending ? query.OrderBy(fd => fd.Title) : query.OrderByDescending(fd => fd.Title);
                case "datecreated":
                    return sortAscending ? query.OrderBy(fd => fd.DateCreated) : query.OrderByDescending(fd => fd.DateCreated);
                case "datemodified":
                    return sortAscending ? query.OrderBy(fd => fd.DateModified) : query.OrderByDescending(fd => fd.DateModified);
                case "createdby":
                    return sortAscending ? query.OrderBy(fd => fd.CreatedBy.FirstName) : query.OrderByDescending(fd => fd.CreatedBy.FirstName);
                default:
                    return query.OrderByDescending(fd => fd.DateModified);
            }
        }

        // Get count of form docs for pagination
        public async Task<int> GetFormDocsCountByEntityAsync(string entityType, int entityId)
        {
            using var context = _dbContextFactory.CreateDbContext();
            IQueryable<FormDoc> query = context.FormDocs;

            // Filter by entity type and ID
            switch (entityType.ToLower())
            {
                case "client":
                    query = query.Where(fd => fd.ClientId == entityId);
                    break;
                case "lead":
                    query = query.Where(fd => fd.LeadId == entityId);
                    break;
                case "submission":
                    query = query.Where(fd => fd.SubmissionId == entityId);
                    break;
                case "policy":
                    query = query.Where(fd => fd.PolicyId == entityId);
                    break;
                case "renewal":
                    query = query.Where(fd => fd.RenewalId == entityId);
                    break;
                default:
                    throw new ArgumentException("Invalid entity type", nameof(entityType));
            }

            return await query.CountAsync();
        }

        // [REVISIONS] ---------------------------------------------------------------//
        // Get all revisions for a form doc
        public async Task<List<FormDocRevision>> GetFormDocRevisionsAsync(int formDocId)
        {
            using var context = _dbContextFactory.CreateDbContext();
            return await context.FormDocRevisions
                .Include(fdr => fdr.CreatedBy)
                .Where(fdr => fdr.FormDocId == formDocId)
                .OrderByDescending(fdr => fdr.DateCreated)
                .ToListAsync();
        }

        // Get a specific revision
        public async Task<FormDocRevision> GetFormDocRevisionByIdAsync(int revisionId)
        {
            using var context = _dbContextFactory.CreateDbContext();
            return await context.FormDocRevisions
                .Include(fdr => fdr.CreatedBy)
                .FirstOrDefaultAsync(fdr => fdr.FormDocRevisionId == revisionId);
        }

        // Create a new revision
        public async Task<int> CreateFormDocRevisionAsync(int formDocId, string jsonData, string revisionName = null)
        {
            using var context = _dbContextFactory.CreateDbContext();
            var currentUser = _stateService.CurrentUser;
            context.Attach(currentUser);

            var formDoc = await context.FormDocs.FindAsync(formDocId);
            if (formDoc == null)
            {
                throw new Exception("FormDoc not found.");
            }

            // If no revision name provided, create a default one
            if (string.IsNullOrEmpty(revisionName))
            {
                var revisionCount = await context.FormDocRevisions
                    .Where(fdr => fdr.FormDocId == formDocId)
                    .CountAsync();
                revisionName = $"Revision {revisionCount + 1}";
            }

            var revision = new FormDocRevision
            {
                FormDocId = formDocId,
                RevisionName = revisionName,
                JSONData = jsonData,
                CreatedBy = currentUser,
                DateCreated = DateTime.UtcNow
            };

            context.FormDocRevisions.Add(revision);
            await context.SaveChangesAsync();
            return revision.FormDocRevisionId;
        }

        // Restore a revision (make it the current version)
        public async Task RestoreFormDocRevisionAsync(int formDocId, int revisionId)
        {
            using var context = _dbContextFactory.CreateDbContext();
            var currentUser = _stateService.CurrentUser;
            context.Attach(currentUser);

            var formDoc = await context.FormDocs.FindAsync(formDocId);
            var revision = await context.FormDocRevisions.FindAsync(revisionId);

            if (formDoc == null || revision == null || revision.FormDocId != formDocId)
            {
                throw new Exception("FormDoc or revision not found or doesn't match.");
            }

            // Create a new revision with the current data before updating
            await CreateFormDocRevisionAsync(formDocId, formDoc.JSONData, "Backup before restore");

            // Update the form doc with the revision data
            formDoc.JSONData = revision.JSONData;
            formDoc.ModifiedBy = currentUser;
            formDoc.DateModified = DateTime.UtcNow;

            context.FormDocs.Update(formDoc);
            await context.SaveChangesAsync();
        }

        // [CREATE / DUPE] -----------------------------------------------------------//
        public async Task<int> CreateCertificate(int clientid)
        {
            using var context = _dbContextFactory.CreateDbContext();
            var currentUser = _stateService.CurrentUser;
            context.Attach(currentUser);

            var newcert = new Certificate
            {
                ClientId = clientid,
                HolderName = "New Certificate",
                JSONData = "{}",
                CreatedBy = currentUser,
                ModifiedBy = currentUser,
                DateCreated = DateTime.UtcNow,
                DateModified = DateTime.UtcNow
            };
            context.Certificates.Add(newcert);
            await context.SaveChangesAsync();

            return newcert.CertificateId;
        }
        public async Task<int> CreateFormDoc(int formPdfId, int? clientId = null, int? leadId = null, int? submissionId = null, int? policyId = null, int? renewalId = null)
        {
            using var context = _dbContextFactory.CreateDbContext();
            var currentUser = _stateService.CurrentUser;
            context.Attach(currentUser);

            var formPdf = await context.FormPdf.FindAsync(formPdfId);
            if (formPdf == null)
            {
                throw new Exception("FormPdf not found.");
            }

            // Ensure at least one of the entity IDs is provided
            if (clientId == null && leadId == null && submissionId == null && policyId == null && renewalId == null)
            {
                throw new Exception("At least one entity ID (client, lead, submission, policy, or renewal) must be provided.");
            }

            // Create new FormDoc
            var newformdoc = new FormDoc
            {
                Title = "New " + formPdf.Title,
                Description = formPdf.Description,
                JSONData = "{}",
                FormPdf = formPdf,
                CreatedBy = currentUser,
                ModifiedBy = currentUser,
                DateCreated = DateTime.UtcNow,
                DateModified = DateTime.UtcNow
            };

            // Set entity IDs based on the provided parameters
            if (clientId != null) newformdoc.ClientId = clientId.Value;
            if (leadId != null) newformdoc.LeadId = leadId.Value;
            if (submissionId != null) newformdoc.SubmissionId = submissionId.Value;
            if (policyId != null) newformdoc.PolicyId = policyId.Value;
            if (renewalId != null) newformdoc.RenewalId = renewalId.Value;

            context.FormDocs.Add(newformdoc);
            await context.SaveChangesAsync();

            // Create initial revision
            await CreateFormDocRevisionAsync(newformdoc.FormDocId, "{}", "Initial Version");

            return newformdoc.FormDocId;
        }
        public async Task<int> DuplicateCertificateAsync(Certificate originalCertificate)
        {
            using var context = _dbContextFactory.CreateDbContext();
            var currentUser = _stateService.CurrentUser;
            context.Attach(currentUser);

            var duplicatedCertificate = new Certificate
            {
                ClientId = originalCertificate.ClientId,
                HolderName = originalCertificate.HolderName,
                ProjectName = originalCertificate.ProjectName,
                JSONData = originalCertificate.JSONData,
                AttachGLAI = originalCertificate.AttachGLAI,
                AttachGLAIfilename = originalCertificate.AttachGLAIfilename,
                AttachGLWOS = originalCertificate.AttachGLWOS,
                AttachGLWOSfilename = originalCertificate.AttachGLWOSfilename,
                AttachWCWOS = originalCertificate.AttachWCWOS,
                AttachWCWOSfilename = originalCertificate.AttachWCWOSfilename,
                BlockAttachments = originalCertificate.BlockAttachments
            };
            duplicatedCertificate.CreatedBy = currentUser;
            duplicatedCertificate.ModifiedBy = currentUser;
            duplicatedCertificate.DateCreated = DateTime.UtcNow;
            duplicatedCertificate.DateModified = DateTime.UtcNow;

            context.Certificates.Add(duplicatedCertificate);
            await context.SaveChangesAsync();

            return duplicatedCertificate.CertificateId;
        }
        public async Task<int> DuplicateFormDocAsync(FormDoc originalFormdoc)
        {
            using var context = _dbContextFactory.CreateDbContext();
            var currentUser = _stateService.CurrentUser;
            context.Attach(currentUser);

            var newFormdoc = new FormDoc
            {
                ClientId = originalFormdoc.ClientId,
                LeadId = originalFormdoc.LeadId,
                SubmissionId = originalFormdoc.SubmissionId,
                PolicyId = originalFormdoc.PolicyId,
                RenewalId = originalFormdoc.RenewalId,
                Title = originalFormdoc.Title + " (Copy)",
                Description = originalFormdoc.Description,
                JSONData = originalFormdoc.JSONData,
                DateCreated = DateTime.Now,
                DateModified = DateTime.Now,
                FormPdf = originalFormdoc.FormPdf
            };
            newFormdoc.CreatedBy = currentUser;
            newFormdoc.ModifiedBy = currentUser;
            newFormdoc.DateCreated = DateTime.UtcNow;
            newFormdoc.DateModified = DateTime.UtcNow;

            context.FormDocs.Add(newFormdoc);
            await context.SaveChangesAsync();

            // Create initial revision for the duplicate
            await CreateFormDocRevisionAsync(newFormdoc.FormDocId, newFormdoc.JSONData, "Initial Version (Duplicated)");

            return newFormdoc.FormDocId;
        }

        // [UPDATE] ------------------------------------------------------------------//
        public async Task UpdateCertificate(Certificate certificate)
        {
            using var context = _dbContextFactory.CreateDbContext();
            var currentUser = _stateService.CurrentUser;
            context.Attach(currentUser);

            var existingCertificate = await context.Certificates.FindAsync(certificate.CertificateId);
            if (existingCertificate != null)
            {
                existingCertificate.HolderName = certificate.HolderName;
                existingCertificate.JSONData = certificate.JSONData;
                existingCertificate.ModifiedBy = currentUser;
                existingCertificate.DateModified = DateTime.UtcNow;
                context.Certificates.Update(existingCertificate);
                await context.SaveChangesAsync();
            }
        }
        public async Task UpdateFormDoc(FormDoc formdoc)
        {
            using var context = _dbContextFactory.CreateDbContext();
            var currentUser = _stateService.CurrentUser;
            context.Attach(currentUser);

            var existingFormDoc = await context.FormDocs.FindAsync(formdoc.FormDocId);
            if (existingFormDoc != null)
            {
                // Create a revision with the old data before updating
                await CreateFormDocRevisionAsync(existingFormDoc.FormDocId, existingFormDoc.JSONData);

                // Update the form doc
                existingFormDoc.Title = formdoc.Title;
                existingFormDoc.Description = formdoc.Description;
                existingFormDoc.JSONData = formdoc.JSONData;
                existingFormDoc.ModifiedBy = currentUser;
                existingFormDoc.DateModified = DateTime.UtcNow;

                context.FormDocs.Update(existingFormDoc);
                await context.SaveChangesAsync();
            }
        }

        // TOOLS ---------------------------------------------------------------------//
        public byte[] FlattenPdf(byte[] pdfBytes)
        {
            using (MemoryStream stream = new MemoryStream(pdfBytes))
            {
                PdfLoadedDocument loadedDocument = new PdfLoadedDocument(stream);
                PdfLoadedForm loadedForm = loadedDocument.Form;
                loadedForm.Flatten = true;
                using (MemoryStream outputStream = new MemoryStream())
                {
                    loadedDocument.Save(outputStream);
                    loadedDocument.Close(true);
                    return outputStream.ToArray();
                }
            }
        }

        // Method to get certificate requests from the external portal
        public async Task<List<CertificateRequest>> GetExternalCertificateRequestsAsync()
        {
            return await _externalPortalService.GetAllCertificateRequestsAsync();
        }

        // Method to get a specific certificate request from the external portal
        public async Task<CertificateRequest> GetExternalCertificateRequestByIdAsync(int requestId)
        {
            return await _externalPortalService.GetCertificateRequestByIdAsync(requestId);
        }

        /// <summary>
        /// Updates the status and notes of a certificate request
        /// </summary>
        /// <param name="requestId">The ID of the certificate request to update</param>
        /// <param name="status">The new status</param>
        /// <param name="notes">The updated notes</param>
        /// <returns>True if the update was successful, false otherwise</returns>
        public async Task<bool> UpdateCertificateRequestStatusAsync(int requestId, string status, string notes)
        {
            try
            {
                using var context = _dbContextFactory.CreateDbContext();
                var request = await context.CertificateRequests.FindAsync(requestId);
                
                if (request == null)
                {
                    return false;
                }
                
                request.Status = status;
                request.Notes = notes;
                
                if (status == "Approved" || status == "Completed")
                {
                    request.ImportedDate = DateTime.UtcNow;
                }
                
                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating certificate request status for request {requestId}");
                return false;
            }
        }
        
        // Method to get external clients information
        public async Task<List<ExternalClient>> GetTopExternalClientsAsync(int count = 20)
        {
            return await _externalPortalService.GetTopExternalClientsAsync(count);
        }

        /// <summary>
        /// Deletes a certificate request by ID
        /// </summary>
        /// <param name="requestId">The ID of the certificate request to delete</param>
        /// <returns>True if delete successful, false otherwise</returns>
        public async Task<bool> DeleteCertificateRequestAsync(int requestId)
        {
            try
            {
                using var context = _dbContextFactory.CreateDbContext();
                var request = await context.CertificateRequests.FindAsync(requestId);
                
                if (request == null)
                {
                    return false;
                }
                
                context.CertificateRequests.Remove(request);
                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting certificate request {requestId}");
                return false;
            }
        }

        /// <summary>
        /// Deletes a form document and all its associated revisions
        /// </summary>
        /// <param name="formDocId">The ID of the form document to delete</param>
        /// <returns>True if deletion was successful, false otherwise</returns>
        public async Task<bool> DeleteFormDocAsync(int formDocId)
        {
            try
            {
                using var context = _dbContextFactory.CreateDbContext();
                
                // First, delete all revisions associated with this form
                var revisions = await context.FormDocRevisions
                    .Where(fdr => fdr.FormDocId == formDocId)
                    .ToListAsync();
                
                context.FormDocRevisions.RemoveRange(revisions);
                
                // Then delete the form document itself
                var formDoc = await context.FormDocs.FindAsync(formDocId);
                if (formDoc == null)
                {
                    _logger.LogWarning($"Form document {formDocId} not found for deletion");
                    return false;
                }
                
                context.FormDocs.Remove(formDoc);
                await context.SaveChangesAsync();
                
                _logger.LogInformation($"Successfully deleted form document {formDocId} and its {revisions.Count} revisions");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting form document {formDocId}");
                throw; // Re-throw the exception to be handled by the UI
            }
        }

        /// <summary>
        /// Gets all certificate requests from both internal and external sources
        /// </summary>
        /// <returns>List of all certificate requests</returns>
        public async Task<List<CertificateRequest>> GetAllCertificateRequestsAsync()
        {
            try
            {
                // Get internal requests
                var internalRequests = await GetInternalCertificateRequestsAsync();
                
                // Try to get external requests, but don't fail if external DB is unavailable
                List<CertificateRequest> externalRequests = new List<CertificateRequest>();
                try
                {
                    externalRequests = await GetExternalCertificateRequestsAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to retrieve external certificate requests. Proceeding with internal requests only.");
                    // Continue with only internal requests
                }
                
                // Combine the lists
                return internalRequests.Concat(externalRequests).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all certificate requests");
                return new List<CertificateRequest>();
            }
        }
        
        /// <summary>
        /// Gets a certificate request by ID, checking both internal and external sources
        /// </summary>
        /// <param name="requestId">The ID of the certificate request</param>
        /// <returns>The certificate request if found, null otherwise</returns>
        public async Task<CertificateRequest> GetCertificateRequestByIdAsync(int requestId)
        {
            try
            {
                // First check internal database
                using var context = _dbContextFactory.CreateDbContext();
                var internalRequest = await context.CertificateRequests.FindAsync(requestId);
                
                if (internalRequest != null)
                {
                    return internalRequest;
                }
                
                // If not found, try to check external database
                try
                {
                    return await GetExternalCertificateRequestByIdAsync(requestId);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"Failed to retrieve external certificate request {requestId}");
                    // Return null if external check failed
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving certificate request {requestId}");
                return null;
            }
        }

        public async Task<Attachment> CreateAndFillSL2(Proposal proposal, string clientName, int clientId, string businessDesc, string coverageCode)
        {
            try
            {
                // Create directory if it doesn't exist
                var directoryPath = Path.Combine("wwwroot", "clients", proposal.ClientId.ToString(), proposal.AttachmentGroupId.ToString());
                Directory.CreateDirectory(directoryPath);

                // Copy the template file
                var sourcePath = Path.Combine("wwwroot", "forms", "sl-2-2024-01.pdf");
                var originalFileName = "sl-2-2024-01.pdf";
                var fileExtension = Path.GetExtension(originalFileName);
                var hash = StringHelper.GenerateFiveCharacterHash(originalFileName);
                var hashedFileName = $"{Path.GetFileNameWithoutExtension(originalFileName)}_{hash}{fileExtension}";
                var destinationPath = Path.Combine(directoryPath, hashedFileName);

                File.Copy(sourcePath, destinationPath, true);

                // Create dictionary with field values
                var dict = new Dictionary<string, object>
                {
                    ["Name of Insured"] = clientName,
                    ["Description of Risk"] = businessDesc,
                    ["Type of Insurance or Coverage Code"] = coverageCode
                };

                // Set dates
                var today = DateTime.Now;
                dict["Insurer 1 Date"] = today.ToString("MM/yyyy");
                dict["Insurer 2 Date"] = today.ToString("MM/yyyy");
                dict["Insurer 3 Date"] = today.ToString("MM/yyyy");
                dict["Signed Date"] = today.ToString("MM/dd/yyyy");

                // Load and fill the PDF
                using (var pdfStream = new FileStream(destinationPath, FileMode.Open, FileAccess.ReadWrite))
                {
                    using var loadedDoc = new Syncfusion.Pdf.Parsing.PdfLoadedDocument(pdfStream);
                    var form = loadedDoc.Form;

                    // Fill text fields and checkboxes
                    foreach (Syncfusion.Pdf.Parsing.PdfLoadedField field in form.Fields)
                    {
                        if (dict.TryGetValue(field.Name, out var value))
                        {
                            if (field is Syncfusion.Pdf.Parsing.PdfLoadedTextBoxField textField)
                            {
                                textField.Text = value?.ToString() ?? string.Empty;
                            }
                            else if (field is Syncfusion.Pdf.Parsing.PdfLoadedCheckBoxField checkBoxField)
                            {
                                var strVal = value?.ToString()?.Trim().ToLowerInvariant();
                                checkBoxField.Checked = strVal == "true" || strVal == "yes" || strVal == "on" || strVal == "1";
                            }
                        }
                    }

                    // Flatten the form before saving
                    form.Flatten = true;

                    // Save the filled form
                    loadedDoc.Save(pdfStream);
                    loadedDoc.Close(true);
                }

                // Create attachment record
                var attachment = new Attachment
                {
                    OriginalFileName = originalFileName,
                    HashedFileName = hashedFileName,
                    FileFormat = fileExtension,
                    FileSize = new FileInfo(destinationPath).Length,
                    DateCreated = DateTime.UtcNow,
                    AttachmentGroupId = proposal.AttachmentGroupId ?? 0,
                    IsSL2 = true,
                    Description = "SL-2 Form",
                    RenewalId = proposal.RenewalId,
                    ClientId = clientId,
                    LocalPath = Path.Combine("clients", proposal.ClientId.ToString(), proposal.AttachmentGroupId.ToString())
                };
                using var context = _dbContextFactory.CreateDbContext();
                context.Attachments.Add(attachment);
                await context.SaveChangesAsync();
                return attachment;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating and filling SL-2 form");
                throw;
            }
        }
    }
}
