﻿@using Surefire.Domain.Plugins
@using Surefire.Domain.Shared.Services
@using Surefire.Domain.Shared.Helpers
@using RingCentral
@using Surefire.Domain.Chat
@inject StateService _stateService
@inject ChatService _chatService
@inject IJSRuntime JSRuntime

<div class="d-flex justify-content-between align-items-center mb-2">
    <h5>Recent Phone Calls</h5>
    <FluentButton Appearance="Appearance.Lightweight" OnClick="RefreshCallLogs" IconStart="@(new Icons.Regular.Size16.ArrowClockwise())" Title="Refresh Call Logs">Refresh</FluentButton>
</div>

@if (recentCalls == null)
{
    <FluentProgressRing Width="30px" Color="#1b8ce3" />
}
else if (!recentCalls.Any())
{
    <FluentIcon Value="@(new Icons.Regular.Size24.CallDismiss())" CustomColor="#b7b7b7" Color="Color.Custom" />
    <span class="phone-none">No recent phone conversations.</span>
}
else
{
    
    <div class="fluent-data-grid">
        <FluentDataGrid Items="@recentCalls" ResizableColumns="true" ShowHover="true" Pagination="@pagination" TGridItem="CallLogRecordFire">
            <PropertyColumn Property="@(p => p.from.phoneNumber)" Title="From" Sortable="true" />
            <PropertyColumn Property="@(p => p.to.phoneNumber)" Title="To" Sortable="true" />
            <PropertyColumn Property="@(p => p.direction)" Title="Direction" Sortable="true" />
            <PropertyColumn Property="@(p => p.startTime)" Title="Start Time" Format="yyyy-MM-dd HH:mm:ss" Sortable="true" />
            <PropertyColumn Property="@(p => TimeSpan.FromSeconds(p.duration ?? 0))" Title="Duration" Format="hh\:mm\:ss" Sortable="true" />
            <PropertyColumn Property="@(p => p.action)" Title="Action" Sortable="true" />
            <PropertyColumn Property="@(p => p.result)" Title="Result" Sortable="true" />
            <TemplateColumn Title="Recording" Align="Align.Start">
                @if (context.recording != null && !string.IsNullOrEmpty(context.recording.contentUri))
                {
                    <FluentAnchor Href="@context.recording.contentUri" Target="_blank" Appearance="Appearance.Hypertext">Listen</FluentAnchor>
                }
                else
                {
                    <span>N/A</span>
                }
            </TemplateColumn>
            <PropertyColumn Property="@(p => p.type)" Title="Type" Sortable="true" />
            <PropertyColumn Property="@(p => p.reason)" Title="Reason" Sortable="true" />
            <PropertyColumn Property="@(p => p.sessionId)" Title="Session ID" Sortable="true" />
        </FluentDataGrid>
        <div class="fluent-data-grid__bottombar">
            <div class="fluent-data-grid__pagination">
                <FluentPaginator State="@pagination" />
            </div>
        </div>
    </div>
}

@code {
    private IQueryable<CallLogRecordFire> recentCalls = Enumerable.Empty<CallLogRecordFire>().AsQueryable();
    private PaginationState pagination = new PaginationState { ItemsPerPage = 17 };
    private bool isLoading = false;
    private string errorMessage = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        Console.WriteLine("Initializing new call logs...");
        await LoadPhoneCallLogsAsync();
    }

    private async Task LoadPhoneCallLogsAsync()
    {
        isLoading = true;
        errorMessage = string.Empty;
        try
        {
            Console.WriteLine("Fetching call logs using ChatService...");
            var logs = await _chatService.GetRecentCallLogsAsync();
            recentCalls = logs.AsQueryable();
            Console.WriteLine($"Loaded {recentCalls.Count()} call logs.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading call logs: {ex.Message}");
            errorMessage = "Failed to load call logs. Please try again later.";
            recentCalls = Enumerable.Empty<CallLogRecordFire>().AsQueryable(); // Clear existing logs on error
        }
        finally
        {
            isLoading = false;
            await InvokeAsync(StateHasChanged); // Ensure UI updates
        }
    }

    private async Task RefreshCallLogs()
    {
        Console.WriteLine("Refresh button clicked.");
        await LoadPhoneCallLogsAsync();
    }
}
