using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Azure.AI.FormRecognizer.DocumentAnalysis;
using Azure;
using Surefire.Domain.Attachments.Models;
using Surefire.Domain.Attachments.Services;
using Surefire.Domain.Logs;

namespace Surefire.Domain.Shared.Services
{
    public class AzureFormService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<AzureFormService> _logger;
        private readonly ILoggingService _loggingService;
        private readonly AttachmentService _attachmentService;
        private readonly DocumentAnalysisClient _documentAnalysisClient;

        public AzureFormService(
            IConfiguration configuration,
            ILogger<AzureFormService> logger,
            ILoggingService loggingService,
            AttachmentService attachmentService)
        {
            _configuration = configuration;
            _logger = logger;
            _loggingService = loggingService;
            _attachmentService = attachmentService;

            // Initialize Azure Form Recognizer client
            var endpoint = Environment.GetEnvironmentVariable("AZURE_FORM_RECOGNIZER_ENDPOINT") 
                          ?? _configuration["Azure:FormRecognizer:Endpoint"];
            var key = Environment.GetEnvironmentVariable("AZURE_FORM_RECOGNIZER_KEY") 
                     ?? _configuration["Azure:FormRecognizer:Key"];

            if (string.IsNullOrEmpty(endpoint) || string.IsNullOrEmpty(key))
            {
                throw new InvalidOperationException("Azure Form Recognizer endpoint and key must be configured");
            }

            _documentAnalysisClient = new DocumentAnalysisClient(new Uri(endpoint), new AzureKeyCredential(key));
        }

        /// <summary>
        /// Processes a PDF attachment using Azure Form Recognizer and saves the extracted data as JSON
        /// </summary>
        /// <param name="attachment">The PDF attachment to process</param>
        /// <returns>The created JSON attachment</returns>
        public async Task<Attachment> ProcessPdfAttachmentAsync(Attachment attachment)
        {
            if (attachment == null)
                throw new ArgumentNullException(nameof(attachment));

            if (!attachment.FileFormat?.ToLower().EndsWith(".pdf") == true)
                throw new ArgumentException("Attachment must be a PDF file", nameof(attachment));

            await _loggingService.LogAsync(LogLevel.Information, 
                $"Starting Azure Form Recognizer processing for attachment: {attachment.OriginalFileName}", 
                "AzureFormService");

            try
            {
                // Step 1: Process PDF with Form Recognizer
                var formResult = await ProcessPdfWithFormRecognizer(attachment);

                // Step 2: Convert Form Recognizer result to structured data
                var rawData = ExtractRawData(formResult);

                // Step 3: Save raw data
                var rawJsonAttachment = await SaveJsonData(rawData, attachment, "raw");

                // Step 4: Minify the data
                var minifiedData = MinifyData(rawData);

                // Step 5: Save minified data
                var minifiedJsonAttachment = await SaveJsonData(minifiedData, attachment, "minified");

                await _loggingService.LogAsync(LogLevel.Information, 
                    $"Successfully processed PDF and created JSON attachments for: {attachment.OriginalFileName}", 
                    "AzureFormService");

                return minifiedJsonAttachment;
            }
            catch (Exception ex)
            {
                await _loggingService.LogAsync(LogLevel.Error, 
                    $"Error processing PDF attachment {attachment.OriginalFileName}: {ex.Message}", 
                    "AzureFormService", ex);
                throw;
            }
        }

        private async Task<AnalyzeResult> ProcessPdfWithFormRecognizer(Attachment attachment)
        {
            await _loggingService.LogAsync(LogLevel.Information, 
                $"Sending PDF to Azure Form Recognizer: {attachment.OriginalFileName}", 
                "AzureFormService");

            // Build the full file path
            var rootPath = Directory.GetCurrentDirectory();
            var filePath = Path.Combine(rootPath, "wwwroot", attachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()), attachment.HashedFileName);

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"PDF file not found: {filePath}");
            }

            // Read the PDF file and send to Form Recognizer
            using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            var operation = await _documentAnalysisClient.AnalyzeDocumentAsync(WaitUntil.Completed, "prebuilt-document", fileStream);

            return operation.Value;
        }

        private object ExtractRawData(AnalyzeResult formResult)
        {
            _loggingService.LogAsync(LogLevel.Information, "Converting Form Recognizer results to structured data", "AzureFormService");
            //Console.WriteLine("Converting Form Recognizer results to structured data");

            var rawData = new
            {
                metadata = new
                {
                    extraction_timestamp = DateTime.Now.ToString("O"),
                    page_count = formResult.Pages?.Count ?? 0
                },
                key_value_pairs = new List<object>(),
                tables = new List<object>(),
                text_content = new List<object>()
            };

            // Extract key-value pairs
            if (formResult.KeyValuePairs != null)
            {
                foreach (var kv in formResult.KeyValuePairs)
                {
                    if (kv.Key != null && kv.Value != null)
                    {
                        ((List<object>)rawData.key_value_pairs).Add(new
                        {
                            key = kv.Key.Content,
                            value = kv.Value.Content,
                            confidence = kv.Confidence
                        });
                    }
                }
            }

            // Extract tables
            if (formResult.Tables != null)
            {
                for (int tableIdx = 0; tableIdx < formResult.Tables.Count; tableIdx++)
                {
                    var table = formResult.Tables[tableIdx];
                    var tableData = new
                    {
                        table_id = tableIdx,
                        row_count = table.RowCount,
                        column_count = table.ColumnCount,
                        cells = new List<object>()
                    };

                    foreach (var cell in table.Cells)
                    {
                        ((List<object>)tableData.cells).Add(new
                        {
                            row_index = cell.RowIndex,
                            column_index = cell.ColumnIndex,
                            content = cell.Content,
                            row_span = cell.RowSpan,
                            column_span = cell.ColumnSpan
                        });
                    }

                    ((List<object>)rawData.tables).Add(tableData);
                }
            }

            // Extract text content from pages
            if (formResult.Pages != null)
            {
                for (int pageIdx = 0; pageIdx < formResult.Pages.Count; pageIdx++)
                {
                    var page = formResult.Pages[pageIdx];
                    var pageData = new
                    {
                        page_number = pageIdx + 1,
                        lines = new List<object>()
                    };

                    if (page.Lines != null)
                    {
                        foreach (var line in page.Lines)
                        {
                            ((List<object>)pageData.lines).Add(new
                            {
                                content = line.Content,
                                bounding_box = line.BoundingPolygon?.Select(p => new { x = p.X, y = p.Y }).ToList()
                            });
                        }
                    }

                    ((List<object>)rawData.text_content).Add(pageData);
                }
            }

            _loggingService.LogAsync(LogLevel.Information, $"Extracted {((List<object>)rawData.key_value_pairs).Count} key-value pairs, {((List<object>)rawData.tables).Count} tables, and text from {((List<object>)rawData.text_content).Count} pages", "AzureFormService");
            //Console.WriteLine()
            return rawData;
        }

        private object MinifyData(object rawData)
        {
            _loggingService.LogAsync(LogLevel.Information, "Starting data minification process", "AzureFormService");

            // Convert to JSON and back to get dynamic object for easier manipulation
            var jsonString = JsonSerializer.Serialize(rawData);
            var data = JsonSerializer.Deserialize<JsonElement>(jsonString);

            var minifiedData = new
            {
                metadata = data.GetProperty("metadata"),
                key_value_pairs = new List<object>(),
                tables = new List<object>(),
                text_content = new List<object>()
            };

            // Filter key-value pairs based on confidence and relevance
            if (data.TryGetProperty("key_value_pairs", out var kvPairs))
            {
                foreach (var kv in kvPairs.EnumerateArray())
                {
                    if (kv.TryGetProperty("confidence", out var confidence) && confidence.GetDouble() < 0.5)
                        continue;

                    if (kv.TryGetProperty("value", out var value) && string.IsNullOrWhiteSpace(value.GetString()))
                        continue;

                    if (kv.TryGetProperty("key", out var key))
                    {
                        var keyLower = key.GetString()?.ToLower() ?? "";
                        if (keyLower.Contains("page") || keyLower.Contains("document") || 
                            keyLower.Contains("footer") || keyLower.Contains("header"))
                            continue;
                    }

                    ((List<object>)minifiedData.key_value_pairs).Add(JsonSerializer.Deserialize<object>(kv.GetRawText()));
                }
            }

            // Filter tables to keep only those with meaningful content
            if (data.TryGetProperty("tables", out var tables))
            {
                foreach (var table in tables.EnumerateArray())
                {
                    if (table.TryGetProperty("cells", out var cells) && cells.GetArrayLength() < 4)
                        continue;

                    bool hasMeaningfulContent = false;
                    foreach (var cell in cells.EnumerateArray())
                    {
                        if (cell.TryGetProperty("content", out var content))
                        {
                            var contentStr = content.GetString()?.Trim() ?? "";
                            if (contentStr.Length > 3)
                            {
                                hasMeaningfulContent = true;
                                break;
                            }
                        }
                    }

                    if (hasMeaningfulContent)
                    {
                        ((List<object>)minifiedData.tables).Add(JsonSerializer.Deserialize<object>(table.GetRawText()));
                    }
                }
            }

            // Filter text content to keep only meaningful lines
            if (data.TryGetProperty("text_content", out var textContent))
            {
                foreach (var page in textContent.EnumerateArray())
                {
                    var pageData = new
                    {
                        page_number = page.GetProperty("page_number").GetInt32(),
                        lines = new List<object>()
                    };

                    if (page.TryGetProperty("lines", out var lines))
                    {
                        foreach (var line in lines.EnumerateArray())
                        {
                            if (line.TryGetProperty("content", out var content))
                            {
                                var contentStr = content.GetString()?.Trim() ?? "";
                                if (contentStr.Length < 3 || 
                                    System.Text.RegularExpressions.Regex.IsMatch(contentStr.ToLower(), @"^page \d+$|^\d+$"))
                                    continue;

                                ((List<object>)pageData.lines).Add(new { content = contentStr });
                            }
                        }
                    }

                    if (((List<object>)pageData.lines).Count > 0)
                    {
                        ((List<object>)minifiedData.text_content).Add(pageData);
                    }
                }
            }

            var originalSize = JsonSerializer.Serialize(rawData).Length;
            var minifiedSize = JsonSerializer.Serialize(minifiedData).Length;
            var reductionPercent = ((double)(originalSize - minifiedSize) / originalSize) * 100;

            _loggingService.LogAsync(LogLevel.Information, $"Data minification complete. Size reduced by {reductionPercent:F2}% ({originalSize} -> {minifiedSize} bytes)", "AzureFormService");

            return minifiedData;
        }

        private async Task<Attachment> SaveJsonData(object data, Attachment originalAttachment, string dataType)
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var baseFileName = Path.GetFileNameWithoutExtension(originalAttachment.OriginalFileName);
            var jsonFileName = $"{baseFileName}_{dataType}_{timestamp}.json";

            // Serialize the data to JSON
            var jsonData = JsonSerializer.Serialize(data, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            // Create the JSON attachment
            var jsonAttachment = new Attachment
            {
                OriginalFileName = jsonFileName,
                FileFormat = ".json",
                FileSize = System.Text.Encoding.UTF8.GetByteCount(jsonData),
                DateCreated = DateTime.UtcNow,
                AttachmentGroupId = originalAttachment.AttachmentGroupId,
                Description = $"Azure Form Recognizer {dataType} data from {originalAttachment.OriginalFileName}",
                RenewalId = originalAttachment.RenewalId,
                ClientId = originalAttachment.ClientId,
                IsClientAccessible = false,
                Status = 1
            };

            // Save the JSON file to the same directory as the original PDF
            var rootPath = Directory.GetCurrentDirectory();
            var targetDirectory = Path.Combine(rootPath, "wwwroot", originalAttachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()));
            
            if (!Directory.Exists(targetDirectory))
            {
                Directory.CreateDirectory(targetDirectory);
            }

            // Generate hashed filename for the JSON file
            var hash = Surefire.Domain.Shared.Helpers.StringHelper.GenerateFiveCharacterHash(Path.GetFileNameWithoutExtension(jsonFileName));
            var hashedJsonFileName = $"{Path.GetFileNameWithoutExtension(jsonFileName)}_{hash}.json";
            var jsonFilePath = Path.Combine(targetDirectory, hashedJsonFileName);

            // Write the JSON data to file
            await File.WriteAllTextAsync(jsonFilePath, jsonData);

            // Update attachment properties
            jsonAttachment.HashedFileName = hashedJsonFileName;
            jsonAttachment.LocalPath = originalAttachment.LocalPath;

            // Save the attachment to database using AttachmentService
            await _attachmentService.SaveAttachmentDirectlyAsync(jsonAttachment);

            await _loggingService.LogAsync(LogLevel.Information, 
                $"Saved {dataType} JSON data to: {jsonFilePath}", 
                "AzureFormService");

            return jsonAttachment;
        }
    }
} 