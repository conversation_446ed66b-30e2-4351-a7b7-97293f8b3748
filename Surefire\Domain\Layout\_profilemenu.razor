﻿@namespace Surefire.Components.Layout
@using Microsoft.FluentUI.AspNetCore.Components
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Components.Routing
@using Syncfusion.Blazor
@using Syncfusion.Blazor.Inputs
@using Surefire.Domain.Clients.Models
@using Surefire.Domain.Shared.Models
@using Surefire.Domain.Shared.Services
@using Surefire.Domain.Logs
@using Surefire.Data
@using System.Security.Claims;
@inject NavigationManager NavigationManager
@inject SharedService SharedService
@inject ILoggingService _log


<AuthorizeView>
    <Authorized>
        @if (AppUser != null)
        {
            <a href="/Profile" class="profile-gear"><FluentIcon Value="@(new Icons.Regular.Size28.Settings())" Color="Color.Custom" CustomColor="#fff" /></a>
            <FluentProfileMenu Image="@("/img/staff/" + AppUser.PictureUrl)"
            Status="@PresenceStatus.Available"
            HeaderLabel="Metro Insurance Services"
            FullName="@(AppUser.FirstName + " " + AppUser.LastName)"
            Initials="@(AppUser.FirstName[0].ToString().ToUpper() + " " + AppUser.LastName[0].ToString().ToUpper())"
            EMail="@AppUser.Email"
            PopoverStyle="min-width: 430px; padding-right:25px; position:relative; top:10px;"
            OnFooterLinkClick="ManageAccount"
            OnHeaderButtonClick="LogMeOut" />
        }
    </Authorized>
    <NotAuthorized>
        <a href="/Account/Login" class="e-icon-btn e-icons">
            <FluentIcon Value="@(new Icons.Regular.Size24.Person())" />
        </a>
    </NotAuthorized>
</AuthorizeView>

@code {
    [Parameter]
    public string UserId { get; set; }

    private ApplicationUser AppUser { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var user = await SharedService.GetCurrentUserAsync();
        if (user == null)
        {
            try
            {
                NavigationManager.NavigateTo("/Account/Login");
            }catch {
                Console.WriteLine("Can't get user 233");
                await _log.LogAsync(LogLevel.Error, "Error: Unable to navigate to /Account/Login", "_profilemenu");
            }
            
        }
        else
        {
            AppUser = user;
        }
    }

    public void LogMeOut()
    {
        NavigationManager.NavigateTo("/Account/Logout", true); // force navigation to reload the page
    }
    public void ManageAccount()
    {
        NavigationManager.NavigateTo("/Profile"); // force navigation to reload the page
    }
}
