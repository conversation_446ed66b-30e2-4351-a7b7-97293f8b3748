@using Surefire.Domain.Forms.Models
@using Surefire.Domain.Forms.Services
@using Syncfusion.Blazor.Spinner
@using Syncfusion.Blazor.Notifications
@inject FormService FormService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<div class="sectiontitletab">Certificate Requests</div>
<div class="leads-box-inner">
    <table id="certtable" cellspacing="0" class="ltable">
        <thead class="lbg">
            <tr>
                <th><span class="ctweak">Client</span></th>
                <th style="width:200px;">Holder</th>
                <th style="width:100px;">Date</th>
                <th style="width:70px;">Status</th>
            </tr>
        </thead>
        <tbody class="lbody">
            @if (isLoading)
            {
                for (var i = 0; i < 10; i++)
                {
                    <tr class="lrow">
                        <td colspan="5">
                            <SfSkeleton Shape=SkeletonType.Rectangle Width="570px" Height="13px" CssClass="e-customize" Visible="true"></SfSkeleton>
                        </td>
                    </tr>
                }
            }
            else if (requests == null || !requests.Any())
            {
                <tr>
                    <td colspan="5" class="no-taskssub">No pending certificate requests</td>
                </tr>
            }
            else
            {
                @foreach (var request in requests)
                {
                    <tr class="lrow main">
                        <td class="lcname lpad" @onclick="() => ShowDetails(request)"><a class="llink" href="/Forms/CertificateRequest/@request.CertificateRequestId">@request.ClientCompany</a></td>
                        <td class="lccon ellipsis">@request.HolderName</td>
                        <td class="lccon ellipsis">@request.RequestDate.ToString("M/d/yyyy")</td>
                        <td class="lccon ellipsis"><span class="dot dot-0"></span><span class="llink link-0">new</span></td>
                    </tr>
                }
            }
        </tbody>
    </table>
    <div style="height:7px;"></div>
</div>

@if (showDetailsModal && selectedRequest != null)
{
    <div class="modal-backdrop" @onclick="CloseDetailsModal">
        <div class="modal-content" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>Certificate Request Details</h3>
                <button type="button" class="close-btn" @onclick="CloseDetailsModal">×</button>
            </div>
            <div class="modal-body">
                <table class="details-table">
                    <tr>
                        <td class="detail-label">Holder:</td>
                        <td>@selectedRequest.HolderName</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Client:</td>
                        <td>@selectedRequest.ClientCompany</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Contact:</td>
                        <td>@selectedRequest.ClientName</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Email:</td>
                        <td>@selectedRequest.ClientEmail</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Phone:</td>
                        <td>@selectedRequest.ClientPhone</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Status:</td>
                        <td><span class="status-badge <EMAIL>()">@selectedRequest.Status</span></td>
                    </tr>
                    <tr>
                        <td class="detail-label">Requested:</td>
                        <td>@selectedRequest.RequestDate.ToString("MM/dd/yyyy HH:mm")</td>
                    </tr>
                    @if (!string.IsNullOrEmpty(selectedRequest.Notes))
                    {
                        <tr>
                            <td class="detail-label">Notes:</td>
                            <td>@selectedRequest.Notes</td>
                        </tr>
                    }
                </table>
                <div class="modal-actions">
                    <button type="button" class="action-btn view-request-btn" @onclick="() => NavigateToCertificateRequest(selectedRequest.CertificateRequestId)">
                        View Certificate Request
                    </button>
                    <button type="button" class="action-btn close-modal-btn" @onclick="CloseDetailsModal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<CertificateRequest> requests;
    private List<CertificateRequest> pendingRequests;
    private bool isLoading = true;
    private bool showDetailsModal = false;
    private CertificateRequest selectedRequest;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            isLoading = true;
            // Get external certificate requests
            requests = await FormService.GetExternalCertificateRequestsAsync();
            
            // Filter for only pending requests
            pendingRequests = requests.Where(r => r.Status == "Pending").ToList();
            
            // Use pending requests for display
            requests = pendingRequests;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error fetching certificate requests: {ex.Message}");
            requests = new List<CertificateRequest>();
        }
        finally
        {
            isLoading = false;
        }
    }
    
    private void ShowDetails(CertificateRequest request)
    {
        selectedRequest = request;
        showDetailsModal = true;
        StateHasChanged();
    }
    
    private void CloseDetailsModal()
    {
        showDetailsModal = false;
        StateHasChanged();
    }
    
    private void NavigateToCertificateRequest(int requestId)
    {
        NavigationManager.NavigateTo($"/Forms/CertificateRequest/{requestId}");
    }
    private int GetStatusClass(string status)
    {
        return status?.ToLower() switch
        {
            "pending" => 2,
            "completed" => 1,
            "sent" => 1,
            "signed" => 1,
            _ => 0
        };
    }
} 