/* ProposalDetails.razor.css */

.proposal-details-stack {
    display: flex;
    flex-direction: row;
    gap: 24px;
    margin-top: 10px;
    align-items: flex-start;
    position: relative;
    overflow: hidden;
}

.proposal-details-col {
    min-width: 340px;
    max-width: 375px;
    display: flex;
    flex-direction: column;
    gap: 18px;
    position: relative;
}

.CardStyle {
    background: #fefefe !important;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    padding: 24px 20px 18px 20px !important;
    margin-bottom: 18px !important;
    border: 1px solid #e5e5e5;
    position: relative;
}

.header-card {
    background-color: #ccc;
    font-family: "montserrat", sans-serif;
    font-size: .9rem;
    font-weight: 400;
    color: #626262;
    text-shadow: 1px 1px 1px #ffffffb9;
    text-transform: uppercase;
    padding: 4px 15px 4px 12px;
    border-top-right-radius: 10px;
    letter-spacing: .2em;
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    z-index: 2;
}
.accou {
    text-decoration: underline;
    margin-left: 4px;
    color: #036ac4;
    position: relative !important;
    left: -18px !important;
    top: 1px !important;
}
    .accou:hover {
        cursor: pointer;
        color: #000;
    }
.proposal-details-card-status {
    background-color:#036ac4 !important;
}

.proposal-details-card {
    padding-top: 38px !important;
    margin-top: 18px;
}

.proposal-details-card .mb-2 {
    margin-bottom: 16px;
}

.proposal-details-file-upload-area {
    border: 2px dashed #b3b3b3;
    border-radius: 6px;
    padding: 18px;
    text-align: center;
    background: #fafbfc;
    margin-bottom: 16px;
}

.proposal-details-dropzone-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.proposal-details-file-list {
    margin-top: 10px;
}

.proposal-details-file-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.97em;
}

.proposal-details-file-table th {
    text-align: left;
    padding: 8px;
    border-bottom: 2px solid #eaeaea;
    background: #f5f7fa;
}

.proposal-details-file-table td {
    padding: 8px;
    border-bottom: 1px solid #eaeaea;
}

.proposal-details-empty-files {
    padding: 18px;
    text-align: center;
    color: #888;
    font-size: 0.98em;
}

.proposal-details-btn {
    margin-top: 18px;
    width: 100%;
    font-size: 1.05em;
}

.proposal-details-special-instructions textarea {
    min-height: 120px;
    font-size: 1em;
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    background: #fafbfc;
}

@media (max-width: 1200px) {
    .proposal-details-stack {
        flex-direction: column;
        gap: 18px;
    }
    .proposal-details-col {
        max-width: 100%;
        min-width: 0;
    }
}
.thumb-icon {
    max-width: 160px;
    box-shadow: 1px 1px 9px #ccc;
}
.attachment-thumbnail {
    margin-top:30px;
}
.tiny-icon-btn {
    min-width: 18px !important;
    height: 18px !important;
    padding: 0 !important;
    font-size: 12px !important;
    line-height: 12px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
}
.attachment-thumbnail {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    margin-bottom: 0;
}

.attachment-actions {
    display: flex;
    flex-direction: row;
    gap: 6px;
    margin-top: 5px;
    margin-bottom: 2px;
    justify-content: flex-end !important;
    width: 100%;
    transition: all .5s;
    opacity: .6;
}
    .attachment-actions:hover {
        opacity: 1;
    }
.delbtn, .folderbtn, .openbtn {
    cursor: pointer;
    font-size: 14px !important;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    transition: background 0.15s;
}

    .delbtn:hover, .folderbtn:hover, .openbtn:hover {
        background: #e8f0fe;
        cursor: pointer;
    }


.drop-zone-quote, .drop-zone-acord, .drop-zone-supplemental, .drop-zone-proposal, .drop-zone-enclosures, .drop-zone-sl2, .drop-zone-binder, .drop-zone-invoice, .drop-zone-lossruns {
    outline: 0px;
    outline-offset: 0px !important;
    padding: 0 !important;
    background: none !important;
}
.flat-card {
    font-family: "montserrat", sans-serif;
    font-size: 1.6em;
    font-weight: 300;
    color: #828282;
    position:relative;
    top:25px;
}

.flat-class-container {
    position: relative;top: -23px;
    margin-left:10px;
}
.inst-me {
    font-family: "montserrat", sans-serif;
    font-size:.9em;
    color:#7a7a7a;
    padding:10px;
}
.flat-up {
    position: relative;
    top: -75px;
}
.advancedbtn {
    font-size: .85em;
    color:#8a8a8a;
    margin-top:8px;
}
.advancedbtn:hover {
    color:#515151;
    text-decoration: underline;
    cursor: pointer;
}

.field-label {
    margin-top:7px;
    width:140px;
    overflow: hidden;
    font-family: 'Segoe UI Variable Display';
    font-size: .95em;
    padding: 5px 0px 0px 0px;
    color:#3d3d3d;
}
.field-label-status {
    margin-top: 7px;
    width: 140px;
    overflow: hidden;
    font-family: Montserrat;
    font-weight:800;
    font-size: 1.1em;
    padding: 5px 0px 0px 0px;
    color: #3d3d3d;
}
.field-value {
    margin-top: 7px;
    max-width:185px;
    font-family: 'Segoe UI Variable Display';
    font-size: 1em;
    padding: 0px 0px 0px 0px;
}
.inc-item {
    background-color: #e0e0e0;
    padding: 0px 5px;
    border-radius: 3px;
    border:1px solid #ccc;
    width: 80px;
}
.inc-sl2 {
    background-color: #e0e0e0;
    padding: 0px 5px;
    border-radius: 3px;
    border: 1px solid #ccc;
    width: 150px;
    color: #036ac4;
   
}
.sl2-btn {
    border: 1px solid #ccc;
    opacity: .6;
    padding: 3px 4px;
    background-color: #bdbdbd;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    position: relative;top:2px;
}
    .sl2-btn:hover {
        opacity: 1;
        cursor: pointer;
        border: 1px solid #036ac4;
        background-color: #e5e5e5;
    }
.inc-title {
    font-family: 'Segoe UI Variable Display';
    font-size: .95em;
    color: #3d3d3d;
    position: relative;

}
.sl2create-icon {
    position: relative;
    top: 4px;
}
.sl2create-txt {
    font-family: 'Segoe UI Variable Display';
    font-size: .75em;
    position: relative;
    top: -1px;
    padding-right:5px;
}
.page-extractor {
    margin: 15px 0;
    padding: 15px;
    background-color: #fcf5fb;
    border-radius: 8px;
    border: 1px solid #bca1d1;
}

.page-inputs {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    justify-content: center;
}

.page-input {
    width: 35px;
    height: 33px;
    padding: 8px;
    border: 2px solid #bca1d1;
    border-radius: 6px;
    text-align: center;
    font-size: 18px;
    font-weight: 500;
    transition: all 0.3s ease;
    background-color: #fcf5fb;
    color: #5e4089;
    -webkit-appearance: none;
    -moz-appearance: textfield;
}

.page-input::-webkit-outer-spin-button,
.page-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.page-input.filled {
    background-color: #892292;
    color: #fcf5fb;
    border-color: #580f8c;
}

.page-input:focus {
    outline: none !important;
    border-color: #580f8c !important;
    box-shadow: 0 0 0 2px rgba(188, 161, 209, 0.3) !important;
}

.page-input::placeholder {
    color: #c587cb;
    font-size: 16px;
}

.page-extractor-instruction {
    font-size: 13px;
    color: #5e4089;
    text-align: center;
    font-weight: 500;
    margin-top: 5px;
}

.proposler-button {
    background-color: #892292 !important;
    border-color: #580f8c !important;
    color: #fcf5fb !important;
    font-weight: 500 !important;
    height: 45px !important;
    transition: all 0.3s ease !important;
    width:100% !important;
}

.proposler-button:hover:not(:disabled) {
    background-color: #580f8c !important;
    border-color: #892292 !important;
}
.docbutton:hover {
    cursor:pointer;
}
.proposler-button:disabled {
    background-color: #bca1d1 !important;
    border-color: #c587cb !important;
    opacity: 0.7 !important;
}

.proposler-button.running {
    background-color: #036ac4 !important;
    border-color: #036ac4 !important;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(3, 106, 196, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(3, 106, 196, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(3, 106, 196, 0);
    }
}