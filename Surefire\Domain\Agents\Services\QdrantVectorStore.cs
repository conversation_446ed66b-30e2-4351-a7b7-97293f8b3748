using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Surefire.Domain.Agents.Interfaces;
using Surefire.Domain.Agents.Models;

namespace Surefire.Domain.Agents.Services
{
    public class QdrantVectorStore : IVectorStore
    {
        private readonly HttpClient _httpClient;
        private readonly string _collectionName;
        private readonly ILogger<QdrantVectorStore> _logger;
        private bool _collectionChecked = false;

        public QdrantVectorStore(IHttpClientFactory httpClientFactory, IConfiguration configuration, ILogger<QdrantVectorStore> logger)
        {
            _httpClient = httpClientFactory.CreateClient("Qdrant");
            _collectionName = configuration["Qdrant:Collection"] ?? throw new ArgumentException("Qdrant collection not configured.");
            _logger = logger;
        }

        public async Task EnsureCollectionExistsAsync(CancellationToken cancellationToken = default)
        {
            if (_collectionChecked) return;

            try
            {
                // Check if collection exists
                var response = await _httpClient.GetAsync($"collections/{_collectionName}", cancellationToken);
                
                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Collection {Collection} already exists", _collectionName);
                    _collectionChecked = true;
                    return;
                }

                // Collection doesn't exist, create it
                _logger.LogInformation("Creating Qdrant collection {Collection}", _collectionName);
                
                var createRequest = new
                {
                    vectors = new
                    {
                        size = 1536, // OpenAI text-embedding-3-small dimension
                        distance = "Cosine"
                    }
                };

                var json = JsonSerializer.Serialize(createRequest);
                _logger.LogDebug("Collection creation request JSON: {Json}", json);
                using var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var createResponse = await _httpClient.PutAsync($"collections/{_collectionName}", content, cancellationToken);
                
                if (!createResponse.IsSuccessStatusCode)
                {
                    var errorContent = await createResponse.Content.ReadAsStringAsync(cancellationToken);
                    _logger.LogError("Qdrant collection creation failed with status {StatusCode}: {ErrorContent}", createResponse.StatusCode, errorContent);
                    throw new HttpRequestException($"Qdrant collection creation failed with status {createResponse.StatusCode}: {errorContent}");
                }
                
                createResponse.EnsureSuccessStatusCode();
                
                _logger.LogInformation("Successfully created Qdrant collection {Collection}", _collectionName);
                _collectionChecked = true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to ensure collection {Collection} exists", _collectionName);
                throw;
            }
        }

        public async Task UpsertAsync(string id, float[] vector, Dictionary<string, object> metadata, CancellationToken cancellationToken = default)
        {
            await EnsureCollectionExistsAsync(cancellationToken);
            
            // Convert string ID to integer for Qdrant compatibility
            if (!int.TryParse(id, out var numericId))
            {
                throw new ArgumentException($"ID must be a valid integer, got: {id}");
            }
            
            var point = new { id = numericId, vector, payload = metadata };
            var req = new { points = new[] { point } };

            _logger.LogInformation("Upserting vector for {Id} into Qdrant collection {Collection}", id, _collectionName);
            var json = JsonSerializer.Serialize(req);
            _logger.LogDebug("Upsert request JSON: {Json}", json);
            using var contentPayload = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync($"collections/{_collectionName}/points?wait=true", contentPayload, cancellationToken);
            
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("Qdrant upsert failed with status {StatusCode}: {ErrorContent}", response.StatusCode, errorContent);
                throw new HttpRequestException($"Qdrant upsert failed with status {response.StatusCode}: {errorContent}");
            }
            
            response.EnsureSuccessStatusCode();
        }

        public async Task<IList<VectorSearchResult>> SearchAsync(float[] vector, int topK, CancellationToken cancellationToken = default)
        {
            await EnsureCollectionExistsAsync(cancellationToken);
            
            var req = new { vector, limit = topK, with_payload = true };
            _logger.LogInformation("Searching Qdrant for top {TopK} in {Collection}", topK, _collectionName);
            var json = JsonSerializer.Serialize(req);
            using var contentPayload = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync($"collections/{_collectionName}/points/search", contentPayload, cancellationToken);
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadFromJsonAsync<SearchResponse>(cancellationToken: cancellationToken);
            return content?.Result?.Select(r => new VectorSearchResult
            {
                Id = r.Id.ToString(),
                Score = r.Score,
                Metadata = r.Payload ?? new Dictionary<string, object>()
            }).ToList() ?? new List<VectorSearchResult>();
        }

        private class SearchResponse
        {
            [JsonPropertyName("result")]
            public List<PointResult>? Result { get; set; }
        }

        private class PointResult
        {
            [JsonPropertyName("id")] public object Id { get; set; } = string.Empty;
            [JsonPropertyName("score")] public float Score { get; set; }
            [JsonPropertyName("payload")] public Dictionary<string, object>? Payload { get; set; }
        }
    }
}
