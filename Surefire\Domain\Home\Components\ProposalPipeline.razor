@using Surefire.Domain.Proposals
@using Surefire.Domain.Proposals.Models
@using Surefire.Domain.Proposals.Services
@using Surefire.Domain.Shared.Helpers
@using Surefire.Domain.Logs
@using Syncfusion.Blazor.Spinner
@using Syncfusion.Blazor.Notifications
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject StateService _stateService
@inject ProposalService ProposalService
@inject ILoggingService _log
@implements IDisposable

<div class="sectiontitletab">Proposal Pipeline</div>
<div class="leads-box-inner">
    <table id="proposaltable" cellspacing="0" class="ltable">
        <thead class="lbg">
            <tr>
                <th class="mid-a">Client</th>
                <th class="mid-b">Send</th>
                <th class="mid-c">Renews</th>
                <th class="mid-d">Status</th>
            </tr>
        </thead>
        <tbody class="lbody">
            @if (isLoading)
            {
                for (var i = 0; i < 10; i++)
                {
                    <tr class="lrow">
                        <td colspan="5">
                            <SfSkeleton Shape=SkeletonType.Rectangle Width="570px" Height="13px" CssClass="e-customize" Visible="true"></SfSkeleton>
                        </td>
                    </tr>
                }
            }
            else if (hasError)
            {
                <tr>
                    <td colspan="5" class="no-taskssub">
                        <div class="alert alert-warning">
                            <strong>Error loading proposals:</strong> @errorMessage
                            <button type="button" class="btn btn-sm btn-outline-warning ms-2" @onclick="RetryLoad">Retry</button>
                        </div>
                    </td>
                </tr>
            }
            else if (proposals == null || !proposals.Any())
            {
                <tr>
                    <td colspan="5" class="no-taskssub">No proposals to display</td>
                </tr>
            }
            else
            {
                var sentProposals = proposals.Where(p => p.Status == 3).OrderByDescending(p => p.Renewal?.RenewalDate).ToList();
                var otherProposals = proposals.Where(p => p.Status != -1 && p.Status != 3)
                    .OrderBy(p => p.Renewal?.RenewalDate).ToList();
                
                var orderedProposals = otherProposals.Concat(sentProposals).ToList();
                
                @foreach (var proposal in orderedProposals)
                {
                    <tr class="lrow main">
                        <td class="mid-a mid-name ellipsis" @onclick="() => NavigateToRenewal(proposal.Renewal.RenewalId)">
                            @(proposal.Renewal?.Client?.Name ?? "Unknown Client")
                        </td>
                        <td class="mid-b mid-color ellipsis" style="font-size:.7em;">
                            @{
                                MarkupString sendPhrase = new("");
                                if (proposal.Status == 0 || proposal.Status == 1 || proposal.Status == 2 || proposal.Status == 10)
                                {
                                    var daysUntilSend = proposal.SendDate.HasValue ? (proposal.SendDate.Value.Date - DateTime.Now.Date).Days.ToString() : "";
                                    sendPhrase = StringHelper.FormatDuePhrase(daysUntilSend);
                                }
                            }
                            @sendPhrase
                        </td>
                        <td class="mid-c mid-date ellipsis">@FormatDate(proposal.Renewal?.RenewalDate)</td>
                        <td class="mid-d ellipsis">
                            <span class="dot dot-@GetStatusClass(proposal.Status)"></span>
                            <span class="mid-dotname link-@GetStatusClass(proposal.Status)">@GetStatusText(proposal.Status)</span>
                        </td>
                    </tr>
                }
            }
        </tbody>
    </table>
    <div style="height:7px;"></div>
</div>

@if (showDetailsModal && selectedProposal != null)
{
    <div class="modal-backdrop" @onclick="CloseDetailsModal">
        <div class="modal-content" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>Proposal Details</h3>
                <button type="button" class="close-btn" @onclick="CloseDetailsModal">×</button>
            </div>
            <div class="modal-body">
                <table class="details-table">
                    <tr>
                        <td class="detail-label">Client:</td>
                        <td>@(selectedProposal.Renewal?.Client?.Name ?? "Unknown Client")</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Created By:</td>
                        <td>@(selectedProposal.CreatedBy?.UserName ?? "Unknown")</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Created Date:</td>
                        <td>@selectedProposal.DateCreated.ToString("MM/dd/yyyy HH:mm")</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Modified Date:</td>
                        <td>@selectedProposal.DateModified.ToString("MM/dd/yyyy HH:mm")</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Status:</td>
                        <td><span class="status-badge status-@GetStatusClass(selectedProposal.Status)">@GetStatusText(selectedProposal.Status)</span></td>
                    </tr>
                </table>
                <div class="modal-actions">
                    <button type="button" class="action-btn view-request-btn" @onclick="() => NavigateToProposal(selectedProposal.ProposalId)">
                        View Proposal
                    </button>
                    <button type="button" class="action-btn close-modal-btn" @onclick="CloseDetailsModal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<Proposal> proposals = new();
    private bool isLoading = true;
    private bool hasError = false;
    private string errorMessage = string.Empty;
    private int retryCount = 0;
    private const int MaxRetries = 3;

    private bool showDetailsModal = false;
    private Proposal selectedProposal;

    protected override async Task OnInitializedAsync()
    {
        // Subscribe to homepage data updates for cache invalidation
        _stateService.OnHomepageDataUpdated += HandleHomepageDataUpdated;
        
        await LoadProposalData();
    }

    private async Task LoadProposalData()
    {
        try
        {
            isLoading = true;
            hasError = false;
            errorMessage = string.Empty;
            StateHasChanged();

            // Try to get cached data first
            if (_stateService.CachedProposals.Any())
            {
                proposals = _stateService.CachedProposals.ToList();
            }
            else
            {
                // Load directly if cache is empty
                proposals = await ProposalService.GetProposalHomepageListAsync();
            }
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Warning, $"Error loading proposal data: {ex.Message}", "ProposalStatusList");
            hasError = true;
            errorMessage = "Failed to load proposal data. Please try again.";
            proposals = new();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void HandleHomepageDataUpdated()
    {
        InvokeAsync(async () =>
        {
            try
            {
                proposals = _stateService.CachedProposals.ToList();
                hasError = false;
                errorMessage = string.Empty;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                await _log.LogAsync(LogLevel.Warning, $"Error updating proposal data from cache: {ex.Message}", "ProposalStatusList");
            }
        });
    }

    private async Task RetryLoad()
    {
        if (retryCount < MaxRetries)
        {
            retryCount++;
            
            // Force refresh the cache on retry
            await _stateService.ForceRefreshHomepageDataAsync();
            await LoadProposalData();
        }
        else
        {
            errorMessage = "Max retries exceeded. Please refresh the page.";
            StateHasChanged();
        }
    }
    
    private void ShowDetails(Proposal proposal)
    {
        selectedProposal = proposal;
        showDetailsModal = true;
        StateHasChanged();
    }
    
    private void CloseDetailsModal()
    {
        showDetailsModal = false;
        StateHasChanged();
    }
    
    private void NavigateToProposal(int proposalId)
    {
        NavigationManager.NavigateTo($"/Proposals/Details/{proposalId}");
    }

    private void NavigateToRenewal(int renewalId)
    {
        // Set the state service to remember which renewal was selected
        _stateService.HtmlRenewalId = renewalId;
        _stateService.HtmlView = "details";
        _stateService.HtmlTab = "tab-5";
        
        NavigationManager.NavigateTo($"/Renewals/Details/{renewalId}");
    }
    private string FormatDate(DateTime? date)
    {
        if (date == null || date == DateTime.MinValue)
            return "N/A";

        return date.Value.ToString("MM/dd/yyyy");
    }
    private int GetStatusClass(int status)
    {
        return status switch
        {
            0 => 0,
            1 => 1,
            2 => 2,
            3 => 3,
            4 => 4,
            5 => 5,
            6 => 6,
            10 => 10,
            _ => 0
        };
    }

    private string GetStatusText(int status)
    {
        return status switch
        {
            0 => "new",
            1 => "ok",
            2 => "need",
            3 => "sent",
            4 => "signed",
            5 => "eject",
            6 => "done",
            10 => "mail",
            _ => "unknown"
        };
    }

    public void Dispose()
    {
        _stateService.OnHomepageDataUpdated -= HandleHomepageDataUpdated;
    }
} 