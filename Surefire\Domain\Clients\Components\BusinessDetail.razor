﻿@using Surefire.Domain.OpenAI
@using Surefire.Domain.Clients.Models
@using Surefire.Domain.Clients.Services
@using Syncfusion.Blazor.InPlaceEditor
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.SmartComponents
@using System.Globalization;
@using System.Text.Json;
@using Newtonsoft.Json
@using OpenAI
@inject IJSRuntime JS
@inject ClientService ClientService
@inject OpenAiService OpenAiService
@inject StateService StateService

@if (BusinessDetails != null && !massEditMode)
{
    <EditForm EditContext="@myeditcontext">
        <FluentStack Wrap="true" HorizontalGap="30">
            <div xs="3" Class="pol-section">
                <!--=====----- BASICS -----=====-->
                <span class="pol-section-title" style="border-left: 5px solid #046BC4;">Basics</span>
                <div class="pol-section-container">
                    <!-- Date Started -->
                    <span class="pol-name">Business Started<span class="copy-spcr" /></span>
                    <span class="pol-value">
                        <SfDatePicker TValue="DateTime?" @bind-Value="BusinessDetails.DateStarted" Placeholder="Date Started" FloatLabelType="FloatLabelType.Never" OnChange="@(args => UpdateBusinessDetails())" />
                    </span><br />

                    <!-- Years Experience -->
                    <span class="pol-name">
                        Years of Experience
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.YearsExperience.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.YearsExperience" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="int?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="n0" @bind-Value="@BusinessDetails.YearsExperience"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="int?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <span class="pol-name">
                        FEIN
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.FEIN.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.FEIN" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Text" TValue="string" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfTextBox @bind-Value="@BusinessDetails.FEIN"></SfTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="string"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <!-- Lapse History -->
                    <span class="pol-name">
                        Coverage History
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.InsuranceHistory.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.InsuranceHistory" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Text" TValue="string" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfTextBox @bind-Value="@BusinessDetails.InsuranceHistory"></SfTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="string"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <!-- Number of Claims -->
                    <span class="pol-name">
                        Number of Claims
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.NumClaims.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.NumClaims" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="int?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="n0" @bind-Value="@BusinessDetails.NumClaims"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="int?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />
                </div>

                <!--=====----- CLASSIFICATIONS -----=====-->
                <div class="pol-column-spacer"></div>
                <span class="pol-section-title" style="border-left: 5px solid #1B8CE3;">Classifications</span><br />
                <!-- Legal Entity Type -->
                <div class="pol-section-container">
                    <span class="pol-name">Legal Entity Type<span class="copy-spcr" /></span>
                    <span class="pol-value">
                        <SfDropDownList TValue="LegalEntityType?" TItem="EnumData<LegalEntityType>" @bind-Value="@BusinessDetails.LegalEntityType" DataSource="@LegalEntityTypeList"
                                        FloatLabelType="FloatLabelType.Never" AllowFiltering="true" ShowClearButton="true">
                            <DropDownListEvents TValue="LegalEntityType?" TItem="EnumData<LegalEntityType>" ValueChange="@UpdateBusinessDetails" />
                            <DropDownListFieldSettings Value="Value" Text="Text"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </span><br />

                    <!-- Business Type -->
                    <span class="pol-name">Business Type<span class="copy-spcr" /></span>
                    <span class="pol-value">
                        <SfDropDownList TValue="BusinessType?" TItem="EnumData<BusinessType>" @bind-Value="@BusinessDetails.BusinessType" DataSource="@BusinessTypeList"
                                        FloatLabelType="FloatLabelType.Never" AllowFiltering="true" ShowClearButton="true">
                            <DropDownListEvents TValue="BusinessType?" TItem="EnumData<BusinessType>" ValueChange="@UpdateBusinessDetails" />
                            <DropDownListFieldSettings Value="Value" Text="Text"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </span><br />

                    <!-- Short Description -->
                    <span class="pol-name">
                        Short Description
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.ShortDescription.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.ShortDescription" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Text" TValue="string" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfTextBox @bind-Value="@BusinessDetails.ShortDescription"></SfTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="string"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <!-- Long Description -->
                    <span class="pol-name">
                        Long Description
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.LongDescription.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.LongDescription" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Text" TValue="string" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfTextBox @bind-Value="@BusinessDetails.LongDescription" Multiline="true"></SfTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="string"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <!-- Business Industry -->
                    <span class="pol-name">
                        SIC / Industry
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.BusinessIndustry.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.BusinessIndustry" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Text" TValue="string" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfTextBox @bind-Value="@BusinessDetails.BusinessIndustry"></SfTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="string"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <!-- Business Specialty -->
                    <span class="pol-name">
                        NCAIS / Specialty
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.BusinessSpecialty.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.BusinessSpecialty" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Text" TValue="string" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfTextBox @bind-Value="@BusinessDetails.BusinessSpecialty"></SfTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="string"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span>
                </div>

                <!--=====----- CONTRACTORS -----=====-->
                <span class="pol-section-title" style="border-left: 5px solid #1F1E61;">Contractors</span>
                <div class="pol-section-container">
                    <!-- License Type -->
                    <span class="pol-name">License Type<span class="copy-spcr" /></span>
                    <span class="pol-value">
                        <SfDropDownList TValue="LicenseType?" TItem="EnumData<LicenseType>" @bind-Value="@BusinessDetails.LicenseType" DataSource="@LicenseTypeList"
                                        Placeholder="Select License Type" FloatLabelType="FloatLabelType.Never" AllowFiltering="true" ShowClearButton="true">
                            <DropDownListEvents TValue="LicenseType?" TItem="EnumData<LicenseType>" ValueChange="@UpdateBusinessDetails" />
                            <DropDownListFieldSettings Value="Value" Text="Text"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </span><br />

                    <!-- License Number -->
                    <span class="pol-name">
                        License Number
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.LicenseNumber))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.LicenseNumber" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Text" TValue="string" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfTextBox @bind-Value="@BusinessDetails.LicenseNumber"></SfTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="string"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <!-- Estimated Subcontracting Expenses -->
                    <span class="pol-name">
                        Sub Costs
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedSubcontractingExpenses.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.EstimatedSubcontractingExpenses" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="decimal?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="c0" @bind-Value="@BusinessDetails.EstimatedSubcontractingExpenses"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="decimal?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />
                </div>
            </div>

            <div xs="3">
                <!--=====----- EMPLOYEES -----=====-->
                <span class="pol-section-title" style="border-left: 5px solid #593294;">Employees</span>
                <div class="pol-section-container">
                    <!-- Number of Part-Time Employees -->
                    <span class="pol-name">
                        Part-Time
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.NumPartTimeEmployees.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.NumPartTimeEmployees" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="int?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="n0" @bind-Value="@BusinessDetails.NumPartTimeEmployees"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="int?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <!-- Number of Full-Time Employees -->
                    <span class="pol-name">
                        Full-Time
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.NumFullTimeEmployees.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.NumFullTimeEmployees" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="int?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="n0" @bind-Value="@BusinessDetails.NumFullTimeEmployees"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="int?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />
                </div>


                <!--=====----- PAYROLL -----=====-->
                <span class="pol-section-title" style="border-left: 5px solid #745DA1;">Payroll</span><br />
                <div class="pol-section-container">
                    <span class="pol-name">
                        Next Year
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedAnnualPayroll0.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.EstimatedAnnualPayroll0" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="decimal?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedAnnualPayroll0"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="decimal?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <span class="pol-name">
                        Current
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedAnnualPayroll1.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.EstimatedAnnualPayroll1" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="decimal?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedAnnualPayroll1"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="decimal?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <span class="pol-name">
                        Previous Year
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedAnnualPayroll2.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.EstimatedAnnualPayroll2" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="decimal?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedAnnualPayroll2"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="decimal?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <span class="pol-name">
                        2 Years Ago
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedAnnualPayroll3.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.EstimatedAnnualPayroll3" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="decimal?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedAnnualPayroll3"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="decimal?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <span class="pol-name">
                        3 Years Ago
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedAnnualPayroll4.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.EstimatedAnnualPayroll4" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="decimal?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedAnnualPayroll4"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="decimal?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />
                </div>

                <!--=====----- GROSS SALES -----=====-->
                <span class="pol-section-title" style="border-left: 5px solid #FF6600;">Gross Sales</span><br />
                <div class="pol-section-container">
                    <span class="pol-name">
                        Next Year
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedGrossSales0.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.EstimatedGrossSales0" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="decimal?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedGrossSales0"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="decimal?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <span class="pol-name">
                        Current Year
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedGrossSales1.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.EstimatedGrossSales1" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="decimal?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedGrossSales1"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="decimal?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <span class="pol-name">
                        Last Year
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedGrossSales2.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.EstimatedGrossSales2" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="decimal?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedGrossSales2"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="decimal?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <span class="pol-name">
                        2 Years Ago
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedGrossSales3.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.EstimatedGrossSales3" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="decimal?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedGrossSales3"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="decimal?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <span class="pol-name">
                        3 Years Ago
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedGrossSales4.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.EstimatedGrossSales4" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="decimal?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedGrossSales4"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="decimal?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />
                </div>
            </div>



            <div xs="3">
                <!--=====----- PRIMARY BUILDING -----=====-->
                <span class="pol-section-title" style="border-left: 5px solid #CF4740;">Primary Building</span>
                <div class="pol-section-container">
                    <!-- Year Built -->
                    <span class="pol-name">
                        Year Built
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.BuildingLocationYearBuilt.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.BuildingLocationYearBuilt" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="int?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="n0" @bind-Value="@BusinessDetails.BuildingLocationYearBuilt"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="int?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <!-- Square Footage -->
                    <span class="pol-name">
                        Square Footage
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.BuildingLocationSquareFootage.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.BuildingLocationSquareFootage" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="int?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="n0" @bind-Value="@BusinessDetails.BuildingLocationSquareFootage"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="int?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <!-- Number of Stories -->
                    <span class="pol-name">
                        Number of Stories
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.BuildingLocationNumberOfStories.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.BuildingLocationNumberOfStories" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="int?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="n0" @bind-Value="@BusinessDetails.BuildingLocationNumberOfStories"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="int?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <!-- Sprinklered -->
                    <span class="pol-name">Sprinklered<span class="copy-spcr" /></span>
                    <span class="pol-value">
                        <SfCheckBox @bind-Checked="@BusinessDetails.BuildingLocationSprinklered" @onchange="@UpdateBusinessDetails"></SfCheckBox>
                    </span><br />

                    <!-- Monitored Security -->
                    <span class="pol-name">Monitored Security<span class="copy-spcr" /></span>
                    <span class="pol-value">
                        <SfCheckBox @bind-Checked="@BusinessDetails.BuildingLocationMonitoredSecurity" @onchange="@UpdateBusinessDetails"></SfCheckBox>
                    </span><br />
                </div>

                <!--=====----- FINANCIALS -----=====-->
                <span class="pol-section-title" style="border-left: 5px solid #CF4740;">FINANCIALS</span>
                <div class="pol-section-container">
                    <!-- Year Built -->
                    <span class="pol-name">
                        Annual Gross Sales
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.AnnualGrossSalesRevenueReceipts.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.AnnualGrossSalesRevenueReceipts" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="decimal?"
                                         EditableOn="EditableType.EditIconClick" Mode="Syncfusion.Blazor.InPlaceEditor.RenderMode.Popup" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.AnnualGrossSalesRevenueReceipts"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="decimal?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <!-- Square Footage -->
                    <span class="pol-name">
                        Annual Payroll
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.AnnualPayrollHazardExposure.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.AnnualPayrollHazardExposure" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="decimal?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.AnnualPayrollHazardExposure"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="decimal?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />

                    <!-- Business Property -->
                    <span class="pol-name">
                        Business Property
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.BusinessPersonalPropertyBPP.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfInPlaceEditor @bind-Value="@BusinessDetails.BusinessPersonalPropertyBPP" Type="Syncfusion.Blazor.InPlaceEditor.InputType.Numeric" TValue="decimal?" EditableOn="EditableType.EditIconClick" ShowButtons="false" EmptyText="">
                            <EditorComponent>
                                <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.BusinessPersonalPropertyBPP"></SfNumericTextBox>
                            </EditorComponent>
                            <InPlaceEditorEvents OnActionBegin="@(args => UpdateBusinessDetails())" TValue="decimal?"></InPlaceEditorEvents>
                        </SfInPlaceEditor>
                    </span><br />
                </div>
            </div>
            <div style="width: 500px;">
                <div class="txt-section">POLICY DATA EXTRACTOR</div>
                <FluentTextField Placeholder="Paste XML here" @bind-Value="xmlContent" Multiline="true" Rows="5" /><br />
                <FluentTextField Placeholder="Paste JSON here" @bind-Value="jsonContent" Multiline="true" Rows="5" /><br />
                @*  <FluentButton OnClick="testSet">TestSet</FluentButton>
            <FluentButton OnClick="toggleMassEditMode">ToggleMode</FluentButton> *@
                <FluentButton OnClick="@(args => MapSpecificJsonProperties(myRoot.RootElement, BusinessDetails))">RunJson</FluentButton>
                <FluentButton OnClick="@ProcessJsonContent">RunLacy</FluentButton>
                &nbsp;&nbsp;
                <FluentButton OnClick="ParseStrict">Strict</FluentButton>
                <FluentButton OnClick="ParseLoose">Loose</FluentButton>
                <FluentButton OnClick="ParseSuper">Super</FluentButton>
                <FluentButton OnClick="ParseGenius">Genius</FluentButton>
                &nbsp;&nbsp;
                <FluentButton OnClick="@(args => UpdateBusinessDetails())">SAVE</FluentButton>

                @if (!string.IsNullOrEmpty(jsonResponse))
                {
                    <div style="max-height: 475px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; color:#959595;">
                        @((MarkupString)jsonResponse)
                    </div>
                }
                <SfSmartPasteButton></SfSmartPasteButton>
            </div>
        </FluentStack>

    </EditForm>
}
<style>
    .key {
        color: #cf4740;
    }

    .string {
        color: #593294;
    }

    .number {
        color: #b5cea8;
    }

    .brace {
        color: #bdbdbd;
    }

    .colon {
        color: #1b8ce3;
    }

    .boolean {
        color: #1b8ce3;
    }

    .null {
        color: #bdbdbd;
    }

    .bracket {
        color: #bdbdbd;
    }

    .comma {
        color: #bdbdbd;
    }
</style>

@code {
    [Parameter]
    public int ClientId { get; set; }

    private BusinessDetails BusinessDetails { get; set; }

    // Data sources for enums and dropdowns
    private IEnumerable<EnumData<LegalEntityType>> LegalEntityTypeList { get; set; }
    private IEnumerable<EnumData<BusinessType>> BusinessTypeList { get; set; }
    private IEnumerable<EnumData<LicenseType>> LicenseTypeList { get; set; }
    private List<string> InsuranceHistoryOptions { get; set; } = new() { "No prior", "1-59 days lapse", "60+ days lapse" };
    private List<string> LapseHistoryOptions { get; set; } = new() { "No prior", "1-59 days lapse", "60+ days lapse" };
    private string xmlContent = string.Empty;
    private string jsonContent = string.Empty;
    private string jsonResponse = string.Empty;
    private bool massEditMode = false;
    private SfInPlaceEditor<decimal?> myInplaceBpp;
    private SfNumericTextBox<decimal?> TfieldBpp;
    public JsonDocument myRoot { get; set; }
    private EditContext? myeditcontext;

    protected override async Task OnParametersSetAsync()
    {
        await LoadBusinessDetails();
        LoadEnumData();
        myeditcontext = new EditContext(BusinessDetails);
    }
    private async Task CopyItem(string? str)
    {
        await JS.InvokeVoidAsync("navigator.clipboard.writeText", str);
    }
    private async Task LoadBusinessDetails()
    {
        BusinessDetails = await ClientService.GetBusinessDetailsByClientId(ClientId);

        if (BusinessDetails == null)
        {
            BusinessDetails = new BusinessDetails
                {
                    ClientId = ClientId
                };

            await ClientService.AddBusinessDetailsAsync(BusinessDetails);

            BusinessDetails = await ClientService.GetBusinessDetailsByClientId(ClientId);
        }
    }


    public async Task UpdateBusinessDetails()
    {
        if (BusinessDetails != null)
        {
            await ClientService.UpdateBusinessDetailsAsync(BusinessDetails);
        }
    }

    private async Task toggleMassEditMode()
    {
        massEditMode = !massEditMode;
        await InvokeAsync(StateHasChanged);
    }

    private async Task ParseStrict()
    {
        StateService.UpdateStatus("Parsing your policy data, please wait...", true);
        await InvokeAsync(StateHasChanged);
        var resp = await OpenAiService.ParseXmlToJsonAsync(xmlContent, 1);
        jsonResponse = "<pre>" + resp + "</pre>";
        StateService.UpdateStatus("Done...", false);
        await InvokeAsync(StateHasChanged);
    }

    private async Task ParseLoose()
    {
        StateService.UpdateStatus("Parsing your policy data, please wait...", true);
        await InvokeAsync(StateHasChanged);
        var resp = await OpenAiService.ParseXmlToJsonAsync(xmlContent, 2);
        jsonResponse = "<pre>" + resp + "</pre>";
        StateService.UpdateStatus("Done...", false);
        await InvokeAsync(StateHasChanged);
    }

    private async Task ParseSuper()
    {
        StateService.UpdateStatus("Parsing your policy data, please wait...", true);
        await InvokeAsync(StateHasChanged);
        var resp = await OpenAiService.ParseXmlToJsonAsync(xmlContent, 3);
        jsonResponse = "<pre>" + resp + "</pre>";
        StateService.UpdateStatus("Done...", false);
        await InvokeAsync(StateHasChanged);
    }

    private async Task ParseGenius()
    {
        StateService.UpdateStatus("Parsing your policy data with function calling, please wait...", true);
        await InvokeAsync(StateHasChanged);

        // Fetch and parse JSON from the OpenAI service
        var jresp = await OpenAiService.ParseXmlToBusinessDetailsWithFunction(xmlContent, ClientId);
        var jsonDoc = JsonDocument.Parse(jresp);
        myRoot = jsonDoc;
        string prettyJson = System.Text.Json.JsonSerializer.Serialize(jsonDoc, new JsonSerializerOptions { WriteIndented = true });
        jsonResponse = StringHelper.ColorizeJSON(prettyJson);

        // Flatten JSON document to match model properties more flexibly
        MapSpecificJsonProperties(jsonDoc.RootElement, BusinessDetails);

        massEditMode = true;
        await InvokeAsync(StateHasChanged);
        await Task.Delay(500);
        massEditMode = false;
        await InvokeAsync(StateHasChanged);

        StateService.UpdateStatus("Done...", false);
        await InvokeAsync(StateHasChanged);
    }
    private void MapSpecificJsonProperties(JsonElement jsonElement, BusinessDetails businessDetails)
    {
        if (jsonElement.TryGetProperty("BusinessDetails", out var businessDetailsElement))
        {
            // Map individual fields with specific conversions
            int ivar = 0;
            ivar++;
            businessDetails.FEIN = GetStringProperty(businessDetailsElement, "FEIN");
            ivar++;
            businessDetails.ShortDescription = GetStringProperty(businessDetailsElement, "ShortDescription");
            ivar++;
            businessDetails.LongDescription = GetStringProperty(businessDetailsElement, "LongDescription");

            ivar++;
            businessDetails.BusinessIndustry = GetStringProperty(businessDetailsElement, "BusinessIndustry");

            ivar++;
            businessDetails.BusinessSpecialty = GetStringProperty(businessDetailsElement, "BusinessSpecialty");
            ivar++;
            businessDetails.AnnualGrossSalesRevenueReceipts = GetDecimalProperty(businessDetailsElement, "AnnualGrossSalesRevenueReceipts");
            ivar++;
            businessDetails.BusinessPersonalPropertyBPP = GetDecimalProperty(businessDetailsElement, "BusinessPersonalPropertyBPP");
            ivar++;
            businessDetails.AnnualPayrollHazardExposure = GetDecimalProperty(businessDetailsElement, "AnnualPayrollHazardExposure");

            ivar++;
            businessDetails.DateStarted = GetDateTimeProperty(businessDetailsElement, "DateStarted");
            ivar++;
            businessDetails.YearsExperience = GetIntProperty(businessDetailsElement, "YearsExperience");

            ivar++;
            businessDetails.LicenseNumber = GetStringProperty(businessDetailsElement, "LicenseNumber");

            if (businessDetailsElement.TryGetProperty("BuildingLocation", out var buildingLocationElement))
            {
                ivar++;
                businessDetails.BuildingLocationYearBuilt = GetIntProperty(buildingLocationElement, "YearBuilt");
                ivar++;
                businessDetails.BuildingLocationSquareFootage = GetIntProperty(buildingLocationElement, "SquareFootage");
                ivar++;
                businessDetails.BuildingLocationNumberOfStories = GetIntProperty(buildingLocationElement, "NumberOfStories");
            }

            ivar++;
            businessDetails.NumPartTimeEmployees = GetIntProperty(businessDetailsElement, "NumPartTimeEmployees");
            ivar++;
            businessDetails.NumFullTimeEmployees = GetIntProperty(businessDetailsElement, "NumFullTimeEmployees");
        }
    }

    // ------------------------------------------
    // -------------  Main Methods  -------------
    // ------------------------------------------
    private void MapLacyJsonProperties(JsonElement jsonRootElement, BusinessDetails businessDetails)
    {
        if (businessDetails == null)
        {
            Console.WriteLine("Error: BusinessDetails object is null. Cannot map data.");
            return;
        }

        Console.WriteLine("Mapping properties based on new schema...");

        // --- Direct Top-Level Properties ---
        businessDetails.FEIN = GetStringProperty(jsonRootElement, "FEIN");
        businessDetails.LegalEntityType = GetEnumProperty<LegalEntityType>(jsonRootElement, "LegalEntityType");
        businessDetails.ShortDescription = GetStringProperty(jsonRootElement, "ShortDescription");
        businessDetails.LongDescription = GetStringProperty(jsonRootElement, "LongDescription");
        businessDetails.BusinessIndustry = GetStringProperty(jsonRootElement, "BusinessIndustry");
        businessDetails.BusinessSpecialty = GetStringProperty(jsonRootElement, "BusinessSpecialty");
        businessDetails.BusinessType = GetEnumProperty<BusinessType>(jsonRootElement, "BusinessType");
        businessDetails.AnnualGrossSalesRevenueReceipts = GetDecimalProperty(jsonRootElement, "AnnualGrossSalesRevenueReceipts");
        businessDetails.BusinessPersonalPropertyBPP = GetDecimalProperty(jsonRootElement, "BusinessPersonalPropertyBPP");
        businessDetails.AnnualPayrollHazardExposure = GetDecimalProperty(jsonRootElement, "AnnualPayrollHazardExposure");
        businessDetails.DateStarted = GetDateTimeProperty(jsonRootElement, "DateStarted"); // Assumes helper handles 'date' format
        businessDetails.YearsExperience = GetIntProperty(jsonRootElement, "YearsExperience");
        businessDetails.InsuranceHistory = GetStringProperty(jsonRootElement, "InsuranceHistory");
        businessDetails.LapseHistory = GetStringProperty(jsonRootElement, "LapseHistory");
        businessDetails.NumClaims = GetIntProperty(jsonRootElement, "NumClaims");
        businessDetails.LicenseType = GetEnumProperty<LicenseType>(jsonRootElement, "LicenseType");
        businessDetails.LicenseNumber = GetStringProperty(jsonRootElement, "LicenseNumber");
        businessDetails.EstimatedSubcontractingExpenses = GetDecimalProperty(jsonRootElement, "EstimatedSubcontractingExpenses");

        // --- Map Gross Sales Array ---
        // Maps [Next Year Est, Year -1, Year -2, Year -3, Year -4] to fields 0, 1, 2, 3, 4
        businessDetails.EstimatedGrossSales0 = GetDecimalArrayProperty(jsonRootElement, "EstimatedGrossSalesNextYearAndLastFourYears", 0);
        businessDetails.EstimatedGrossSales1 = GetDecimalArrayProperty(jsonRootElement, "EstimatedGrossSalesNextYearAndLastFourYears", 1);
        businessDetails.EstimatedGrossSales2 = GetDecimalArrayProperty(jsonRootElement, "EstimatedGrossSalesNextYearAndLastFourYears", 2);
        businessDetails.EstimatedGrossSales3 = GetDecimalArrayProperty(jsonRootElement, "EstimatedGrossSalesNextYearAndLastFourYears", 3);
        businessDetails.EstimatedGrossSales4 = GetDecimalArrayProperty(jsonRootElement, "EstimatedGrossSalesNextYearAndLastFourYears", 4);

        // --- Nested Building Properties ---
        if (jsonRootElement.TryGetProperty("Building", out var buildingElement) && buildingElement.ValueKind == JsonValueKind.Object)
        {
            Console.WriteLine("Mapping Building properties...");
            businessDetails.BuildingLocationYearBuilt = GetIntProperty(buildingElement, "YearBuilt");
            businessDetails.BuildingLocationSquareFootage = GetIntProperty(buildingElement, "SquareFootage");
            businessDetails.BuildingLocationNumberOfStories = GetIntProperty(buildingElement, "NumberOfStories");
            businessDetails.BuildingLocationSprinklered = GetBooleanProperty(buildingElement, "Sprinklered");
            businessDetails.BuildingLocationMonitoredSecurity = GetBooleanProperty(buildingElement, "MonitoredSecurity");
        }
        else
        {
            Console.WriteLine("Building property not found or not an object in JSON root.");
            // Optionally clear existing building details if not found
            // businessDetails.BuildingLocationYearBuilt = null;
            // ... etc ...
        }

        // --- Nested Employees Properties ---
        if (jsonRootElement.TryGetProperty("Employees", out var employeesElement) && employeesElement.ValueKind == JsonValueKind.Object)
        {
            Console.WriteLine("Mapping Employees properties...");
            businessDetails.NumPartTimeEmployees = GetIntProperty(employeesElement, "NumberOfPartTimeEmployees");
            businessDetails.NumFullTimeEmployees = GetIntProperty(employeesElement, "NumberOfFullTimeEmployees");

            // --- Map Payroll Array ---
            // Maps [Next Year Est, Year -1, Year -2, Year -3, Year -4] to fields 0, 1, 2, 3, 4
            businessDetails.EstimatedAnnualPayroll0 = GetDecimalArrayProperty(employeesElement, "EstimatedAnnualPayrollNextYearAndLastFourYears", 0);
            businessDetails.EstimatedAnnualPayroll1 = GetDecimalArrayProperty(employeesElement, "EstimatedAnnualPayrollNextYearAndLastFourYears", 1);
            businessDetails.EstimatedAnnualPayroll2 = GetDecimalArrayProperty(employeesElement, "EstimatedAnnualPayrollNextYearAndLastFourYears", 2);
            businessDetails.EstimatedAnnualPayroll3 = GetDecimalArrayProperty(employeesElement, "EstimatedAnnualPayrollNextYearAndLastFourYears", 3);
            businessDetails.EstimatedAnnualPayroll4 = GetDecimalArrayProperty(employeesElement, "EstimatedAnnualPayrollNextYearAndLastFourYears", 4);
        }
        else
        {
            Console.WriteLine("Employees property not found or not an object in JSON root.");
            // Optionally clear existing employee/payroll details if not found
            // businessDetails.NumPartTimeEmployees = null;
            // ... etc ...
        }

        // --- Properties NOT mapped to BusinessDetails (from new schema) ---
        // ClientId, Name, PhoneNumber, Email, Website, Address, Contacts, Policies, Claims, Losses, Attachments
        // These would require changes to the BusinessDetails model or separate handling.

        Console.WriteLine("Finished mapping properties from new schema.");
    }
    private async Task ProcessJsonContent()
    {
        if (string.IsNullOrWhiteSpace(jsonContent))
        {
            jsonResponse = "<pre style='color: red;'>JSON content is empty. Please paste JSON data first.</pre>";
            await InvokeAsync(StateHasChanged);
            return;
        }

        if (BusinessDetails == null)
        {
            jsonResponse = "<pre style='color: red;'>Error: BusinessDetails object is not loaded. Cannot map data.</pre>";
            await InvokeAsync(StateHasChanged);
            return;
        }

        StateService.UpdateStatus("Processing pasted JSON data...", true);
        await InvokeAsync(StateHasChanged);

        JsonDocument jsonDoc = null;
        try
        {
            // 1. Parse the JSON text from the jsonContent field
            jsonDoc = JsonDocument.Parse(jsonContent);

            // Optional: Display the formatted JSON in the response area for confirmation
            string prettyJson = System.Text.Json.JsonSerializer.Serialize(jsonDoc.RootElement, new JsonSerializerOptions { WriteIndented = true });
            jsonResponse = StringHelper.ColorizeJSON(prettyJson); // Use your existing helper

            // 2. Pass the parsed JSON root element to the mapping method
            MapLacyJsonProperties(jsonDoc.RootElement, BusinessDetails);

            // 3. Trigger UI update to show mapped values
            massEditMode = true;
            await InvokeAsync(StateHasChanged);
            await Task.Delay(100); // Brief delay seems sufficient
            massEditMode = false;
            // StateHasChanged called in finally block

            StateService.UpdateStatus("JSON processing complete.", false);

        }
        catch (System.Text.Json.JsonException jsonEx)
        {
            // Handle JSON parsing errors specifically
            jsonResponse = $"<pre style='color: red;'>Error parsing JSON: {jsonEx.Message}<br />" +
                           $"Path: {jsonEx.Path}, Line: {jsonEx.LineNumber}, Position: {jsonEx.BytePositionInLine}<br /><br />" +
                           $"Content Snippet:<br />{GetJsonSnippetOnError(jsonContent, jsonEx.LineNumber)}</pre>";
            StateService.UpdateStatus("Error parsing JSON.", false);
            Console.WriteLine($"JSON Parsing Error: {jsonEx}");
        }
        catch (Exception ex)
        {
            // Handle potential errors during mapping or other issues
            jsonResponse = $"<pre style='color: red;'>An unexpected error occurred: {ex.Message}<br />{ex.StackTrace}</pre>";
            StateService.UpdateStatus("An error occurred during processing.", false);
            Console.WriteLine($"Error processing JSON content: {ex}");
        }
        finally
        {
            // Ensure the status is updated and UI refreshed regardless of success/failure
            await InvokeAsync(StateHasChanged);
            // Dispose the JsonDocument if it was created
            jsonDoc?.Dispose();
        }
    }
    // ------------------------------------------
    // ------------- Helper Methods -------------
    // ------------------------------------------
    private decimal? GetDecimalProperty(JsonElement element, string propertyName)
    {
        if (element.TryGetProperty(propertyName, out var propertyElement) && propertyElement.ValueKind != JsonValueKind.Null)
        {
            // Direct number handling
            if (propertyElement.ValueKind == JsonValueKind.Number && propertyElement.TryGetDecimal(out var directDecimalValue))
            {
                return directDecimalValue;
            }
            // String handling (existing logic)
            if (propertyElement.ValueKind == JsonValueKind.String)
            {
                string value = propertyElement.GetString();
                if (value != null && decimal.TryParse(value.Replace("$", "").Replace(",", ""), NumberStyles.Any, CultureInfo.InvariantCulture, out var decimalValue))
                {
                    return decimalValue;
                }
                else
                {
                    Console.WriteLine($"Could not parse decimal from string for '{propertyName}'. Raw value: '{value}'");
                }
            }
            // Array handling (if schema allows string/number in array for single value)
            if (propertyElement.ValueKind == JsonValueKind.Array && propertyElement.GetArrayLength() > 0)
            {
                var firstElement = propertyElement[0];
                if (firstElement.ValueKind == JsonValueKind.Number && firstElement.TryGetDecimal(out var arrayDecimalValue))
                {
                    return arrayDecimalValue;
                }
                if (firstElement.ValueKind == JsonValueKind.String)
                {
                    string value = firstElement.GetString();
                    if (value != null && decimal.TryParse(value.Replace("$", "").Replace(",", ""), NumberStyles.Any, CultureInfo.InvariantCulture, out var decimalValue))
                    {
                        return decimalValue;
                    }
                }
            }
        }
        // Console.WriteLine($"Property '{propertyName}' not found, null, or could not be parsed to decimal."); // Less verbose logging
        return null;
    }
    private decimal? GetDecimalArrayProperty(JsonElement element, string propertyName, int index)
    {
        if (element.TryGetProperty(propertyName, out var propertyElement) && propertyElement.ValueKind == JsonValueKind.Array && propertyElement.GetArrayLength() > index)
        {
            var arrayElement = propertyElement[index];
            if (arrayElement.ValueKind != JsonValueKind.Null)
            {
                // Direct number handling
                if (arrayElement.ValueKind == JsonValueKind.Number && arrayElement.TryGetDecimal(out var directDecimalValue))
                {
                    return directDecimalValue;
                }
                // String handling
                if (arrayElement.ValueKind == JsonValueKind.String)
                {
                    string value = arrayElement.GetString();
                    if (value != null && decimal.TryParse(value.Replace("$", "").Replace(",", ""), NumberStyles.Any, CultureInfo.InvariantCulture, out var decimalValue))
                    {
                        return decimalValue;
                    }
                    else
                    {
                        Console.WriteLine($"Could not parse decimal from array string at index {index} for '{propertyName}'. Raw value: '{value}'");
                    }
                }
            }
        }
        // Console.WriteLine($"Property '{propertyName}' array element at index {index} not found, null, or could not be parsed."); // Less verbose
        return null;
    }
    private int? GetIntProperty(JsonElement element, string propertyName)
    {
        if (element.TryGetProperty(propertyName, out var propertyElement) && propertyElement.ValueKind != JsonValueKind.Null)
        {
            // Direct number handling
            if (propertyElement.ValueKind == JsonValueKind.Number && propertyElement.TryGetInt32(out var directIntValue))
            {
                return directIntValue;
            }
            // String handling (existing logic)
            if (propertyElement.ValueKind == JsonValueKind.String)
            {
                var stringValue = propertyElement.GetString();
                // Clean potential formatting if necessary (e.g., commas) - safer to just check IsDigit
                stringValue = new string(stringValue.Where(char.IsDigit).ToArray());
                if (int.TryParse(stringValue, out var intValue))
                {
                    return intValue;
                }
                else
                {
                    Console.WriteLine($"Could not parse int from string for '{propertyName}'. Raw value: '{propertyElement.GetString()}'");
                }
            }
            // Array handling (if schema allows string/number in array for single value)
            if (propertyElement.ValueKind == JsonValueKind.Array && propertyElement.GetArrayLength() > 0)
            {
                var firstElement = propertyElement[0];
                if (firstElement.ValueKind == JsonValueKind.Number && firstElement.TryGetInt32(out var arrayIntValue))
                {
                    return arrayIntValue;
                }
                if (firstElement.ValueKind == JsonValueKind.String)
                {
                    var stringValue = firstElement.GetString();
                    stringValue = new string(stringValue.Where(char.IsDigit).ToArray());
                    if (int.TryParse(stringValue, out var intValue))
                    {
                        return intValue;
                    }
                }
            }
        }
        // Console.WriteLine($"Property '{propertyName}' not found or could not be parsed to int."); // Less verbose
        return null;
    }
    private bool? GetBooleanProperty(JsonElement element, string propertyName)
    {
        if (element.TryGetProperty(propertyName, out var propertyElement))
        {
            if (propertyElement.ValueKind == JsonValueKind.True) return true;
            if (propertyElement.ValueKind == JsonValueKind.False) return false;
            if (propertyElement.ValueKind == JsonValueKind.String)
            {
                string val = propertyElement.GetString()?.ToLowerInvariant();
                if (val == "true" || val == "yes") return true;
                if (val == "false" || val == "no") return false;
            }
        }
        // Console.WriteLine($"Property '{propertyName}' not found or could not be parsed to bool."); // Less verbose
        return null; // Or return false as default if appropriate
    }
    private TEnum? GetEnumProperty<TEnum>(JsonElement element, string propertyName) where TEnum : struct, Enum
    {
        string stringValue = GetStringProperty(element, propertyName);
        if (string.IsNullOrEmpty(stringValue))
        {
            return null;
        }

        // Strategy 1: Direct case-insensitive parsing
        if (Enum.TryParse<TEnum>(stringValue, true, out var enumValue))
        {
            return enumValue;
        }

        // Strategy 2 & 3: Check parts before/after underscore
        foreach (TEnum val in Enum.GetValues(typeof(TEnum)))
        {
            string enumName = val.ToString();
            int underscoreIndex = enumName.IndexOf('_');

            if (underscoreIndex > 0) // Check part after underscore (e.g., "Corporation" in "C_Corporation")
            {
                string partAfter = enumName.Substring(underscoreIndex + 1);
                if (string.Equals(partAfter, stringValue, StringComparison.OrdinalIgnoreCase))
                {
                    Console.WriteLine($"Matched '{stringValue}' to '{enumName}' based on part after underscore.");
                    return val;
                }
            }

            if (underscoreIndex >= 0) // Check part before underscore (e.g., "B" in "B_GeneralBuilding")
            {
                string partBefore = enumName.Substring(0, underscoreIndex > 0 ? underscoreIndex : enumName.Length);
                if (string.Equals(partBefore, stringValue, StringComparison.OrdinalIgnoreCase) && partBefore.Length == stringValue.Length)
                {
                    Console.WriteLine($"Matched '{stringValue}' to '{enumName}' based on part before underscore.");
                    return val;
                }
            }
            else // Handle enums without underscores (like LLC, Trust etc.)
            {
                if (string.Equals(enumName, stringValue, StringComparison.OrdinalIgnoreCase))
                {
                    Console.WriteLine($"Matched '{stringValue}' to '{enumName}' (no underscore).");
                    return val;
                }
            }
        }

        // Strategy 4: Replace separators and try again
        string modifiedStringValue = stringValue.Replace(" ", "_").Replace("-", "_");
        if (!string.Equals(modifiedStringValue, stringValue, StringComparison.OrdinalIgnoreCase)) // Only try if modification happened
        {
            if (Enum.TryParse<TEnum>(modifiedStringValue, true, out enumValue))
            {
                Console.WriteLine($"Matched '{stringValue}' (as '{modifiedStringValue}') to '{enumValue}' after replacing separators.");
                return enumValue;
            }
        }

        // If all strategies fail
        Console.WriteLine($"Could not parse enum '{typeof(TEnum).Name}' for property '{propertyName}'. Original Value: '{stringValue}'");
        return null;
    }
    private DateTime? GetDateTimeProperty(JsonElement element, string propertyName)
    {
        if (element.TryGetProperty(propertyName, out var propertyElement) && propertyElement.ValueKind == JsonValueKind.String)
        {
            var value = propertyElement.GetString();
            // Prioritize YYYY-MM-DD format as specified by 'date'
            if (DateTime.TryParseExact(value, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var dateValue))
            {
                return dateValue;
            }
            // Fallback for 'date-time' or other common formats
            if (DateTime.TryParse(value, CultureInfo.InvariantCulture, DateTimeStyles.AssumeLocal, out dateValue))
            {
                return dateValue;
            }
            else
            {
                Console.WriteLine($"Could not parse DateTime for '{propertyName}'. Raw value: '{value}'");
            }
        }
        // Console.WriteLine($"Property '{propertyName}' not found, not a string, or null in JSON element."); // Less verbose
        return null;
    }
    private string GetStringProperty(JsonElement element, string propertyName)
    {
        if (element.TryGetProperty(propertyName, out var propertyElement) && propertyElement.ValueKind != JsonValueKind.Null)
        {
            if (propertyElement.ValueKind == JsonValueKind.Array && propertyElement.GetArrayLength() > 0)
            {
                return propertyElement[0].GetString(); // Assuming single value in array if present
            }
            return propertyElement.GetString();
        }
        // Console.WriteLine($"Property '{propertyName}' not found or null in JSON element."); // Less verbose
        return null;
    }
    private string GetJsonSnippetOnError(string fullJson, long? errorLineNumber)
    {
        if (errorLineNumber == null || errorLineNumber <= 0) return "[Could not determine error location]";

        int lineNum = (int)errorLineNumber.Value; // Cast to int
        var lines = fullJson.Split('\n');
        int startLine = Math.Max(0, lineNum - 3); // Show 2 lines before
        int endLine = Math.Min(lines.Length - 1, lineNum + 1); // Show error line and 1 after

        System.Text.StringBuilder snippet = new System.Text.StringBuilder();
        for (int i = startLine; i <= endLine; i++)
        {
            string prefix = (i + 1 == lineNum) ? ">> " : "   "; // Mark the error line
            // Basic HTML encoding for safety, replace with more robust if needed
            string lineContent = System.Net.WebUtility.HtmlEncode(lines[i].TrimEnd('\r'));
            snippet.AppendLine($"{prefix}{lineContent}");
        }
        return snippet.ToString();
    }


    private void SetBusinessDetailsPropertiesFromJson(JsonElement jsonElement, object targetObject)
    {
        foreach (var prop in targetObject.GetType().GetProperties())
        {
            string propName = prop.Name;
            JsonElement matchedElement;

            try
            {
                if (jsonElement.TryGetProperty(propName, out matchedElement))
                {
                    // Match property by type, converting if necessary
                    SetPropertyFromJsonElement(prop, matchedElement, targetObject);
                }
                else if (prop.PropertyType.IsClass && prop.PropertyType != typeof(string))
                {
                    // Recursively check nested properties
                    var nestedObject = Activator.CreateInstance(prop.PropertyType);
                    prop.SetValue(targetObject, nestedObject);
                    SetBusinessDetailsPropertiesFromJson(jsonElement, nestedObject);
                }
                else
                {
                    Console.WriteLine($"Property '{propName}' not found in JSON.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting property '{propName}' from JSON: {ex.Message}");
            }
        }
    }
    private void SetPropertyFromJsonElement(System.Reflection.PropertyInfo prop, JsonElement jsonElement, object targetObject)
    {
        try
        {
            object convertedValue = null;

            // Determine the type of the property and handle accordingly
            if (prop.PropertyType == typeof(int?))
            {
                convertedValue = jsonElement.ValueKind == JsonValueKind.Number && jsonElement.TryGetInt32(out var intValue) ? (int?)intValue : null;
            }
            else if (prop.PropertyType == typeof(decimal?))
            {
                // Handle "$" and commas by parsing as string
                convertedValue = decimal.TryParse(jsonElement.GetString(), NumberStyles.Currency, CultureInfo.InvariantCulture, out var decimalValue) ? (decimal?)decimalValue : null;
            }
            else if (prop.PropertyType == typeof(DateTime?))
            {
                convertedValue = DateTime.TryParse(jsonElement.GetString(), out var dateTimeValue) ? (DateTime?)dateTimeValue : null;
            }
            else if (prop.PropertyType == typeof(bool?))
            {
                // Check if the JSON element is of Boolean kind
                convertedValue = jsonElement.ValueKind == JsonValueKind.True ? (bool?)true :
                                 jsonElement.ValueKind == JsonValueKind.False ? (bool?)false : null;
            }
            else if (prop.PropertyType.IsEnum)
            {
                if (Enum.TryParse(prop.PropertyType, jsonElement.GetString(), true, out var enumValue))
                {
                    convertedValue = enumValue;
                }
            }
            else if (prop.PropertyType == typeof(string))
            {
                convertedValue = jsonElement.GetString();
            }

            prop.SetValue(targetObject, convertedValue);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error converting JSON value for '{prop.Name}': {ex.Message}");
        }
    }
    private void LoadEnumData()
    {
        LegalEntityTypeList = Enum.GetValues(typeof(LegalEntityType)).Cast<LegalEntityType>().Select(e => new EnumData<LegalEntityType> { Value = e, Text = e.ToString().Replace("_", " ") });
        BusinessTypeList = Enum.GetValues(typeof(BusinessType)).Cast<BusinessType>().Select(e => new EnumData<BusinessType> { Value = e, Text = e.ToString().Replace("_", " ") });
        LicenseTypeList = Enum.GetValues(typeof(LicenseType)).Cast<LicenseType>().Select(e => new EnumData<LicenseType> { Value = e, Text = e.ToString().Replace("_", " ") });
    }
    public class EnumData<T>
    {
        public T Value { get; set; }
        public string Text { get; set; }
    }




    // Helper function to get a snippet of JSON around the error line


}
