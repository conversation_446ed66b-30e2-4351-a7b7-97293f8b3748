using System;
using System.IO;
using System.Threading.Tasks;
using System.Collections.Generic;
using Azure;
using Azure.AI.FormRecognizer.DocumentAnalysis;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Microsoft.Extensions.Logging;
using Surefire.Domain.Logs;
using iText.Kernel.Pdf;
using iText.Kernel.Pdf.Canvas.Parser;
using iText.Kernel.Pdf.Canvas.Parser.Listener;

namespace Surefire.Domain.Proposals
{
    public class ExtractorService
    {
        private readonly string _formRecognizerEndpoint;
        private readonly string _formRecognizerKey;
        private readonly ILoggingService _log;
        private readonly DocumentAnalysisClient _client;

        public ExtractorService(string formRecognizerEndpoint, string formRecognizerKey, ILoggingService log)
        {
            _formRecognizerEndpoint = formRecognizerEndpoint;
            _formRecognizerKey = formRecognizerKey;
            _log = log;
            _client = new DocumentAnalysisClient(new Uri(_formRecognizerEndpoint), new AzureKeyCredential(_formRecognizer<PERSON>ey));
        }

        /// <summary>
        /// Processes a single page from a PDF file using Azure Form Recognizer
        /// </summary>
        public async Task<object> ProcessPageAsync(string pdfFilePath, int pageNumber)
        {
            await _log.LogAsync(LogLevel.Information, $"Processing page {pageNumber} from {pdfFilePath}", "ExtractorService");
            
            try
            {
                // Extract the specific page to a new PDF
                byte[] singlePagePdf = ExtractSinglePage(pdfFilePath, pageNumber);
                
                // Process with Form Recognizer
                using var stream = new MemoryStream(singlePagePdf);
                var operation = await _client.AnalyzeDocumentAsync(WaitUntil.Completed, "prebuilt-document", stream);
                var result = operation.Value;
                
                // Serialize the complete result to JSON
                var rawJson = Newtonsoft.Json.JsonConvert.SerializeObject(result, Newtonsoft.Json.Formatting.Indented);
                
                // Create a JObject to add metadata
                var resultWithMetadata = new Newtonsoft.Json.Linq.JObject();
                resultWithMetadata["page_number"] = pageNumber;
                resultWithMetadata["timestamp"] = DateTime.UtcNow.ToString("o");
                resultWithMetadata["raw_form_recognizer_data"] = Newtonsoft.Json.Linq.JObject.Parse(rawJson);
                
                return resultWithMetadata;
            }
            catch (Exception ex)
            {
                await _log.LogAsync(LogLevel.Error, $"Error processing page {pageNumber}: {ex.Message}", "ExtractorService");
                throw;
            }
        }

        /// <summary>
        /// Extracts a single page from a PDF file
        /// </summary>
        private byte[] ExtractSinglePage(string pdfFilePath, int pageNumber)
        {
            using var inputStream = new FileStream(pdfFilePath, FileMode.Open, FileAccess.Read);
            using var reader = new PdfReader(inputStream);
            using var inputPdf = new PdfDocument(reader);
            using var outputStream = new MemoryStream();
            using var writer = new PdfWriter(outputStream);
            using var outputPdf = new PdfDocument(writer);

            int totalPages = inputPdf.GetNumberOfPages();
            if (pageNumber < 1 || pageNumber > totalPages)
            {
                throw new ArgumentOutOfRangeException(nameof(pageNumber), $"Page number {pageNumber} is out of range. Total pages: {totalPages}");
            }

            inputPdf.CopyPagesTo(pageNumber, pageNumber, outputPdf);
            outputPdf.Close();
            return outputStream.ToArray();
        }

        ///// <summary>
        ///// Processes the AnalyzeResult from Form Recognizer and returns a structured object
        ///// </summary>
        //private JObject ProcessExtractedData(AnalyzeResult result, int pageNumber)
        //{
        //    var extractedData = new JObject();
        //    extractedData["page_number"] = pageNumber;
        //    extractedData["timestamp"] = DateTime.UtcNow.ToString("o");
            
        //    var keyValuePairs = new JObject();
        //    var textContent = new JArray();
        //    var tablesArray = new JArray();

        //    // Process key-value pairs
        //    if (result.KeyValuePairs != null)
        //    {
        //        foreach (var kv in result.KeyValuePairs)
        //        {
        //            if (kv.Key != null && kv.Value != null)
        //            {
        //                string key = CleanValue(kv.Key.Content);
        //                string value = CleanValue(kv.Value.Content);
        //                keyValuePairs[key] = value;
        //            }
        //        }
        //    }

        //    // Process text content from pages
        //    if (result.Pages != null)
        //    {
        //        foreach (var page in result.Pages)
        //        {
        //            if (page.Lines != null)
        //            {
        //                foreach (var line in page.Lines)
        //                {
        //                    string content = line.Content?.Trim();
        //                    if (!string.IsNullOrEmpty(content))
        //                    {
        //                        textContent.Add(content);
        //                        // Look for simple key-value patterns in the line
        //                        if (content.Contains(":") &&
        //                            !content.StartsWith("http:", StringComparison.OrdinalIgnoreCase) &&
        //                            !content.StartsWith("https:", StringComparison.OrdinalIgnoreCase))
        //                        {
        //                            var parts = content.Split(new[] { ':' }, 2);
        //                            if (parts.Length == 2)
        //                            {
        //                                string k = CleanValue(parts[0]);
        //                                string v = CleanValue(parts[1]);
        //                                if (!string.IsNullOrEmpty(k) && !string.IsNullOrEmpty(v))
        //                                {
        //                                    keyValuePairs[k] = v;
        //                                }
        //                            }
        //                        }
        //                    }
        //                }
        //            }
        //        }
        //    }

        //    // Process tables
        //    if (result.Tables != null)
        //    {
        //        foreach (var table in result.Tables)
        //        {
        //            // Determine dimensions (assuming cell.RowIndex and cell.ColumnIndex are zero-indexed)
        //            int rows = table.Cells.Max(c => c.RowIndex) + 1;
        //            int cols = table.Cells.Max(c => c.ColumnIndex) + 1;
        //            var tableData = new JArray();

        //            // Initialize table rows with empty cells
        //            for (int i = 0; i < rows; i++)
        //            {
        //                var rowArray = new JArray(Enumerable.Repeat("", cols).ToArray());
        //                tableData.Add(rowArray);
        //            }

        //            // Fill the cells with cleaned content
        //            foreach (var cell in table.Cells)
        //            {
        //                int rowIndex = cell.RowIndex;
        //                int colIndex = cell.ColumnIndex;
        //                var rowArray = (JArray)tableData[rowIndex];
        //                rowArray[colIndex] = CleanValue(cell.Content);
        //            }
        //            tablesArray.Add(tableData);
        //        }
        //    }

        //    extractedData["text_content"] = textContent;
        //    extractedData["key_value_pairs"] = keyValuePairs;
        //    extractedData["tables"] = tablesArray;

        //    return extractedData;
        //}

        /// <summary>
        /// Cleans up extracted string values by removing extra whitespace and common prefixes.
        /// </summary>
        private string CleanValue(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return value;
            // Remove extra whitespace
            value = string.Join(" ", value.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries));
            // Remove common prefixes
            string[] prefixes = { "Phone:", "Email:", "Website:", "Address:", "Tel:", "Fax:" };
            foreach (var prefix in prefixes)
            {
                if (value.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
                {
                    value = value.Substring(prefix.Length).Trim();
                }
            }
            return value;
        }
    }
} 