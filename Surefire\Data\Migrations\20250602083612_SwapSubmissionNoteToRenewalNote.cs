﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Surefire.Migrations
{
    /// <inheritdoc />
    public partial class SwapSubmissionNoteToRenewalNote : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SubmissionNotes");

            migrationBuilder.AddColumn<int>(
                name: "SubmissionId",
                table: "RenewalNotes",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_RenewalNotes_SubmissionId",
                table: "RenewalNotes",
                column: "SubmissionId");

            migrationBuilder.AddForeignKey(
                name: "FK_RenewalNotes_Submissions_SubmissionId",
                table: "RenewalNotes",
                column: "SubmissionId",
                principalTable: "Submissions",
                principalColumn: "SubmissionId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RenewalNotes_Submissions_SubmissionId",
                table: "RenewalNotes");

            migrationBuilder.DropIndex(
                name: "IX_RenewalNotes_SubmissionId",
                table: "RenewalNotes");

            migrationBuilder.DropColumn(
                name: "SubmissionId",
                table: "RenewalNotes");

            migrationBuilder.CreateTable(
                name: "SubmissionNotes",
                columns: table => new
                {
                    SubmissionNoteId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    SubmissionId = table.Column<int>(type: "int", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Deleted = table.Column<bool>(type: "bit", nullable: false),
                    Note = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SubmissionNotes", x => x.SubmissionNoteId);
                    table.ForeignKey(
                        name: "FK_SubmissionNotes_Submissions_SubmissionId",
                        column: x => x.SubmissionId,
                        principalTable: "Submissions",
                        principalColumn: "SubmissionId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SubmissionNotes_SubmissionId",
                table: "SubmissionNotes",
                column: "SubmissionId");
        }
    }
}
