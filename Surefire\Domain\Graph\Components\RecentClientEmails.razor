@namespace Surefire.Domain.Graph.Components
@using Microsoft.FluentUI.AspNetCore.Components
@using Surefire.Domain.Graph
@using Surefire.Domain.Shared.Helpers
@using Surefire.Domain.Clients.Services
@using System.Timers
@inject GraphService GraphService
@inject Surefire.Domain.Clients.Services.ClientStateService ClientStateService

<!-- Recent Emails Section -->

<div class="std-module-title">
    Recent <b>Emails</b>
    @if (isLoading)
    {
        <small class="text-muted me-2">Loading emails...</small>
    }
    else
    {
        <small class="text-muted me-2">@(emails?.Count ?? 0) found</small>

    }
</div>
@if (isLoading)
{
    <FluentProgress Class="loading-progress" />
}
<div class="recent-emails-section">
    
    
    <div class="recent-emails-container">
        @if (isLoading && firstLoad && !emails?.Any() == true)
        {
            <div class="d-flex justify-content-center py-4">
                <FluentProgressRing />
            </div>
        }
        else if (errorMessage != null)
        {
            <FluentMessageBar Intent="MessageIntent.Error" Class="mb-2">
                <div class="d-flex">
                    <div>
                        <FluentIcon Value="@(new Icons.Regular.Size20.Warning())" Class="me-2" />
                    </div>
                    <div>
                        <strong>Error loading emails</strong>
                        <p class="mb-0">@errorMessage</p>
                    </div>
                </div>
                <FluentButton Appearance="Appearance.Outline" OnClick="RefreshEmails" Class="mt-2">
                    <FluentIcon Value="@(new Icons.Regular.Size16.ArrowSync())" Class="me-1" />
                    Try Again
                </FluentButton>
            </FluentMessageBar>
        }
        else if ((emails == null || !emails.Any()) && !isLoading)
        {
            <div class="empty-state text-center py-4">
                <FluentIcon Value="@(new Icons.Regular.Size32.MailAlert())" Class="text-muted d-block mx-auto mb-2" />
                <p class="mt-3 text-muted">
                    @if (EmailAddresses?.Any() == true)
                    {
                        <span>No recent emails found for @EmailAddressesDisplay</span>
                    }
                    else
                    {
                        <span>No email addresses provided</span>
                    }
                </p>
                @if (EmailAddresses?.Any() == true)
                {
                    <FluentButton Appearance="Appearance.Outline" OnClick="RefreshEmails" Class="mt-2">
                        <FluentIcon Value="@(new Icons.Regular.Size16.ArrowSync())" Class="me-1" />
                        Check Again
                    </FluentButton>
                }
            </div>
        }
        else
        {
            <div class="list-group email-list">
                @foreach (var email in emails)
                {
                    <div class="list-group-item email-item @GetEmailCssClass(email)" 
                         @key="@($"{email.MessageId}_{email.Mailbox}")">
                        <div class="top-emailbarbox-@GetEmailStyleType(email)">
                            <FluentStack>
                                @if (email.FromClient)
                                {
                                    <span class="mailflow"><FluentIcon Value="@(new Icons.Regular.Size24.ArrowLeft())" Class="me-1" CustomColor="#fff" Color="Color.Custom" /></span>
                                }
                                else if (email.IsInternal)
                                {
                                    <span class="mailflow"><FluentIcon Value="@(new Icons.Regular.Size24.ArrowSync())" Class="me-1" CustomColor="#fff" Color="Color.Custom" /></span>
                                }
                                else
                                {
                                    <span class="mailflow"><FluentIcon Value="@(new Icons.Regular.Size24.Send())" Class="me-1" CustomColor="#fff" Color="Color.Custom" /></span>
                                }
                                <span class="top-tobox"><strong>TO</strong> @email.ToRecipients.FirstOrDefault()?.Name</span>
                                <span class="top-frombox"><strong>FROM</strong> @email.Sender</span>
                                <span class="top-datebox">@email.ReceivedDateTime.ToLocalTime().ToString("MMM d, h:mm tt")</span>
                            </FluentStack>
                        </div>

                        <div class="email-subject">@email.Subject</div>

                        <div class="email-preview">
                            @if (email.Preview?.Length > 200)
                            {
                                @email.Preview.Substring(0, 200)

                                <span>...</span>
                            }
                            else
                            {
                                @email.Preview
                            }
                        </div>
                    </div>
                }
            </div>
            <div class="text-center py-3 bottom-capitem">
                @if (isLoadingMore)
                {
                    <div class="d-flex justify-content-center py-3">
                        <FluentProgressRing />
                    </div>
                }
                else if (hasMoreEmails)
                {
                    <FluentButton Appearance="Appearance.Neutral" OnClick="LoadMoreEmails" Disabled="@isLoading" Class="loadmore-btn" Color="#fff">
                        <FluentIcon Value="@(new Icons.Regular.Size16.AddCircle())" Class="me-1" />
                        <span class="loadmore-btn-text">Load More</span>
                    </FluentButton>   
                }
            </div>
        }
    </div>

    <div class="recent-emails-footer d-flex justify-content-between align-items-center mt-2">
        <small class="text-muted">
            @if (lastUpdated.HasValue)
            {
                <span>
                    <FluentIcon Value="@(new Icons.Regular.Size16.History())" Class="me-1" />
                    Updated @GetTimeAgo(lastUpdated.Value)
                </span>
            }
        </small>
        <FluentAnchor Appearance="Appearance.Hypertext" Href="/email" IconStart="@(new Icons.Regular.Size16.Open())">
            View All Emails
        </FluentAnchor>

        <FluentAnchor Appearance="Appearance.Hypertext" Href="#" OnClick="RefreshEmails" IconStart="@(new Icons.Regular.Size16.ArrowSync())">
            Refresh
        </FluentAnchor>
    </div>
</div>

@code {
    [Parameter]
    public List<string> EmailAddresses { get; set; } = new();

    [Parameter]
    public int InitialEmailCount { get; set; } = 5;

    [Parameter]
    public int AdditionalEmailCount { get; set; } = 5;

    [Parameter]
    public int CacheRefreshMinutes { get; set; } = 60;

    private List<EmailMessage> emails = new List<EmailMessage>();
    private string cacheKey => string.Join("|", EmailAddresses.OrderBy(e => e));
    private DateTime? lastCachedTime = null;

    private bool isLoading = false;
    private bool isLoadingMore = false;
    private bool firstLoad = true;
    private bool hasMoreEmails = false;
    private string errorMessage;
    private DateTime? lastUpdated;
    private Timer refreshTimer;
    private int currentOffset = 0;
    private int loadingProgress = 0;

    // Track new emails for animation
    private HashSet<string> newEmailIds = new HashSet<string>();
    // Track emails to be removed (duplicates)
    private HashSet<string> fadeOutEmailIds = new HashSet<string>();
    private Timer newEmailClearTimer;
    private Timer fadeOutTimer;

    // Track previous email addresses to detect changes
    private string _previousEmailAddressesKey = string.Empty;

    // Track all message IDs we've seen
    private HashSet<string> processedMessageIds = new HashSet<string>();

    // Store top 5 email preview texts for AI summary
    private List<string> topEmailTexts = new List<string>();

    private string EmailAddressesDisplay => EmailAddresses.Count switch
    {
        0 => string.Empty,
        1 => EmailAddresses[0],
        2 => $"{EmailAddresses[0]} and {EmailAddresses[1]}",
        _ => $"{EmailAddresses[0]} and {EmailAddresses.Count - 1} others"
    };

    protected override async Task OnInitializedAsync()
    {
        refreshTimer = new Timer(CacheRefreshMinutes * 60 * 1000);
        refreshTimer.Elapsed += async (sender, e) => await RefreshEmailsTimerCallback();
        refreshTimer.AutoReset = true;
        refreshTimer.Start();

        // Create timer to clear animation class after emails appear
        newEmailClearTimer = new Timer(3000); // 3 seconds
        newEmailClearTimer.Elapsed += (sender, e) => ClearNewEmailsAnimation();
        newEmailClearTimer.AutoReset = false;

        // Create timer for fade-out animation
        fadeOutTimer = new Timer(1000); // 1 second to fade out
        fadeOutTimer.Elapsed += (sender, e) => RemoveFadedEmails();
        fadeOutTimer.AutoReset = false;

        _previousEmailAddressesKey = cacheKey;
        await LoadEmails();
    }

    protected override async Task OnParametersSetAsync()
    {
        // Check if email addresses have changed by comparing cache keys
        if (cacheKey != _previousEmailAddressesKey)
        {
            _previousEmailAddressesKey = cacheKey;
            await LoadEmails();
        }
    }

    private async Task LoadEmails()
    {
        if (!EmailAddresses.Any())
        {
            emails = new List<EmailMessage>();
            firstLoad = false;
            return;
        }

        // Check ClientStateService for cached emails
        var cachedEmails = ClientStateService.GetCachedEmails(cacheKey);
        // We'll use browser local time for cache freshness (not persisted timestamps)
        if (cachedEmails != null && cachedEmails.Any())
        {
            emails = cachedEmails.Take(InitialEmailCount).ToList();
            currentOffset = InitialEmailCount;
            hasMoreEmails = cachedEmails.Count > InitialEmailCount;
            // Optionally, you could persist timestamps in ClientStateService for more robust freshness logic
            lastUpdated = DateTime.Now; // Could be improved with timestamp support
            firstLoad = false;
            return;
        }

        // Otherwise, fetch fresh data
        await FetchEmailsFromService();
    }

    private async Task FetchEmailsFromService()
    {
        if (!EmailAddresses.Any())
            return;

        isLoading = true;
        errorMessage = null;
        emails = new List<EmailMessage>();
        newEmailIds.Clear();
        fadeOutEmailIds.Clear();
        processedMessageIds.Clear();
        loadingProgress = 0;

        try
        {
            var allEmails = new List<EmailMessage>();

            // Calculate progress increment per email address
            int progressIncrement = 100 / Math.Max(1, EmailAddresses.Count);
            int currentEmailAddressIndex = 0;

            foreach (var emailAddress in EmailAddresses)
            {
                currentEmailAddressIndex++;
                try
                {
                    var addressEmails = await GraphService.SearchBySenderEmailAsync(emailAddress, 50);

                    // Update progress
                    loadingProgress = progressIncrement * currentEmailAddressIndex;

                    // Process emails progressively
                    if (addressEmails?.Any() == true)
                    {
                        foreach (var email in addressEmails)
                        {
                            string emailKey = email.MessageId + email.Mailbox;

                            // If we've seen this message ID before, it might be a duplicate
                            if (processedMessageIds.Contains(email.MessageId))
                            {
                                // Check if we already have this exact message
                                var existingEmailIndex = allEmails.FindIndex(e => 
                                    e.MessageId == email.MessageId && e.Mailbox == email.Mailbox);

                                if (existingEmailIndex >= 0)
                                {
                                    // Already have this exact message, skip
                                    continue;
                                }

                                // This is a duplicate in a different mailbox, mark for fade-out
                                fadeOutEmailIds.Add(emailKey);

                                // Add it to the working list anyway (will be removed after fade-out)
                                allEmails.Add(email);

                                // Update the UI with the complete set of emails
                                emails = allEmails
                                    .OrderByDescending(e => e.ReceivedDateTime)
                                    .Take(InitialEmailCount)
                                    .ToList();

                                await InvokeAsync(StateHasChanged);

                                // Start the fade-out timer
                                fadeOutTimer.Stop();
                                fadeOutTimer.Start();

                                // Small delay before continuing
                                await Task.Delay(50);

                                continue;
                            }

                            // This is a new message
                            processedMessageIds.Add(email.MessageId);
                            newEmailIds.Add(emailKey);

                            // Add it to working list
                            allEmails.Add(email);

                            // Update display with sort and limit
                            emails = allEmails
                                .OrderByDescending(e => e.ReceivedDateTime)
                                .Take(InitialEmailCount)
                                .ToList();

                            // Update the UI
                            await InvokeAsync(StateHasChanged);

                            // Small delay for staggered effect
                            await Task.Delay(50);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error fetching emails for {emailAddress}: {ex.Message}");
                    // Continue with other email addresses
                }

                // Update UI after each email address
                await InvokeAsync(StateHasChanged);
            }

            // Sort by date, most recent first
            var finalSortedEmails = allEmails
                .OrderByDescending(e => e.ReceivedDateTime)
                .ToList();

            // Save to persistent cache
            ClientStateService.SetCachedEmails(cacheKey, finalSortedEmails);
            await ClientStateService.SaveStateAsync();
            lastUpdated = DateTime.Now;

            // Set initial display (if not already displayed)
            emails = finalSortedEmails.Take(InitialEmailCount).ToList();
            currentOffset = InitialEmailCount;
            hasMoreEmails = finalSortedEmails.Count > InitialEmailCount;
            
            // Start timer to clear animation class
            if (newEmailIds.Any())
            {
                newEmailClearTimer.Stop();
                newEmailClearTimer.Start();
            }
        }
        catch (Exception ex)
        {
            errorMessage = ex.Message;
            Console.WriteLine($"Error loading emails: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            firstLoad = false;
            StateHasChanged();
        }
    }

    private void ClearNewEmailsAnimation()
    {
        InvokeAsync(() => 
        {
            newEmailIds.Clear();
            StateHasChanged();
        });
    }
    
    private void RemoveFadedEmails()
    {
        InvokeAsync(() => 
        {
            // Remove faded out emails from the list
            emails = emails.Where(e => !fadeOutEmailIds.Contains(e.MessageId + e.Mailbox)).ToList();
            fadeOutEmailIds.Clear();
            StateHasChanged();
        });
    }

    private async Task LoadMoreEmails()
    {
        var cachedEmails = ClientStateService.GetCachedEmails(cacheKey);
        if (cachedEmails == null || isLoadingMore)
            return;

        isLoadingMore = true;
        newEmailIds.Clear();

        try
        {
            // Load additional emails with staggered animation
            for (int i = 0; i < AdditionalEmailCount && (currentOffset + i) < cachedEmails.Count; i++)
            {
                var email = cachedEmails[currentOffset + i];
                
                // Mark as new for animation
                string emailKey = email.MessageId + email.Mailbox;
                newEmailIds.Add(emailKey);
                
                // Add to displayed emails
                emails.Add(email);
                
                // Update UI
                await InvokeAsync(StateHasChanged);
                
                // Small delay for staggered effect
                await Task.Delay(100);
            }
            
            // Update counters
            int addedCount = Math.Min(AdditionalEmailCount, cachedEmails.Count - currentOffset);
            currentOffset += addedCount;
            hasMoreEmails = currentOffset < cachedEmails.Count;
            
            // Start timer to clear animation class
            if (newEmailIds.Any())
            {
                newEmailClearTimer.Stop();
                newEmailClearTimer.Start();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading more emails: {ex.Message}");
        }
        finally
        {
            isLoadingMore = false;
            StateHasChanged();
        }
    }

    private async Task RefreshEmails()
    {
        await FetchEmailsFromService();
    }

    private async Task RefreshEmailsTimerCallback()
    {
        // This method will be called on a background thread
        await InvokeAsync(async () =>
        {
            await FetchEmailsFromService();
            StateHasChanged();
        });
    }
    
    private string GetEmailCssClass(EmailMessage email)
    {
        string baseClass = "";
        string emailKey = email.MessageId + email.Mailbox;
        
        if (email.FromClient)
            baseClass = "email-from-client";
        else if (email.IsInternal)
            baseClass = "email-internal";
        else
            baseClass = "email-outbound";
            
        if (newEmailIds.Contains(emailKey))
            baseClass += " email-new";
            
        if (fadeOutEmailIds.Contains(emailKey))
            baseClass += " email-fadeout";
            
        return baseClass;
    }
    
    private string GetEmailStyleType(EmailMessage email)
    {
        if (email.FromClient)
            return "fromclient";
        else if (email.IsInternal)
            return "internal";
        else
            return "outbound";
    }

    private string GetMailboxDisplayName(string email)
    {
        return StringHelper.ToTitleCase(email.Split('@')[0]);
    }

    private string GetTimeAgo(DateTime dateTime)
    {
        var timeSpan = DateTime.Now - dateTime;

        if (timeSpan.TotalMinutes < 1)
            return "just now";
        if (timeSpan.TotalMinutes < 60)
            return $"{(int)timeSpan.TotalMinutes} minutes ago";
        if (timeSpan.TotalHours < 24)
            return $"{(int)timeSpan.TotalHours} hours ago";

        return $"{(int)timeSpan.TotalDays} days ago";
    }

    // Public method to get the top 5 email texts
    public List<string> GetTopEmailTexts()
    {
        topEmailTexts = emails.Take(5).Select(e =>
            {
                // Filter out payment provider automated messages from the body
                string filteredPreview = e.Preview ?? "No preview available";
                filteredPreview = filteredPreview.Replace("How do I reverse this payment?", "")
                                                .Replace("How do I subscribe others to notifications?", "")
                                                .Trim();

                return $"SUBJECT: {e.Subject}\n" +
                       $"FROM: {e.Sender}\n" +
                       $"TO: {e.ToRecipients.FirstOrDefault()?.Name ?? e.ToRecipients.FirstOrDefault()?.EmailAddress ?? "Unknown"}\n" +
                       $"BODY:\n{filteredPreview}";
            }).ToList();
        return topEmailTexts;
    }

    public void Dispose()
    {
        try
        {
            refreshTimer?.Stop();
            refreshTimer?.Dispose();
            refreshTimer = null;
            
            newEmailClearTimer?.Stop();
            newEmailClearTimer?.Dispose();
            newEmailClearTimer = null;
            
            fadeOutTimer?.Stop();
            fadeOutTimer?.Dispose();
            fadeOutTimer = null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error disposing RecentClientEmails: {ex.Message}");
        }
    }
}