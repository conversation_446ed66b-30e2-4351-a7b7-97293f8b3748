﻿@page "/Clients/{LoadClientId:int}"
@inherits ClientsBase
@using Surefire.Domain.Attachments.Components
@using Surefire.Domain.Policies.Components
@using Surefire.Domain.Forms.Components
@using Surefire.Domain.Proposals
@using Surefire.Domain.Clients.Services
@using Surefire.Domain.Graph.Components
@using Surefire.Domain.Shared.Components
@using Surefire.Domain.Utilities
@using Syncfusion.Blazor.Spinner
@using Syncfusion.Blazor.Lists
@using Microsoft.AspNetCore.Components
@using Surefire.Domain.Clients.Models
@using Surefire.Domain.OpenAI.Simple
@inject IOpenAISimpleService OpenAIService
@inject IJSRuntime JSRuntime
@inject ClientService ClientService


<_toolbar ClientId="@(selectedClient?.ClientId ?? 0)" OnNewPolicy="@SetShowCreatePolicy" ClientLoaded="@(selectedClient != null)" EmailAddresses="@GetAllEmailAddresses()" ClientName="@(selectedClient?.Name ?? "Unknown Client")" PageName="Browse" OnShowNotes="EventCallback.Factory.Create(this, () => showNotesToggle())" OnShowAiSummary="EventCallback.Factory.Create(this, ShowAiSummaryDialog)" OnShowAiRecentActivitySummary="EventCallback.Factory.Create(this, ShowAiRecentActivitySummaryDialog)" />

<div class="page-content-client">
    <div class="@dynamicClass">
        <FluentTextField @bind-Value="searchTerm" @oninput="OnInputChanged" Placeholder="Search for a client..." Class="sf-searchquick" AutoComplete="off">
            <FluentIcon Value="@(new Icons.Regular.Size16.Search())" Slot="start" Color="Color.Custom" CustomColor="#cf4640" />
        </FluentTextField>
        <div class="client-list">
            @foreach (var item in filteredClients)
            {
                <div class="client-list-item" @onclick="async () => await LoadClientClickHandler(item.ClientId)">
                    @item.Name
                </div>
            }
        </div>
    </div>

    <div class="sf-selected">
        <div class="tinyexpander" @onclick="() => ExpandDetails()"><img src="/img/tinyexpand.png" /></div>
        <div class="client-top">
            <ClientHeader selectedClient="@selectedClient" />
        </div>
        @if (selectedClient != null)
        {
            <GlobalNotes EntityType="EntityType.Client" EntityId="@selectedClient.ClientId" ClientName="@selectedClient.Name" ShowNotes="@showNotes" />
        }
        <div class="client-main">
            <div class="btn-notestog"><img src="/img/tinyexpand-right.png" @onclick="@(() => showNotesToggle())"></div>
            <FluentTabs @ref="tabInterface" @bind-ActiveTabId="ClientStateService.ActiveTab" @bind-ActiveTabId:after="OnActiveTabChanged" Size="TabSize.Large" Style="padding:0;">
                <FluentTab Id="tab-1" Label="Overview" Class="sf-tab-client">
                    <div class="sf-col-container">
                        <div class="sf-col sf-col-1">
                            <div class="std-module-container-alt">
                                @if (selectedClient != null)
                                {
                                <FluentStack>
                                    @if (!string.IsNullOrEmpty(selectedClient.LogoFilename))
                                    {
                                        <div class="client-logo-section-sf">
                                            <div class="client-logo-container-sf">
                                                <img src="/uploads/logos/@selectedClient.LogoFilename" alt="@selectedClient.Name Logo" class="client-logo-sf" 
                                                        onerror="this.style.display='none'; this.parentElement.parentElement.parentElement.querySelector('.no-logo-fallback').style.display='flex';" />
                                            </div>
                                        </div>
                                    }
                                    else
                                    {
                                        <div class="fallback-logo-container enhanced-fallback">
                                            <img src="/img/briefcase.png" alt="Business" class="fallback-logo-img" />
                                            <div class="fallback-business-text">
                                                @{
                                                    var businessName = selectedClient?.Name ?? "Business";
                                                    var words = businessName.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                                                }
                                                @if (words.Length == 3)
                                                {
                                                    <div class="business-name-line1">@words[0]</div>
                                                    <div class="business-name-line2">@words[1] @words[2]</div>
                                                }
                                                else if (words.Length >= 4)
                                                {
                                                    <div class="business-name-line1">@words[0] @words[1]</div>
                                                    <div class="business-name-line2">@string.Join(" ", words.Skip(2))</div>
                                                }
                                                else
                                                {
                                                    <div class="business-name-line1">@businessName</div>
                                                }
                                            </div>
                                        </div>
                                    }
                                    <div class="quickstats-col-1 enhanced-stats">
                                        <div class="stat-row">
                                            <span class="stat-icon"><FluentIcon Value="@(new Icons.Regular.Size16.Document())" Color="Color.Neutral" /></span>
                                            <span class="stat-content"><b>Active Policies:</b> <span class="stat-number">@(currentPolicies?.Count ?? 0)</span></span>
                                        </div>
                                        <div class="stat-row">
                                            <span class="stat-icon"><FluentIcon Value="@(new Icons.Regular.Size16.DocumentCopy())" Color="Color.Neutral" /></span>
                                            <span class="stat-content"><b>Total Policies:</b> <span class="stat-number">@GetTotalPolicyCount()</span></span>
                                        </div>
                                        <div class="stat-row">
                                            <span class="stat-icon"><FluentIcon Value="@(new Icons.Regular.Size16.Money())" Color="Color.Neutral" /></span>
                                            <span class="stat-content"><b>Premium Volume:</b> <span class="stat-number highlight">@GetTotalActivePremium()</span></span>
                                        </div>
                                        <div class="stat-row">
                                            <span class="stat-icon"><FluentIcon Value="@(new Icons.Regular.Size16.Briefcase())" Color="Color.Neutral" /></span>
                                            <span class="stat-content"><b>Client Since:</b> <span class="stat-number">@GetClientSinceDate()</span></span>
                                        </div>
                                    </div>
                                </FluentStack>
                                }
                            </div>

                            <div class="pol-tweak">
                                <div class="std-module-title">CURRENT <b>POLICIES</b></div>
                                <CurrentPolicies selectedClient="@selectedClient" currentPolicies="@currentPolicies" OnOutlookSearchForPolicy="OutlookSearchForThisPolicy" />
                            </div>

                        </div>

                        <div class="sf-col sf-col-2">
                            <div class="std-module-title">CLIENT <b>CONTACTS</b></div>
                            <div class="std-module-container">
                                @if (selectedClient != null)
                                {
                                    <ListContactsForParent Contacts="@contactList" ParentType="Client" ParentId="@selectedClient.ClientId" PrimaryContactId="@selectedClient.PrimaryContactId" />
                                }
                            </div>

                            <div class="client-vspacer"></div>

                            <div class="std-module-title">RECENT <b>PAYMENTS</b></div>
                            <div class="std-module-container">
                                @if (selectedClient != null)
                                {
                                    <RecentPaymentsList ContactsList="selectedClient?.Contacts" />
                                }
                                else
                                {
                                    <span class="std-loading">Loading...</span>
                                }
                            </div>

                            <div class="client-vspacer"></div>

                            <div class="std-module-title">RECENT <b>CALLS</b></div>
                            <div class="std-module-container">
                                @if (selectedClient != null)
                                {
                                    <RecentPhoneCallsList @ref="RecentPhoneCallsListComponent" PhoneNumbers="@phoneNumbers" />
                                }
                                else
                                {
                                    <span class="std-loading">Loading...</span>
                                }
                            </div>
                        </div>

                        <div class="sf-col sf-col-3 sf-gright-col">
                            @if (_stateService.DisablePlugins != true)
                            {
                                <RecentClientEmails @ref="recentEmailsComponent" EmailAddresses="@GetAllEmailAddresses()" />
                            }
                        </div>
                    </div>
                </FluentTab>
                <FluentTab Id="tab-2" Label="Policies" Class="sf-tab-client">
                    <div class="tabcontentsnopad">
                        <div class="txt-section">List of Policies</div>
                        @if (selectedClient != null)
                        {
                            @if (showCreatePolicy)
                            {
                                <CreatePolicyForClient ClientId="selectedClient.ClientId" OnCloseCreatePolicy="@SetHideCreatePolicy" />
                            }
                            else
                            {
                                <PolicyListGrid policyList="selectedClient?.Policies.AsQueryable()" />
                            }
                        }
                    </div>
                </FluentTab>
                <FluentTab Id="tab-3" Label="Attachments" Class="sf-tab">
                    <div class="tabcontents">
                        <div class="txt-section">Attachments</div>
                        @if (selectedClient != null)
                        {
                            <DropzoneContainer ClientId="selectedClient?.ClientId ?? 0" OnAttachmentAdded="HandleAttachmentAdded">
                                <AttachmentListGrid ClientId="selectedClient?.ClientId ?? 0" />
                            </DropzoneContainer>
                        }
                    </div>
                </FluentTab>
                <FluentTab Id="tab-4" Label="Forms" Class="sf-tab">
                    <div class="tabcontents">
                        <div class="txt-section">Certificates</div>
                        @if (selectedClient != null)
                        {
                            <CertificateList certList="selectedClient.Certificates.ToList()" />
                        }
                        <div style="height:25px;"></div>
                        <div class="txt-section">Applications</div>
                        @if (selectedClient != null)
                        {
                            @foreach (var item in allFormPdfs)
                            {
                                <FluentButton @onclick="() => CreateNewForm(item.FormPdfId)">@item.Title</FluentButton>
                            }
                            <FormDocList formDocList="selectedClient.FormDocs.ToList()" />
                        }

                    </div>
                </FluentTab>

                <FluentTab Id="tab-4b" Label="Details" Class="sf-tab">
                    @if (selectedClient != null)
                    {
                        <BusinessDetailsEditor ClientId="selectedClient.ClientId" />
                    }
                </FluentTab>
                @* <FluentTab Id="tab-5" Label="Proposals" Class="sf-tab">
                    @if (showProposalCleaner)
                    {
                        <ProposalCleanerPartial ClientId="@LoadClientId" ProposalId="@currentProposalId" OnReturnToList="@(() => showProposalCleaner = false)" />
                    }
                    else
                    {
                        <Proposals ClientId="@LoadClientId" OnShowCleaner="@(EventCallback.Factory.Create<(int clientId, int proposalId)>(this, ShowProposalCleaner))" />
                    }
                </FluentTab> *@
                <FluentTab Id="tab-6" Label="Utilities" Class="sf-tab-client">
                    <div class="tabcontentsnopad">
                        @if (selectedClient != null)
                        {
                            <Utilities 
                            ClientId="@selectedClient.ClientId"
                            PolicyId="@currentPolicies.FirstOrDefault()?.PolicyId"
                            ContactId="@selectedClient.PrimaryContact?.ContactId" />
                        }
                    </div>
                </FluentTab>
            </FluentTabs>
        </div>
        <div class="top-loader top-loader-@isLoading"><SfSpinner Type="SpinnerType.Bootstrap5" Visible="true" Size="100" CssClass="e-spin-overlay"></SfSpinner></div>
    </div>
    <div style="clear:both;"></div>
</div>

<FluentDialog Hidden="!IsAiSummaryDialogOpen" TrapFocus="true" Modal="true" Style="--dialog-width:60vw; --dialog-height:80vh;">
    <FluentDialogHeader Style="padding-bottom: 0px !important;">
        <div class="dialog-header-custom">
            <FluentStack HorizontalGap="8">
                <FluentIcon Value="@(new Icons.Regular.Size24.Bot())" Color="Color.Neutral" />
                <span class="dialog-header-texter">@AiSummaryTitle</span>
            </FluentStack>
        </div>
    </FluentDialogHeader>
    <FluentDialogBody>
        @if (IsGeneratingAiSummary)
        {
            <div class="ai-summary-loading">
                <FluentProgressRing />
                <p>@AiSummaryLoadingMessage</p>
            </div>
        }
        else if (!string.IsNullOrEmpty(AiSummary))
        {
            <div class="ai-summary-content">
                @((MarkupString)AiSummary)
            </div>
        }
    </FluentDialogBody>
    <FluentDialogFooter>
        <FluentButton Appearance="Appearance.Outline" OnClick="CloseAiSummaryDialog">Close</FluentButton>
        <FluentButton Appearance="Appearance.Accent" OnClick="CopyAiSummary" Disabled="@(string.IsNullOrEmpty(AiSummary) || IsGeneratingAiSummary)">
            <FluentIcon Value="@(new Icons.Regular.Size16.Copy())" Slot="start" Color="Color.Fill" />
            Copy to Clipboard
        </FluentButton>
    </FluentDialogFooter>
</FluentDialog>

<!-- Transcription Confirmation Dialog -->
<FluentDialog Hidden="!IsTranscriptionConfirmDialogOpen" TrapFocus="true" Modal="true" Style="--dialog-width:500px;">
    <FluentDialogHeader>
        <div class="dialog-header-custom">
            <FluentStack HorizontalGap="8">
                <FluentIcon Value="@(new Icons.Regular.Size24.SoundWaveCircle())" Color="Color.Neutral" />
                <span class="dialog-header-texter">Un-transcribed Calls Found</span>
            </FluentStack>
        </div>
    </FluentDialogHeader>
    <FluentDialogBody>
        <div class="transcription-confirm-content">
            <p>
                There are <strong>@UntranscribedCallsCount un-transcribed calls</strong> for this client.
            </p>
            <p>
                Would you like to transcribe them first to get a more complete AI summary, or proceed with the currently available data?
            </p>
            <div class="transcription-options">
                <div class="option-card">
                    <FluentIcon Value="@(new Icons.Regular.Size20.SoundWaveCircle())" Color="Color.Neutral" />
                    <div>
                        <strong>Transcribe First</strong>
                        <p>Transcribe all calls, then generate AI summary with complete data</p>
                    </div>
                </div>
                <div class="option-card">
                    <FluentIcon Value="@(new Icons.Regular.Size20.Bot())" Color="Color.Neutral" />
                    <div>
                        <strong>Use Available Data</strong>
                        <p>Generate AI summary with currently transcribed calls only</p>
                    </div>
                </div>
            </div>
        </div>
    </FluentDialogBody>
    <FluentDialogFooter>
        <FluentButton Appearance="Appearance.Outline" OnClick="OnProceedWithAvailableData">
            Use Available Data
        </FluentButton>
        <FluentButton Appearance="Appearance.Accent" OnClick="OnTranscribeCallsFirst">
            <FluentIcon Value="@(new Icons.Regular.Size16.SoundWaveCircle())" Slot="start" Color="Color.Fill" />
            Transcribe First
        </FluentButton>
    </FluentDialogFooter>
</FluentDialog>

@code {
    private string GetClientSinceDate()
    {
        if (selectedClient?.Policies?.Any() == true)
        {
            var firstPolicy = selectedClient.Policies
                .OrderBy(p => p.EffectiveDate)
                .FirstOrDefault();

            if (firstPolicy != null)
            {
                return firstPolicy.EffectiveDate.ToString("MMM yyyy");
            }
        }

        return selectedClient?.CreatedDate.ToString("MMM yyyy") ?? "Unknown";
    }

    private int GetTotalPolicyCount()
    {
        return selectedClient?.Policies?.Count ?? 0;
    }

    private string GetTotalActivePremium()
    {
        if (currentPolicies?.Any() == true)
        {
            var totalPremium = currentPolicies
                .Where(p => p.Premium > 0)
                .Sum(p => p.Premium);

            return totalPremium.ToString("C0");
        }

        return "$0";
    }
}