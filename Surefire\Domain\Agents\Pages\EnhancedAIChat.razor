@page "/agents"
@using Surefire.Domain.Agents.Models
@using Surefire.Domain.Agents.Services
@using Surefire.Domain.Agents.Interfaces
@using System.Text.Json
@using System.Text.RegularExpressions
@inject IOpenAIAgent OpenAIAgent
@inject IVoiceRecordingService VoiceRecordingService
@inject NavigationManager Navigation
@inject Surefire.Domain.Shared.Services.StateService StateService
@inject IJSRuntime JS
@inject ILogger<EnhancedAIChat> Logger

<PageTitle>Enhanced AI Assistant</PageTitle>

<div class="enhanced-chat-container">
    <div class="chat-body">
        <div class="chat-messages" @ref="messagesContainer">
            <div class="quick-actions-bar">
                <div class="quick-actions-label">Quick Actions:</div>
                <div class="quick-actions-list">
                    @foreach (var action in quickActions)
                    {
                        <button class="quick-action" @onclick="() => ExecuteQuickAction(action.Command)">
                            <i class="fas @action.Icon"></i>
                            <span>@action.Label</span>
                        </button>
                    }
                </div>
            </div>

            @foreach (var message in messages)
            {
                <div class="message-wrapper @message.Type">
                    <div class="message-bubble">
                        <div class="message-header">
                            <div class="sender-info">
                                @if (message.Type == "user")
                                {
                                    <div class="avatar user-avatar">
                                        <img src="/img/staff/s-nathan.png" class="img-responsive" />
                                    </div>
                                    <span class="sender-name">You</span>
                                }
                                else
                                {
                                    <div class="avatar ai-avatar">
                                        <img src="/img/ball.png" class="img-responsive" />
                                    </div>
                                    <span class="sender-name">Surefire</span>
                                }
                            </div>
                            <div class="message-meta">
                                <span class="timestamp">@message.Timestamp.ToString("HH:mm")</span>
                                @if (message.Intent != IntentType.Unknown && message.Type == "assistant")
                                {
                                    <span class="intent-indicator @GetIntentClass(message.Intent)">
                                        <i class="fas @GetIntentIcon(message.Intent)"></i>
                                        @message.Intent
                                    </span>
                                }
                                @if (message.ExecutionTime > 0)
                                {
                                    <span class="execution-time">@($"{message.ExecutionTime:F1}s")</span>
                                }
                            </div>
                        </div>

                        <div class="message-content" style="white-space: pre-wrap;">
                            @if (message.IsStreaming)
                            {
                                <div class="streaming-text">
                                    @((MarkupString)message.Content)
                                    <span class="typing-cursor">|</span>
                                </div>
                            }
                            else
                            {
                                @((MarkupString)message.Content)
                            }
                        </div>

                        @if (message.Data != null && message.Type == "assistant")
                        {
                            <div class="data-results">
                                <div class="results-header">
                                    <i class="fas fa-database"></i>
                                    <span>Query Results</span>
                                    <button class="expand-toggle" @onclick="() => ToggleDataExpansion(message)">
                                        <i class="fas @(message.IsDataExpanded ? "fa-chevron-up" : "fa-chevron-down")"></i>
                                    </button>
                                </div>
                                @if (message.IsDataExpanded)
                                {
                                    <div class="results-content">
                                        @RenderDataResults(message.Data)
                                    </div>
                                }
                            </div>
                        }

                        @if (message.Suggestions?.Any() == true)
                        {
                            <div class="suggestions-container">
                                <div class="suggestions-header">
                                    <i class="fas fa-lightbulb"></i>
                                    <span>Suggested actions:</span>
                                </div>
                                <div class="suggestions-grid">
                                    @foreach (var suggestion in message.Suggestions.Take(4))
                                    {
                                        <button class="suggestion-chip" @onclick="() => HandleSuggestion(suggestion)">
                                            <i class="fas fa-arrow-right"></i>
                                            @suggestion
                                        </button>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>

        <div class="chat-input-section">
            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="error-banner">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>@errorMessage</span>
                    <button class="dismiss-error" @onclick="DismissError">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            }

            <div class="input-container">
                <div class="input-wrapper">
                    <textarea @ref="inputElement" 
                             @bind="currentInput" 
                             @oninput="OnInputChange"
                             @onkeydown="HandleKeyDown"
                             placeholder="Ask me anything about your insurance business..."
                             class="chat-input"
                             disabled="@isProcessing"
                             maxlength="2000"></textarea>
                    
                    <div class="input-actions">
                        <button class="action-btn attachment-btn" title="Attach file" disabled>
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <button class="action-btn voice-btn @(isRecording ? "recording" : "")" 
                               title="@(isRecording ? "Stop recording" : "Voice input")" 
                               @onclick="ToggleVoiceRecording"
                               disabled="@isProcessing">
                            @if (isRecording)
                            {
                                <FluentIcon Value="@(new Icons.Regular.Size20.MicOff())" />
                            }
                            else
                            {
                                <FluentIcon Value="@(new Icons.Regular.Size20.Mic())" />
                            }
                        </button>
                        <button class="send-button" 
                               @onclick="SendMessage" 
                               disabled="@(isProcessing || string.IsNullOrWhiteSpace(currentInput))"
                               title="Send message (Ctrl+Enter)">
                            @if (isProcessing)
                            {
                                <i class="fas fa-spinner fa-spin"></i>
                            }
                            else
                            {
                                <i class="fas fa-paper-plane"></i>
                            }
                        </button>
                    </div>
                </div>
                
                <div class="input-footer">
                    <div class="left-info">
                        <span class="char-counter @(GetCharCounterClass())">
                            @(currentInput?.Length ?? 0) / 2000
                        </span>
                        @if (isTranscribing)
                        {
                            <div class="transcribing-indicator">
                                <div class="spinner"></div>
                                <span>Transcribing voice...</span>
                            </div>
                        }
                    </div>
                    <div class="right-actions">
                        <button class="footer-btn" @onclick="ClearConversation" title="Clear conversation">
                            <i class="fas fa-broom"></i>
                            Clear
                        </button>
                        <button class="footer-btn" @onclick="ExportConversation" title="Export conversation">
                            <i class="fas fa-download"></i>
                            Export
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<ChatMessage> messages = new();
    private string currentInput = string.Empty;
    private string errorMessage = string.Empty;
    private string processingStatus = "Thinking...";
    private bool isProcessing = false;
    private bool isHealthy = true;
    private bool isRecording = false;
    private bool isTranscribing = false;
    private bool isProcessingVoiceTranscription = false;
    private ElementReference messagesContainer;
    private ElementReference inputElement;
    private string sessionId = Guid.NewGuid().ToString();

    private readonly List<QuickAction> quickActions = new()
    {
        new QuickAction { Label = "Client Count", Icon = "fa-users", Command = "How many clients do we have?" },
        new QuickAction { Label = "Recent Policies", Icon = "fa-file-contract", Command = "Show me policies created this month" },
        new QuickAction { Label = "Top Carriers", Icon = "fa-building", Command = "What are our top 5 carriers by policy count?" },
        new QuickAction { Label = "Loss Run", Icon = "fa-paper-plane", Command = "Send loss run for [client name]" },
        new QuickAction { Label = "Renewals Due", Icon = "fa-calendar-alt", Command = "Show me renewals due this month" }
    };

    protected override async Task OnInitializedAsync()
    {
        await CheckAIHealth();
        await InitializeConversation();
        
        // Check for pending voice transcription from push-to-talk
        await CheckForPendingVoiceTranscription();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await FocusInput();
            await SetupVoiceTranscriptionListeners();
        }
        await ScrollToBottom();
    }

    private async Task CheckAIHealth()
    {
        try
        {
            isHealthy = await OpenAIAgent.IsHealthyAsync();
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "Failed to check AI health status");
            isHealthy = false;
        }
    }

    private async Task InitializeConversation()
    {
        var welcomeMessage = new ChatMessage
        {
            Type = "assistant",
            Content = @"<div class='welcome-message'><span class='aiwelcome'>Think of me as your sidearm... I can provide you with intelligent assistance for your insurance business. My skills are grouped into three main areas...</span><ul><li><strong>Data Queries:</strong> Ask about clients, policies, carriers, and get instant data insights</li><li><strong>Agent Actions:</strong> Send loss runs, request certificates, generate proposals</li><li><strong>Knowledge Hub:</strong> Get explanations about insurance concepts and business processes</li></ul></div>",
            Timestamp = DateTime.Now,
            Suggestions = new List<string>
            {
                "Show me a summary of our business",
                "What are the most common policy types?",
                "How can I improve client retention?",
                "Explain workers compensation insurance"
            }
        };

        messages.Add(welcomeMessage);
    }

    private async Task SendMessage()
    {
        if (string.IsNullOrWhiteSpace(currentInput) || isProcessing)
            return;

        var userInput = currentInput.Trim();
        currentInput = string.Empty;
        errorMessage = string.Empty;

        // Add user message
        var userMessage = new ChatMessage
        {
            Type = "user",
            Content = userInput,
            Timestamp = DateTime.Now
        };
        messages.Add(userMessage);

        await ProcessAIRequest(userInput);
    }

    private async Task ProcessAIRequest(string input)
    {
        isProcessing = true;
        var startTime = DateTime.Now;

        var statusMessages = new[]
        {
            "Analyzing your request...",
            "Determining the best approach...",
            "Processing with AI...",
            "Generating response..."
        };

        try
        {
            Console.WriteLine("Processing A1");
            var request = new UnifiedRequest
            {
                Input = input,
                UserId = "enhanced-user",
                SessionId = sessionId,
                Context = BuildContext()
            };

            // Add streaming placeholder message
            var assistantMessage = new ChatMessage
            {
                Type = "assistant",
                Content = string.Empty,
                Timestamp = DateTime.Now,
                IsStreaming = true
            };
            messages.Add(assistantMessage);

            // Initialize processing status and start update timer
            assistantMessage.Content = statusMessages[0];
            var statusIndex = 0;
            var statusTimer = new System.Timers.Timer(1500);
            statusTimer.Elapsed += async (s, e) =>
            {
                if (statusIndex < statusMessages.Length - 1)
                {
                    statusIndex++;
                    assistantMessage.Content = statusMessages[statusIndex];
                    await InvokeAsync(StateHasChanged);
                }
            };
            statusTimer.Start();

            // Stream responses and capture final content
            var response = await OpenAIAgent.ProcessRequestStreamingAsync(
                request,
                async (chunk) =>
                {
                    if (chunk.Type == "final_response")
                    {
                        Console.WriteLine("final_response reached");
                        statusTimer.Stop();
                        statusTimer.Dispose();
                        // Convert markdown bold (**text**) to HTML strong tags and bold quoted names
                        var raw = chunk.Content ?? "I apologize, but I couldn't generate a response.";
                        // **bold** to <strong>
                        var processed = Regex.Replace(raw, @"\*\*(.+?)\*\*", "<strong>$1</strong>");
                        // 'name' to <strong>name</strong>
                        processed = Regex.Replace(processed, "'([^']+)'", m => $"<strong>{m.Groups[1].Value}</strong>");
                        assistantMessage.Content = processed;
                        assistantMessage.Data = chunk.Data;
                        assistantMessage.IsStreaming = false;
                        await InvokeAsync(StateHasChanged);
                    }
                });

            // Update assistant message metadata after full response received
            assistantMessage.Intent = response.DetectedIntent;
            assistantMessage.Suggestions = response.Suggestions;
            assistantMessage.Success = response.Success;
            assistantMessage.ExecutionTime = (DateTime.Now - startTime).TotalSeconds;

            // Handle navigation responses
            if (response.DetectedIntent == IntentType.Navigation && response.Success && response.Data != null)
            {
                await HandleNavigationResponse(response.Data);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error processing AI request: {Input}", input);
            errorMessage = "A technical error occurred. Please try again.";
        }
        finally
        {
            isProcessing = false;
            StateHasChanged();
            await FocusInput();
        }
    }

    private Dictionary<string, object> BuildContext()
    {
        return new Dictionary<string, object>
        {
            ["conversationLength"] = messages.Count,
            ["lastIntent"] = messages.LastOrDefault(m => m.Type == "assistant")?.Intent.ToString() ?? "None",
            ["sessionDuration"] = DateTime.Now.Subtract(messages.FirstOrDefault()?.Timestamp ?? DateTime.Now).TotalMinutes
        };
    }

    private async Task ExecuteQuickAction(string command)
    {
        currentInput = command;
        await SendMessage();
    }

    private async Task HandleSuggestion(string suggestion)
    {
        currentInput = suggestion;
        await SendMessage();
    }

    private async Task HandleKeyDown(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            if (e.CtrlKey || e.MetaKey)
            {
                await SendMessage();
            }
            else if (!e.ShiftKey)
            {
                await SendMessage();
            }
        }
    }

    private void OnInputChange(ChangeEventArgs e)
    {
        currentInput = e.Value?.ToString() ?? string.Empty;
    }

    private void ToggleDataExpansion(ChatMessage message)
    {
        message.IsDataExpanded = !message.IsDataExpanded;
    }

    private void DismissError()
    {
        errorMessage = string.Empty;
    }

    private async Task ClearConversation()
    {
        messages.Clear();
        sessionId = Guid.NewGuid().ToString();
        await InitializeConversation();
        StateHasChanged();
    }

    private async Task ExportConversation()
    {
        // TODO: Implement conversation export functionality
        await JS.InvokeVoidAsync("alert", "Export functionality coming soon!");
    }

    private async Task FocusInput()
    {
        try
        {
            await inputElement.FocusAsync();
        }
        catch
        {
            // Ignore focus errors
        }
    }

    private async Task ScrollToBottom()
    {
        try
        {
            await JS.InvokeVoidAsync("scrollToBottom", messagesContainer);
        }
        catch
        {
            // Ignore scrolling errors
        }
    }

    private string GetIntentClass(IntentType intent) => intent switch
    {
        IntentType.AgentAction => "intent-action",
        IntentType.DatabaseQuery => "intent-query",
        IntentType.GeneralAI => "intent-general",
        IntentType.Navigation => "intent-navigation",
        _ => "intent-unknown"
    };

    private string GetIntentIcon(IntentType intent) => intent switch
    {
        IntentType.AgentAction => "fa-cogs",
        IntentType.DatabaseQuery => "fa-database",
        IntentType.GeneralAI => "fa-brain",
        IntentType.Navigation => "fa-compass",
        _ => "fa-question"
    };

    private string GetCharCounterClass()
    {
        var length = currentInput?.Length ?? 0;
        return length switch
        {
            > 1900 => "danger",
            > 1800 => "warning",
            _ => "normal"
        };
    }

    private async Task ToggleVoiceRecording()
    {
        if (isRecording)
        {
            await StopVoiceRecording();
        }
        else
        {
            await StartVoiceRecording();
        }
    }

    private async Task StartVoiceRecording()
    {
        try
        {
            var hasPermission = await JS.InvokeAsync<bool>("startVoiceRecording");
            if (hasPermission)
            {
                isRecording = true;
                errorMessage = string.Empty;
                StateHasChanged();
            }
            else
            {
                errorMessage = "Microphone permission denied. Please allow microphone access and try again.";
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error starting voice recording");
            errorMessage = "Unable to start voice recording. Please check your microphone settings.";
            StateHasChanged();
        }
    }

    private async Task StopVoiceRecording()
    {
        try
        {
            isRecording = false;
            isTranscribing = true;
            StateHasChanged();

            var base64Audio = await JS.InvokeAsync<string>("stopVoiceRecording");
            
            if (!string.IsNullOrWhiteSpace(base64Audio))
            {
                // Convert base64 string back to byte array
                var audioData = Convert.FromBase64String(base64Audio);
                
                var transcription = await VoiceRecordingService.TranscribeAudioAsync(audioData);
                
                if (!string.IsNullOrWhiteSpace(transcription) && !transcription.StartsWith("Audio validation failed") && !transcription.StartsWith("Sorry, I couldn't process"))
                {
                    currentInput = transcription;
                    await SendMessage();
                }
                else
                {
                    errorMessage = transcription.StartsWith("Audio validation failed") || transcription.StartsWith("Sorry, I couldn't process") 
                        ? transcription 
                        : "No speech detected. Please try speaking more clearly.";
                }
            }
            else
            {
                errorMessage = "No audio data received. Please try recording again.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error processing voice recording");
            errorMessage = "Error processing voice recording. Please try again.";
        }
        finally
        {
            isTranscribing = false;
            StateHasChanged();
        }
    }

    private async Task HandleNavigationResponse(object navigationData)
    {
        try
        {
            var dataString = JsonSerializer.Serialize(navigationData);
            var jsonData = JsonSerializer.Deserialize<JsonElement>(dataString);

            if (jsonData.TryGetProperty("NavigationUrl", out var urlElement))
            {
                var navigationUrl = urlElement.GetString();
                
                // Apply state changes if specified
                if (jsonData.TryGetProperty("StateChanges", out var stateElement))
                {
                    foreach (var prop in stateElement.EnumerateObject())
                    {
                        var value = prop.Value.GetString();
                        if (!string.IsNullOrEmpty(value))
                        {
                            switch (prop.Name)
                            {
                                case "ClientTab":
                                    StateService.ClientTab = value;
                                    break;
                                case "HtmlTab":
                                    StateService.HtmlTab = value;
                                    break;
                            }
                        }
                    }
                }

                // Navigate to the URL
                if (!string.IsNullOrEmpty(navigationUrl))
                {
                    Logger.LogInformation("[Navigation] Navigating to: {Url}", navigationUrl);
                    Navigation.NavigateTo(navigationUrl);
                }
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "[Navigation] Error handling navigation response");
        }
    }

    private RenderFragment RenderDataResults(object data)
    {
        return builder =>
        {
            builder.OpenElement(0, "div");
            builder.AddAttribute(1, "class", "json-data");
            builder.OpenElement(2, "pre");
            builder.AddContent(3, JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true }));
            builder.CloseElement();
            builder.CloseElement();
        };
    }

    private async Task CheckForPendingVoiceTranscription()
    {
        try
        {
            // Prevent duplicate processing
            if (isProcessingVoiceTranscription)
                return;

            // Check if there's a pending transcription from push-to-talk
            var pendingTranscription = await JS.InvokeAsync<string>("sessionStorage.getItem", "pendingVoiceTranscription");
            if (!string.IsNullOrWhiteSpace(pendingTranscription))
            {
                isProcessingVoiceTranscription = true;
                Logger.LogInformation("[VoiceTranscription] Processing pending transcription: {Length} characters", pendingTranscription.Length);

                // Clear the stored transcription
                await JS.InvokeVoidAsync("sessionStorage.removeItem", "pendingVoiceTranscription");
                
                // Set the input and send the message
                currentInput = pendingTranscription;
                StateHasChanged();
                await Task.Delay(100); // Small delay to ensure UI updates
                await SendMessage();
                
                isProcessingVoiceTranscription = false;
            }

            // Check for errors
            var pendingError = await JS.InvokeAsync<string>("sessionStorage.getItem", "voiceTranscriptionError");
            if (!string.IsNullOrWhiteSpace(pendingError))
            {
                // Clear the stored error
                await JS.InvokeVoidAsync("sessionStorage.removeItem", "voiceTranscriptionError");
                
                // Display the error
                errorMessage = pendingError;
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error checking for pending voice transcription");
            isProcessingVoiceTranscription = false;
        }
    }

    private async Task SetupVoiceTranscriptionListeners()
    {
        try
        {
            await JS.InvokeVoidAsync("setupVoiceTranscriptionListeners", 
                DotNetObjectReference.Create(this));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error setting up voice transcription listeners");
        }
    }

    [JSInvokable]
    public async Task HandleVoiceTranscription(string transcription)
    {
        try
        {
            // Prevent duplicate processing
            if (isProcessingVoiceTranscription || string.IsNullOrWhiteSpace(transcription))
                return;

            isProcessingVoiceTranscription = true;
            Logger.LogInformation("[VoiceTranscription] Processing transcription: {Length} characters", transcription.Length);

            currentInput = transcription;
            StateHasChanged();
            await Task.Delay(100); // Small delay to ensure UI updates
            await SendMessage();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error handling voice transcription");
        }
        finally
        {
            isProcessingVoiceTranscription = false;
        }
    }

    [JSInvokable]
    public void HandleVoiceTranscriptionError(string error)
    {
        try
        {
            errorMessage = error;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error handling voice transcription error");
        }
    }

    public class ChatMessage
    {
        public string Type { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public IntentType Intent { get; set; } = IntentType.Unknown;
        public List<string>? Suggestions { get; set; }
        public object? Data { get; set; }
        public bool IsStreaming { get; set; }
        public bool Success { get; set; } = true;
        public double ExecutionTime { get; set; }
        public bool IsDataExpanded { get; set; }
    }

    public class QuickAction
    {
        public string Label { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Command { get; set; } = string.Empty;
    }
}

<style>
    .voice-btn.recording {
        background-color: #e74c3c !important;
        animation: pulse 1.5s infinite;
    }
    
    .voice-btn.recording:hover {
        background-color: #c0392b !important;
    }
    
    @@keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(231, 76, 60, 0); }
        100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
    }
    
    .transcribing-indicator {
        display: inline-flex;
        align-items: center;
        color: #3498db;
        font-size: 0.9em;
        margin-left: 8px;
    }
    
    .transcribing-indicator .spinner {
        width: 16px;
        height: 16px;
        border: 2px solid #ecf0f1;
        border-top: 2px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 6px;
    }
    
    .intent-navigation {
        background-color: #9b59b6;
        color: white;
    }
    
    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<script>
    window.scrollToBottom = (element) => {
        if (element) {
            element.scrollTop = element.scrollHeight;
        }
    };

    let mediaRecorder;
    let audioChunks = [];
    let audioStream;

    window.startVoiceRecording = async function() {
        try {
            // Request microphone permission
            audioStream = await navigator.mediaDevices.getUserMedia({ 
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                } 
            });
            
            // Reset audio chunks for new recording
            audioChunks = [];
            
            // Create MediaRecorder with WebM format (widely supported)
            const options = { mimeType: 'audio/webm' };
            if (!MediaRecorder.isTypeSupported(options.mimeType)) {
                // Fallback to default format
                mediaRecorder = new MediaRecorder(audioStream);
            } else {
                mediaRecorder = new MediaRecorder(audioStream, options);
            }
            
            mediaRecorder.ondataavailable = function(event) {
                if (event.data.size > 0) {
                    audioChunks.push(event.data);
                }
            };
            
            mediaRecorder.start(1000); // Collect data every second
            return true;
        } catch (error) {
            console.error('Error starting voice recording:', error);
            return false;
        }
    };

    window.stopVoiceRecording = function() {
        return new Promise((resolve, reject) => {
            if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                mediaRecorder.onstop = async function() {
                    try {
                        // Stop all audio tracks
                        if (audioStream) {
                            audioStream.getTracks().forEach(track => track.stop());
                        }
                        
                        // Create blob from recorded chunks
                        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                        
                        // Convert blob to base64 string for safer transfer
                        const reader = new FileReader();
                        reader.onloadend = function() {
                            const base64String = reader.result.split(',')[1]; // Remove data:audio/webm;base64,
                            resolve(base64String);
                        };
                        reader.onerror = function() {
                            reject(new Error('Failed to convert audio to base64'));
                        };
                        reader.readAsDataURL(audioBlob);
                    } catch (error) {
                        console.error('Error processing audio:', error);
                        reject(error);
                    }
                };
                
                mediaRecorder.stop();
            } else {
                resolve('');
            }
        });
    };

    // Setup event listeners for push-to-talk voice transcription
    window.setupVoiceTranscriptionListeners = function(dotNetRef) {
        // Listen for voice transcription ready event
        window.addEventListener('voiceTranscriptionReady', function(event) {
            if (event.detail && event.detail.transcription) {
                dotNetRef.invokeMethodAsync('HandleVoiceTranscription', event.detail.transcription);
            }
        });

        // Listen for voice transcription error event
        window.addEventListener('voiceTranscriptionError', function(event) {
            if (event.detail && event.detail.error) {
                dotNetRef.invokeMethodAsync('HandleVoiceTranscriptionError', event.detail.error);
            }
        });
    };
</script>