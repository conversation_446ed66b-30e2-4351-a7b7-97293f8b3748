# Phase 1: Critical Threading Fixes - Implementation Guide

## Immediate Actions Required (Fix These First)

### 1. StateService Thread Safety Fix

**File**: `Domain/Shared/Services/StateService.cs`

**Current Problem**:
```csharp
private Dictionary<string, int> _unreadSmsCountsByPhone = new();
```

**Fix Implementation**:
```csharp
// Replace the Dictionary with ConcurrentDictionary
private readonly ConcurrentDictionary<string, int> _unreadSmsCountsByPhone = new();
private readonly object _smsCountLock = new object();

// Update all methods to use thread-safe operations
public void UpdateUnconfirmedSmsCounts(Dictionary<string, int> unconfirmedCounts)
{
    lock (_smsCountLock)
    {
        _unreadSmsCountsByPhone.Clear();
        foreach (var kvp in unconfirmedCounts)
        {
            _unreadSmsCountsByPhone[kvp.Key] = kvp.Value;
        }
        
        _unreadSmsMessageCount = _unreadSmsCountsByPhone.Values.Sum();
    }
    OnUnreadSmsMessageCountChanged?.Invoke();
}

public int GetUnreadSmsCountForPhone(string phoneNumber)
{
    return _unreadSmsCountsByPhone.TryGetValue(phoneNumber, out int count) ? count : 0;
}

public Dictionary<string, int> GetUnconfirmedSmsCounts()
{
    lock (_smsCountLock)
    {
        return new Dictionary<string, int>(_unreadSmsCountsByPhone);
    }
}
```

### 2. ChopperMessaging Collections Thread Safety

**File**: `Domain/Chat/Components/ChopperMessaging.razor`

**Current Problems**:
```csharp
private List<SmsMessageEntity> smsMessages = new();
private ObservableCollection<MessageItem> messages = new();
private Dictionary<string, DateTime> optimisticMessageTimes = new();
```

**Fix Implementation**:
```csharp
// Thread-safe backing stores
private readonly ConcurrentQueue<MessageItem> _staffMessageQueue = new();
private readonly object _smsMessagesLock = new object();
private readonly object _uiUpdateLock = new object();
private readonly ConcurrentDictionary<string, DateTime> _optimisticMessageTimes = new();

// UI-only collections (only access from UI thread)
private List<SmsMessageEntity> _uiSmsMessages = new();
private ObservableCollection<MessageItem> _uiStaffMessages = new();

// Thread-safe methods for updating from background threads
private async Task AddStaffMessageThreadSafe(MessageItem message)
{
    _staffMessageQueue.Enqueue(message);
    
    if (!isDisposed)
    {
        await InvokeAsync(() =>
        {
            // Copy from queue to UI collection on UI thread
            var messagesToAdd = new List<MessageItem>();
            while (_staffMessageQueue.TryDequeue(out var msg))
            {
                messagesToAdd.Add(msg);
            }
            
            foreach (var msg in messagesToAdd)
            {
                _uiStaffMessages.Add(msg);
            }
            
            StateHasChanged();
        });
    }
}

private async Task UpdateSmsMessagesThreadSafe(List<SmsMessageEntity> newMessages)
{
    if (!isDisposed)
    {
        await InvokeAsync(() =>
        {
            lock (_smsMessagesLock)
            {
                _uiSmsMessages.Clear();
                _uiSmsMessages.AddRange(newMessages.OrderBy(m => m.Timestamp));
            }
            StateHasChanged();
        });
    }
}
```

### 3. Fix Async Void Handler

**File**: `Domain/Chat/Services/SmsBackgroundService.cs`

**Current Problem**:
```csharp
private async void OnNewSmsMessage(SmsMessage message, StateService stateService)
```

**Fix Implementation**:
```csharp
private async Task OnNewSmsMessage(SmsMessage message, StateService stateService)
{
    try
    {
        _logger.LogInformation($"Processing new SMS message - ID: {message.Id}");
        
        if (message.IsInbound)
        {
            using var scope = _serviceProvider.CreateScope();
            var smsMessageService = scope.ServiceProvider.GetRequiredService<SmsMessageService>();
            var storedMessage = await smsMessageService.StoreSmsMessageAsync(message);
            
            if (storedMessage != null)
            {
                var unconfirmedCounts = await smsMessageService.GetUnconfirmedCountsByPhoneAsync();
                stateService.UpdateUnconfirmedSmsCounts(unconfirmedCounts);
                
                await _hubContext.Clients.All.SendAsync("ReceiveSmsMessage", message);
                
                var normalizedPhone = StringHelper.NormalizePhoneNumber(message.PhoneNumber);
                await _hubContext.Clients.Group($"SmsChat_{normalizedPhone}").SendAsync("ReceiveSmsMessage", message);
            }
        }
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error processing new SMS message {MessageId}", message.Id);
        // Don't rethrow - log and continue
    }
}

// Update the calling code to properly await
private async Task CheckForNewSmsMessagesAsync()
{
    try
    {
        using var scope = _serviceProvider.CreateScope();
        var chatService = scope.ServiceProvider.GetRequiredService<ChatService>();
        var stateService = scope.ServiceProvider.GetRequiredService<StateService>();

        var newMessages = await chatService.GetNewSmsMessagesSinceAsync(_lastCheckTime);
        
        // Process all messages in parallel but wait for completion
        var processingTasks = newMessages
            .Where(m => m.IsInbound)
            .Select(message => OnNewSmsMessage(message, stateService));
            
        await Task.WhenAll(processingTasks);
        
        _lastCheckTime = DateTime.UtcNow;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error checking for new SMS messages");
    }
}
```

### 4. SignalR Group Management Fix

**File**: `Domain/Chat/Components/ChopperMessaging.razor`

**Current Problem**: No cleanup of old groups

**Fix Implementation**:
```csharp
private string? _currentSmsGroup = null;
private readonly object _groupLock = new object();

private async Task JoinSmsGroup(string phoneNumber)
{
    if (hubConnection?.State == HubConnectionState.Connected)
    {
        try
        {
            var normalizedPhone = StringHelper.NormalizePhoneNumber(phoneNumber);
            var newGroup = $"SmsChat_{normalizedPhone}";
            
            lock (_groupLock)
            {
                // Leave old group first
                if (_currentSmsGroup != null && _currentSmsGroup != newGroup)
                {
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await hubConnection.SendAsync("LeaveSmsChat", _currentSmsGroup.Replace("SmsChat_", ""));
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error leaving SMS group {_currentSmsGroup}: {ex.Message}");
                        }
                    });
                }
                
                _currentSmsGroup = newGroup;
            }
            
            // Join new group
            await hubConnection.SendAsync("JoinSmsChat", normalizedPhone);
            Console.WriteLine($"Joined SMS group: {newGroup}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error joining SMS chat group: {ex.Message}");
        }
    }
}

// Update component disposal
public async ValueTask DisposeAsync()
{
    isDisposed = true;
    smsRefreshTimer?.Dispose();
    
    if (hubConnection != null)
    {
        try
        {
            if (hubConnection.State == HubConnectionState.Connected)
            {
                await hubConnection.SendAsync("LeaveStaffChat");
                
                // Leave current SMS group
                lock (_groupLock)
                {
                    if (_currentSmsGroup != null)
                    {
                        var phoneNumber = _currentSmsGroup.Replace("SmsChat_", "");
                        _ = hubConnection.SendAsync("LeaveSmsChat", phoneNumber);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error during cleanup: {ex.Message}");
        }
        
        try
        {
            await hubConnection.DisposeAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error disposing SignalR connection: {ex.Message}");
        }
    }
}
```

### 5. Timer Thread Safety

**File**: `Domain/Chat/Components/ChopperMessaging.razor`

**Current Problem**: Timer callbacks can run after disposal

**Fix Implementation**:
```csharp
private readonly object _timerLock = new object();
private volatile bool _timerDisposed = false;

private async Task RefreshSmsMessagesIfNeeded()
{
    // Early exit checks
    if (_timerDisposed || isDisposed || !_smsChatVisible || !isComponentRendered)
        return;

    try
    {
        lock (_timerLock)
        {
            if (_timerDisposed) return;
        }
        
        if (DateTime.Now - lastSmsRefresh >= SmsRefreshInterval)
        {
            lastSmsRefresh = DateTime.Now;
            
            await InvokeAsync(async () =>
            {
                if (!isDisposed && !_timerDisposed && _smsChatVisible)
                {
                    await LoadSmsMessages();
                    CleanupOptimisticMessages();
                }
            });
        }
    }
    catch (Exception ex)
    {
        if (!_timerDisposed && !isDisposed)
        {
            Console.WriteLine($"Error in SMS auto-refresh: {ex.Message}");
        }
    }
}

private void StartSmsRefreshTimer()
{
    lock (_timerLock)
    {
        if (!_timerDisposed)
        {
            smsRefreshTimer = new Timer(async _ => await RefreshSmsMessagesIfNeeded(), 
                null, SmsRefreshInterval, SmsRefreshInterval);
        }
    }
}

// Update disposal
public async ValueTask DisposeAsync()
{
    isDisposed = true;
    
    lock (_timerLock)
    {
        _timerDisposed = true;
        smsRefreshTimer?.Dispose();
        smsRefreshTimer = null;
    }
    
    // ... rest of disposal logic
}
```

## Testing the Fixes

### 1. Thread Safety Test
Create a simple test to verify collections don't throw under concurrent access:

```csharp
[Test]
public async Task StateService_ConcurrentAccess_NoExceptions()
{
    var stateService = new StateService(/* dependencies */);
    var tasks = new List<Task>();
    
    // Simulate concurrent updates
    for (int i = 0; i < 100; i++)
    {
        var phoneNumber = $"+1555123{i:D4}";
        tasks.Add(Task.Run(() => 
        {
            var counts = new Dictionary<string, int> { [phoneNumber] = i };
            stateService.UpdateUnconfirmedSmsCounts(counts);
        }));
        
        tasks.Add(Task.Run(() => 
        {
            var count = stateService.GetUnreadSmsCountForPhone(phoneNumber);
        }));
    }
    
    await Task.WhenAll(tasks);
    // Should complete without exceptions
}
```

### 2. DbContext Test
Verify no re-entrancy issues:

```csharp
[Test]
public async Task SmsMessageService_ConcurrentOperations_NoReentrancy()
{
    var service = new SmsMessageService(dbContextFactory, stateService);
    var message = new SmsMessage { /* test data */ };
    
    var tasks = new List<Task>();
    for (int i = 0; i < 10; i++)
    {
        tasks.Add(service.StoreSmsMessageAsync(message));
        tasks.Add(service.GetUnconfirmedCountAsync("+15551234567"));
    }
    
    await Task.WhenAll(tasks);
    // Should complete without "second operation" errors
}
```

## Deployment Strategy

1. **Deploy fixes in order**: StateService → ChopperMessaging → BackgroundService → SignalR
2. **Monitor logs** for threading exceptions after each deployment
3. **Load test** with multiple concurrent users
4. **Rollback plan**: Keep the original code in branches for quick revert if needed

## Success Metrics

- Zero "Collection was modified" exceptions in logs
- Zero "A second operation was started" DbContext errors
- No unhandled exceptions from background services
- Stable memory usage under load
- SMS unread counts working correctly

These fixes address the most critical threading issues that can crash the application. Once these are stable, we can proceed with the architectural improvements in the subsequent phases. 