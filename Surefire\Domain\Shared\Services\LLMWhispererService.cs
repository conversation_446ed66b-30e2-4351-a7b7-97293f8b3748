using System;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Surefire.Domain.Attachments.Models;
using Surefire.Domain.Attachments.Services;
using Surefire.Domain.Logs;

namespace Surefire.Domain.Shared.Services
{
    public class LLMWhispererService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<LLMWhispererService> _logger;
        private readonly ILoggingService _loggingService;
        private readonly AttachmentService _attachmentService;
        private readonly HttpClient _httpClient;
        private readonly string _apiUrl;
        private readonly string _apiKey;
        private const int MaxRetries = 30; // 5 minutes with 10-second intervals
        private const int RetryIntervalSeconds = 10;

        public LLMWhispererService(
            IConfiguration configuration,
            ILogger<LLMWhispererService> logger,
            ILoggingService loggingService,
            AttachmentService attachmentService,
            IHttpClientFactory httpClientFactory)
        {
            _configuration = configuration;
            _logger = logger;
            _loggingService = loggingService;
            _attachmentService = attachmentService;
            _httpClient = httpClientFactory.CreateClient("LLMWhisperer");

            // Get configuration values
            _apiKey = Environment.GetEnvironmentVariable("LLMWHISPERER_API_KEY") 
                     ?? _configuration["LLMWhisperer:ApiKey"];
            _apiUrl = Environment.GetEnvironmentVariable("LLMWHISPERER_API_URL") 
                     ?? _configuration["LLMWhisperer:ApiUrl"];

            if (string.IsNullOrEmpty(_apiKey) || string.IsNullOrEmpty(_apiUrl))
            {
                throw new InvalidOperationException("LLMWhisperer API key and URL must be configured");
            }

            // Ensure API URL doesn't end with slash for consistent URL building
            _apiUrl = _apiUrl.TrimEnd('/');
        }

        /// <summary>
        /// Processes a PDF attachment using LLMWhisperer and saves the extracted text as a TXT file
        /// </summary>
        /// <param name="attachment">The PDF attachment to process</param>
        /// <param name="mode">The mode to use for processing</param>
        /// <param name="attachmentGroupId">The ID of the attachment group</param>
        /// <returns>The created TXT attachment</returns>
        public async Task<Attachment> ProcessPdfAttachmentAsync(Attachment attachment, string mode = null, int? attachmentGroupId = null)
        {
            if (attachment == null)
                throw new ArgumentNullException(nameof(attachment));

            if (!attachment.FileFormat?.ToLower().EndsWith(".pdf") == true)
                throw new ArgumentException("Attachment must be a PDF file", nameof(attachment));

            await _loggingService.LogAsync(LogLevel.Information, 
                $"Starting LLMWhisperer processing for attachment: {attachment.OriginalFileName}", 
                "LLMWhispererService");

            try
            {
                // Step 1: Process PDF with LLMWhisperer
                var extractedText = await ProcessPdfWithLLMWhisperer(attachment, mode);

                // Step 2: Save extracted text as TXT file
                var txtAttachment = await SaveTextData(extractedText, attachment, attachmentGroupId);

                await _loggingService.LogAsync(LogLevel.Information, 
                    $"Successfully processed PDF and created TXT attachment for: {attachment.OriginalFileName}", 
                    "LLMWhispererService");

                return txtAttachment;
            }
            catch (Exception ex)
            {
                await _loggingService.LogAsync(LogLevel.Error, 
                    $"Error processing PDF attachment {attachment.OriginalFileName}: {ex.Message}", 
                    "LLMWhispererService", ex);
                throw;
            }
        }

        private async Task<string> ProcessPdfWithLLMWhisperer(Attachment attachment, string mode = null)
        {
            await _loggingService.LogAsync(LogLevel.Information, 
                $"Sending PDF to LLMWhisperer: {attachment.OriginalFileName}", 
                "LLMWhispererService");

            // Build the full file path
            var rootPath = Directory.GetCurrentDirectory();
            var filePath = Path.Combine(rootPath, "wwwroot", attachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()), attachment.HashedFileName);

            await _loggingService.LogAsync(LogLevel.Information, 
                $"Looking for PDF file at: {filePath}", 
                "LLMWhispererService");
            
            await _loggingService.LogAsync(LogLevel.Information, 
                $"Attachment details - LocalPath: {attachment.LocalPath}, HashedFileName: {attachment.HashedFileName}, OriginalFileName: {attachment.OriginalFileName}", 
                "LLMWhispererService");

            if (!File.Exists(filePath))
            {
                // Try to list files in the directory to help debug
                var directory = Path.GetDirectoryName(filePath);
                if (Directory.Exists(directory))
                {
                    var filesInDir = Directory.GetFiles(directory);
                    await _loggingService.LogAsync(LogLevel.Information, 
                        $"Files in directory {directory}: {string.Join(", ", filesInDir.Select(Path.GetFileName))}", 
                        "LLMWhispererService");
                }
                else
                {
                    await _loggingService.LogAsync(LogLevel.Error, 
                        $"Directory does not exist: {directory}", 
                        "LLMWhispererService");
                }
                
                throw new FileNotFoundException($"PDF file not found: {filePath}");
            }

            // Read the PDF file into memory first
            byte[] fileBytes = await File.ReadAllBytesAsync(filePath);
            
            await _loggingService.LogAsync(LogLevel.Information, 
                $"Read PDF file: {fileBytes.Length} bytes", 
                "LLMWhispererService");

            // Build the query string parameters
            var queryParams = new Dictionary<string, string>
            {
                { "mode", mode ?? "high_quality" },
                { "output_mode", "layout_preserving" }
            };
            var queryString = string.Join("&", queryParams.Select(kvp => $"{kvp.Key}={Uri.EscapeDataString(kvp.Value)}"));

            // Build the submit URL with query string (use full URL like the CLI version)
            var submitUrl = $"{_apiUrl}/whisper?{queryString}";
            
            await _loggingService.LogAsync(LogLevel.Information, 
                $"Submitting PDF to LLMWhisperer API at: {submitUrl}", 
                "LLMWhispererService");

            await _loggingService.LogAsync(LogLevel.Information, 
                $"Using API Key: {_apiKey}", 
                "LLMWhispererService");

            try
            {
                // Clear any existing headers and set the required ones
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("unstract-key", _apiKey);

                // Prepare the content using ByteArrayContent
                using var fileContent = new ByteArrayContent(fileBytes);
                fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/octet-stream");

                await _loggingService.LogAsync(LogLevel.Information, 
                    $"Request headers: {string.Join(", ", _httpClient.DefaultRequestHeaders.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}"))}",
                    "LLMWhispererService");

                await _loggingService.LogAsync(LogLevel.Information, 
                    $"Sending PDF as application/octet-stream, length: {fileBytes.Length}", 
                    "LLMWhispererService");

                // Send request to LLMWhisperer API
                var response = await _httpClient.PostAsync(submitUrl, fileContent);
                var responseContent = await response.Content.ReadAsStringAsync();
                
                await _loggingService.LogAsync(LogLevel.Information, 
                    $"Submit response status: {response.StatusCode}", 
                    "LLMWhispererService");

                await _loggingService.LogAsync(LogLevel.Information, 
                    $"Submit response headers: {string.Join(", ", response.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}"))}",
                    "LLMWhispererService");

                await _loggingService.LogAsync(LogLevel.Information, 
                    $"Submit response content: {responseContent}", 
                    "LLMWhispererService");
                
                if (!response.IsSuccessStatusCode)
                {
                    await _loggingService.LogAsync(LogLevel.Error, 
                        $"LLMWhisperer API error - Status: {response.StatusCode}, Response: {responseContent}", 
                        "LLMWhispererService");
                }
                
                response.EnsureSuccessStatusCode();

                var initialResponse = JsonSerializer.Deserialize<WhisperResponse>(responseContent);
                if (initialResponse == null || string.IsNullOrEmpty(initialResponse.WhisperHash))
                {
                    throw new Exception($"Failed to parse initial response from LLMWhisperer API. Response: {responseContent}");
                }

                await _loggingService.LogAsync(LogLevel.Information, 
                    $"Extraction job started with hash: {initialResponse.WhisperHash}", 
                    "LLMWhispererService");

                // Poll for completion
                for (int i = 0; i < MaxRetries; i++)
                {
                    await Task.Delay(TimeSpan.FromSeconds(RetryIntervalSeconds));

                    var statusUrl = $"{_apiUrl}/whisper-status?whisper_hash={initialResponse.WhisperHash}";
                    
                    await _loggingService.LogAsync(LogLevel.Information, 
                        $"Polling LLMWhisperer status (attempt {i + 1}/{MaxRetries}) at: {statusUrl}", 
                        "LLMWhispererService");

                    await _loggingService.LogAsync(LogLevel.Information, 
                        $"Request headers: {string.Join(", ", _httpClient.DefaultRequestHeaders.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}"))}",
                        "LLMWhispererService");

                    var statusResponse = await _httpClient.GetAsync(statusUrl);
                    var statusContent = await statusResponse.Content.ReadAsStringAsync();

                    await _loggingService.LogAsync(LogLevel.Information, 
                        $"Status response status: {statusResponse.StatusCode}", 
                        "LLMWhispererService");

                    await _loggingService.LogAsync(LogLevel.Information, 
                        $"Status response headers: {string.Join(", ", statusResponse.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}"))}",
                        "LLMWhispererService");

                    await _loggingService.LogAsync(LogLevel.Information, 
                        $"Status response content: {statusContent}", 
                        "LLMWhispererService");

                    if (!statusResponse.IsSuccessStatusCode)
                    {
                        await _loggingService.LogAsync(LogLevel.Error, 
                            $"Status check failed with status code {statusResponse.StatusCode}. Response: {statusContent}", 
                            "LLMWhispererService");
                        throw new Exception($"Status check failed: {statusResponse.StatusCode} - {statusContent}");
                    }

                    var status = JsonSerializer.Deserialize<WhisperStatusResponse>(statusContent);
                    if (status == null)
                    {
                        throw new Exception($"Failed to parse status response from LLMWhisperer API. Response: {statusContent}");
                    }

                    await _loggingService.LogAsync(LogLevel.Information, 
                        $"Job status: {status.Status}", 
                        "LLMWhispererService");

                    if (status.Status == "processed" || status.Status == "retrieved")
                    {
                        // Get the final result
                        var resultUrl = $"{_apiUrl}/whisper-retrieve?whisper_hash={initialResponse.WhisperHash}&text_only=true";
                        
                        await _loggingService.LogAsync(LogLevel.Information, 
                            $"Retrieving final result from: {resultUrl}", 
                            "LLMWhispererService");

                        await _loggingService.LogAsync(LogLevel.Information, 
                            $"Request headers: {string.Join(", ", _httpClient.DefaultRequestHeaders.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}"))}",
                            "LLMWhispererService");

                        var resultResponse = await _httpClient.GetAsync(resultUrl);
                        var resultContent = await resultResponse.Content.ReadAsStringAsync();

                        await _loggingService.LogAsync(LogLevel.Information, 
                            $"Result response status: {resultResponse.StatusCode}", 
                            "LLMWhispererService");

                        await _loggingService.LogAsync(LogLevel.Information, 
                            $"Result response headers: {string.Join(", ", resultResponse.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}"))}",
                            "LLMWhispererService");

                        await _loggingService.LogAsync(LogLevel.Information, 
                            $"Result response content: {resultContent}", 
                            "LLMWhispererService");

                        if (!resultResponse.IsSuccessStatusCode)
                        {
                            await _loggingService.LogAsync(LogLevel.Error, 
                                $"Result retrieval failed with status code {resultResponse.StatusCode}. Response: {resultContent}", 
                                "LLMWhispererService");
                            throw new Exception($"Result retrieval failed: {resultResponse.StatusCode} - {resultContent}");
                        }

                        await _loggingService.LogAsync(LogLevel.Information, 
                            $"Successfully extracted text from PDF ({resultContent.Length} characters)", 
                            "LLMWhispererService");

                        return resultContent;
                    }
                    else if (status.Status == "failed")
                    {
                        throw new Exception($"Extraction failed: {status.Message}");
                    }
                }

                throw new Exception($"Extraction timed out after {MaxRetries * RetryIntervalSeconds} seconds");
            }
            catch (Exception ex)
            {
                await _loggingService.LogAsync(LogLevel.Error, 
                    $"Error processing PDF attachment {attachment.OriginalFileName}: {ex.Message}", 
                    "LLMWhispererService", ex);
                throw;
            }
        }

        private async Task<Attachment> SaveTextData(string textData, Attachment originalAttachment, int? attachmentGroupId = null)
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var baseFileName = Path.GetFileNameWithoutExtension(originalAttachment.OriginalFileName);
            var txtFileName = $"{baseFileName}_llmwhisperer_{timestamp}.txt";

            // Create the TXT attachment
            var txtAttachment = new Attachment
            {
                OriginalFileName = txtFileName,
                FileFormat = ".txt",
                FileSize = System.Text.Encoding.UTF8.GetByteCount(textData),
                DateCreated = DateTime.UtcNow,
                AttachmentGroupId = attachmentGroupId ?? originalAttachment.AttachmentGroupId,
                Description = $"LLMWhisperer extracted text from {originalAttachment.OriginalFileName}",
                RenewalId = originalAttachment.RenewalId,
                ClientId = originalAttachment.ClientId,
                IsClientAccessible = false,
                Status = 1,
                Comments = "ExtractedSupplementalText"
            };

            // Save the TXT file to the same directory as the original PDF
            var rootPath = Directory.GetCurrentDirectory();
            var targetDirectory = Path.Combine(rootPath, "wwwroot", originalAttachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()));
            
            if (!Directory.Exists(targetDirectory))
            {
                Directory.CreateDirectory(targetDirectory);
            }

            // Generate hashed filename for the TXT file
            var hash = Surefire.Domain.Shared.Helpers.StringHelper.GenerateFiveCharacterHash(Path.GetFileNameWithoutExtension(txtFileName));
            var hashedTxtFileName = $"{Path.GetFileNameWithoutExtension(txtFileName)}_{hash}.txt";
            var txtFilePath = Path.Combine(targetDirectory, hashedTxtFileName);

            // Write the text data to file
            await File.WriteAllTextAsync(txtFilePath, textData);

            // Update attachment properties
            txtAttachment.HashedFileName = hashedTxtFileName;
            txtAttachment.LocalPath = originalAttachment.LocalPath;

            // Save the attachment to database using AttachmentService
            await _attachmentService.SaveAttachmentDirectlyAsync(txtAttachment);

            await _loggingService.LogAsync(LogLevel.Information, 
                $"Saved extracted text to: {txtFilePath}", 
                "LLMWhispererService");

            return txtAttachment;
        }

        #region Response Models

        private class WhisperResponse
        {
            [JsonPropertyName("message")]
            public string Message { get; set; } = string.Empty;

            [JsonPropertyName("status")]
            public string Status { get; set; } = string.Empty;

            [JsonPropertyName("whisper_hash")]
            public string WhisperHash { get; set; } = string.Empty;
        }

        private class WhisperStatusResponse
        {
            [JsonPropertyName("detail")]
            public List<StatusDetail> Detail { get; set; } = new();

            [JsonPropertyName("message")]
            public string Message { get; set; } = string.Empty;

            [JsonPropertyName("status")]
            public string Status { get; set; } = string.Empty;

            public class StatusDetail
            {
                [JsonPropertyName("execution_time_in_seconds")]
                public int ExecutionTimeInSeconds { get; set; }

                [JsonPropertyName("message")]
                public string Message { get; set; } = string.Empty;

                [JsonPropertyName("page_no")]
                public int PageNo { get; set; }
            }
        }

        #endregion
    }
} 