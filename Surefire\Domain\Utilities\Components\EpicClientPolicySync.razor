@namespace Surefire.Domain.Utilities.Components
@using Surefire.Domain.Shared.Components
@using Surefire.Domain.Shared.Models
@using Surefire.Domain.Shared.Services
@using Surefire.Domain.Policies.Models
@using Surefire.Domain.Policies.Services
@using Surefire.Domain.Clients.Models
@using Surefire.Domain.Clients.Services
@using Surefire.Domain.Attachments.Models
@using Surefire.Domain.Attachments.Services
@using Surefire.Domain.Ember
@using Microsoft.FluentUI.AspNetCore.Components
@using System.Xml
@using System.Xml.Linq
@using System.Text.Json
@inject PolicyService PolicyService
@inject ClientService ClientService
@inject AttachmentService AttachmentService
@inject IMessageService MessageService
@inject EmberService EmberService
@inject StateService StateService
@inject IJSRuntime JS

<div class="epic-sync-container">
   

    <div class="epic-sync-header">
        <div class="main-head">Epic Policy Data Sync</div>
        <img src="/img/tutorialepicsync.png" class="img-fluid" />
    </div>
    <div class="message-bar-container">
        <FluentMessageBarProvider />
    </div>
    <div class="sync-status">
        @if (isProcessing)
        {
            <div class="processing-indicator">
                <FluentProgressRing Size="ProgressRingSize.Large" />
                <p class="status-text">@statusMessage</p>
            </div>
        }
        else if (!string.IsNullOrEmpty(lastSyncResult))
        {
            <div class="sync-result @(lastSyncSuccess ? "success" : "error")">
                @if (lastSyncSuccess)
                {
                    <FluentIcon Value="@(new Icons.Filled.Size24.CheckmarkCircle())" />
                }
                else
                {
                    <FluentIcon Value="@(new Icons.Filled.Size24.ErrorCircle())" />
                }
                <p>@lastSyncResult</p>
            </div>
        }
    </div>

    <div class="sync-actions">
        <FluentButton Appearance="Appearance.Accent"
                      OnClick="HandleSyncNow"
                      Disabled="@(isProcessing || !ClientId.HasValue)"
                      Style="transform: scale(1.75);">
            <FluentIcon Value="@(new Icons.Filled.Size24.ArrowSync())" Slot="start" Color="Color.Fill" />
            @(isProcessing ? "Syncing..." : "Sync Now")
        </FluentButton>

        @if (!string.IsNullOrEmpty(lastSyncResult) && !isProcessing)
        {
            <FluentButton Appearance="Appearance.Lightweight"
                          OnClick="ClearStatus"
                          Style="margin-left: 16px;">
                <FluentIcon Value="@(new Icons.Filled.Size20.Dismiss())" Slot="start" />
                Clear Status
            </FluentButton>
        }
    </div>

    @if (debugMessages.Any())
    {
        <div class="debug-panel">
            <div class="debug-header">
                <h4>Sync Details</h4>
                <FluentButton Appearance="Appearance.Lightweight"
                              OnClick="ClearDebugOutput"
                              Style="padding: 4px 8px; font-size: 12px;">
                    Clear
                </FluentButton>
            </div>
            <div class="debug-content">
                @foreach (var message in debugMessages.TakeLast(20))
                {
                    <div class="debug-message @message.Type.ToLower()">
                        <span class="debug-time">@message.Timestamp.ToString("HH:mm:ss")</span>
                        <span class="debug-text">@message.Message</span>
                    </div>
                }
            </div>
        </div>
    }
</div> 