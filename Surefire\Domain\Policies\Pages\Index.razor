﻿@page "/Policies"
@using Surefire.Domain.Policies.Models
@using Surefire.Domain.Policies.Services
@using Microsoft.EntityFrameworkCore
@inject PolicyService PolicyService

<_toolbar PolicyId="@PolicyId" PolicyNumber="@SelectedPolicyNumber" PageName="Policies" />

<div class="page-content-datagrid">
    <div class="fluent-data-grid">
        <FluentDataGrid Items="@FilteredPolicies" ResizableColumns="true" Pagination="@pagination" ShowHover="true" OnRowClick="@((FluentDataGridRow<Policy> row) => HandleRowClick(row))">
            <PropertyColumn Property="@(p => p.PolicyId)" Title="Policy Id" Width="60px" Sortable="true" />
            <PropertyColumn Property="@(p => p.PolicyNumber)" Title="Policy Number" Width="200px" Sortable="true" />
            <PropertyColumn Property="@(p => p.Product.LineNickname ?? "-")" Title="Line Type" />
            <PropertyColumn Property="@(p => p.Carrier.CarrierName ?? "-")" Title="Carrier Name" />
            <PropertyColumn Property="@(p => p.Wholesaler.CarrierName ?? "-")" Title="Wholesaler Name" />
            <PropertyColumn Property="@(p => p.EffectiveDate)" Title="Effective Date" Sortable="true" Format="MM/dd/yyyy" />
            <PropertyColumn Property="@(p => p.ExpirationDate)" Title="Expiration Date" Sortable="true" Format="MM/dd/yyyy" />
            <PropertyColumn Property="@(p => p.Client.Name)" Title="Client Name" Sortable="true" />
            <PropertyColumn Property="@(p => p.Premium)" Title="Premium" Sortable="true" Format="C2" />
        </FluentDataGrid>
        <div class="fluent-data-grid__bottombar">
            <div class="fluent-data-grid__pagination">
                <FluentPaginator State="@pagination" />
            </div>

            <div class="fluent-data-grid__search">
                <FluentSearch Placeholder="Search..." @bind-Value="SearchTerm" @oninput="HandleSearchInput" />
            </div>
        </div>
    </div>
</div>

@code {
    public IQueryable<Policy> PolicyData { get; set; }
    public IQueryable<Policy> FilteredPolicies { get; set; }
    public int PolicyId { get; set; }
    public string SelectedPolicyNumber { get; set; } = "";
    public string SearchTerm { get; set; } = string.Empty;
    PaginationState pagination = new PaginationState { ItemsPerPage = 18 };
    private System.Timers.Timer debounceTimer;
    bool SelectFromEntireRow = true;
    [CascadingParameter] public Action<string> UpdateHeader { get; set; }

    protected override async Task OnInitializedAsync()
    {
        UpdateHeader?.Invoke("Policies");
        PolicyData = PolicyService.GetAllPolicies();
        FilteredPolicies = PolicyData;
        debounceTimer = new System.Timers.Timer(300); // Debounce for 300ms
        debounceTimer.AutoReset = false; // Prevent multiple triggers
        debounceTimer.Elapsed += async (sender, e) =>
        {
            await InvokeAsync(ApplySearchFilter); // Apply the search filter after the debounce period
        };
    }

    private void HandleSearchInput(Microsoft.AspNetCore.Components.ChangeEventArgs e)
    {
        if (e.Value is string searchTerm)
        {
            SearchTerm = searchTerm;
            debounceTimer.Stop();
            debounceTimer.Start(); // Start the debounce timer
        }
    }

    private void ApplySearchFilter()
    {
        FilteredPolicies = string.IsNullOrWhiteSpace(SearchTerm)
        ? PolicyData
        : PolicyData.Where(p =>
              EF.Functions.Like(p.PolicyNumber.ToLower(), $"%{SearchTerm.ToLower()}%")
           || EF.Functions.Like(p.Client.Name.ToLower(), $"%{SearchTerm.ToLower()}%"));

        InvokeAsync(StateHasChanged); // Trigger UI refresh after filtering
    }

    private void HandleRowClick(FluentDataGridRow<Policy> row)
    {
        if (row != null && row.Item != null)
        {
            var selectedPolicy = row.Item;
            PolicyId = selectedPolicy.PolicyId;
            Navigation.NavigateTo($"/Policies/Details/{PolicyId}");
        }
    }
}
