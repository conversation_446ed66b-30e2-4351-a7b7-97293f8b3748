# Task Enhancements Implementation Plan

## Progress Update (as of [today's date])


## Updated Next Steps & Implementation Plan

### 1. Subtask UI & Logic Completion
- [ ] **Checkboxes:** Implement completion toggling for subtasks (update DB, UI, and parent task status if needed).
- [ ] **Context Menu:** Implement context menu for each subtask with actions: Set Goal Date, Set Assigned To, Edit, Delete. Use FluentUI/Syncfusion popover or menu.
- [ ] **Avatar:** Ensure assigned user avatar loads correctly (handle missing images gracefully).
- [ ] **Due Date:** Show relative due date (e.g., "Due in 6 days") using the helper Surefire.Domain.Shared.Helpers.FormatDateTimeDifference.

### 2. Task & Subtask Creation
- [ ] **Menu Items:** Add "New Task" and "New Subtask" to the "New" button/menu in renewal details. Ensure correct parent/child relationships on creation.
- [ ] **Dialog:** Use/create a dialog for subtask creation, with assignment and due date fields.

### 3. Assignment Improvements
- [ ] **Contextual Menu:** Add "Assign To" in the subtask context menu. Show user list in a submenu or dialog. Persist assignment and update UI immediately.
