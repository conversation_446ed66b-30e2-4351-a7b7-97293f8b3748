﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Surefire.Migrations
{
    /// <inheritdoc />
    public partial class SmsMessageEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SmsMessages",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RingCentralId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    PhoneNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Text = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Timestamp = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsInbound = table.Column<bool>(type: "bit", nullable: false),
                    ConfirmedBy = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true),
                    ConfirmedOn = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SmsMessages", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SmsMessages_AspNetUsers_ConfirmedBy",
                        column: x => x.ConfirmedBy,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SmsMessages_ConfirmedBy",
                table: "SmsMessages",
                column: "ConfirmedBy");

            migrationBuilder.CreateIndex(
                name: "IX_SmsMessages_PhoneNumber",
                table: "SmsMessages",
                column: "PhoneNumber");

            migrationBuilder.CreateIndex(
                name: "IX_SmsMessages_PhoneNumber_IsInbound_ConfirmedBy",
                table: "SmsMessages",
                columns: new[] { "PhoneNumber", "IsInbound", "ConfirmedBy" });

            migrationBuilder.CreateIndex(
                name: "IX_SmsMessages_RingCentralId",
                table: "SmsMessages",
                column: "RingCentralId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SmsMessages_Timestamp",
                table: "SmsMessages",
                column: "Timestamp");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SmsMessages");
        }
    }
}
