﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Surefire.Migrations
{
    /// <inheritdoc />
    public partial class ProperContactPhoneEmailNavsDupFix : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Contacts_EmailAddresses_PrimaryEmailEmailAddressId",
                table: "Contacts");

            migrationBuilder.DropForeignKey(
                name: "FK_Contacts_PhoneNumbers_PrimaryPhonePhoneNumberId",
                table: "Contacts");

            migrationBuilder.DropIndex(
                name: "IX_Contacts_PrimaryEmailEmailAddressId",
                table: "Contacts");

            migrationBuilder.DropIndex(
                name: "IX_Contacts_PrimaryPhonePhoneNumberId",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "PrimaryEmailEmailAddressId",
                table: "Contacts");

            migrationBuilder.DropColumn(
                name: "PrimaryPhonePhoneNumberId",
                table: "Contacts");

            migrationBuilder.CreateIndex(
                name: "IX_Contacts_PrimaryEmailId",
                table: "Contacts",
                column: "PrimaryEmailId");

            migrationBuilder.CreateIndex(
                name: "IX_Contacts_PrimaryPhoneId",
                table: "Contacts",
                column: "PrimaryPhoneId");

            migrationBuilder.AddForeignKey(
                name: "FK_Contacts_EmailAddresses_PrimaryEmailId",
                table: "Contacts",
                column: "PrimaryEmailId",
                principalTable: "EmailAddresses",
                principalColumn: "EmailAddressId",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Contacts_PhoneNumbers_PrimaryPhoneId",
                table: "Contacts",
                column: "PrimaryPhoneId",
                principalTable: "PhoneNumbers",
                principalColumn: "PhoneNumberId",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Contacts_EmailAddresses_PrimaryEmailId",
                table: "Contacts");

            migrationBuilder.DropForeignKey(
                name: "FK_Contacts_PhoneNumbers_PrimaryPhoneId",
                table: "Contacts");

            migrationBuilder.DropIndex(
                name: "IX_Contacts_PrimaryEmailId",
                table: "Contacts");

            migrationBuilder.DropIndex(
                name: "IX_Contacts_PrimaryPhoneId",
                table: "Contacts");

            migrationBuilder.AddColumn<int>(
                name: "PrimaryEmailEmailAddressId",
                table: "Contacts",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PrimaryPhonePhoneNumberId",
                table: "Contacts",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Contacts_PrimaryEmailEmailAddressId",
                table: "Contacts",
                column: "PrimaryEmailEmailAddressId");

            migrationBuilder.CreateIndex(
                name: "IX_Contacts_PrimaryPhonePhoneNumberId",
                table: "Contacts",
                column: "PrimaryPhonePhoneNumberId");

            migrationBuilder.AddForeignKey(
                name: "FK_Contacts_EmailAddresses_PrimaryEmailEmailAddressId",
                table: "Contacts",
                column: "PrimaryEmailEmailAddressId",
                principalTable: "EmailAddresses",
                principalColumn: "EmailAddressId");

            migrationBuilder.AddForeignKey(
                name: "FK_Contacts_PhoneNumbers_PrimaryPhonePhoneNumberId",
                table: "Contacts",
                column: "PrimaryPhonePhoneNumberId",
                principalTable: "PhoneNumbers",
                principalColumn: "PhoneNumberId");
        }
    }
}
