#region Usings Statements
using DotNetEnv;
using Surefire.Data;
using Surefire.Domain.Accounting.Services;
using Surefire.Domain.Agents.Interfaces;
using Surefire.Domain.Agents.Services;
using Surefire.Domain.Agents.TaskAgents;
using Surefire.Domain.Attachments.Services;
using Surefire.Domain.Carriers.Services;
using Surefire.Domain.Chat;
using Surefire.Domain.Chat.Services;
using Surefire.Domain.Clients.Services;
using Surefire.Domain.Contacts.Services;
using Surefire.Domain.DocuSign;
using Surefire.Domain.Ember;
using Surefire.Domain.Forms.Services;
using Surefire.Domain.Graph;
using Surefire.Domain.Logs;
using Surefire.Domain.OpenAI;
using Surefire.Domain.OpenAI.Simple;
using Surefire.Domain.Plugins;
using Surefire.Domain.Policies.Services;
using Surefire.Domain.Proposals;
using Surefire.Domain.Proposals.Services;
using Surefire.Domain.Renewals.Services;
using Surefire.Domain.Shared.Services;
using Surefire.Domain.Users.Services;
using Surefire.Hubs;
using Surefire.Interfaces;
using Surefire.Components.Account;
using Surefire.Components;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.FluentUI.AspNetCore.Components;
using Microsoft.FluentUI.AspNetCore.Components.Components.Tooltip;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.EntityFrameworkCore;
using Syncfusion.Blazor;
using Syncfusion.Blazor.SmartComponents;
using System.Net.Http.Headers;
#endregion

// INITIAL VARIABLES -- -- -- -   -     -     -                -           -              -            -   -       -  -   -  - -  ---  -
WebApplicationBuilder builder = WebApplication.CreateBuilder(args);
builder.Services.AddHttpClient();
builder.Services.AddRazorComponents().AddInteractiveServerComponents();
builder.Services.AddMemoryCache();
Env.Load();
bool detailedErrorsEnabled = builder.Configuration.GetValue<bool>("DetailedErrors:Enabled");

// IDEN AND AUTH -- -- -- -   -     -     -      -             -           -            -           -   -      -  -   -  --  ---  --
builder.Services.AddCascadingAuthenticationState();
builder.Services.AddScoped<IdentityUserAccessor>();
builder.Services.AddScoped<IdentityRedirectManager>();
builder.Services.AddAuthorization();
builder.Services.AddScoped<AuthenticationStateProvider, IdentityRevalidatingAuthenticationStateProvider>();
builder.Services.AddAuthentication(options => { 
    options.DefaultScheme = IdentityConstants.ApplicationScheme;
    options.DefaultSignInScheme = IdentityConstants.ExternalScheme;
}).AddIdentityCookies();
builder.Services.AddIdentityCore<ApplicationUser>(options => options.SignIn.RequireConfirmedAccount = true)
    .AddEntityFrameworkStores<ApplicationDbContext>().AddSignInManager().AddDefaultTokenProviders();

// DATABASE  -- -- -- -   -     -
string? connectionString = Environment.GetEnvironmentVariable("DEFAULTCONNECTION");
builder.Services.AddDbContextFactory<ApplicationDbContext>(options =>
    options.UseSqlServer(connectionString, sqlOptions =>
    {
        sqlOptions.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
        sqlOptions.EnableRetryOnFailure(maxRetryCount: 5, maxRetryDelay: TimeSpan.FromSeconds(30), errorNumbersToAdd: null);
    }));

// LLMs -- -- -- -   -     -  -     -      -                -           -              -            -   -       -  -   -  - -  ---  -
builder.Services.AddHttpClient<OpenAiService>();
builder.Services.AddHttpClient("OpenAI", client => {
    var openAiKey = builder.Configuration["OpenAi:ApiKey"] ?? throw new InvalidOperationException("OpenAI API key missing");
    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", openAiKey);
    client.Timeout = TimeSpan.FromSeconds(230);
    client.BaseAddress = new Uri("https://api.openai.com/v1/");
});
builder.Services.AddHttpClient("LLMWhisperer", client => {
    client.Timeout = TimeSpan.FromMinutes(10);
});
builder.Services.AddScoped<IOpenAISimpleService, OpenAISimpleService>();
string qdrantBaseUrl = Environment.GetEnvironmentVariable("QDRANT_BASE_URL") ?? "http://localhost:6333/";
string qdrantApiKey = Environment.GetEnvironmentVariable("QDRANT_API_KEY") ?? "api-key";
builder.Services.AddHttpClient("Qdrant", client => {
    client.BaseAddress = new Uri(qdrantBaseUrl);
    client.DefaultRequestHeaders.Add("api-key", qdrantApiKey);
});
builder.Services.AddScoped<IEmbeddingService, OpenAIEmbeddingService>();
builder.Services.AddScoped<IVectorStore, QdrantVectorStore>();
builder.Services.AddScoped<EmbeddingLoaderService>();
builder.Configuration["Qdrant:Collection"] = "surefirex1";
string openAiApiKey = Environment.GetEnvironmentVariable("OPENAI") ?? "MISSING";
builder.Configuration["OpenAi:ApiKey"] = openAiApiKey;


// COMPONENTS AND SYNCFUSION  -     -      -                -           -              -            -   -       -  -   -  - -  ---  -
builder.Services.AddSyncfusionBlazor();
builder.Services.AddFluentUIComponents();
builder.Services.AddDataGridEntityFrameworkAdapter();
builder.Services.AddSyncfusionSmartComponents().ConfigureCredentials(new AIServiceCredentials(openAiApiKey, "gpt-3.5-turbo", "")).InjectOpenAIInference();
string? syncFusionKey = Environment.GetEnvironmentVariable("SYNCFUSION");
Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(syncFusionKey);

// DEPENDENCIES -- -- -- -   -     -      -                -           -              -            -   -       -  -   -  - -  ---  -
builder.Services.AddScoped<AttachmentService>();
builder.Services.AddScoped<AzureFormService>();
builder.Services.AddScoped<LLMWhispererService>();
builder.Services.AddScoped<OpenAIPro>();
builder.Services.AddScoped<CarrierService>();
builder.Services.AddScoped<ClientService>();
builder.Services.AddScoped<ContactService>();
builder.Services.AddScoped<EmberService>();
builder.Services.AddScoped<FormService>();
builder.Services.AddScoped<ExternalPortalService>();
builder.Services.AddScoped<HomeService>();
builder.Services.AddScoped<PolicyService>();
builder.Services.AddScoped<RenewalService>();
builder.Services.AddScoped<AccountingService>();
builder.Services.AddScoped<SearchService>();
builder.Services.AddScoped<SharedService>();
builder.Services.AddScoped<TaskService>();
builder.Services.AddScoped<UserService>();
builder.Services.AddScoped<StateService>();
builder.Services.AddScoped<ProposalService>();
builder.Services.AddScoped<ClientStateService>();
builder.Services.AddScoped<PhoneLookupService>();
builder.Services.AddScoped<ProposalWordDocumentService>();
builder.Services.AddScoped<PolicyExtractorService>();
builder.Services.AddScoped<Surefire.Domain.Proposals.Services.PackagerService>();
builder.Services.AddScoped<GraphService>();
builder.Services.AddScoped<SurefireDialogService>();
builder.Services.AddScoped<ISubmissionService, SubmissionService>();
builder.Services.AddScoped<ILoggingService, LoggingService>();
builder.Services.AddScoped<ITooltipService, TooltipService>();
builder.Services.AddScoped<IEmailTemplateService, EmailTemplateService>();


// DOCUSIGN SERVICES   --   --   -  -   -       -             -                  -             -   -       -    -   -  - -   ---  -
builder.Services.AddScoped<IDocuSignConfigService, DocuSignConfigService>();
builder.Services.AddHttpClient<DocuSignService>();
builder.Services.AddScoped<IDocuSignService>(sp => {
    var httpClientFactory = sp.GetRequiredService<IHttpClientFactory>();
    var configService = sp.GetRequiredService<IDocuSignConfigService>();
    var logger = sp.GetRequiredService<ILogger<DocuSignService>>();
    var cache = sp.GetRequiredService<IMemoryCache>();
    var httpClient = httpClientFactory.CreateClient(nameof(DocuSignService));
    return new DocuSignService(httpClient, configService, logger, cache);
});

// UNIFIED AGENTIC AI PIPELINE SERVICES -- -- -- -   -     -          -              -            -   -       -  -   -  - -  ---  -
builder.Services.AddScoped<INavigationAgent, NavigationAgent>();
builder.Services.AddScoped<IOpenAIAgent, OpenAIAgent>();

// TASK AGENT SERVICES -- -- -- -   -     -
builder.Services.AddSingleton<ITaskAgentRegistry, TaskAgentRegistry>();
builder.Services.AddScoped<IParameterExtractionService, ParameterExtractionService>();
builder.Services.AddScoped<ITaskAgentOrchestrator, TaskAgentOrchestrator>();

// REGISTER INDIVIDUAL TASK AGENTS -- -- -- -   -    -     -          -              -            -   -       -  -   -  - -  ---  -
builder.Services.AddScoped<LossRunRequestAgent>();
builder.Services.AddScoped<PaymentLinkAgent>();
builder.Services.AddScoped<SimplePayLinkSender>();
builder.Services.AddSingleton<IEmailSender<ApplicationUser>, IdentityNoOpEmailSender>();
builder.Services.AddSingleton<IConfiguration>(builder.Configuration);
builder.Services.AddSignalR(hubOptions =>
{
    // Increase buffer sizes for large data transfers (like Word documents)
    hubOptions.MaximumReceiveMessageSize = 10 * 1024 * 1024; // 10MB
    hubOptions.StreamBufferCapacity = 100;
    hubOptions.MaximumParallelInvocationsPerClient = 10;
    // Enable detailed errors in development
    #if DEBUG
    hubOptions.EnableDetailedErrors = true;
    #endif
});
builder.Services.AddHttpContextAccessor();
builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true).AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true).AddEnvironmentVariables();
builder.Services.AddServerSideBlazor().AddHubOptions(o => { o.MaximumReceiveMessageSize = 102400000; });

// PLUGINS -- -- -- -   -     -
builder.Services.AddScoped<PluginManager>();
#if DEBUG
    var pluginsPath = Path.Combine(Directory.GetCurrentDirectory(), "Plugins");
#else
    var pluginsPath = Path.Combine(Directory.GetCurrentDirectory(), "plugins");
#endif
var serviceProvider = builder.Services.BuildServiceProvider();
var logger = serviceProvider.GetRequiredService<ILoggingService>();
PluginLoader.LoadPlugins(builder.Services, pluginsPath, serviceProvider, logger);

// RINGCENTRAL AND CHAT SERVICES -- -- -- -   -  -     -            -                -            -   -       -  -   -  - -  ---  -
builder.Services.AddHttpClient<ChatService>(client => {
    client.Timeout = TimeSpan.FromSeconds(30);
});
builder.Services.AddScoped<ChatService>();
builder.Services.AddScoped<SmsMessageService>();
builder.Services.AddHostedService<SmsBackgroundService>();

// Background Services
// Add transcription service for call recordings (WebSocket-based with REST fallback)
builder.Services.AddHttpClient<TranscriptionService>(client => {
    client.Timeout = TimeSpan.FromSeconds(120); // Allow longer timeout for transcription
});
builder.Services.AddScoped<TranscriptionService>();

// Add voice recording service for Enhanced AI Chat
builder.Services.AddScoped<IVoiceRecordingService, VoiceRecordingService>();

// ---------------------------------------------------//
// App Configuration Protocols ---------------------- //
// ---------------------------------------------------//
Microsoft.AspNetCore.Builder.WebApplication app = builder.Build();
#if DEBUG
    app.UseDeveloperExceptionPage();
#endif

app.UseHsts();
app.UseHttpsRedirection(); //Turn off for WenView2 Desktop runtime
app.UseDeveloperExceptionPage();
app.UseMigrationsEndPoint();
app.MapStaticAssets();

// Configure static files
app.UseDefaultFiles();
app.UseStaticFiles(new StaticFileOptions
{
    OnPrepareResponse = ctx =>
    {
        // Cache static files for 1 day
        ctx.Context.Response.Headers.Append("Cache-Control", "public,max-age=86400");
    },
    ServeUnknownFileTypes = true,
    DefaultContentType = "application/octet-stream"
});

// Removed the UseStaticFiles for working with uploads directory for dynamically generated files

app.UseAntiforgery();
app.UseAuthentication();
app.UseAuthorization();
app.MapHub<NotificationHub>("/notificationHub");
app.MapHub<MessagingHub>("/messagingHub");
app.MapHub<EmberHub>("/emberHub");
app.MapRazorComponents<App>().AddInteractiveServerRenderMode();
//app.MapAdditionalIdentityEndpoints();

// -- -- -- -   -  -     -            -                -            -   -       -  -   -  - -  ---  -
#region API Endpoints

// Get a call recording
app.MapGet("/api/call/recording/{recordingId}", async (string recordingId, ChatService chatService) => {
    var recording = await chatService.GetCallRecordingAsync(recordingId);
    if (recording == null)
        return Results.NotFound("Recording not found");
        
    return Results.File(recording.Value.Data, recording.Value.ContentType);
}).AllowAnonymous();

// Get a call recording using its content URI
app.MapGet("/api/call/recording-by-uri", async ([FromQuery] string contentUri, ChatService chatService) => {
    if (string.IsNullOrEmpty(contentUri))
        return Results.BadRequest("Content URI is required");
        
    var recording = await chatService.GetCallRecordingByUriAsync(contentUri);
    if (recording == null)
        return Results.NotFound("Recording not found");
        
    return Results.File(recording.Value.Data, recording.Value.ContentType);
}).AllowAnonymous();

// Get a call transcription
app.MapGet("/api/call/transcription/{callId}", async (string callId, TranscriptionService transcriptionService) => {
    var transcription = await transcriptionService.GetTranscriptionAsync(callId);
    if (transcription == null)
        return Results.NotFound("Transcription not found");
        
    return Results.Ok(transcription);
}).AllowAnonymous();

// Endpoint to test vector search
app.MapPost("/api/embeddings/search", async (IEmbeddingService embeddingService, IVectorStore vectorStore, SearchRequest request) =>
{
    try
    {
        var searchVector = await embeddingService.GenerateEmbeddingAsync(request.Query);
        var results = await vectorStore.SearchAsync(searchVector, request.TopK ?? 5);

        return Results.Ok(new
        {
            Query = request.Query,
            ResultCount = results.Count,
            Results = results.Select(r => new
            {
                Id = r.Id,
                Score = r.Score,
                Entity = r.Metadata.TryGetValue("Entity", out var entity) ? entity?.ToString() ?? "Unknown" : "Unknown",
                EntityId = GetMetadataInt(r.Metadata, "EntityId"),
                Name = GetMetadataString(r.Metadata, "Name") ??
                       GetMetadataString(r.Metadata, "CarrierName") ??
                       $"{GetMetadataString(r.Metadata, "FirstName")} {GetMetadataString(r.Metadata, "LastName")}".Trim() ??
                       GetMetadataString(r.Metadata, "LineName") ?? ""
            })
        });
    }
    catch (Exception ex)
    {
        return Results.Problem($"Search failed: {ex.Message}");
    }
}).AllowAnonymous();

// Trigger embedding upsert
app.MapPost("/api/embeddings/upsert", async (EmbeddingLoaderService loader, ILogger<Program> logger) =>
{
    try
    {
        logger.LogInformation("Starting embedding upsert process...");
        await loader.UpsertAllAsync();
        logger.LogInformation("Embedding upsert completed successfully.");
        return Results.Ok("Embeddings upserted.");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error during embedding upsert: {ErrorMessage}", ex.Message);
        return Results.Problem($"Error during embedding upsert: {ex.Message}", statusCode: 500);
    }
}).AllowAnonymous();

// Initialize task agents
using (var scope = app.Services.CreateScope())
{
    var registry = scope.ServiceProvider.GetRequiredService<ITaskAgentRegistry>();
    if (registry is TaskAgentRegistry concreteRegistry)
    {
        concreteRegistry.InitializeAgents(scope.ServiceProvider);
    }
}
#endregion

app.Run();

static string? GetMetadataString(IDictionary<string, object> metadata, string key)
{
    // Helper method for metadata string extraction
    return metadata.TryGetValue(key, out var value) ? value?.ToString() : null;
}
static int GetMetadataInt(IDictionary<string, object> metadata, string key)
{
    // Helper method for metadata integer extraction
    if (!metadata.TryGetValue(key, out var value))
        return 0;
        
    return value switch
    {
        int i => i,
        long l => (int)l,
        string s when int.TryParse(s, out var parsed) => parsed,
        System.Text.Json.JsonElement je when je.ValueKind == System.Text.Json.JsonValueKind.Number => je.GetInt32(),
        _ => 0
    };
}
public record SearchRequest(string Query, int? TopK); // Record types for API endpoints