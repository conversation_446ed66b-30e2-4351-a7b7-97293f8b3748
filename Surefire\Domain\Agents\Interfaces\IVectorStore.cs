using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Surefire.Domain.Agents.Models;

namespace Surefire.Domain.Agents.Interfaces
{
    public interface IVectorStore
    {
        /// <summary>
        /// Ensures the collection exists, creating it if necessary.
        /// </summary>
        Task EnsureCollectionExistsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Inserts or updates a vector entry with metadata.
        /// </summary>
        Task UpsertAsync(string id, float[] vector, Dictionary<string, object> metadata, CancellationToken cancellationToken = default);

        /// <summary>
        /// Searches for topK similar vectors to the given vector.
        /// </summary>
        Task<IList<VectorSearchResult>> SearchAsync(float[] vector, int topK, CancellationToken cancellationToken = default);
    }
}
