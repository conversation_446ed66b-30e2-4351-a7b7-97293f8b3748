.alink {
    width: 100%;
    font-size: 12px;
    word-break: break-all;
    white-space: normal;
    color: #bbbbbb;
    font-weight:bold;
    line-height:14px;
}

.blink {
    width:80px;
    white-space:nowrap;
    overflow:hidden;
}

.acol {
    color:#c5403b;
}

.bcol {
    color: #0f6cbd;
}
.gurl {
    height:20px;

}
.gurl2 {
    font-size: 12px;
    word-break: break-all;
    white-space: normal;
    color:#0f6cbd;
}

.div-section {
    padding: 10px;
    border-top: 1px solid #ccc;
    border-left: 5px solid #ccc;
    /*background-color: #ffffff43;*/
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#e5e5e5+0,ffffff+26,000000+55,e5e5e5+100&0.65+0,1+26,0+55,0.65+100 */
    /*background: linear-gradient(135deg, rgba(229,229,229,0.65) 0%,rgba(255,255,255,1) 26%,rgba(0,0,0,0) 55%,rgba(229,229,229,0.65) 100%);*/ /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#cccccc+0,f2f2f2+13,000000+100&1+0,1+13,0+100 */
    background: linear-gradient(165deg, rgba(204,204,204,1) 0%,rgba(242,242,242,1) 13%,rgba(0,0,0,0) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.txt-section-bar {
    background-color: #ccc;
    font-family: "montserrat", sans-serif;
    font-size: .9rem;
    font-weight: 400;
    color: #454545;
    text-transform: uppercase;
    padding: 4px 15px 4px 12px;
    border-top-right-radius: 10px;
    letter-spacing: .2em;
    position: relative;
    top: -2px;
}

.paylink-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding: 0;
    max-width: 1200px;
    margin: 0 auto;
}

.paylink-content {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.paylink-header {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--neutral-foreground-1);
    margin-bottom: 0.5rem;
}

.paylink-form {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    background: #fff;
    padding: 1.5rem;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    min-width: 400px;
}

.paylink-results {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    background: #fff;
    padding: 1.5rem;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    min-width: 400px;
}

.form-row {
    display: flex;
    gap: 1rem;
}

.form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-size: 0.875rem;
    color: var(--neutral-foreground-2);
}

.results-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.results-header {
    font-size: 1rem;
    font-weight: 600;
    color: var(--neutral-foreground-1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.results-content {
    background: var(--neutral-layer-1);
    padding: 1rem;
    border-radius: 4px;
    border: 1px solid var(--neutral-stroke-1);
    word-break: break-all;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.email-preview-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.email-preview-header {
    font-size: 1rem;
    font-weight: 600;
    color: var(--neutral-foreground-1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.email-preview-content {
    background: var(--neutral-layer-1);
    padding: 1.5rem;
    border-radius: 4px;
    border: 1px solid var(--neutral-stroke-1);
    font-family: Arial, sans-serif;
    line-height: 1.5;
}

.email-preview-content p {
    margin: 0 0 1rem 0;
}

.email-preview-content a {
    display: inline-block;
    padding: 10px 20px;
    background-color: #0078d4;
    color: white !important;
    text-decoration: none;
    border-radius: 4px;
    margin: 0.5rem 0;
    font-weight: 500;
    border: 1px solid #006cbe;
}

.email-preview-content a:hover {
    background-color: #106ebe;
    border-color: #005a9e;
}

.email-preview-content br {
    margin-bottom: 0.5rem;
}