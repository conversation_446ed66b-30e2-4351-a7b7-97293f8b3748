@namespace Surefire.Domain.Proposals
@using Microsoft.FluentUI.AspNetCore.Components
@using Surefire.Data
@using System.Linq
@using System.IO
@using Microsoft.JSInterop
@using Surefire.Domain.Attachments.Models
@using Surefire.Domain.Attachments.Services
@using Newtonsoft.Json
@using Newtonsoft.Json.Linq

<FluentStack>
    <FluentButton Appearance="Appearance.Lightweight" OnClick="@OnReturn">
        <FluentIcon Value="@(new Icons.Regular.Size16.ArrowLeft())" />
        <span class="ms-2">Back to Proposals</span>
    </FluentButton>
    <FluentLabel Typo="Typography.H3">Clean Proposal Data</FluentLabel>
</FluentStack>

@if (error != null)
{
    <FluentMessageBar Intent="MessageIntent.Error" OnDismiss="@(() => error = null)">
        @error
    </FluentMessageBar>
}

@if (statusMessage != null)
{
    <FluentMessageBar Intent="MessageIntent.Success" OnDismiss="@(() => statusMessage = null)">
        @statusMessage
    </FluentMessageBar>
}

@code {
    [Parameter] public int ClientId { get; set; }
    [Parameter] public int ProposalId { get; set; }
    [Parameter] public EventCallback OnReturnToList { get; set; }
    private string error;
    private string statusMessage;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Clean the Form Recognizer data
            //string cleanedJsonPath = await ProposalService.CleanAndSaveFormRecognizerJsonAsync(ProposalId);
            string cleanedJsonPath = "";
            string cleanedJson = await File.ReadAllTextAsync(cleanedJsonPath);
            
            // Create an attachment for the cleaned JSON data
            var cleanedJsonAttachment = new Attachment
            {
                OriginalFileName = Path.GetFileName(cleanedJsonPath),
                FileFormat = ".json",
                FileSize = cleanedJson.Length,
                Description = $"Cleaned extracted data from proposal",
                IsClientAccessible = false,
                Status = 1,
                IsProposal = false,
                ClientId = ClientId
            };
            
            // Save the cleaned JSON attachment
            //await AttachmentService.SaveDropZoneAttachmentAsync(cleanedJsonAttachment);

            // TODO: Add UI for reviewing and editing the cleaned data
            statusMessage = "Data cleaned successfully. UI for reviewing the data will be added soon.";
        }
        catch (Exception ex)
        {
            error = $"Error cleaning proposal data: {ex.Message}";
            Console.WriteLine($"Error in ProposalCleaner: {ex}");
        }
    }

    private async Task OnReturn()
    {
        await OnReturnToList.InvokeAsync();
    }
} 