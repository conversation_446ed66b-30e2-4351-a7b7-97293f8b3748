@namespace Surefire.Domain.Contacts.Components
@using Microsoft.FluentUI.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using Surefire.Domain.Contacts.Models
@using Microsoft.EntityFrameworkCore
@using Surefire.Domain.Contacts.Services
@using Microsoft.AspNetCore.Components.Routing
@inject ContactService ContactService
@inject NavigationManager NavigationManager

<div class="fluent-data-grid">
    <div class="fluent-data-grid__toolbar">
        <FluentButton Appearance="Appearance.Lightweight" OnClick="@(() => showColumnPicker = !showColumnPicker)">
            <FluentIcon Value="@(new Icons.Regular.Size20.ColumnEdit())" />
            Columns
        </FluentButton>
        @if (showColumnPicker)
        {
            <div class="column-picker">
                <div class="column-picker__header">
                    <span>Select Columns</span>
                    <FluentButton Appearance="Appearance.Lightweight" OnClick="@(() => showColumnPicker = false)">
                        <FluentIcon Value="@(new Icons.Regular.Size20.Dismiss())" />
                    </FluentButton>
                </div>
                <div class="column-picker__content">
                    @foreach (var column in availableColumns)
                    {
                        <FluentCheckbox @bind-Checked="@column.IsVisible" 
                                      Label="@column.Title"
                                      OnChange="@(() => OnColumnVisibilityChanged())" />
                    }
                </div>
            </div>
        }
    </div>

    <FluentDataGrid Items="@FilteredContacts" ResizableColumns="true" Pagination="@pagination" ShowHover="true" 
                  OnRowClick="@((FluentDataGridRow<Contact> row) => HandleRowClick(row))">
        @if (GetColumn("ContactId").IsVisible)
        {
            <PropertyColumn Property="@(p => p.ContactId)" Title="Contact Id" Width="60px" Sortable="true" />
        }
        @if (GetColumn("FirstName").IsVisible)
        {
            <PropertyColumn Property="@(p => p.FirstName)" Title="First Name" Width="200px" Sortable="true" />
        }
        @if (GetColumn("LastName").IsVisible)
        {
            <PropertyColumn Property="@(p => p.LastName)" Title="Last Name" Width="200px" Sortable="true" />
        }
        @if (GetColumn("Title").IsVisible)
        {
            <PropertyColumn Property="@(p => p.Title)" Title="Title" Sortable="true" />
        }
        @if (GetColumn("Email").IsVisible)
        {
            <TemplateColumn Title="Email" Sortable="true">
                <ChildContent>
                    @{
                        var primaryEmail = context.EmailAddresses?.FirstOrDefault(e => e.IsPrimary)?.Email ?? context.EmailAddresses?.FirstOrDefault()?.Email;
                        @primaryEmail
                    }
                </ChildContent>
            </TemplateColumn>
        }
        @if (GetColumn("Phone").IsVisible)
        {
            <TemplateColumn Title="Phone" Sortable="true">
                <ChildContent>
                    @{
                        var primaryPhone = context.PhoneNumbers?.FirstOrDefault(p => p.IsPrimary && p.Type == PhoneType.Office)?.Number;
                        @primaryPhone
                    }
                </ChildContent>
            </TemplateColumn>
        }
        @if (GetColumn("Mobile").IsVisible)
        {
            <TemplateColumn Title="Mobile" Sortable="true">
                <ChildContent>
                    @{
                        var mobilePhone = context.PhoneNumbers?.FirstOrDefault(p => p.IsPrimary && p.Type == PhoneType.Mobile)?.Number;
                        @mobilePhone
                    }
                </ChildContent>
            </TemplateColumn>
        }
        @if (GetColumn("City").IsVisible)
        {
            <TemplateColumn Title="City" Sortable="true">
                <ChildContent>
                    @context.Address?.City
                </ChildContent>
            </TemplateColumn>
        }
        @if (GetColumn("State").IsVisible)
        {
            <TemplateColumn Title="State" Sortable="true">
                <ChildContent>
                    @context.Address?.State
                </ChildContent>
            </TemplateColumn>
        }
        @if (GetColumn("Zip").IsVisible)
        {
            <TemplateColumn Title="Zip" Sortable="true">
                <ChildContent>
                    @context.Address?.PostalCode
                </ChildContent>
            </TemplateColumn>
        }
        @if (GetColumn("DateCreated").IsVisible)
        {
            <PropertyColumn Property="@(p => p.DateCreated)" Title="Date Created" Sortable="true" />
        }
    </FluentDataGrid>
    <div class="fluent-data-grid__bottombar">
        <div class="fluent-data-grid__pagination">
            <FluentPaginator State="@pagination" />
        </div>
    </div>
</div>

<style>
    .fluent-data-grid__toolbar {
        display: flex;
        align-items: center;
        padding: 8px;
        gap: 8px;
        position: relative;
    }

    .column-picker {
        position: absolute;
        top: 100%;
        left: 0;
        background: white;
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        z-index: 1000;
        min-width: 200px;
    }

    .column-picker__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px;
        border-bottom: 1px solid #eee;
        background: #f5f5f5;
    }

    .column-picker__content {
        padding: 8px;
        max-height: 300px;
        overflow-y: auto;
    }

    .column-picker__content fluent-checkbox {
        display: block;
        margin: 4px 0;
    }
</style>

@code {
    [Parameter]
    public int SelectedContactId { get; set; }

    [Parameter]
    public EventCallback<int> SelectedContactIdChanged { get; set; }

    [Parameter]
    public string SearchTerm { get; set; } = string.Empty;

    private IQueryable<Contact> ContactData { get; set; }
    private IQueryable<Contact> FilteredContacts { get; set; }
    private PaginationState pagination = new PaginationState { ItemsPerPage = 18 };
    private System.Timers.Timer debounceTimer;
    private bool showColumnPicker = false;

    private class ColumnConfig
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public bool IsVisible { get; set; }
    }

    private List<ColumnConfig> availableColumns = new()
    {
        new ColumnConfig { Id = "ContactId", Title = "Contact Id", IsVisible = true },
        new ColumnConfig { Id = "FirstName", Title = "First Name", IsVisible = true },
        new ColumnConfig { Id = "LastName", Title = "Last Name", IsVisible = true },
        new ColumnConfig { Id = "Title", Title = "Title", IsVisible = true },
        new ColumnConfig { Id = "Email", Title = "Email", IsVisible = true },
        new ColumnConfig { Id = "Phone", Title = "Phone", IsVisible = true },
        new ColumnConfig { Id = "Mobile", Title = "Mobile", IsVisible = true },
        new ColumnConfig { Id = "City", Title = "City", IsVisible = false },
        new ColumnConfig { Id = "State", Title = "State", IsVisible = false },
        new ColumnConfig { Id = "Zip", Title = "Zip", IsVisible = false },
        new ColumnConfig { Id = "DateCreated", Title = "Date Created", IsVisible = true }
    };

    private ColumnConfig GetColumn(string id) => availableColumns.FirstOrDefault(c => c.Id == id) ?? new ColumnConfig { IsVisible = false };

    private void OnColumnVisibilityChanged()
    {
        InvokeAsync(StateHasChanged);
    }

    protected override async Task OnInitializedAsync()
    {        
        ContactData = ContactService.GetAllContacts()
            .Include(c => c.EmailAddresses)
            .Include(c => c.PhoneNumbers)
            .Include(c => c.Address)
            .OrderByDescending(c => c.DateCreated);
        FilteredContacts = ContactData;
        
        debounceTimer = new System.Timers.Timer(300); // Debounce for 300ms
        debounceTimer.AutoReset = false; // Prevent multiple triggers
        debounceTimer.Elapsed += async (sender, e) =>
        {
            await InvokeAsync(ApplySearchFilter); // Apply the search filter after the debounce period
        };
        
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {        
        if (SearchTerm != null && !string.IsNullOrWhiteSpace(SearchTerm))
        {
            debounceTimer.Stop();
            debounceTimer.Start(); // Start the debounce timer
        }
        else
        {
            FilteredContacts = ContactData;
        }
        
        await base.OnParametersSetAsync();
    }

    private void ApplySearchFilter()
    {        
        FilteredContacts = string.IsNullOrWhiteSpace(SearchTerm)
            ? ContactData
            : ContactData.Where(c =>
                EF.Functions.Like(c.FirstName.ToLower(), $"%{SearchTerm.ToLower()}%")
                || EF.Functions.Like(c.LastName.ToLower(), $"%{SearchTerm.ToLower()}%")
                || c.EmailAddresses.Any(e => EF.Functions.Like(e.Email.ToLower(), $"%{SearchTerm.ToLower()}%"))
                || c.PhoneNumbers.Any(p => EF.Functions.Like(p.Number.ToLower(), $"%{SearchTerm.ToLower()}%"))
                || (c.Address != null && (
                    EF.Functions.Like(c.Address.City.ToLower(), $"%{SearchTerm.ToLower()}%")
                    || EF.Functions.Like(c.Address.State.ToLower(), $"%{SearchTerm.ToLower()}%")
                    || EF.Functions.Like(c.Address.PostalCode.ToLower(), $"%{SearchTerm.ToLower()}%"))))
                .OrderByDescending(c => c.DateCreated);

        InvokeAsync(StateHasChanged); // Trigger UI refresh after filtering
    }

    private async Task HandleRowClick(FluentDataGridRow<Contact> row)
    {        
        if (row != null && row.Item != null)
        {            
            var selectedContact = row.Item;
            row.Class = "selected-row";
            await SelectedContactIdChanged.InvokeAsync(selectedContact.ContactId);
            
            // Update the URL to include the contact ID
            NavigationManager.NavigateTo($"/Contacts/{selectedContact.ContactId}");
            
            // Notify parent to show edit view (this will be handled in Contacts.razor.cs)
            await OnRowSelected.InvokeAsync(selectedContact.ContactId);
        }
    }
    
    [Parameter]
    public EventCallback<int> OnRowSelected { get; set; }

    public void Dispose()
    {        
        debounceTimer?.Dispose();
    }
}
