@page "/docusign-callback"
@using System.Text.Json
@using System.Net.Http.Headers
@inject IHttpClientFactory HttpClientFactory
@inject IDocuSignConfigService ConfigService
@inject ILogger<DocuSignCallbackPage> Logger

<h3>DocuSign Consent Callback</h3>

<div class="mt-4">
    @if (loading)
    {
        <div class="alert alert-info">
            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
            <span class="ms-2">Processing DocuSign consent callback...</span>
        </div>
    }
    else if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="alert alert-danger">
            <h5>Error:</h5>
            <p>@errorMessage</p>
        </div>
    }
    else if (success)
    {
        <div class="alert alert-success">
            <h5>Consent Granted!</h5>
            <p>DocuSign consent has been successfully processed. You can now return to the JWT test page.</p>
            <a href="/docusign-jwt-test" class="btn btn-primary mt-3">Return to JWT Test</a>
        </div>
    }
</div>

@code {
    private bool loading = true;
    private bool success = false;
    private string errorMessage = string.Empty;

    [Parameter]
    [SupplyParameterFromQuery(Name = "code")]
    public string AuthCode { get; set; }

    [Parameter]
    [SupplyParameterFromQuery(Name = "error")]
    public string Error { get; set; }

    [Parameter]
    [SupplyParameterFromQuery(Name = "error_description")]
    public string ErrorDescription { get; set; }

    protected override async Task OnInitializedAsync()
    {
        Logger.LogInformation("DocuSign callback page initialized");
        
        if (!string.IsNullOrEmpty(Error))
        {
            Logger.LogWarning("DocuSign returned an error: {Error}, {ErrorDescription}", Error, ErrorDescription);
            errorMessage = $"{Error}: {ErrorDescription}";
            loading = false;
            return;
        }

        if (string.IsNullOrEmpty(AuthCode))
        {
            Logger.LogWarning("No authorization code provided in the callback");
            errorMessage = "No authorization code was provided in the callback URL. Please try again.";
            loading = false;
            return;
        }

        Logger.LogInformation("Authorization code received: {AuthCode}", AuthCode[..4] + "...");
        
        try
        {
            await ExchangeCodeForToken();
            success = true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error exchanging authorization code for token");
            errorMessage = $"Failed to exchange the authorization code for a token: {ex.Message}";
        }
        finally
        {
            loading = false;
        }
    }

    private async Task ExchangeCodeForToken()
    {
        var config = ConfigService.GetDocuSignConfig();
        string authServer = ConfigService.IsProduction() ? "account.docusign.com" : "account.docusign.com";
        
        Logger.LogInformation("Exchanging code for token with auth server: {AuthServer}", authServer);
        
        var httpClient = HttpClientFactory.CreateClient();
        var redirectUri = "https://localhost:7074/docusign-callback";
        
        var content = new FormUrlEncodedContent(new Dictionary<string, string>
        {
            { "grant_type", "authorization_code" },
            { "code", AuthCode },
            { "client_id", config.IntegratorKey },
            { "redirect_uri", redirectUri }
        });

        var response = await httpClient.PostAsync($"https://{authServer}/oauth/token", content);
        var responseContent = await response.Content.ReadAsStringAsync();
        
        if (!response.IsSuccessStatusCode)
        {
            Logger.LogError("Token exchange failed. Status: {StatusCode}, Response: {Response}", 
                response.StatusCode, responseContent);
            throw new Exception($"Failed to exchange authorization code. Server responded with {response.StatusCode}: {responseContent}");
        }

        var tokenResponse = JsonDocument.Parse(responseContent);
        var accessToken = tokenResponse.RootElement.GetProperty("access_token").GetString();
        
        Logger.LogInformation("Successfully obtained access token from authorization code");
        
        // Now we use the token to get user info to confirm it worked
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
        var userInfoResponse = await httpClient.GetAsync($"https://{authServer}/oauth/userinfo");
        var userInfoContent = await userInfoResponse.Content.ReadAsStringAsync();
        
        if (!userInfoResponse.IsSuccessStatusCode)
        {
            Logger.LogWarning("UserInfo endpoint failed. Status: {StatusCode}, Response: {Response}",
                userInfoResponse.StatusCode, userInfoContent);
        }
        else
        {
            var userInfo = JsonDocument.Parse(userInfoContent);
            var name = userInfo.RootElement.GetProperty("name").GetString();
            var email = userInfo.RootElement.GetProperty("email").GetString();
            
            Logger.LogInformation("Successfully verified user: {Name}, {Email}", name, email);
        }
    }
} 