﻿@namespace Surefire.Components.Layout
@inherits LayoutComponentBase
<img class="bf" src="../img/banner-homepage-metro.png" />
<img class="tf" src="../img/MetroLogo.png" />
@Body

@code {
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
    }
    
    protected override void OnParametersSet()
    {
        base.OnParametersSet();
    }
}
<style>
    .bf {
        position:absolute;
        left:0px;
        top:0px;
        width:100vw;
        z-index: 1;
    }

    .tf {
        position: absolute;
        right: 20px;
        bottom: 50px;
        z-index:1;
    }
    .page-content {
        height: 100vh !important;
    }
</style>