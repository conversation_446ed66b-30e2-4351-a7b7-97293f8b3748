using System.Drawing;
using Surefire.Data;
using Surefire.Domain.Attachments.Models;
using Surefire.Domain.Shared.Helpers;
using Surefire.Domain.Shared.Services;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Surefire.Domain.Logs;
using SixLabors.ImageSharp;
using Syncfusion.PdfToImageConverter;

namespace Surefire.Domain.Attachments.Services
{
    public class AttachmentService
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly ILoggingService _log;
        private readonly StateService _stateService;

        public AttachmentService(ApplicationDbContext context, UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor, ILoggingService log, IDbContextFactory<ApplicationDbContext> contextFactory, StateService stateService)
        {
            _context = context;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
            _contextFactory = contextFactory;
            _log = log;
            _stateService = stateService;
        }

        //Headshots and Logos and Such
        public async Task<Attachment> AddPolicyAttachmentAsync(string fileName, int coverageId, string attachmentType)
        {
            switch (attachmentType.ToLower())
            {
                case "ai": // Additional Insured
                    var attachment = new Attachment
                    {
                        OriginalFileName = fileName,
                        Description = "Additional Insured Endorsements for GL Policy"
                    };

                    var coverage = await _context.GeneralLiabilityCoverages
                        .FirstOrDefaultAsync(c => c.GeneralLiabilityCoverageId == coverageId);

                    if (coverage == null) return null;

                    coverage.AdditionalInsuredAttachment = attachment;
                    _context.Attachments.Add(attachment);
                    await _context.SaveChangesAsync();
                    return attachment;
                case "wos": // Waiver of Subrogation
                    var attachmentwos = new Attachment
                    {
                        OriginalFileName = fileName,
                        Description = "Waiver Of Subrogation Endorsements for GL Policy"
                    };

                    var coveragewos = await _context.GeneralLiabilityCoverages
                        .FirstOrDefaultAsync(c => c.GeneralLiabilityCoverageId == coverageId);

                    if (coveragewos == null) return null;

                    coveragewos.WaiverOfSubAttachment = attachmentwos;
                    _context.Attachments.Add(attachmentwos);
                    await _context.SaveChangesAsync();
                    return attachmentwos;

                case "wc-wos": //WORK COMP  Waiver of Subrogation
                    var attachmentwcwos = new Attachment
                    {
                        OriginalFileName = fileName,
                        Description = "Waiver Of Subrogation Endorsements for WC Policy"
                    };

                    var coveragewcwos = await _context.WorkCompCoverages
                        .FirstOrDefaultAsync(c => c.WorkCompCoverageId == coverageId);

                    if (coveragewcwos == null) return null;

                    coveragewcwos.WaiverOfSubAttachment = attachmentwcwos;
                    _context.Attachments.Add(attachmentwcwos);
                    await _context.SaveChangesAsync();
                    return attachmentwcwos;
            }
            return null;
        }
        public async Task RemovePolicyAttachmentAsync(int coverageId, string attachmentType)
        {
            switch (attachmentType.ToLower())
            {
                case "gl-ai": // Additional Insured
                    var coverage = await _context.GeneralLiabilityCoverages
                    .Include(c => c.AdditionalInsuredAttachment)
                    .FirstOrDefaultAsync(c => c.GeneralLiabilityCoverageId == coverageId);

                    if (coverage == null) return;

                    if (coverage.AdditionalInsuredAttachment != null)
                    {
                        _context.Attachments.Remove(coverage.AdditionalInsuredAttachment);
                        coverage.AdditionalInsuredAttachment = null;
                    }
                    break;
                case "gl-wos":
                    var coveragewos = await _context.GeneralLiabilityCoverages
                    .Include(c => c.WaiverOfSubAttachment)
                    .FirstOrDefaultAsync(c => c.GeneralLiabilityCoverageId == coverageId);

                    if (coveragewos == null) return;

                    if (coveragewos.WaiverOfSubAttachment != null)
                    {
                        _context.Attachments.Remove(coveragewos.WaiverOfSubAttachment);
                        coveragewos.WaiverOfSubAttachment = null;
                    }
                    break;
            }

            await _context.SaveChangesAsync();
        }
        public async Task RemoveHeadshotFromContact(int contactId)
        {
            // Retrieve the contact by ID
            var contact = await _context.Contacts.FirstOrDefaultAsync(c => c.ContactId == contactId);

            if (contact == null || string.IsNullOrEmpty(contact.HeadshotFilename))
            {
                return; // No contact found or no headshot to remove
            }

            // Construct the file path for the headshot
            string filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot/uploads/clients", contact.HeadshotFilename);

            // Check if the file exists, and if it does, delete it
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }

            // Set the HeadshotFilename to null and update the contact in the database
            contact.HeadshotFilename = null;
            _context.Contacts.Update(contact);
            await _context.SaveChangesAsync();
        }

        //Attachments
        //public async Task SaveAttachmentAsync(Attachment attachment)
        //{
        //    await _log.LogAsync(LogLevel.Information, $"Saving SL2 attachment: {attachment.OriginalFileName}", "AttachmentService");
        //    var userId = _userManager.GetUserId(_httpContextAccessor.HttpContext.User);
        //    attachment.UploadedById = userId;
        //    attachment.DateCreated = DateTime.UtcNow;

        //}
        public async Task SaveDropZoneAttachmentAsync(Attachment attachment)
        {
            await _log.LogAsync(LogLevel.Information, $"Saving attachment: {attachment.OriginalFileName}", "AttachmentService");
            // Get the current user
            var userId = _userManager.GetUserId(_httpContextAccessor.HttpContext.User);
            attachment.UploadedById = userId;
            attachment.DateCreated = DateTime.UtcNow;

            // Paths
            var tempFolder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", "temp");
            var tempFilePath = Path.Combine(tempFolder, attachment.OriginalFileName); // fileName is already set on attachment

            // Determine entity folder and ID
            string entityFolder = null;
            string entityId = null;

            if (attachment.IsQuote && attachment.ClientId.HasValue && attachment.AttachmentGroupId.HasValue)
            {
                entityFolder = $"clients/{attachment.ClientId}/";
                entityId = attachment.AttachmentGroupId.Value.ToString();
            }
            else if (attachment.IsAcord && attachment.ClientId.HasValue && attachment.AttachmentGroupId.HasValue)
            {
                entityFolder = $"clients/{attachment.ClientId}/";
                entityId = attachment.AttachmentGroupId.Value.ToString();
            }
            else if (attachment.IsEnclosure && attachment.ClientId.HasValue && attachment.AttachmentGroupId.HasValue)
            {
                entityFolder = $"clients/{attachment.ClientId}/";
                entityId = attachment.AttachmentGroupId.Value.ToString();
            }
            else if (attachment.IsSL2 && attachment.ClientId.HasValue && attachment.AttachmentGroupId.HasValue)
            {
                entityFolder = $"clients/{attachment.ClientId}/";
                entityId = attachment.AttachmentGroupId.Value.ToString();
            }
            else if (attachment.IsSupplemental && attachment.ClientId.HasValue && attachment.AttachmentGroupId.HasValue)
            {
                entityFolder = $"clients/{attachment.ClientId}/";
                entityId = attachment.AttachmentGroupId.Value.ToString();
            }
            else if (attachment.ClientId.HasValue)
            {
                entityFolder = "clients";
                entityId = attachment.ClientId.Value.ToString();
            }
            else if (attachment.PolicyId.HasValue)
            {
                entityFolder = "policies";
                entityId = attachment.PolicyId.Value.ToString();
            }
            else if (attachment.CarrierId.HasValue)
            {
                entityFolder = "carriers";
                entityId = attachment.CarrierId.Value.ToString();
            }
            else if (attachment.RenewalId.HasValue)
            {
                entityFolder = "renewals";
                entityId = attachment.RenewalId.Value.ToString();
            }
            else if (attachment.AttachmentGroupId.HasValue)
            {
                entityFolder = "attachments";
                entityId = attachment.AttachmentGroupId.Value.ToString();
            }

            if (entityFolder != null && entityId != null)
            {
                var uploadsFolder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", entityFolder, entityId);
                if (!Directory.Exists(uploadsFolder))
                {
                    Directory.CreateDirectory(uploadsFolder);
                }
                // Get the original filename and extension
                var originalFileName = Path.GetFileNameWithoutExtension(attachment.OriginalFileName);
                var fileExtension = Path.GetExtension(attachment.FileFormat);

                // Generate a five-character hash
                var hash = StringHelper.GenerateFiveCharacterHash(originalFileName);
                
                // Create a new filename with the hash appended
                attachment.HashedFileName = $"{originalFileName}_{hash}{fileExtension}";

                var newFilePath = Path.Combine(uploadsFolder, attachment.HashedFileName);
                
                // Retry mechanism for file move operation to handle file locking issues
                bool moveSuccess = false;
                int maxRetries = 5;
                int retryDelay = 500; // milliseconds
                
                for (int attempt = 1; attempt <= maxRetries; attempt++)
                {
                    try
                    {
                        // Add a small delay to ensure file handle is released
                        if (attempt > 1)
                        {
                            await Task.Delay(retryDelay * attempt);
                        }
                        
                        System.IO.File.Move(tempFilePath, newFilePath);
                        Console.WriteLine($"Moved {tempFilePath} to {newFilePath}");
                        moveSuccess = true;
                        break;
                    }
                    catch (IOException ex) when (attempt < maxRetries)
                    {
                        Console.WriteLine($"Attempt {attempt} failed to move file: {ex.Message}. Retrying...");
                        await _log.LogAsync(LogLevel.Warning, $"File move attempt {attempt} failed: {ex.Message}. Retrying...", "AttachmentService");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Failed to move file: {ex}");
                        await _log.LogAsync(LogLevel.Error, $"Failed to move file: {ex.Message}", "AttachmentService", ex);
                        throw;
                    }
                }
                
                if (!moveSuccess)
                {
                    var errorMessage = $"Failed to move file after {maxRetries} attempts due to file locking issues.";
                    Console.WriteLine(errorMessage);
                    await _log.LogAsync(LogLevel.Error, errorMessage, "AttachmentService");
                    throw new IOException(errorMessage);
                }

                attachment.LocalPath = Path.Combine("uploads", entityFolder, entityId);

                //Generate thumbnail if the file is a PDF
                if (Path.GetExtension(attachment.FileFormat).Equals(".pdf", StringComparison.OrdinalIgnoreCase))
                {
                    var thumbnailFilePath = Path.Combine(uploadsFolder, Path.GetFileNameWithoutExtension(attachment.HashedFileName) + "_thumb.jpg");
                    GeneratePdfThumbnail(newFilePath, thumbnailFilePath);
                }
            }
            else
            {
                // Handle error: No associated entity
                await _log.LogAsync(LogLevel.Error, "Attachment not associated with a entity", "AttachmentService");
                throw new Exception("Attachment must be associated with an entity.");
                
            }

            // Save attachment to database
            using var context = _contextFactory.CreateDbContext();
            context.Attachments.Add(attachment);
            await context.SaveChangesAsync();
        }
        public async Task<List<Attachment>> GetAttachmentsByClientIdAsync(int clientId)
        {
            using var context = _contextFactory.CreateDbContext();
            var myrecords = await context.Attachments
                .Include(a => a.Folder)
                .Include(a => a.Policy)
                .Include(a => a.UploadedBy)
                .Where(a => a.ClientId == clientId)
                .ToListAsync();

            return myrecords;
        }

        public async Task<List<Folder>> GetFoldersAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.Folders.ToListAsync();
        }
        public async Task<Attachment> GetAttachmentByIdAsync(int attachmentId)
        {
            using var context = _contextFactory.CreateDbContext();
            var attachment = await context.Attachments
                .Include(a => a.Folder)
                .Include(a => a.Policy)
                    .ThenInclude(p => p.Carrier)
                .Include(a => a.Policy)
                    .ThenInclude(p => p.Wholesaler)
                .Include(a => a.Policy)
                    .ThenInclude(p => p.Product)
                .Include(a => a.UploadedBy)
                .AsNoTracking()
                .FirstOrDefaultAsync(a => a.AttachmentId == attachmentId);
            if (attachment != null)
            {
                return attachment;
            }
            else { return null; }
        }

        //Renewal and Invoices
        public async Task<List<Attachment>> GetInvoiceAttachmentsByClientAndRenewalIdAsync(int clientId, int renewalId)
        {
            using var context = _contextFactory.CreateDbContext();

            var attachments = await context.Attachments
                .Include(a => a.Folder) // Include the Folder navigation property
                .Where(a => a.ClientId == clientId && a.RenewalId == renewalId && a.Folder.Name == "Invoice")
                .ToListAsync();

            return attachments;
        }
        public async Task<Attachment> GetMostRecentAttachmentByRenewalIdAsync(int renewalId)
        {
            using var context = _contextFactory.CreateDbContext();

            // Query to find the most recent attachment for the given RenewalId
            var attachment = await context.Attachments
                .Where(a => a.RenewalId == renewalId)
                .OrderByDescending(a => a.DateCreated) // Sort by DateCreated in descending order
                .FirstOrDefaultAsync(); // Take the most recent attachment

            return attachment; // Returns null if no attachment found
        }

        public async Task<List<Attachment>> GetAttachmentsByGroupIdAsync(int attachmentGroupId)
        {
            using var context = _contextFactory.CreateDbContext();
            return await context.Attachments
                .Where(a => a.AttachmentGroupId == attachmentGroupId)
                .OrderByDescending(a => a.DateCreated)
                .ToListAsync();
        }
       

        /// <summary>
        /// Deletes the attachment record from the database and deletes the file from the filesystem.
        /// </summary>
        public async Task DeleteAttachmentAndFileAsync(int attachmentId)
        {
            using var context = _contextFactory.CreateDbContext();
            var attachment = await context.Attachments.FirstOrDefaultAsync(a => a.AttachmentId == attachmentId);
            if (attachment == null)
                return;
            // Build the file path
            try
            {
                if (!string.IsNullOrEmpty(attachment.LocalPath) && !string.IsNullOrEmpty(attachment.HashedFileName))
                {
                    var root = Directory.GetCurrentDirectory();
                    var filePath = Path.Combine(root, "wwwroot", attachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()), attachment.HashedFileName);
                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);
                    }
                    // Also try to delete the thumbnail if it exists
                    var thumbPath = Path.Combine(root, "wwwroot", attachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()), Path.GetFileNameWithoutExtension(attachment.HashedFileName) + "_thumb.jpg");
                    if (File.Exists(thumbPath))
                    {
                        File.Delete(thumbPath);
                    }
                }
            }
            catch (Exception ex)
            {
                await _log.LogAsync(LogLevel.Error, $"Error deleting attachment file: {ex.Message}", "AttachmentService", ex);
            }
            context.Attachments.Remove(attachment);
            await context.SaveChangesAsync();
        }

        /// <summary>
        /// Saves an attachment directly to the database when the file is already in its final location.
        /// This bypasses the temp file process used by SaveDropZoneAttachmentAsync.
        /// </summary>
        public async Task SaveAttachmentDirectlyAsync(Attachment attachment)
        {
            await _log.LogAsync(LogLevel.Information, $"Saving attachment directly: {attachment.OriginalFileName}", "AttachmentService");
            
            // Get the current user
            var currentUser = _stateService.CurrentUser;
            attachment.UploadedById = currentUser.Id;
            attachment.DateCreated = DateTime.UtcNow;

            // Save attachment to database
            using var context = _contextFactory.CreateDbContext();
            context.Attachments.Add(attachment);
            await context.SaveChangesAsync();
        }

        public async Task DeleteAttachmentAsync(int attachmentId)
        {
            using var context = await _contextFactory.CreateDbContextAsync();
            
            // Get the attachment with all its navigation properties
            var attachment = await context.Attachments
                .Include(a => a.Renewal)
                .Include(a => a.AttachmentGroup)
                .Include(a => a.Policy)
                .Include(a => a.Carrier)
                .Include(a => a.Submission)
                .FirstOrDefaultAsync(a => a.AttachmentId == attachmentId);

            if (attachment == null)
            {
                throw new ArgumentException($"Attachment with ID {attachmentId} not found.");
            }

            // Get the server file path using StringHelper
            var serverFilePath = StringHelper.BuildWindowsPath(attachment, false, false, true);

            // Delete the physical file if it exists
            if (File.Exists(serverFilePath))
            {
                try
                {
                    File.Delete(serverFilePath);
                }
                catch (Exception ex)
                {
                    // Log the error but continue with database deletion
                    Console.WriteLine($"Error deleting physical file: {ex.Message}");
                }
            }

            // Remove from database
            context.Attachments.Remove(attachment);
            await context.SaveChangesAsync();
        }

        /// <summary>
        /// Deletes the attachment record from the database only.
        /// </summary>
        public async Task DeleteAttachmentAsync2(int attachmentId)
        {
            using var context = _contextFactory.CreateDbContext();
            var attachment = await context.Attachments.FirstOrDefaultAsync(a => a.AttachmentId == attachmentId);
            if (attachment == null)
                return;
            context.Attachments.Remove(attachment);
            await context.SaveChangesAsync();
        }

        // Ensure a supplemental attachment group exists for a client, or create it if not
        public async Task<int> EnsureSupplementalAttachmentGroupAsync(int clientId)
        {
            var groupName = $"Supplemental for Client {clientId}";
            using var context = _contextFactory.CreateDbContext();
            var group = await context.AttachmentGroups.FirstOrDefaultAsync(g => g.Name == groupName);
            if (group != null)
                return group.AttachmentGroupId;
            var newGroup = new AttachmentGroup
            {
                Name = groupName,
                CreatedAt = DateTime.UtcNow,
                Attachments = new List<Attachment>()
            };
            context.AttachmentGroups.Add(newGroup);
            await context.SaveChangesAsync();
            return newGroup.AttachmentGroupId;
        }

        // Generates a thumbnail for the first page of a PDF file
        private void GeneratePdfThumbnail(string pdfPath, string thumbnailFilePath)
        {
            // Initialize PDF to Image converter and convert the first page to an image
            using (var inputStream = new FileStream(pdfPath, FileMode.Open, FileAccess.Read))
            {
                PdfToImageConverter imageConverter = new PdfToImageConverter();
                imageConverter.Load(inputStream);
                // Convert the first page to an image
                using (var outputStream = imageConverter.Convert(0, false, false))
                {
                    using (var memoryStream = outputStream as MemoryStream)
                    {
                        var imageBytes = memoryStream.ToArray();
                        using (var image = (Bitmap)System.Drawing.Image.FromStream(new MemoryStream(imageBytes)))
                        {
                            // Resize the image to a 300px width, keeping the aspect ratio
                            int thumbnailWidth = 300;
                            int thumbnailHeight = (int)(image.Height * (300.0 / image.Width));

                            using (var thumbnail = new Bitmap(image, new System.Drawing.Size(thumbnailWidth, thumbnailHeight)))
                            {
                                thumbnail.Save(thumbnailFilePath, System.Drawing.Imaging.ImageFormat.Jpeg);
                                Console.WriteLine("Thumbnail saved!");
                            }
                        }
                    }
                }
            }
        }
    }
}
