﻿.pol-section-title {
    font-family: "montserrat", sans-serif;
    font-size: 1.25em;
    font-weight: 400;
    color: #6b6b6b;
    text-transform: uppercase;
    letter-spacing: 2px;
    background-color: #e9e9e9;
    padding: 3px 20px 3px 5px;
    border-top-right-radius: 20px;
    text-shadow: 1px 1px 1px #fff;
}
.pol-name {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: .8em;
    display: inline-block;
    font-weight: bold;
    color: #484848;
    text-align: right;
    position: relative;
    top: 0px;
}

.pol-value {
    width: 200px;
    display: inline-block;
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: 14px;
    color: #000;
    height: 30px;
}
.business-details {
    font-size: 1em;
}
.pol-section {

}
.pol-section-container {
    border-left: 5px solid #dedede;
    padding-top: 10px;
    padding-bottom: 20px;
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#e5e5e5+0,ffffff+35&1+0,0+35 */
    background: linear-gradient(165deg, rgba(229,229,229,1) 0%,rgba(255,255,255,0) 35%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}

.pol-column-spacer {
    height:0px;
}
.copy-spcr {
    position: relative;
    display: inline-block;
    top: 6px;
    left: 0px;
    width: 17px;
    height: 17px;
}
.copy-btn {
    opacity: .3;
    position: relative;
    display: inline-block;
    top: 6px;
    left: 0px;
    width: 17px;
    height: 17px;
}
.copy-btn:hover {
    cursor:pointer;
    opacity:1;
}
    .copy-btn:active {
        cursor: pointer;
        opacity: 1;
        top: 7px;
        left: 1px;
    }