﻿@using Surefire.Data
@using Surefire.Components
@using Surefire.Components.Layout
@using Surefire.Domain.Shared.Components
@using Surefire.Domain.Shared.Models
@using Surefire.Domain.Shared.Helpers
@using Surefire.Domain.Shared.Services
@using System.Net.Http
@using System.Net.Http.Json
@using Microsoft.JSInterop
@using Microsoft.FluentUI.AspNetCore.Components
@using Icons = Microsoft.FluentUI.AspNetCore.Components.Icons
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using static Microsoft.AspNetCore.Components.Web.RenderMode
@using Syncfusion.Blazor
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Spinner
@inject NavigationManager Navigation

@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]