## Bugs

## Quick Fixes
- Integrate XML AI Importer into Data Importer Utility
- PROPSLR: Store email address as simple field so the paylink builder works

## Priority Projects

## Attachments
- Group View: Like the renewal files tab but per policy or renewal
- List View: Enhanced version of what we have with filtering and searching

## Finish Projects
- SMS: Everything
- CONFIG: Settings pages for animation preferences, API keys, etc

## Release
- Desktop MAUI App
- Paylink String Template

# Backburner
- CERTS: Requests
- CLIENT: Details page on/off blank modes and polish/redesign
- LEADS: Finish
- AGENT: Email follower-uper
- CLIENT: Pinned note for client at top skinny bar, click to show notes area
- PROPSLR: Named driver warranty on commercial auto proposals
 
# Ideas


## Create testing page for different endpoints

```razor
// Endpoint to test vector search
app.MapPost("/api/embeddings/search", async (IEmbeddingService embeddingService, IVectorStore vectorStore, SearchRequest request) =>
{
    try
    {
        var searchVector = await embeddingService.GenerateEmbeddingAsync(request.Query);
        var results = await vectorStore.SearchAsync(searchVector, request.TopK ?? 5);

        return Results.Ok(new
        {
            Query = request.Query,
            ResultCount = results.Count,
            Results = results.Select(r => new
            {
                Id = r.Id,
                Score = r.Score,
                Entity = r.Metadata.TryGetValue("Entity", out var entity) ? entity?.ToString() ?? "Unknown" : "Unknown",
                EntityId = GetMetadataInt(r.Metadata, "EntityId"),
                Name = GetMetadataString(r.Metadata, "Name") ??
                       GetMetadataString(r.Metadata, "CarrierName") ??
                       $"{GetMetadataString(r.Metadata, "FirstName")} {GetMetadataString(r.Metadata, "LastName")}".Trim() ??
                       GetMetadataString(r.Metadata, "LineName") ?? ""
            })
        });
    }
    catch (Exception ex)
    {
        return Results.Problem($"Search failed: {ex.Message}");
    }
}).AllowAnonymous();

// Endpoint to check configuration
app.MapGet("/api/embeddings/status", () =>
{
    var openAiKey = Environment.GetEnvironmentVariable("OPENAI");
    var dbConnection = Environment.GetEnvironmentVariable("DEFAULTCONNECTION");
    
    return Results.Ok(new {
        OpenAIConfigured = !string.IsNullOrEmpty(openAiKey) && openAiKey != "MISSING",
        DatabaseConfigured = !string.IsNullOrEmpty(dbConnection),
        QdrantCollection = "surefirex1",
        QdrantUrl = "http://192.168.1.13:6333/",
        Status = "Configuration check"
    });
}).AllowAnonymous();
```