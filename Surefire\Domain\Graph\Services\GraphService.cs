﻿using Azure.Identity;
using Microsoft.Graph;

namespace Surefire.Domain.Graph
{
    public class GraphService
    {
        private readonly IConfiguration _config;
        private readonly GraphServiceClient _graphClient;
        private readonly List<string> _mailboxes;
        private readonly ILogger<GraphService> _logger;
        private bool _isInitialized = false;

        public GraphService(IConfiguration config, ILogger<GraphService> logger = null)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _logger = logger;
            
            // Get mailbox list from configuration or use defaults
            _mailboxes = _config.GetSection("Graph:Mailboxes").Get<List<string>>() ??
                         new List<string>
                         {
                             "<EMAIL>",
                             "<EMAIL>",
                             "<EMAIL>",
                             "<EMAIL>"
                         };
            
            try
            {
                // Initialize graph client with authentication
                _graphClient = InitializeGraphClient();
                _isInitialized = true;
            }
            catch (Exception ex)
            {
                LogError($"Failed to initialize GraphService: {ex.Message}");
                if (ex.InnerException != null)
                {
                    LogError($"Inner exception: {ex.InnerException.Message}");
                }
                // Don't rethrow - let the service degrade gracefully
            }
        }

        private GraphServiceClient InitializeGraphClient()
        {
            // Get required configuration values from environment variables (using Azure AD values from .env)
            var tenantId = Environment.GetEnvironmentVariable("AZURE_AD_TENANTID") 
                ?? Environment.GetEnvironmentVariable("AZURE_TENANT_ID")
                ?? _config["Graph:TenantId"];
                
            var clientId = Environment.GetEnvironmentVariable("AZURE_AD_CLIENTID") 
                ?? Environment.GetEnvironmentVariable("AZURE_CLIENT_ID") 
                ?? _config["Graph:ClientId"];
                
            var clientSecret = Environment.GetEnvironmentVariable("AZURE_AD_CLIENTSECRETVALUE") 
                ?? Environment.GetEnvironmentVariable("AZURE_ENTRA_KEY") 
                ?? _config["Graph:ClientSecret"];

            // Verify all required authentication values are present
            if (string.IsNullOrEmpty(tenantId))
            {
                throw new InvalidOperationException("Graph tenant ID is not configured. Please check your .env file or configuration.");
            }

            if (string.IsNullOrEmpty(clientId))
            {
                throw new InvalidOperationException("Graph client ID is not configured. Please check your .env file or configuration.");
            }

            if (string.IsNullOrEmpty(clientSecret))
            {
                throw new InvalidOperationException("Graph client secret is not configured. Please check your .env file or configuration.");
            }

            LogInfo($"Initializing Graph client with tenant ID: {tenantId} and client ID: {clientId}");

            try
            {
                // Create credential and graph client using Azure.Identity
                var credential = new ClientSecretCredential(tenantId, clientId, clientSecret);
                
                // The client credentials flow requires these exact scopes
                var scopes = new[] { "https://graph.microsoft.com/.default" };
                
                // Create and return the graph client
                var client = new GraphServiceClient(credential, scopes);
                
                LogDebug("GraphServiceClient initialized successfully");
                return client;
            }
            catch (Exception ex)
            {
                LogError($"Failed to initialize Graph client: {ex.Message}");
                if (ex.InnerException != null)
                {
                    LogError($"Inner exception: {ex.InnerException.Message}");
                }
                
                throw;
            }
        }

        private void LogError(string message)
        {
            _logger?.LogError(message);
            Console.Error.WriteLine($"GraphService Error: {message}");
        }

        private void LogInfo(string message)
        {
            //_logger?.LogInformation(message);
            //Console.WriteLine($"GraphService: {message}");
        }

        private void LogDebug(string message)
        {
            //_logger?.LogDebug(message);
            //Console.WriteLine($"GraphService Debug: {message}");
        }

        public async Task<List<EmailMessage>> SearchBySenderEmailAsync(string emailAddress, int count = 10)
        {
            //Console.WriteLine("===SearchBySenderEmailAsync-------------------------------------------------");

            if (!_isInitialized)
            {
                LogError("GraphService is not properly initialized - returning empty results");
                return new List<EmailMessage>();
            }

            if (string.IsNullOrEmpty(emailAddress))
            {
                throw new ArgumentException("Email address must be provided", nameof(emailAddress));
            }

            var allEmails = new List<EmailMessage>();
            var errors = new List<string>();

            // Normalize the email address for comparison (lowercase)
            emailAddress = emailAddress.ToLowerInvariant().Trim();
            LogInfo($"Searching for emails to/from '{emailAddress}' in all mailboxes");

            // Build a search string by enclosing the email address in quotes
            var searchString = $"\"{emailAddress}\"";

            foreach (var mailbox in _mailboxes)
            {
                // Search received emails in the Inbox
                try
                {
                    //LogDebug($"Searching Inbox for emails in mailbox '{mailbox}'");
                    var inboxResponse = await _graphClient
                        .Users[mailbox]
                        .MailFolders["Inbox"]
                        .Messages
                        .GetAsync(config =>
                        {
                            config.QueryParameters.Top = 20;
                            // Remove OrderBy as it's not supported with $search
                            config.QueryParameters.Search = searchString;
                            config.QueryParameters.Select = new[]
                            {
                        "id", "conversationId", "subject", "bodyPreview", "receivedDateTime",
                        "sender", "toRecipients", "ccRecipients", "internetMessageId"
                            };
                            config.Headers.Add("ConsistencyLevel", "eventual");
                        });

                    if (inboxResponse?.Value != null)
                    {
                        var receivedEmails = inboxResponse.Value
                            .Select(m => MapToDetailedEmailMessage(m, mailbox, false, emailAddress))
                            .ToList();
                        LogDebug($"Found {receivedEmails.Count} received emails in mailbox '{mailbox}'");
                        allEmails.AddRange(receivedEmails);
                    }
                }
                catch (Exception ex)
                {
                    errors.Add($"{mailbox} Inbox: {ex.Message}");
                    LogError($"Error searching Inbox in mailbox {mailbox}: {ex.Message}");
                }

                // Search sent emails in SentItems
                try
                {
                    //LogDebug($"Searching SentItems for emails in mailbox '{mailbox}'");
                    var sentResponse = await _graphClient
                        .Users[mailbox]
                        .MailFolders["SentItems"]
                        .Messages
                        .GetAsync(config =>
                        {
                            config.QueryParameters.Top = 20;
                            // Remove OrderBy since $search doesn't support it
                            config.QueryParameters.Search = searchString;
                            config.QueryParameters.Select = new[]
                            {
                        "id", "conversationId", "subject", "bodyPreview", "sentDateTime",
                        "sender", "toRecipients", "ccRecipients", "internetMessageId"
                            };
                            config.Headers.Add("ConsistencyLevel", "eventual");
                        });

                    if (sentResponse?.Value != null)
                    {
                        var sentEmails = sentResponse.Value
                            .Select(m => MapToDetailedEmailMessage(m, mailbox, true, emailAddress))
                            .ToList();
                        //LogDebug($"Found {sentEmails.Count} sent emails in mailbox '{mailbox}'");
                        allEmails.AddRange(sentEmails);
                    }
                }
                catch (Exception ex)
                {
                    errors.Add($"{mailbox} SentItems: {ex.Message}");
                    LogError($"Error searching SentItems in mailbox {mailbox}: {ex.Message}");
                }
            }

            if (errors.Count > 0 && allEmails.Count == 0)
            {
                LogError($"Failed to search emails for '{emailAddress}' in all mailboxes: {string.Join("; ", errors)}");
            }
            else
            {
                LogInfo($"Search complete. Found {allEmails.Count} total emails related to '{emailAddress}'");
            }

            // Remove duplicates if needed and order the emails by date descending client-side
            var uniqueEmails = RemoveDuplicateEmails(allEmails);
            return uniqueEmails.OrderByDescending(e => e.ReceivedDateTime).Take(count).ToList();
        }

        // Helper method to map a message to our detailed model with recipient information
        private EmailMessage MapToDetailedEmailMessage(Microsoft.Graph.Models.Message message, string mailbox, bool isSent, string clientEmail)
        {
            // Get DateTime (use sentDateTime for sent items, receivedDateTime for inbox)
            var messageDate = isSent 
                ? message.SentDateTime?.DateTime ?? DateTime.MinValue 
                : message.ReceivedDateTime?.DateTime ?? DateTime.MinValue;
            
            // Determine if the sender is from the client or if this is an internal message
            var senderEmail = message.Sender?.EmailAddress?.Address?.ToLowerInvariant();
            var isFromClient = !string.IsNullOrEmpty(senderEmail) && 
                                senderEmail.Equals(clientEmail, StringComparison.OrdinalIgnoreCase) &&
                                !_mailboxes.Contains(senderEmail, StringComparer.OrdinalIgnoreCase);
                                
            var isInternal = false;
            
            // Create the basic email message
            var emailMessage = new EmailMessage
            {
                Subject = message.Subject,
                Sender = message.Sender?.EmailAddress?.Name,
                SenderEmail = senderEmail,
                ReceivedDateTime = messageDate,
                Preview = message.BodyPreview,
                Mailbox = mailbox,
                MessageId = message.Id,
                ConversationId = message.ConversationId,
                InternetMessageId = message.InternetMessageId, // Add Internet Message ID for better deduplication
                FromClient = isFromClient
            };
            
            // Map ToRecipients
            if (message.ToRecipients != null)
            {
                foreach (var recipient in message.ToRecipients)
                {
                    if (recipient.EmailAddress != null)
                    {
                        emailMessage.ToRecipients.Add(new Recipient
                        {
                            Name = recipient.EmailAddress.Name,
                            EmailAddress = recipient.EmailAddress.Address
                        });
                        
                        // If this is one of our monitored mailboxes, add it to recipients list
                        if (_mailboxes.Any(m => m.Equals(recipient.EmailAddress.Address, StringComparison.OrdinalIgnoreCase)))
                        {
                            emailMessage.AllRecipientMailboxes.Add(recipient.EmailAddress.Address);
                            
                            // Set as direct recipient
                            if (string.IsNullOrEmpty(emailMessage.DirectRecipientMailbox))
                            {
                                emailMessage.DirectRecipientMailbox = recipient.EmailAddress.Address;
                            }
                        }
                    }
                }
            }
            
            // Map CcRecipients
            if (message.CcRecipients != null)
            {
                foreach (var recipient in message.CcRecipients)
                {
                    if (recipient.EmailAddress != null)
                    {
                        emailMessage.CcRecipients.Add(new Recipient
                        {
                            Name = recipient.EmailAddress.Name,
                            EmailAddress = recipient.EmailAddress.Address
                        });
                        
                        // If this is one of our monitored mailboxes, add it to recipients list
                        if (_mailboxes.Any(m => m.Equals(recipient.EmailAddress.Address, StringComparison.OrdinalIgnoreCase)))
                        {
                            emailMessage.AllRecipientMailboxes.Add(recipient.EmailAddress.Address);
                        }
                    }
                }
            }
            
            // Check if this is an internal message (between internal mailboxes with client CC'ed)
            // Only consider sent items for this check
            if (isSent) 
            {
                // Message is sent from internal mailbox
                var senderIsInternal = _mailboxes.Any(m => m.Equals(senderEmail, StringComparison.OrdinalIgnoreCase));
                
                // Check if there are internal mailbox recipients
                var hasInternalRecipients = emailMessage.ToRecipients
                    .Any(r => _mailboxes.Any(m => m.Equals(r.EmailAddress, StringComparison.OrdinalIgnoreCase))) ||
                    emailMessage.CcRecipients
                    .Any(r => _mailboxes.Any(m => m.Equals(r.EmailAddress, StringComparison.OrdinalIgnoreCase)));
                
                // Check if client is only CC'ed
                var clientIsCced = emailMessage.CcRecipients
                    .Any(r => r.EmailAddress?.Equals(clientEmail, StringComparison.OrdinalIgnoreCase) == true);
                
                // If sender is internal, has internal recipients, and client is CC'ed but not a direct recipient
                isInternal = senderIsInternal && hasInternalRecipients && clientIsCced;
                
                emailMessage.IsInternal = isInternal;
            }
            
            return emailMessage;
        }

        // Helper function to remove duplicate emails based on message ID
        private List<EmailMessage> RemoveDuplicateEmails(List<EmailMessage> emails)
        {
            //Console.WriteLine("RemoveDups-----");

            var uniqueEmails = new List<EmailMessage>();
            
            // First, separate messages by type
            var clientMessages = emails.Where(e => e.FromClient).ToList();
            var internalMessages = emails.Where(e => e.IsInternal).ToList();
            var otherMessages = emails.Where(e => !e.FromClient && !e.IsInternal).ToList();
            
            // Group by InternetMessageId first (most accurate) then by MessageId
            
            // Process client messages - all should be displayed
            uniqueEmails.AddRange(DeduplicateMessageGroup(clientMessages));
            
            // Process internal messages - should only appear once
            uniqueEmails.AddRange(DeduplicateMessageGroup(internalMessages));
            
            // Process other messages
            uniqueEmails.AddRange(DeduplicateMessageGroup(otherMessages));
            
            LogDebug($"Found {uniqueEmails.Count} unique emails from {emails.Count} total");
            return uniqueEmails;
        }
        
        // Helper method to deduplicate a group of messages
        private List<EmailMessage> DeduplicateMessageGroup(List<EmailMessage> messages)
        {
            if (messages.Count == 0) return new List<EmailMessage>();
            
            var uniqueMessages = new List<EmailMessage>();
            var messageGroups = new Dictionary<string, List<EmailMessage>>();
            
            // First try to group by InternetMessageId
            if (messages.Any(e => !string.IsNullOrEmpty(e.InternetMessageId)))
            {
                messageGroups = messages
                    .Where(e => !string.IsNullOrEmpty(e.InternetMessageId))
                    .GroupBy(e => e.InternetMessageId)
                    .ToDictionary(g => g.Key, g => g.ToList());
                
                // Process emails without InternetMessageId separately
                var messagesWithoutInternetId = messages
                    .Where(e => string.IsNullOrEmpty(e.InternetMessageId))
                    .ToList();
                
                if (messagesWithoutInternetId.Any())
                {
                    var fallbackGroups = messagesWithoutInternetId
                        .Where(e => !string.IsNullOrEmpty(e.MessageId))
                        .GroupBy(e => e.MessageId)
                        .ToDictionary(g => g.Key, g => g.ToList());
                    
                    // Add these groups to our main dictionary
                    foreach (var group in fallbackGroups)
                    {
                        messageGroups[group.Key] = group.Value;
                    }
                    
                    // Any remaining unmatched emails just add directly
                    foreach (var email in messagesWithoutInternetId.Where(e => string.IsNullOrEmpty(e.MessageId)))
                    {
                        uniqueMessages.Add(email);
                    }
                }
            }
            else
            {
                // Fall back to regular MessageId
                messageGroups = messages
                    .Where(e => !string.IsNullOrEmpty(e.MessageId))
                    .GroupBy(e => e.MessageId)
                    .ToDictionary(g => g.Key, g => g.ToList());
            }
            
            // For each group, pick the best one
            foreach (var group in messageGroups.Values)
            {
                if (group.Count == 1)
                {
                    // Only one instance of this message, just add it
                    uniqueMessages.Add(group[0]);
                }
                else
                {
                    // Multiple instances, prioritize:
                    // 1. If internal message, we only want one copy (first in the mailbox list)
                    // 2. Direct (To:) recipients over CC recipients
                    // 3. First mailbox in our list that received it directly
                    
                    // Check if this is a message where we want just one instance
                    if (group.Any(e => e.IsInternal))
                    {
                        // For internal messages, just take the first one from our mailbox list
                        var prioritizedEmail = group
                            .OrderBy(e => _mailboxes.IndexOf(e.Mailbox))
                            .First();
                            
                        // Add all other mailboxes that received it
                        foreach (var email in group)
                        {
                            foreach (var otherMailbox in email.AllRecipientMailboxes)
                            {
                                if (!prioritizedEmail.AllRecipientMailboxes.Contains(otherMailbox))
                                {
                                    prioritizedEmail.AllRecipientMailboxes.Add(otherMailbox);
                                }
                            }
                        }
                        
                        uniqueMessages.Add(prioritizedEmail);
                    }
                    else
                    {
                        // For other messages, look for direct recipients first
                        var directEmails = group.Where(e => !string.IsNullOrEmpty(e.DirectRecipientMailbox)).ToList();
                        if (directEmails.Any())
                        {
                            // Pick the first mailbox in our ordered list that got it directly
                            var prioritizedEmail = directEmails
                                .OrderBy(e => _mailboxes.IndexOf(e.DirectRecipientMailbox))
                                .First();
                            
                            // Add all other mailboxes as CC info
                            var otherMailboxes = group
                                .Where(e => e.MessageId == prioritizedEmail.MessageId && e.Mailbox != prioritizedEmail.Mailbox)
                                .Select(e => e.Mailbox)
                                .Distinct()
                                .ToList();
                                
                            // Add the info about which other mailboxes received this
                            foreach (var otherMailbox in otherMailboxes)
                            {
                                if (!prioritizedEmail.AllRecipientMailboxes.Contains(otherMailbox))
                                {
                                    prioritizedEmail.AllRecipientMailboxes.Add(otherMailbox);
                                }
                            }
                            
                            uniqueMessages.Add(prioritizedEmail);
                        }
                        else
                        {
                            // No direct recipients, just take the first one
                            uniqueMessages.Add(group[0]);
                        }
                    }
                }
            }
            
            return uniqueMessages;
        }
    }
}
