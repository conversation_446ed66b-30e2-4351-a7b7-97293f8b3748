﻿/* Main container for the entire header */
.client-header-container {
    display: flex;
    align-items: center;
    gap: 20px;
    width: 100%;
    min-height: 80px;
}
/* Logo section styling */
.client-logo-section-sf {
    overflow: hidden;
    display: flex;
    /*flex: 0 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;*/
}

.client-logo-container-sf {
    max-height: 80px;
    /*    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
    border-radius: 12px;
    padding: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    max-width: 140px;
    
    display: flex;
    align-items: center;
    justify-content: center;*/
}

.client-logo-sf {
    max-height: 80px;
    /*max-width: 120px;
    max-height: 60px;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: 6px;
    transition: all 0.3s ease;*/
}
/* Logo section styling */
.client-logo-section {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.client-logo-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
    border-radius: 12px;
    padding: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    max-width: 140px;
    max-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.client-logo-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border-color: #036ac4;
}

.client-logo {
    max-width: 120px;
    max-height: 60px;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: 6px;
    transition: all 0.3s ease;
}

/* Client info section - takes remaining space */
.client-info-section {
    flex: 1;
    min-width: 0; /* Allows flex item to shrink below content size */
}

.top-rowone {
    width: 100%;
    height: 50px;
    overflow: hidden;
}
.client-name {
    font-weight: 800;
    font-style: normal;
    font-size: 2.5em;
    position: relative;
    top: 5px;
    color: #2d2d2d;
    text-shadow: 1px 2px 1px #afafaf;
}

.client-phone {
    float: right;
}

.client-phonenumber {
    font-weight: 300;
    font-style: normal;
    font-size: 2.5em;
    position: relative;
    top: -10px;
    left: -5px;
    color: #8d8d8d;
}

.phone-icon {
    position: relative;
    top: 2px;
    color: #8d8d8d;
    filter: grayscale(100%);
}

    .phone-icon:hover {
        color: #0f6cbd;
        filter: grayscale(0%);
    }

.email-icon {
    position: relative;
    top: 2px;
    color: #8d8d8d;
}

.map-icon {
    position: relative;
    top: 2px;
    color: #8d8d8d;
}
.client-data {
    font-weight: 300;
    position: relative;
    left: -4px;
    top:-4px;
    font-style: normal;
    font-size: 1.125em;
    margin-right: 25px;
    text-decoration: none;
    color: #1e1e1e;
}

    .client-data:hover {
        color: #0f6cbd;
        cursor: pointer;
    }

.data-icon {
    position: relative;
    top: -1px;
    left:-1px;
    color: #8d8d8d;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .client-header-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .client-logo-container {
        max-width: 100px;
        max-height: 60px;
        padding: 8px;
    }
    
    .client-logo {
        max-width: 85px;
        max-height: 45px;
    }
    
    .client-name {
        font-size: 2em;
    }
    
    .client-phonenumber {
        font-size: 2em;
    }
}

