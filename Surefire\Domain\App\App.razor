﻿@namespace Surefire.Components
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Surefire</title>
    <base href="/" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <link rel="stylesheet" href="app.css" />
    <link rel="stylesheet" href="Surefire.styles.css" />
    <link rel="stylesheet" href="https://use.typekit.net/kir6qlu.css">
    <link rel="stylesheet" href="_content/Syncfusion.Blazor.Themes/fluent2.css" />
    <ImportMap />
    <HeadOutlet @rendermode="PageRenderMode" />
    <script src="_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js" type="text/javascript"></script>
    <script src="_content/Syncfusion.Blazor.SfPdfViewer/scripts/syncfusion-blazor-sfpdfviewer.min.js" type="text/javascript"></script>
    <script src="/scripts/forms.js" type="text/javascript"></script>
</head>

<body>
    <Routes @rendermode="PageRenderMode" />
    <script src="_framework/blazor.web.js"></script>
    <style>
        :root html, body {
            margin: 0px !important;
            padding: 0px !important;
        }
    </style>
</body>
</html>
@code {
    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    private IComponentRenderMode? PageRenderMode => HttpContext.AcceptsInteractiveRouting() ? InteractiveServer : null;
    //On init log something to the console
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
    }
    
    protected override void OnParametersSet()
    {
        base.OnParametersSet();
    }
}
