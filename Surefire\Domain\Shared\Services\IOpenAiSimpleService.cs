using System.Threading;
using System.Threading.Tasks;

namespace Surefire.Domain.OpenAI.Simple // Adjust namespace as needed
{
    /// <summary>
    /// Defines a service for making simple text-based requests to OpenAI's Chat Completions API.
    /// </summary>
    public interface IOpenAISimpleService
    {
        /// <summary>
        /// Sends a text prompt to the specified OpenAI model and returns the generated text response.
        /// </summary>
        /// <param name="prompt">The text prompt to send to the model.</param>
        /// <param name="model">The OpenAI model to use (e.g., "gpt-4o-mini", "gpt-3.5-turbo"). Defaults to "gpt-4o-mini".</param>
        /// <param name="cancellationToken">Optional cancellation token.</param>
        /// <returns>The text response from the OpenAI model, or null if an error occurred.</returns>
        Task<string?> GenerateResponseAsync(
            string prompt,
            string model = "gpt-4o-mini", // Provide a sensible default
            CancellationToken cancellationToken = default);
    }
}