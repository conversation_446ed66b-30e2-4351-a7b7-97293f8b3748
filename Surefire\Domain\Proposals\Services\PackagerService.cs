using System;
using System.IO;
using System.Threading.Tasks;
using System.Collections.Generic;
using Syncfusion.DocIO;
using Syncfusion.DocIO.DLS;
using Syncfusion.DocIORenderer;
using Syncfusion.Pdf;
using Syncfusion.Pdf.Parsing;
using Surefire.Domain.Attachments.Models;
using Surefire.Domain.Attachments.Services;
using Surefire.Domain.Logs;
using Surefire.Domain.Shared.Helpers;

namespace Surefire.Domain.Proposals.Services
{
    public class PackagerService
    {
        private readonly AttachmentService _attachmentService;
        private readonly ILoggingService _log;

        public PackagerService(
            AttachmentService attachmentService,
            ILoggingService log)
        {
            _attachmentService = attachmentService;
            _log = log;
        }

        /// <summary>
        /// Creates a proposal package by converting the Word document to PDF and appending additional forms
        /// </summary>
        /// <param name="proposal">The proposal containing package settings</param>
        /// <param name="proposalWordAttachment">The Word document attachment to convert</param>
        /// <param name="acordAttachment">Optional Acord PDF attachment</param>
        /// <param name="supplementalAttachment">Optional Supplemental PDF attachment</param>
        /// <param name="attachmentGroupId">Attachment group ID for saving the package</param>
        /// <param name="clientId">Client ID</param>
        /// <param name="renewalId">Renewal ID</param>
        /// <returns>The created package PDF attachment</returns>
        public async Task<Attachment> CreateProposalPackageAsync(Proposal proposal, Attachment proposalWordAttachment, Attachment? acordAttachment, Attachment? supplementalAttachment, int attachmentGroupId, int clientId, int renewalId)
        {
            try
            {
                // Create a list to hold all PDF streams that need to be merged
                var pdfStreams = new List<MemoryStream>();
                
                try
                {
                    // Step 1: Convert Word document to PDF
                    var wordPdfStream = await ConvertWordToPdf(proposalWordAttachment);
                    pdfStreams.Add(wordPdfStream);

                    // Step 2: Add Acord PDF if available
                    if (acordAttachment != null)
                    {
                        var acordStream = await LoadPdfToStream(acordAttachment, "Acord");
                        if (acordStream != null)
                            pdfStreams.Add(acordStream);
                    }

                    // Step 3: Add Supplemental PDF if available
                    if (supplementalAttachment != null)
                    {
                        var supplementalStream = await LoadPdfToStream(supplementalAttachment, "Supplemental");
                        if (supplementalStream != null)
                            pdfStreams.Add(supplementalStream);
                    }

                    // Step 4: Add form PDFs based on proposal settings
                    await AddFormPdfStreams(proposal, pdfStreams);

                    // Step 5: Merge all PDFs and save
                    var packageAttachment = await MergeAndSavePdfs(pdfStreams, proposal, attachmentGroupId, clientId, renewalId);

                    //await _log.LogAsync(LogLevel.Information, $"Successfully created proposal package: {packageAttachment.OriginalFileName}", "PackagerService");

                    return packageAttachment;
                }
                finally
                {
                    // Dispose all streams
                    foreach (var stream in pdfStreams)
                    {
                        stream?.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                await _log.LogAsync(LogLevel.Error, 
                    $"Error creating proposal package: {ex.Message}", 
                    "PackagerService", ex);
                throw;
            }
        }

        private async Task<MemoryStream> ConvertWordToPdf(Attachment wordAttachment)
        {
            var wordFilePath = Path.Combine( Directory.GetCurrentDirectory(), "wwwroot", wordAttachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()), wordAttachment.HashedFileName);

            if (!File.Exists(wordFilePath))
            {
                throw new FileNotFoundException($"Word document not found: {wordFilePath}");
            }

            // Load the Word document
            using var fileStream = new FileStream(wordFilePath, FileMode.Open, FileAccess.Read);
            using var wordDocument = new WordDocument(fileStream, FormatType.Docx);
            
            EnsureProperMargins(wordDocument);
            LoadCustomFonts(wordDocument);

            using var render = new DocIORenderer();
            
            // Convert Word document to PDF
            using var proposalPdf = render.ConvertToPDF(wordDocument);
            var memoryStream = new MemoryStream();
            proposalPdf.Save(memoryStream);
            memoryStream.Position = 0;

            return memoryStream;
        }

        private void EnsureProperMargins(WordDocument wordDocument)
        {
            foreach (WSection section in wordDocument.Sections)
            {
                // Convert 0.5 inches to points (1 inch = 72 points)
                float marginInPoints = 0.5f * 72f; // 36 points
                
                section.PageSetup.Margins.Top = marginInPoints;
                section.PageSetup.Margins.Bottom = marginInPoints;
                section.PageSetup.Margins.Left = marginInPoints;
                section.PageSetup.Margins.Right = marginInPoints;
            }
        }

        private void LoadCustomFonts(WordDocument wordDocument)
        {
            var fontsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "fonts");
            
            if (!Directory.Exists(fontsDirectory))
            {
                _log.LogAsync(LogLevel.Warning, $"Fonts directory not found: {fontsDirectory}. Custom fonts will not be loaded.", "PackagerService");
                return;
            }

            try
            {
                var fontFiles = Directory.GetFiles(fontsDirectory, "*.ttf");
                
                // Note: Font embedding will rely on system fonts and EmbedFonts=true setting
                // The fonts in the wwwroot/fonts directory should be accessible if they're installed on the system
                
                foreach (var fontFile in fontFiles)
                {
                    var fileName = Path.GetFileName(fontFile);
                }
            }
            catch (Exception ex)
            {
                _log.LogAsync(LogLevel.Error, $"Failed to configure custom fonts: {ex.Message}", "PackagerService", ex);
            }
        }

        private async Task<MemoryStream?> LoadPdfToStream(Attachment pdfAttachment, string attachmentType)
        {
            await _log.LogAsync(LogLevel.Information, 
                $"Loading {attachmentType} PDF: {pdfAttachment.OriginalFileName}", 
                "PackagerService");

            var pdfFilePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", pdfAttachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()), pdfAttachment.HashedFileName);

            if (!File.Exists(pdfFilePath))
            {
                await _log.LogAsync(LogLevel.Warning, $"{attachmentType} PDF not found: {pdfFilePath}", "PackagerService");
                return null;
            }

            // Load the PDF into a memory stream
            var memoryStream = new MemoryStream();
            using (var fileStream = new FileStream(pdfFilePath, FileMode.Open, FileAccess.Read))
            {
                await fileStream.CopyToAsync(memoryStream);
            }
            memoryStream.Position = 0;

            return memoryStream;
        }

        private async Task AddFormPdfStreams(Proposal proposal, List<MemoryStream> pdfStreams)
        {
            var formsDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "forms");

            // Add SL-2 form if requested
            if (proposal.IncludeSL2)
            {
                var sl2Stream = await LoadFormPdfToStream(formsDirectory, "sl-2-2024-01.pdf", "SL-2");
                if (sl2Stream != null)
                    pdfStreams.Add(sl2Stream);
            }

            // Add D1 form if requested
            if (proposal.IncludeD1)
            {
                var d1Stream = await LoadFormPdfToStream(formsDirectory, "d1-form-rev-01-01-2020.pdf", "D1");
                if (d1Stream != null)
                    pdfStreams.Add(d1Stream);
            }

            // Add D2 form if requested
            if (proposal.IncludeD2)
            {
                var d2Stream = await LoadFormPdfToStream(formsDirectory, "d2-form-rev-01-01-2020.pdf", "D2");
                if (d2Stream != null)
                    pdfStreams.Add(d2Stream);
            }
        }

        private async Task<MemoryStream?> LoadFormPdfToStream(string formsDirectory, string formFileName, string formType)
        {
            var formPath = Path.Combine(formsDirectory, formFileName);

            if (!File.Exists(formPath))
            {
                await _log.LogAsync(LogLevel.Warning, $"{formType} form not found: {formPath}", "PackagerService");
                return null;
            }

            // Load the form PDF into a memory stream
            var memoryStream = new MemoryStream();
            using (var fileStream = new FileStream(formPath, FileMode.Open, FileAccess.Read))
            {
                await fileStream.CopyToAsync(memoryStream);
            }
            memoryStream.Position = 0;

            return memoryStream;
        }

        private async Task<Attachment> MergeAndSavePdfs(List<MemoryStream> pdfStreams, Proposal proposal, int attachmentGroupId, int clientId, int renewalId)
        {

            // Create the final PDF document
            using var finalPdf = new PdfDocument();
            int totalPages = 0;

            // Merge all PDF streams
            foreach (var stream in pdfStreams)
            {
                if (stream != null && stream.Length > 0)
                {
                    stream.Position = 0;
                    using var sourcePdf = new PdfLoadedDocument(stream);
                    finalPdf.ImportPageRange(sourcePdf, 0, sourcePdf.Pages.Count - 1);
                    totalPages += sourcePdf.Pages.Count;
                }
            }

            // Create output file path
            var outputFileName = $"ProposalPackage_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
            var outputDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", "clients", clientId.ToString(), attachmentGroupId.ToString());

            if (!Directory.Exists(outputDirectory))
            {
                Directory.CreateDirectory(outputDirectory);
            }

            var hash = StringHelper.GenerateFiveCharacterHash(outputFileName);
            var hashedFileName = $"{Path.GetFileNameWithoutExtension(outputFileName)}_{hash}.pdf";
            var outputPath = Path.Combine(outputDirectory, hashedFileName);

            // Save the final PDF
            using var outputStream = new FileStream(outputPath, FileMode.Create, FileAccess.Write);
            finalPdf.Save(outputStream);

            // Get file size
            var fileInfo = new FileInfo(outputPath);

            // Create attachment record
            var attachment = new Attachment
            {
                OriginalFileName = outputFileName,
                HashedFileName = hashedFileName,
                LocalPath = $"uploads/clients/{clientId}/{attachmentGroupId}",
                FileFormat = ".pdf",
                FileSize = fileInfo.Length,
                DateCreated = DateTime.UtcNow,
                AttachmentGroupId = attachmentGroupId,
                ClientId = clientId,
                RenewalId = renewalId,
                Description = "Generated Proposal Package",
                IsProposal = true
            };

            // Save attachment to database
            await _attachmentService.SaveAttachmentDirectlyAsync(attachment);

            //await _log.LogAsync(LogLevel.Information, $"Successfully saved proposal package PDF: {outputFileName} ({fileInfo.Length} bytes, {totalPages} pages)", "PackagerService");

            return attachment;
        }
    }
} 