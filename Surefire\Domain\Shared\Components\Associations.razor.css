﻿body {
}

.associations-container {
    margin-bottom: 20px;
}

.primary-association {
    margin-bottom: 8px;
    font-size: 16px;
}

.clickable-client {
    cursor: pointer;
    color: #0066cc;
    padding-right: 8px;
}

.clickable-client:hover {
    text-decoration: underline;
    
}

.mt-2 {
    margin-top: 10px;
}

.search-box {
    position: relative;
    margin-bottom: 15px;
}

.search-spinner {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
}

.search-results-container {
    max-height: 250px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    margin-bottom: 15px;
}

.search-results-table {
    width: 100%;
    border-collapse: collapse;
}

.search-results-table th {
    background-color: #f5f5f5;
    padding: 8px;
    text-align: left;
    font-weight: 500;
    border-bottom: 1px solid #e0e0e0;
}

.search-results-table td {
    padding: 8px;
    border-bottom: 1px solid #e0e0e0;
}

.search-result-row {
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-result-row:hover {
    background-color: #f0f7ff;
}

.empty-search-results {
    padding: 15px;
    text-align: center;
    color: #666;
    font-style: italic;
}

.selected-result {
    margin-top: 20px;
    padding: 12px;
    background-color: #f0f7ff;
    border-radius: 4px;
    border-left: 4px solid #0066cc;
}

.selected-result-header {
    font-weight: 500;
    margin-bottom: 5px;
    color: #333;
}

.selected-result-name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
}

.selected-result-parent {
    font-size: 13px;
    color: #666;
}

.mt-4 {
    margin-top: 20px;
}

.mb-2 {
    margin-bottom: 5px;
}

.mb-3 {
    margin-bottom: 15px;
}

.txt-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-group {
    margin-bottom: 15px;
}

.dialog-content {
    padding: 20px;
}

.empty-message {
    padding: 15px;
    text-align: center;
    color: #666;
    font-style: italic;
    margin-bottom: 15px;
}

.relationship-category-header {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    font-weight: bold;
    border-bottom: 1px solid #e0e0e0;
}

.relationship-type-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
}

.category-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
    text-transform: uppercase;
}

.category-badge.business {
    background-color: #e3f2fd;
    color: #1976d2;
}

.category-badge.personal {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.category-badge.generic {
    background-color: #e8f5e9;
    color: #2e7d32;
}

/* Ensure the dropdown items have proper spacing */
.e-dropdownbase .e-list-item {
    padding: 0;
}

.e-dropdownbase .e-list-item:hover {
    background-color: #f5f5f5;
}

/* Box Sections ------------------------- */
.sf-section-title {
    font-family: "montserrat", sans-serif;
    border-bottom-right-radius: 8px;
    background-color: #fbfbfb;
    text-transform: uppercase;
    border-bottom: 1px solid #00000026;
    letter-spacing: 2px;
    padding-right: 10px;
    padding-bottom: 3px;
    position: relative;
    padding-left: 0px;
    font-weight: 400;
    font-size: 1em;
    color: #717171;
    top: 8px;
}

.sf-section-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    padding-top: 16px;
    padding-left: 0px;
    border-left: 1px solid #dedede;
    border-bottom: 1px solid #dedede;
    border-right: 1px solid #fff;
    background: linear-gradient(182deg, rgba(219,219,219,1) 0%,rgba(239,239,239,1) 27%,rgba(244,244,244,1) 100%);
}

.sf-txt-column {
    font-family: "montserrat", sans-serif;
    font-weight: 300;
    font-size: .75em;
    color: #5f5f5f;
    text-transform: uppercase;
}

/* --------------------------------------------------------- */
/* SECTION TABLE ------------------------------------------- */
/* --------------------------------------------------------- */
.flauxentTable {
    width: 100%;
    border-collapse: collapse; /* Essential for clean lines */
    border-spacing: 0;
    font-family: var(--body-font, Segoe UI, -apple-system, BlinkMacSystemFont, Roboto, "Helvetica Neue", sans-serif);
    font-size: var(--type-ramp-base-font-size, 14px);
    line-height: var(--type-ramp-base-line-height, 20px);
    color: var(--neutral-foreground-rest, #212121);
    margin-bottom: .3rem; /* Spacing below the table          */
}

    /* --- Table Header (thead) ---                          */
    .flauxentTable thead th {
        padding: 0px 12px;
        font-weight: 300;
        border-bottom: 1px solid #c3c3c3;
        white-space: nowrap;
        vertical-align: middle;
        text-align: left;
    }

    /* --- Table Body (tbody) ---                            */
    .flauxentTable tbody tr {
        transition: background-color 0.15s ease-in-out;
    }

        /* Zebra striping                                    */
        .flauxentTable tbody tr:nth-child(even) {
            background-color: #ffffff2d;
        }

    .flauxentTable thead th:first-child {
        padding-left: 10px !important;
    }

    .flauxentTable tbody td:first-child {
        padding-left: 10px !important;
    }

    /* Hover effect                                      */
    .flauxentTable tbody tr:hover {
        background-color: #00000010;
    }

    /* --- Table Cells (td) ---                              */
    .flauxentTable tbody td {
        padding: 0px 12px;
        border-bottom: 1px solid var(--neutral-stroke-divider-rest, #e0e0e0);
        vertical-align: middle;
        line-height: var(--type-ramp-base-line-height, 20px);
    }

    .flauxentTable tbody tr:last-child td {
        border-bottom: none;
    }

    /* --- Action Buttons Alignment ---                      */
    .flauxentTable td:last-child {
        text-align: right;
        white-space: nowrap;
    }

        .flauxentTable td:last-child > fluent-button + fluent-button,
        .flauxentTable td:last-child > button + button {
            margin-left: 4px;
        }

.featuredrow {
    font-weight: bold;
    font-size: 1.2em;
    font-family: 'Montserrat';
    position: relative;
    top: 2px;
}

.shmedium {
    width: 50px;
    text-align: center;
}

.tlabel {
}
/* END TABLE -----------------------------------------------  */
/* ---------------------------------------------------------  */

.primary-association {
    width: 100%;
    text-align: right;
    position:relative;
    top: -15px;
    font-size:.9em;
}