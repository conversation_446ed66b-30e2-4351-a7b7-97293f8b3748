using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Surefire.Domain.Agents.Models
{
    /// <summary>
    /// Represents the execution context for an agent - whether it's from AI chat or button click
    /// </summary>
    public enum AgentExecutionContext
    {
        /// <summary>
        /// Agent triggered from AI chat with potential missing parameters
        /// </summary>
        AIChat,
        
        /// <summary>
        /// Agent triggered from action button with all parameters provided
        /// </summary>
        ActionButton
    }

    /// <summary>
    /// Represents the type of outcome an agent action produces
    /// </summary>
    public enum AgentOutcomeType
    {
        /// <summary>
        /// Action completed with a simple message response
        /// </summary>
        Message,
        
        /// <summary>
        /// Action completed and navigated to a specific page/screen
        /// </summary>
        Navigation,
        
        /// <summary>
        /// Action completed with downloadable data/files
        /// </summary>
        Download,
        
        /// <summary>
        /// Action completed with structured data for display
        /// </summary>
        Data,
        
        /// <summary>
        /// Action requires user confirmation before proceeding
        /// </summary>
        Confirmation,
        
        /// <summary>
        /// Action failed with error information
        /// </summary>
        Error
    }

    /// <summary>
    /// Parameter validation and extraction status
    /// </summary>
    public enum ParameterStatus
    {
        /// <summary>
        /// Parameter is present and valid
        /// </summary>
        Valid,
        
        /// <summary>
        /// Parameter is missing and required
        /// </summary>
        Missing,
        
        /// <summary>
        /// Parameter is present but invalid format/value
        /// </summary>
        Invalid,
        
        /// <summary>
        /// Parameter is ambiguous and needs clarification
        /// </summary>
        Ambiguous
    }

    /// <summary>
    /// Definition of a parameter required by an agent
    /// </summary>
    public class AgentParameterDefinition
    {
        /// <summary>
        /// Parameter name (e.g., "client_name", "policy_type")
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// Human-readable parameter description
        /// </summary>
        public string Description { get; set; } = string.Empty;
        
        /// <summary>
        /// Data type of the parameter
        /// </summary>
        public Type ParameterType { get; set; } = typeof(string);
        
        /// <summary>
        /// Whether this parameter is required
        /// </summary>
        public bool IsRequired { get; set; } = true;
        
        /// <summary>
        /// Default value if not provided
        /// </summary>
        public object? DefaultValue { get; set; }
        
        /// <summary>
        /// Valid values for enumerated parameters
        /// </summary>
        public List<string> ValidValues { get; set; } = new();
        
        /// <summary>
        /// AI-friendly prompt for extracting this parameter from user input
        /// </summary>
        public string ExtractionPrompt { get; set; } = string.Empty;
        
        /// <summary>
        /// Clarification question to ask user if parameter is missing/ambiguous
        /// </summary>
        public string ClarificationQuestion { get; set; } = string.Empty;
        
        /// <summary>
        /// Whether this parameter should use entity extraction (client names, carriers, etc.)
        /// </summary>
        public bool UseEntityExtraction { get; set; } = false;
        
        /// <summary>
        /// Entity type for extraction (ClientName, CarrierName, PolicyType, etc.)
        /// </summary>
        public string? EntityType { get; set; }
    }

    /// <summary>
    /// Result of parameter validation for a specific parameter
    /// </summary>
    public class ParameterValidationResult
    {
        /// <summary>
        /// Parameter name
        /// </summary>
        public string ParameterName { get; set; } = string.Empty;
        
        /// <summary>
        /// Validation status
        /// </summary>
        public ParameterStatus Status { get; set; }
        
        /// <summary>
        /// Extracted/validated value
        /// </summary>
        public object? Value { get; set; }
        
        /// <summary>
        /// Error or clarification message
        /// </summary>
        public string? Message { get; set; }
        
        /// <summary>
        /// Suggested values if ambiguous
        /// </summary>
        public List<string> SuggestedValues { get; set; } = new();
    }

    /// <summary>
    /// Complete parameter validation result for an agent request
    /// </summary>
    public class AgentParameterValidation
    {
        /// <summary>
        /// Whether all required parameters are valid
        /// </summary>
        public bool IsComplete { get; set; }
        
        /// <summary>
        /// Individual parameter validation results
        /// </summary>
        public List<ParameterValidationResult> Parameters { get; set; } = new();
        
        /// <summary>
        /// Missing parameters that need clarification
        /// </summary>
        public List<string> MissingParameters { get; set; } = new();
        
        /// <summary>
        /// Ambiguous parameters that need clarification
        /// </summary>
        public List<string> AmbiguousParameters { get; set; } = new();
        
        /// <summary>
        /// Clarification message to present to user
        /// </summary>
        public string? ClarificationMessage { get; set; }
        
        /// <summary>
        /// Validated parameters ready for agent execution
        /// </summary>
        public Dictionary<string, object> ValidatedParameters { get; set; } = new();
    }

    /// <summary>
    /// Navigation information for agents that end on a specific screen
    /// </summary>
    public class AgentNavigationInfo
    {
        /// <summary>
        /// URL or route to navigate to
        /// </summary>
        public string NavigationUrl { get; set; } = string.Empty;
        
        /// <summary>
        /// Parameters to pass to the target page
        /// </summary>
        public Dictionary<string, object> NavigationParameters { get; set; } = new();
        
        /// <summary>
        /// Form fields to pre-populate on target page
        /// </summary>
        public Dictionary<string, object> PrePopulatedFields { get; set; } = new();
        
        /// <summary>
        /// Whether to open in new tab/window
        /// </summary>
        public bool OpenInNewTab { get; set; } = false;
    }

    /// <summary>
    /// Metadata about a registered task agent
    /// </summary>
    public class TaskAgentDefinition
    {
        /// <summary>
        /// Unique identifier for the agent
        /// </summary>
        public string AgentId { get; set; } = string.Empty;
        
        /// <summary>
        /// Human-readable name
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// Description of what the agent does
        /// </summary>
        public string Description { get; set; } = string.Empty;
        
        /// <summary>
        /// Category for grouping agents (e.g., "Loss Runs", "Certificates", "Communications")
        /// </summary>
        public string Category { get; set; } = string.Empty;
        
        /// <summary>
        /// Keywords and phrases that should trigger this agent
        /// </summary>
        public List<string> TriggerPhrases { get; set; } = new();
        
        /// <summary>
        /// Parameters this agent requires
        /// </summary>
        public List<AgentParameterDefinition> Parameters { get; set; } = new();
        
        /// <summary>
        /// Type of outcome this agent produces
        /// </summary>
        public AgentOutcomeType OutcomeType { get; set; }
        
        /// <summary>
        /// Whether this agent can be triggered from AI chat
        /// </summary>
        public bool SupportsAIChat { get; set; } = true;
        
        /// <summary>
        /// Whether this agent can be triggered from action buttons
        /// </summary>
        public bool SupportsActionButton { get; set; } = true;
        
        /// <summary>
        /// Estimated execution time in seconds
        /// </summary>
        public int EstimatedExecutionSeconds { get; set; } = 30;
        
        /// <summary>
        /// Whether this agent requires user confirmation before execution
        /// </summary>
        public bool RequiresConfirmation { get; set; } = false;
        
        /// <summary>
        /// Navigation information if agent ends on a specific screen
        /// </summary>
        public AgentNavigationInfo? NavigationInfo { get; set; }
    }

    /// <summary>
    /// Request to execute a task agent
    /// </summary>
    public class TaskAgentRequest
    {
        /// <summary>
        /// ID of the agent to execute
        /// </summary>
        public string AgentId { get; set; } = string.Empty;
        
        /// <summary>
        /// Execution context (AI chat or action button)
        /// </summary>
        public AgentExecutionContext Context { get; set; }
        
        /// <summary>
        /// User ID making the request
        /// </summary>
        public string UserId { get; set; } = string.Empty;
        
        /// <summary>
        /// Session ID for conversation context
        /// </summary>
        public string SessionId { get; set; } = string.Empty;
        
        /// <summary>
        /// Raw user input (for AI chat context)
        /// </summary>
        public string? RawInput { get; set; }
        
        /// <summary>
        /// Provided parameters
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new();
        
        /// <summary>
        /// Additional context from the current page/state
        /// </summary>
        public Dictionary<string, object> PageContext { get; set; } = new();
        
        /// <summary>
        /// Whether to skip confirmation even if agent normally requires it
        /// </summary>
        public bool SkipConfirmation { get; set; } = false;
    }

    /// <summary>
    /// Result of task agent execution
    /// </summary>
    public class TaskAgentResult
    {
        /// <summary>
        /// Whether execution was successful
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// Type of outcome produced
        /// </summary>
        public AgentOutcomeType OutcomeType { get; set; }
        
        /// <summary>
        /// Primary message/response to user
        /// </summary>
        public string Message { get; set; } = string.Empty;
        
        /// <summary>
        /// Structured data result (for data outcomes)
        /// </summary>
        public object? Data { get; set; }
        
        /// <summary>
        /// Navigation information (for navigation outcomes)
        /// </summary>
        public AgentNavigationInfo? NavigationInfo { get; set; }
        
        /// <summary>
        /// Download URLs or file information (for download outcomes)
        /// </summary>
        public List<string> DownloadUrls { get; set; } = new();
        
        /// <summary>
        /// Error message if execution failed
        /// </summary>
        public string? ErrorMessage { get; set; }
        
        /// <summary>
        /// Time taken to execute
        /// </summary>
        public TimeSpan ExecutionTime { get; set; }
        
        /// <summary>
        /// Follow-up suggestions for the user
        /// </summary>
        public List<string> Suggestions { get; set; } = new();
        
        /// <summary>
        /// Whether this result requires user confirmation to proceed
        /// </summary>
        public bool RequiresConfirmation { get; set; }
        
        /// <summary>
        /// Confirmation prompt if confirmation is required
        /// </summary>
        public string? ConfirmationPrompt { get; set; }
    }

    /// <summary>
    /// Intent detection result specifically for task agents
    /// </summary>
    public class TaskAgentIntentResult
    {
        /// <summary>
        /// Whether a task agent was identified
        /// </summary>
        public bool AgentIdentified { get; set; }
        
        /// <summary>
        /// ID of the identified agent
        /// </summary>
        public string? AgentId { get; set; }
        
        /// <summary>
        /// Confidence score (0.0 to 1.0)
        /// </summary>
        public double Confidence { get; set; }
        
        /// <summary>
        /// Parameter validation result
        /// </summary>
        public AgentParameterValidation? ParameterValidation { get; set; }
        
        /// <summary>
        /// Whether the request is ready for execution
        /// </summary>
        public bool ReadyForExecution { get; set; }
        
        /// <summary>
        /// Alternative agent suggestions if confidence is low
        /// </summary>
        public List<string> AlternativeSuggestions { get; set; } = new();
    }
} 