USE [surefire-x1-dev]
GO

/****** Object:  Table [dbo].[Products]    Script Date: 6/10/2025 1:50:34 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Products](
	[ProductId] [int] IDENTITY(1,1) NOT NULL,
	[LineName] [nvarchar](max) NOT NULL,
	[Description] [nvarchar](max) NULL,
	[LineCode] [nvarchar](max) NOT NULL,
	[LineNickname] [nvarchar](max) NOT NULL,
 CONSTRAINT [PK_Products] PRIMARY KEY CLUSTERED 
(
	[ProductId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[Products] ADD  DEFAULT (N'') FOR [LineCode]
GO

ALTER TABLE [dbo].[Products] ADD  DEFAULT (N'') FOR [LineNickname]
GO

EXEC sys.sp_addextendedproperty @name=N'LLM_SearchHints', @value=N'Contains insurance coverage lines data. Primary key: ProductId. Columns: LineName, Description, LineCode, LineNickname. This table represents lines of coverage (e.g., Work Comp, General Liability, Auto). There is no ProductName column. To filter by line, use LIKE on both the LineName and LineNickname columns. Join to Policies on ProductId to find related policies.' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Products'
GO
