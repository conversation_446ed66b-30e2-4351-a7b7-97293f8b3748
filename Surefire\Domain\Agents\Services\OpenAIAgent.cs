using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using System.ComponentModel;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using Surefire.Domain.Agents.Models;
using Surefire.Domain.Agents.Interfaces;
using Surefire.Domain.Agents.TaskAgents;
using Surefire.Data;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Text;

namespace Surefire.Domain.Agents.Services
{
    /// <summary>
    /// Advanced OpenAI agent using Semantic Kernel for unified AI operations
    /// </summary>
    public class OpenAIAgent : IOpenAIAgent
    {
        private readonly Kernel _kernel;
        private readonly IChatCompletionService _chatService;
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly ILogger<OpenAIAgent> _logger;
        private readonly string _databaseSchema;
        private readonly Dictionary<string, ChatHistory> _conversationHistory;

        private readonly INavigationAgent _navigationAgent;
        private readonly IEmbeddingService _embeddingService;
        private readonly IVectorStore _vectorStore;
        private readonly IServiceProvider _serviceProvider;

        public OpenAIAgent(
            IConfiguration configuration,
            IDbContextFactory<ApplicationDbContext> dbContextFactory,
            INavigationAgent navigationAgent,
            IEmbeddingService embeddingService,
            IVectorStore vectorStore,
            IServiceProvider serviceProvider,
            ILogger<OpenAIAgent> logger)
        {
            _dbContextFactory = dbContextFactory;
            _navigationAgent = navigationAgent;
            _embeddingService = embeddingService;
            _vectorStore = vectorStore;
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger;
            _conversationHistory = new Dictionary<string, ChatHistory>();

            // Load database schema from JSON file
            _databaseSchema = LoadDatabaseSchema();

            // Initialize Semantic Kernel with OpenAI
            var builder = Kernel.CreateBuilder();
            var openAiApiKey = configuration["OpenAi:ApiKey"] ?? Environment.GetEnvironmentVariable("OPENAI") ?? throw new InvalidOperationException("OpenAI API key not configured");
            builder.AddOpenAIChatCompletion(
                modelId: "gpt-4.1-mini",
                apiKey: openAiApiKey);

            _kernel = builder.Build();
            _chatService = _kernel.GetRequiredService<IChatCompletionService>();
        }

        /// <summary>
        /// Process a unified request using AI capabilities
        /// </summary>
        /// <param name="request">The unified request containing user input and context</param>
        /// <param name="cancellationToken">Cancellation token for the operation</param>
        /// <param name="manualIntent">Optional manual intent to bypass automatic intent classification. 
        /// When provided, the system will use this intent instead of calling the AI classifier.</param>
        /// <returns>Unified response with AI-generated content and metadata</returns>
        public async Task<UnifiedResponse> ProcessRequestAsync(UnifiedRequest request, CancellationToken cancellationToken = default, IntentType? manualIntent = null)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                // Validate input
                if (!ValidateInput(request.Input))
                {
                    return CreateErrorResponse("Invalid input detected", stopwatch.Elapsed);
                }

                // Get or create conversation history
                var conversationKey = $"{request.UserId}_{request.SessionId}";
                var chatHistory = GetChatHistory(conversationKey);

                // Add user message to history
                chatHistory.AddUserMessage(request.Input);

                // Use manual intent if provided, otherwise classify intent using enhanced AI
                IntentClassificationResult intent;
                if (manualIntent.HasValue)
                {
                    intent = new IntentClassificationResult
                    {
                        Intent = manualIntent.Value,
                        Confidence = 1.0, // High confidence since it's manually specified
                        Reasoning = "Manually specified intent"
                    };
                }
                else
                {
                    intent = await ClassifyIntentAsync(request.Input, request.Context, cancellationToken);
                }
                
                UnifiedResponse response = intent.Intent switch
                {
                    IntentType.DatabaseQuery => await HandleDatabaseQueryAsync(request.Input, chatHistory, cancellationToken),
                    IntentType.AgentAction => await HandleAgentActionAsync(request.Input, chatHistory, cancellationToken),
                    IntentType.GeneralAI => await HandleGeneralAIAsync(request.Input, chatHistory, cancellationToken),
                    IntentType.Navigation => await HandleNavigationAsync(request.Input, chatHistory, cancellationToken),
                    _ => CreateErrorResponse("Unable to determine intent", stopwatch.Elapsed)
                };
                Console.WriteLine("Intent Type: " + IntentType.DatabaseQuery.ToString());
                _logger.LogInformation("Intent Type (Logged): {Input}", request.Input);
                // Add AI response to conversation history
                if (response.Success && !string.IsNullOrEmpty(response.Response))
                {
                    chatHistory.AddAssistantMessage(response.Response);
                    UpdateChatHistory(conversationKey, chatHistory);
                }

                response.DetectedIntent = intent.Intent;
                response.IntentConfidence = intent.Confidence;
                response.ExecutionTime = stopwatch.Elapsed;

                return response;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error processing request. Loggings...");
                _logger.LogError(ex, "Error processing request: {Input}", request.Input);
                return CreateErrorResponse($"An error occurred: {ex.Message}", stopwatch.Elapsed);
            }
        }

        public async Task<IntentClassificationResult> ClassifyIntentAsync(
            string input, 
            Dictionary<string, object> context, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                var prompt = CreateIntentClassificationPrompt(input, context);
                var chatHistory = new ChatHistory();
                chatHistory.AddSystemMessage("You are an AI that classifies user intents for an insurance business application. Always respond with valid JSON.");
                chatHistory.AddUserMessage(prompt);

                var result = await _chatService.GetChatMessageContentAsync(chatHistory, cancellationToken: cancellationToken);
                Console.WriteLine(result.Content);
                return ParseIntentResult(result.Content ?? "");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Intent classification failed, using fallback");
                return FallbackIntentClassification(input);
            }
        }

        /// <summary>
        /// Stream responses in real-time with optional manual intent
        /// </summary>
        /// <param name="request">The unified request containing user input and context</param>
        /// <param name="onChunk">Callback function to handle streaming response chunks</param>
        /// <param name="cancellationToken">Cancellation token for the operation</param>
        /// <param name="manualIntent">Optional manual intent to bypass automatic intent classification</param>
        /// <returns>Unified response with AI-generated content and metadata</returns>
        public async Task<UnifiedResponse> ProcessRequestStreamingAsync(
            UnifiedRequest request,
            Func<StreamingResponseChunk, Task> onChunk,
            CancellationToken cancellationToken = default,
            IntentType? manualIntent = null)
        {
            await onChunk(new StreamingResponseChunk
            {
                Type = "status",
                Content = "Processing your request...",
                IsFinal = false
            });
            Console.WriteLine("Preccing request 111...");
            var response = await ProcessRequestAsync(request, cancellationToken, manualIntent);
            Console.WriteLine("Preccing request DONE chunk...");
            await onChunk(new StreamingResponseChunk
            {
                Type = "final_response",
                Content = response.Response,
                Data = response.Data,
                IsFinal = true
            });

            return response;
        }

        public async Task<List<string>> GetSuggestionsAsync(string input, Dictionary<string, object> context, CancellationToken cancellationToken = default)
        {
            try
            {
                var prompt = $@"Based on this user input: '{input}', suggest 3 helpful follow-up questions or actions for an insurance business application. 
                             Return only a JSON array of strings.";

                var chatHistory = new ChatHistory();
                chatHistory.AddSystemMessage("You provide helpful suggestions for insurance business users. Always respond with valid JSON array.");
                chatHistory.AddUserMessage(prompt);

                var result = await _chatService.GetChatMessageContentAsync(chatHistory, cancellationToken: cancellationToken);
                var suggestions = JsonSerializer.Deserialize<List<string>>(result.Content ?? "[]");
                return suggestions ?? new List<string>();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to generate suggestions");
                return GetDefaultSuggestions();
            }
        }

        public bool ValidateInput(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return false;

            //if (input.Length > 2000)
            //    return false;

            // Check for malicious patterns
            var maliciousPatterns = new[]
            {
                @"<script", @"javascript:", @"vbscript:", @"onload=", @"onerror=",
                @"exec\s*\(", @"eval\s*\(", @"system\s*\(", @"shell_exec"
            };

            return !maliciousPatterns.Any(pattern => 
                Regex.IsMatch(input, pattern, RegexOptions.IgnoreCase));
        }

        public async Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var testPrompt = "Respond with 'OK' if you can process this message.";
                var chatHistory = new ChatHistory();
                chatHistory.AddUserMessage(testPrompt);

                var result = await _chatService.GetChatMessageContentAsync(chatHistory, cancellationToken: cancellationToken);
                return !string.IsNullOrEmpty(result.Content);
            }
            catch
            {
                return false;
            }
        }

        private async Task<UnifiedResponse> HandleDatabaseQueryAsync(
            string input, 
            ChatHistory chatHistory, 
            CancellationToken cancellationToken)
        {
            // First, try semantic search to find relevant entities
            _logger.LogInformation("[DBQuery] Starting semantic search for: {Input}", input);
            var relevantEntities = await PerformSemanticSearchAsync(input, cancellationToken);
            
            // Enhance the prompt with semantic search results
            var enhancedInput = input;
            if (relevantEntities.Any())
            {
                // Group by entity type and prioritize clients first
                var clientMatches = relevantEntities.Where(e => e.EntityType == "Client").ToList();
                var otherMatches = relevantEntities.Where(e => e.EntityType != "Client").ToList();
                
                var contextParts = new List<string>();
                if (clientMatches.Any())
                {
                    contextParts.Add($"Client matches: {string.Join(", ", clientMatches.Select(e => $"{e.Name} (ID: {e.EntityId}, Score: {e.Score:F2})"))}");
                }
                if (otherMatches.Any())
                {
                    contextParts.Add($"Other matches: {string.Join(", ", otherMatches.Select(e => $"{e.EntityType}: {e.Name} (ID: {e.EntityId}, Score: {e.Score:F2})"))}");
                }
                
                var entityContext = string.Join("; ", contextParts);
                enhancedInput = $"{input}\n\nRelevant entities found via semantic search: {entityContext}";
                _logger.LogInformation("[DBQuery] Enhanced input with {Count} semantic matches: {Context}", relevantEntities.Count, entityContext);
            }
            else
            {
                _logger.LogInformation("[DBQuery] No semantic matches found, proceeding with original query");
            }
            try
            {
                Console.WriteLine("Attemping Database query...");
                _logger.LogInformation("[DBQuery] Received input: {Input}", input);
                // Generate SQL using AI with schema context
                var sqlPrompt = CreateSqlGenerationPrompt(enhancedInput);
                //_logger.LogInformation("[DBQuery] SQL Prompt: {Prompt}", sqlPrompt);
                var sqlChatHistory = new ChatHistory();
                sqlChatHistory.AddSystemMessage("You are a SQL expert for an insurance database. Always respond with valid JSON containing the SQL query.");
                sqlChatHistory.AddUserMessage(sqlPrompt);

                var sqlResult = await _chatService.GetChatMessageContentAsync(sqlChatHistory, cancellationToken: cancellationToken);
                _logger.LogInformation("[DBQuery] Raw SQL Generation Response: {Response}", sqlResult.Content);
                var sqlResponse = ParseSqlGenerationResult(sqlResult.Content ?? "");
                _logger.LogInformation("[DBQuery] Parsed SQL Success: {Success}, SQL: {Sql}, ErrorMessage: {Error}", sqlResponse.Success, sqlResponse.Sql, sqlResponse.ErrorMessage);

                if (!sqlResponse.Success)
                {
                    _logger.LogWarning("[DBQuery] SQL generation failed. ErrorMessage: {ErrorMessage}", sqlResponse.ErrorMessage);
                    return new UnifiedResponse
                    {
                        Success = false,
                        ErrorMessage = "Failed to generate SQL query",
                        Suggestions = new List<string>
                        {
                            "Try rephrasing your question more specifically",
                            "Ask about clients, policies, carriers, or contacts",
                            "Use simpler language"
                        }
                    };
                }

                // Validate and execute the query
                var validationResult = ValidateSQL(sqlResponse.Sql);
                _logger.LogInformation("[DBQuery] SQL Validation - IsValid: {IsValid}, Errors: {Errors}", validationResult.IsValid, string.Join(", ", validationResult.Errors));
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("[DBQuery] SQL validation failed." );
                    return new UnifiedResponse
                    {
                        Success = false,
                        ErrorMessage = "Generated query failed safety validation",
                        Suggestions = new List<string> { "Try a simpler query", "Be more specific about what you want to find" }
                    };
                }

                var queryResult = await ExecuteQuerySafelyAsync(sqlResponse.Sql, cancellationToken);
                _logger.LogInformation("[DBQuery] Query execution result - Success: {Success}, RowCount: {RowCount}, Error: {ErrorMessage}", queryResult.Success, queryResult.RowCount, queryResult.ErrorMessage);
                if (!queryResult.Success)
                {
                    return new UnifiedResponse
                    {
                        Success = false,
                        ErrorMessage = queryResult.ErrorMessage,
                        Suggestions = new List<string> { "Check if the data exists", "Try a different approach" }
                    };
                }

                // Generate natural language explanation
                var explanation = await GenerateExplanationAsync(input, queryResult.Data, sqlResponse.Sql, relevantEntities, cancellationToken);

                return new UnifiedResponse
                {
                    Success = true,
                    Response = explanation,
                    Data = new
                    {
                        Results = queryResult.Data,
                        RowCount = queryResult.RowCount,
                        GeneratedSQL = sqlResponse.Sql,
                        ExecutionTime = queryResult.ExecutionTimeMs
                    },
                    Suggestions = GenerateDatabaseQuerySuggestions(queryResult)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling database query: {Input}", input);
                return new UnifiedResponse
                {
                    Success = false,
                    ErrorMessage = "Database query processing failed"
                };
            }
        }

        private async Task<UnifiedResponse> HandleAgentActionAsync(
            string input, 
            ChatHistory chatHistory, 
            CancellationToken cancellationToken)
        {
            try
            {
                Console.WriteLine("Attemping Agent action query...");
                // Extract parameters using AI
                var paramPrompt = CreateParameterExtractionPrompt(input);
                var paramChatHistory = new ChatHistory();
                paramChatHistory.AddSystemMessage("You extract parameters from user input for business actions. Always respond with valid JSON.");
                paramChatHistory.AddUserMessage(paramPrompt);

                var paramResult = await _chatService.GetChatMessageContentAsync(paramChatHistory, cancellationToken: cancellationToken);
                var parameters = JsonSerializer.Deserialize<Dictionary<string, object>>(paramResult.Content ?? "{}") ?? new();

                // Determine which agent to route to based on the action type
                var actionType = DetermineActionType(input, parameters);
                
                return actionType switch
                {
                    "loss_run" => await HandleLossRunRequestAsync(parameters, cancellationToken),
                    "certificate" => await HandleCertificateRequestAsync(parameters, cancellationToken),
                    "proposal" => await HandleProposalRequestAsync(parameters, cancellationToken),
                    "payment_link" => await HandlePaymentLinkRequestAsync(parameters, cancellationToken),
                    _ => new UnifiedResponse
                    {
                        Success = false,
                        ErrorMessage = "Unknown agent action type",
                        Suggestions = new List<string>
                        {
                            "Try: 'Send loss run for [client name]'",
                            "Try: 'Request certificate for [client name]'",
                            "Try: 'Generate proposal for [client name]'",
                            "Try: 'Create paylink for [client name] for $[amount]'"
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling agent action: {Input}", input);
                return new UnifiedResponse
                {
                    Success = false,
                    ErrorMessage = "Agent action processing failed"
                };
            }
        }

        private async Task<UnifiedResponse> HandleGeneralAIAsync(
            string input, 
            ChatHistory chatHistory, 
            CancellationToken cancellationToken)
        {
            try
            {
                Console.WriteLine("Attemping general AI query...");
                // Use context-aware chat for general AI responses
                var systemPrompt = @"You are an AI assistant for an insurance business application. You help users understand:
- Insurance concepts and terminology
- Business processes and workflows
- Policy management and underwriting
- Risk assessment and claims

Provide accurate, helpful, and professional responses about insurance and business topics.";

                var responseChatHistory = new ChatHistory();
                responseChatHistory.AddSystemMessage(systemPrompt);
                responseChatHistory.AddUserMessage(input);

                var result = await _chatService.GetChatMessageContentAsync(responseChatHistory, cancellationToken: cancellationToken);
                
                return new UnifiedResponse
                {
                    Success = true,
                    Response = result.Content ?? "I'm here to help with insurance and business questions.",
                    Suggestions = new List<string>
                    {
                        "Ask about insurance types",
                        "Learn about policy management", 
                        "Ask about business processes"
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling general AI: {Input}", input);
                return new UnifiedResponse
                {
                    Success = false,
                    ErrorMessage = "General AI processing failed"
                };
            }
        }

        private async Task<UnifiedResponse> HandleNavigationAsync(
            string input, 
            ChatHistory chatHistory, 
            CancellationToken cancellationToken)
        {
            try
            {
                Console.WriteLine("Attempting navigation request...");
                
                var navigationRequest = new NavigationRequest
                {
                    Input = input,
                    UserId = "enhanced-user",
                    SessionId = Guid.NewGuid().ToString(),
                    Context = new Dictionary<string, object>()
                };

                var navigationResponse = await _navigationAgent.ProcessNavigationRequestAsync(navigationRequest, cancellationToken);
                
                if (navigationResponse.Success)
                {
                    return new UnifiedResponse
                    {
                        Success = true,
                        Response = navigationResponse.Message,
                        Data = new
                        {
                            NavigationUrl = navigationResponse.NavigationUrl,
                            StateChanges = navigationResponse.StateChanges,
                            ExtractedEntities = navigationResponse.ExtractedEntities
                        },
                        Suggestions = new List<string>
                        {
                            "Open another client",
                            "View different policy types",
                            "Navigate to renewals"
                        }
                    };
                }
                else
                {
                    return new UnifiedResponse
                    {
                        Success = false,
                        ErrorMessage = navigationResponse.ErrorMessage,
                        Response = navigationResponse.Message,
                        Suggestions = new List<string>
                        {
                            "Try specifying a client name",
                            "Be more specific about what you want to view",
                            "Ask about available navigation options"
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling navigation: {Input}", input);
                return new UnifiedResponse
                {
                    Success = false,
                    ErrorMessage = "Navigation processing failed",
                    Response = "I had trouble processing that navigation request. Please try again."
                };
            }
        }

        private string LoadDatabaseSchema()
        {
            try
            {
                var schemaPath = Path.Combine("wwwroot", "schema");
                if (!Directory.Exists(schemaPath))
                {
                    _logger.LogWarning("Schema directory not found at {Path}", schemaPath);
                    return GenerateBasicSchemaDocumentation();
                }

                var sqlFiles = Directory.GetFiles(schemaPath, "*.sql");
                if (!sqlFiles.Any())
                {
                    _logger.LogWarning("No SQL schema files found in {Path}", schemaPath);
                    return GenerateBasicSchemaDocumentation();
                }

                var schemaBuilder = new System.Text.StringBuilder();
                schemaBuilder.AppendLine("# Database Schema Documentation");
                schemaBuilder.AppendLine();

                foreach (var sqlFile in sqlFiles.OrderBy(f => f))
                {
                    var fileName = Path.GetFileNameWithoutExtension(sqlFile);
                    var tableName = fileName.Replace("dbo.", "").Replace(".Table", "");
                    
                    schemaBuilder.AppendLine($"## Table: {tableName}");
                    
                    try
                    {
                        var sqlContent = File.ReadAllText(sqlFile);
                        var columns = ExtractColumnsFromSQL(sqlContent);
                        
                        if (columns.Any())
                        {
                            schemaBuilder.AppendLine("### Columns:");
                            foreach (var column in columns)
                            {
                                schemaBuilder.AppendLine($"- {column}");
                            }
                        }
                        
                        // Extract extended LLM_SearchHints property (case-insensitive, across lines)
                        var hintRegex = new Regex(@"@name\s*=\s*N'LLM_SearchHints'\s*,\s*@value\s*=\s*N'(.*?)'(?=(\s*,|\s*\)))", RegexOptions.Singleline | RegexOptions.IgnoreCase);
                        var hintMatch = hintRegex.Match(sqlContent);
                        if (hintMatch.Success)
                        {
                            var raw = hintMatch.Groups[1].Value;
                            var hintText = raw.Replace("''", "'");
                            schemaBuilder.AppendLine("### Hints:");
                            schemaBuilder.AppendLine($"- {hintText}");
                            schemaBuilder.AppendLine();
                        }
                        else
                        {
                            //_logger.LogWarning("[DBSchema] No LLM_SearchHints found in {File}", sqlFile);
                        }
                        
                        schemaBuilder.AppendLine();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to read SQL file: {File}", sqlFile);
                    }
                }

                var result = schemaBuilder.ToString();
                //_logger.LogInformation("Loaded database schema from {Count} SQL files", sqlFiles.Length);
                //_logger.LogInformation("[DBSchema] Full schema document:\n{Schema}", result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load database schema from SQL files");
                return GenerateBasicSchemaDocumentation();
            }
        }

        private List<string> ExtractColumnsFromSQL(string sqlContent)
        {
            var columns = new List<string>();
            
            try
            {
                // Extract column definitions from CREATE TABLE statements
                var lines = sqlContent.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                bool inColumnSection = false;
                
                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();
                    
                    if (trimmedLine.StartsWith("CREATE TABLE", StringComparison.OrdinalIgnoreCase) ||
                        trimmedLine.Contains("("))
                    {
                        inColumnSection = true;
                        continue;
                    }
                    
                    if (inColumnSection && (trimmedLine.StartsWith(")") || trimmedLine.Contains("CONSTRAINT")))
                    {
                        break;
                    }
                    
                    if (inColumnSection && !string.IsNullOrWhiteSpace(trimmedLine) && 
                        !trimmedLine.StartsWith("--") && !trimmedLine.StartsWith("/*"))
                    {
                        // Clean up the column definition
                        var columnDef = trimmedLine.TrimEnd(',');
                        if (!columnDef.StartsWith("CONSTRAINT") && !columnDef.StartsWith("PRIMARY KEY") && 
                            !columnDef.StartsWith("FOREIGN KEY") && !columnDef.StartsWith("INDEX") &&
                            !columnDef.StartsWith("KEY") && columnDef.Contains("["))
                        {
                            columns.Add(columnDef);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse SQL content for columns");
            }
            
            return columns;
        }

        private string GenerateBasicSchemaDocumentation()
        {
            return @"# Insurance Database Schema

## Core Tables (ranked in order of importance):
- **Clients**: Customer information and business details
- **Products**: Insurance product types and configurations
- **Policies**: Insurance policy records and coverages  
- **Contacts**: Contact persons associated with clients
- **Carriers**: Insurance carrier/company information
- **Renewals**: Policy renewal tracking and management


## Other Tables:
- **PhoneNumbers**: Phone numbers for contacts
- **EmailAddresses**: Emails for contacts  
- **Proposals**: Quote and proposal management
- **Submissions**: Underwriting submission tracking


## Key Relationships:
- Clients have multiple Policies
- Policies are issued by Carriers  
- Clients have associated Contacts
- Policies have Renewals
- Renewals generate Proposals";
        }

        private ChatHistory GetChatHistory(string conversationKey)
        {
            if (_conversationHistory.TryGetValue(conversationKey, out var history))
            {
                return history;
            }

            var newHistory = new ChatHistory();
            newHistory.AddSystemMessage(@"You are an AI assistant for an insurance business application. You help users with:
1. Querying database information about clients, policies, carriers, and business data
2. Executing business workflows like sending loss runs, requesting certificates
3. Providing knowledge about insurance concepts and business processes

Always be helpful, accurate, and professional. When generating SQL, ensure it's safe and read-only.");

            _conversationHistory[conversationKey] = newHistory;
            return newHistory;
        }

        private void UpdateChatHistory(string conversationKey, ChatHistory chatHistory)
        {
            _conversationHistory[conversationKey] = chatHistory;
            
            // Limit conversation history to prevent memory issues
            if (chatHistory.Count > 50)
            {
                // Keep system message and recent history
                var recentHistory = new ChatHistory();
                recentHistory.AddSystemMessage(chatHistory[0].Content ?? "");
                
                var recentMessages = chatHistory.Skip(chatHistory.Count - 20).ToList();
                foreach (var message in recentMessages)
                {
                    recentHistory.Add(message);
                }
                
                _conversationHistory[conversationKey] = recentHistory;
            }
        }

        private SQLValidationResult ValidateSQL(string sql)
        {
            var result = new SQLValidationResult { IsValid = true };

            if (string.IsNullOrWhiteSpace(sql))
            {
                result.IsValid = false;
                result.Errors.Add("SQL query is empty");
                return result;
            }

            // Enhanced SQL validation patterns
            var dangerousPatterns = new[]
            {
                @"\bDROP\s+TABLE\b", @"\bDELETE\s+FROM\b", @"\bTRUNCATE\b", @"\bALTER\s+TABLE\b",
                @"\bCREATE\s+TABLE\b", @"\bINSERT\s+INTO\b", @"\bUPDATE\s+SET\b", @"\bEXEC\b",
                @"\bEXECUTE\b", @"\bxp_\w+\b", @"\bsp_\w+\b", @"\b--\b", @"/\*.*\*/"
            };

            foreach (var pattern in dangerousPatterns)
            {
                if (Regex.IsMatch(sql, pattern, RegexOptions.IgnoreCase))
                {
                    result.IsValid = false;
                    result.Errors.Add($"SQL contains potentially dangerous pattern: {pattern}");
                    return result;
                }
            }

            // Ensure a safe starter, regex gets the first non-commented command
            //var firstToken = Regex.Match(sql, @"^\s*(?:--.*?\n|/\*.*?\*/\s*)*(\b\w+\b)", RegexOptions.Singleline | RegexOptions.IgnoreCase).Groups[1].Value.ToUpperInvariant();
            //var safeStarters = new HashSet<string>{ "SELECT", "WITH", "SET", "DECLARE", "PRINT", "WAITFOR", "RAISERROR", "EXPLAIN", "SHOW", "DESCRIBE", "DESC", "VALUES", "TABLE", "PRAGMA", "USE" };
            //if (!safeStarters.Contains(firstToken))
            //{
            //    result.IsValid = false;
            //    result.Errors.Add($"Statement begins with disallowed keyword: {firstToken}");
            //}

            return result;
        }

        private async Task<QueryResult> ExecuteQuerySafelyAsync(string sql, CancellationToken cancellationToken)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
                using var command = context.Database.GetDbConnection().CreateCommand();
                
                command.CommandText = sql;
                command.CommandTimeout = 30; // 30 second timeout
                
                await context.Database.OpenConnectionAsync(cancellationToken);
                using var reader = await command.ExecuteReaderAsync(cancellationToken);
                
                var results = new List<Dictionary<string, object>>();
                
                while (await reader.ReadAsync(cancellationToken) && results.Count < 1000)
                {
                    var row = new Dictionary<string, object>();
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        var value = reader.IsDBNull(i) ? null : reader.GetValue(i);
                        row[reader.GetName(i)] = value ?? DBNull.Value;
                    }
                    results.Add(row);
                }
                
                return new QueryResult
                {
                    Success = true,
                    Data = results,
                    RowCount = results.Count,
                    ExecutionTimeMs = stopwatch.Elapsed.TotalMilliseconds
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing SQL: {Sql}", sql);
                return new QueryResult
                {
                    Success = false,
                    ErrorMessage = $"Query execution failed: {ex.Message}",
                    ExecutionTimeMs = stopwatch.Elapsed.TotalMilliseconds
                };
            }
        }

        // Helper methods for creating prompts and parsing results
        private string CreateIntentClassificationPrompt(string input, Dictionary<string, object> context)
        {
            return $@"Classify this user input into one of four categories for an insurance business application:

1. **AgentAction**: Commands that trigger business workflows
   - Examples: 'Send loss run for Acme Corp', 'Request certificates for Pacific Security', 'Create paylink for TWS for $400'
   - Keywords: send, request, generate, create, process, paylink, payment link

2. **DatabaseQuery**: Questions about data that require database queries
   - Examples: 'How many policies does Acme have?', 'What policies are expiring soon?'
   - Keywords: how many, what, list, find, count

3. **Navigation**: Requests to navigate to different parts of the application
   - Examples: 'Open client screen for TWS', 'Show policies for Baron\'s Heating', 'Show me the renewal for ABC Corporation\'s property policy'
   - Keywords: open, view, show, navigate, go to, display

4. **GeneralAI**: General knowledge or explanatory questions
   - Examples: 'What is workers compensation?', 'Explain general liability'
   - Keywords: what is, explain, define, help

Input: ""{input}""

Respond with ONLY raw JSON (no markdown, no code blocks, no explanations):
{{
  ""intent"": ""AgentAction|DatabaseQuery|Navigation|GeneralAI"",
  ""confidence"": 0.0-1.0,
  ""reasoning"": ""Brief explanation""
}}";
        }

        private string CreateSqlGenerationPrompt(string input)
        {
            // Filter schema to relevant tables for policy queries
            string schemaDoc = _databaseSchema;
            var inputLower = input.ToLower();
            // Match singular or plural for policy-based schema filtering
            if (inputLower.Contains("policy") || inputLower.Contains("policies"))
            {
                var sections = _databaseSchema.Split(new[] {"## Table:"}, StringSplitOptions.None);
                var sb = new StringBuilder();
                sb.AppendLine("# Database Schema Documentation");
                for (int i = 1; i < sections.Length; i++)
                {
                    var sect = sections[i];
                    var parts = sect.Split(new[] {"\n"}, 2, StringSplitOptions.None);
                    var tbl = parts[0].Trim();
                    if (string.Equals(tbl, "Policies", StringComparison.OrdinalIgnoreCase)
                        || string.Equals(tbl, "Products", StringComparison.OrdinalIgnoreCase)
                        || string.Equals(tbl, "Clients", StringComparison.OrdinalIgnoreCase))
                    {
                        sb.AppendLine("## Table:" + sect);
                    }
                }
                schemaDoc = sb.ToString();
            }
                // Build prompt
                return $@"Generate a SQL query for this question: ""{input}""

**IMPORTANT: If the question contains semantic search results with specific EntityId values, you MUST use those exact IDs instead of pattern matching.**

Database Schema:
{schemaDoc}

Requirements:
1. Use proper JOINs to connect related tables
2. Only SELECT queries are allowed
3. Use appropriate WHERE clauses
4. Handle NULL values properly
5. Use SQL Server syntax ONLY - NO MySQL or PostgreSQL syntax
6. For date arithmetic, use DATEADD function: DATEADD(MONTH, 1, GETDATE()) for next month, DATEADD(DAY, -30, GETDATE()) for 30 days ago
7. Policies are 'active' if today's date falls within the effectivedate and expiration date. Past tense phrasing is used when talking about older policies.
8. For month/year comparisons, use MONTH() and YEAR() functions with DATEADD. NEVER use INTERVAL syntax (that's MySQL/PostgreSQL)
9. When listing several policies, list each policy row in a plain pipe-delimited table (Number | Line | Effective | Expiration | Client | Carrier) with no narrative sentences or numbered lists.
10. When selecting client columns (e.g., ClientId, Name), join the Clients table on p.ClientId = c.ClientId and alias it as c
11. Select these columns for policy listings: p.PolicyId, pr.LineName AS Line, c.Name AS ClientName, p.PolicyNumber, ca.CarrierName AS CarrierName, w.CarrierName AS WholesalerName, p.EffectiveDate, p.ExpirationDate
12. Join the Carriers table twice: alias as ca on p.CarrierId = ca.CarrierId and as w on p.WholesalerId = w.CarrierId
13. Never include numeric client IDs; always select client Name as ClientName and use that instead of ID
14. **CRITICAL COLUMN NAMES - Use these EXACT names from Policies table:**
    - Premium amount: `p.Premium` (NOT PremiumAmount, NOT premium.Amount)
    - Policy dates: `p.EffectiveDate` and `p.ExpirationDate` (NOT Expiry_Date)
    - Policy number: `p.PolicyNumber`
    - Primary key: `p.PolicyId`
15. **CRITICAL: When semantic search provides matches with EntityId values, you MUST use those exact IDs in WHERE clauses BUT ONLY for the correct entity type:**
    - Client matches: Use in WHERE c.ClientId = [EntityId] (EntityId is the REAL database ID, NOT the vector store ID)
    - Carrier matches: Use in WHERE ca.CarrierId = [EntityId] or w.CarrierId = [EntityId] 
    - Product matches: Use in WHERE pr.ProductId = [EntityId] (EntityId is the REAL database ID, NOT the vector store ID)
    - Contact matches: Use in WHERE contact.ContactId = [EntityId] (only for contact queries)
    - **IMPORTANT**: The EntityId in semantic search results is the actual database primary key (e.g., ProductId=2, ClientId=137)
    - **NEVER** use vector store IDs (which are in millions like 2000002, 1000137, etc.) - these are internal indexing IDs
    - NEVER use a Contact EntityId as a ProductId or any other entity type
    - NEVER use LIKE pattern matching when semantic search provides specific entity IDs of the correct type
    - If semantic search provides a Contact match but you need Product data, use LIKE pattern matching on LineName instead
16. For email address queries, join EmailAddresses on Contacts.PrimaryEmailId = EmailAddresses.EmailId with alias ea and select ea.Email AS EmailAddress; never return ContactId
17. For phone number queries, join PhoneNumbers on Contacts.PrimaryPhoneId = PhoneNumbers.PhoneNumberId with alias pn and select pn.Number AS PhoneNumber; filter using FirstName LIKE without additional role or inactivity conditions

**SQL Server Date Examples:**
- Next month: WHERE MONTH(p.ExpirationDate) = MONTH(DATEADD(MONTH, 1, GETDATE())) AND YEAR(p.ExpirationDate) = YEAR(DATEADD(MONTH, 1, GETDATE()))
- Last 30 days: WHERE p.CreatedDate >= DATEADD(DAY, -30, GETDATE())
- This year: WHERE YEAR(p.EffectiveDate) = YEAR(GETDATE())

**EntityId Usage Examples:**
- If semantic search finds ""Product: Worker's Compensation (ID: 2, Score: 0.42)"" → Use WHERE pr.ProductId = 2
- If semantic search finds ""Client: ABC Corp (ID: 137, Score: 0.85)"" → Use WHERE c.ClientId = 137
- If semantic search finds ""Carrier: State Fund (ID: 111, Score: 0.67)"" → Use WHERE ca.CarrierId = 111
- **WRONG**: Never use WHERE pr.ProductId = 2000002 (that's the vector store ID, not database ID)

Respond with ONLY raw JSON (no markdown, no code blocks, no explanations):
{{
  ""sql"": ""SELECT ..."",
  ""explanation"": ""What this query does"",
  ""success"": true
}}";
        }

        private string CreateParameterExtractionPrompt(string input)
        {
            return $@"Extract parameters from this business action request: ""{input}""

Extract these if present:
- client_name: Name of the client/company
- action_type: Type of action (loss_run, certificate, proposal, payment_link)
- policy_type: Type of insurance
- time_period: Time range requested
- urgency: Any urgency indicators
- amount: Payment amount (for payment links, extract dollar amounts like $400)
- description: Payment description (for payment links, what the payment is for)

Respond with ONLY raw JSON (no markdown, no code blocks) containing the parameters found.";
        }

        private IntentClassificationResult ParseIntentResult(string result)
        {
            try
            {
                // Handle markdown code blocks by extracting JSON content
                var jsonStart = result.IndexOf('{');
                var jsonEnd = result.LastIndexOf('}');
                
                string jsonContent = result;
                if (jsonStart >= 0 && jsonEnd > jsonStart)
                {
                    jsonContent = result.Substring(jsonStart, jsonEnd - jsonStart + 1);
                }

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                options.Converters.Add(new JsonStringEnumConverter());
                var jsonResult = JsonSerializer.Deserialize<IntentClassificationResult>(jsonContent, options);
                Console.WriteLine("Intent Result has been parsed.");
                return jsonResult ?? FallbackIntentClassification(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Logging... parse fail1");
                _logger.LogWarning(ex, "Failed to parse intent classification result: {Result}", result);
                return FallbackIntentClassification(result);
            }
        }

        private SqlGenerationResult ParseSqlGenerationResult(string result)
        {
            try
            {
                // Extract JSON from response
                var jsonStart = result.IndexOf('{');
                var jsonEnd = result.LastIndexOf('}');
                string jsonContent = result;
                if (jsonStart >= 0 && jsonEnd > jsonStart)
                {
                    jsonContent = result.Substring(jsonStart, jsonEnd - jsonStart + 1);
                }
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                var jsonResult = JsonSerializer.Deserialize<SqlGenerationResult>(jsonContent, options);
                return jsonResult ?? new SqlGenerationResult { Success = false };
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse SQL generation result: {Result}", result);
                return new SqlGenerationResult { Success = false, ErrorMessage = "Failed to parse SQL generation result" };
            }
        }

        private IntentClassificationResult FallbackIntentClassification(string input)
        {
            var lowerInput = input.ToLower();
            
            if (lowerInput.Contains("send") || lowerInput.Contains("request") || lowerInput.Contains("generate") || 
                lowerInput.Contains("create") || lowerInput.Contains("paylink") || lowerInput.Contains("payment link"))
                return new IntentClassificationResult { Intent = IntentType.AgentAction, Confidence = 0.6 };
            
            if (lowerInput.Contains("open") || lowerInput.Contains("view") || lowerInput.Contains("navigate") || lowerInput.Contains("go to"))
                return new IntentClassificationResult { Intent = IntentType.Navigation, Confidence = 0.6 };
            
            if (lowerInput.Contains("how many") || lowerInput.Contains("what") || lowerInput.Contains("show"))
                return new IntentClassificationResult { Intent = IntentType.DatabaseQuery, Confidence = 0.6 };
                
            return new IntentClassificationResult { Intent = IntentType.GeneralAI, Confidence = 0.5 };
        }

        private string DetermineActionType(string input, Dictionary<string, object> parameters)
        {
            var lowerInput = input.ToLower();
            
            if (lowerInput.Contains("loss run"))
                return "loss_run";
            if (lowerInput.Contains("certificate"))
                return "certificate";
            if (lowerInput.Contains("proposal"))
                return "proposal";
            if (lowerInput.Contains("paylink") || lowerInput.Contains("pay link") || lowerInput.Contains("payment link"))
                return "payment_link";
                
            return "unknown";
        }

        private async Task<UnifiedResponse> HandleLossRunRequestAsync(Dictionary<string, object> parameters, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("=== HANDLING LOSS RUN REQUEST VIA OPENAI AGENT ===");
                _logger.LogInformation("Parameters received: {ParameterCount}", parameters.Count);
                
                // Log all parameters
                foreach (var param in parameters)
                {
                    _logger.LogInformation("  Parameter: {Key} = {Value}", param.Key, param.Value);
                }

                // Get the LossRunRequestAgent directly from the service provider
                var lossRunAgent = _serviceProvider.GetService<LossRunRequestAgent>();
                if (lossRunAgent == null)
                {
                    _logger.LogError("❌ LossRunRequestAgent could not be resolved from service provider");
                    return new UnifiedResponse
                    {
                        Success = false,
                        ErrorMessage = "Loss run agent is not available"
                    };
                }

                _logger.LogInformation("✅ LossRunRequestAgent resolved successfully");

                // Create a task agent request
                var request = new TaskAgentRequest
                {
                    AgentId = "loss_run_request",
                    Parameters = parameters,
                    Context = AgentExecutionContext.AIChat,
                    UserId = "enhanced-user", // Default user ID for OpenAI agent context
                    SessionId = Guid.NewGuid().ToString(),
                    PageContext = new Dictionary<string, object>()
                };

                _logger.LogInformation("Executing LossRunRequestAgent directly...");

                // Execute the agent directly
                var agentResult = await lossRunAgent.ExecuteAsync(request, cancellationToken);

                _logger.LogInformation("Task agent execution completed. Success: {Success}", agentResult.Success);

                if (agentResult.Success)
                {
                    return new UnifiedResponse
                    {
                        Success = true,
                        Response = agentResult.Message,
                        Data = agentResult.Data,
                        Suggestions = agentResult.Suggestions?.ToList() ?? new List<string>(),
                        ExecutionTime = agentResult.ExecutionTime
                    };
                }
                else
                {
                    return new UnifiedResponse
                    {
                        Success = false,
                        ErrorMessage = agentResult.ErrorMessage ?? "Loss run request failed",
                        Response = agentResult.Message,
                        ExecutionTime = agentResult.ExecutionTime
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing loss run request through task agent");
                return new UnifiedResponse
                {
                    Success = false,
                    ErrorMessage = "Failed to process loss run request: " + ex.Message,
                    Suggestions = new List<string>
                    {
                        "Try specifying a client name",
                        "Include the policy type (workers comp, general liability, etc.)",
                        "Specify the time period (e.g., last 5 years)"
                    }
                };
            }
        }

        private async Task<UnifiedResponse> HandleCertificateRequestAsync(Dictionary<string, object> parameters, CancellationToken cancellationToken)
        {
            return new UnifiedResponse
            {
                Success = true,
                Response = "Certificate request initiated. Documents will be prepared and sent.",
                Suggestions = new List<string>
                {
                    "Track certificate status",
                    "Request additional certificates",
                    "View certificate templates"
                }
            };
        }

        private async Task<UnifiedResponse> HandleProposalRequestAsync(Dictionary<string, object> parameters, CancellationToken cancellationToken)
        {
            return new UnifiedResponse
            {
                Success = true,
                Response = "Proposal generation started. You will be notified when complete.",
                Suggestions = new List<string>
                {
                    "Check proposal status",
                    "Generate additional proposals",
                    "View proposal templates"
                }
            };
        }

        private async Task<UnifiedResponse> HandlePaymentLinkRequestAsync(Dictionary<string, object> parameters, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("=== HANDLING PAYMENT LINK REQUEST VIA OPENAI AGENT ===");
                _logger.LogInformation("Parameters received: {ParameterCount}", parameters.Count);
                
                // Log all parameters
                foreach (var param in parameters)
                {
                    _logger.LogInformation("  Parameter: {Key} = {Value}", param.Key, param.Value);
                }

                // Get the PaymentLinkAgent directly from the service provider
                var paymentLinkAgent = _serviceProvider.GetService<PaymentLinkAgent>();
                if (paymentLinkAgent == null)
                {
                    _logger.LogError("❌ PaymentLinkAgent could not be resolved from service provider");
                    return new UnifiedResponse
                    {
                        Success = false,
                        ErrorMessage = "Payment link agent is not available"
                    };
                }

                _logger.LogInformation("✅ PaymentLinkAgent resolved successfully");

                // Create a task agent request
                var request = new TaskAgentRequest
                {
                    AgentId = "payment_link",
                    Parameters = parameters,
                    Context = AgentExecutionContext.AIChat,
                    UserId = "enhanced-user", // Default user ID for OpenAI agent context
                    SessionId = Guid.NewGuid().ToString(),
                    PageContext = new Dictionary<string, object>()
                };

                _logger.LogInformation("Executing PaymentLinkAgent directly...");

                // Execute the agent directly
                var agentResult = await paymentLinkAgent.ExecuteAsync(request, cancellationToken);

                _logger.LogInformation("Payment link agent execution completed. Success: {Success}", agentResult.Success);

                if (agentResult.Success)
                {
                    return new UnifiedResponse
                    {
                        Success = true,
                        Response = agentResult.Message,
                        Data = agentResult.Data,
                        Suggestions = agentResult.Suggestions?.ToList() ?? new List<string>(),
                        ExecutionTime = agentResult.ExecutionTime
                    };
                }
                else
                {
                    return new UnifiedResponse
                    {
                        Success = false,
                        ErrorMessage = agentResult.ErrorMessage ?? "Payment link request failed",
                        Response = agentResult.Message,
                        ExecutionTime = agentResult.ExecutionTime
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing payment link request through task agent");
                return new UnifiedResponse
                {
                    Success = false,
                    ErrorMessage = "Failed to process payment link request: " + ex.Message,
                    Suggestions = new List<string>
                    {
                        "Try specifying a client name",
                        "Include the payment amount (e.g., $400)",
                        "Add a description (e.g., 'for work comp broker fee')"
                    }
                };
            }
        }

        private UnifiedResponse CreateErrorResponse(string message, TimeSpan executionTime) =>
            new UnifiedResponse
            {
                Success = false,
                ErrorMessage = message,
                ExecutionTime = executionTime
            };

        private async Task<string> GenerateExplanationAsync(string question, object data, string sql, List<RelevantEntity> relevantEntities, CancellationToken cancellationToken)
        {
            try
            {
                // Build entity context information for the AI to use proper names
                var entityContext = "";
                if (relevantEntities.Any())
                {
                    var clientMatches = relevantEntities.Where(e => e.EntityType == "Client").ToList();
                    var otherMatches = relevantEntities.Where(e => e.EntityType != "Client").ToList();
                    
                    var contextParts = new List<string>();
                    if (clientMatches.Any())
                    {
                        contextParts.Add($"Client entities found: {string.Join(", ", clientMatches.Select(e => $"ID {e.EntityId} = '{e.Name}'"))}");
                    }
                    if (otherMatches.Any())
                    {
                        contextParts.Add($"Other entities found: {string.Join(", ", otherMatches.Select(e => $"{e.EntityType} ID {e.EntityId} = '{e.Name}'"))}");
                    }
                    
                    entityContext = $"\n\nEntity Mappings (use these proper names):\n{string.Join("\n", contextParts)}";
                }

                var prompt = $@"Explain these query results in natural language:

Original Question: ""{question}""
SQL Query: {sql}
Results: {JsonSerializer.Serialize(data)}{entityContext}

IMPORTANT: When explaining the results, use the full proper entity names from the Entity Mappings above instead of abbreviated names from the original question. For example, if the user asked about 'Big Bang' but the entity mapping shows 'The Big Bang Distribution (Ahead Products, Inc.)', use the full proper name in your explanation.

Provide a clear, business-friendly explanation of what the data shows.";

                var chatHistory = new ChatHistory();
                chatHistory.AddSystemMessage("You explain database query results in natural language for business users. Always use the full proper entity names when available, not abbreviated versions from user questions.");
                chatHistory.AddUserMessage(prompt);

                var result = await _chatService.GetChatMessageContentAsync(chatHistory, cancellationToken: cancellationToken);
                return result.Content ?? $"Query completed successfully. Found {((List<object>)data).Count} results.";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to generate explanation");
                return $"Query completed successfully. Found results for your question.";
            }
        }

        private List<string> GenerateDatabaseQuerySuggestions(QueryResult result)
        {
            var suggestions = new List<string>();
            
            if (result.RowCount == 0)
            {
                suggestions.AddRange(new[]
                {
                    "Try broadening your search criteria",
                    "Check if the data exists in the system",
                    "Use partial name matches instead of exact names"
                });
            }
            else if (result.RowCount > 100)
            {
                suggestions.AddRange(new[]
                {
                    "Narrow down your search with more specific criteria",
                    "Add date ranges to limit results",
                    "Export results for detailed analysis"
                });
            }
            else
            {
                suggestions.AddRange(new[]
                {
                    "Get more details about these results",
                    "Export this data",
                    "Create a report from this data"
                });
            }
            
            return suggestions;
        }

        private List<string> GetDefaultSuggestions()
        {
            return new List<string>
            {
                "How many policies do we have?",
                "Send loss run for a client",
                "What is workers compensation?",
                "Show me recent renewals"
            };
        }

        /// <summary>
        /// Perform semantic search to find relevant entities for the user query
        /// </summary>
        private async Task<List<RelevantEntity>> PerformSemanticSearchAsync(string query, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("[SemanticSearch] Searching for entities related to: {Query}", query);
                
                // Generate embedding for the user query
                var queryVector = await _embeddingService.GenerateEmbeddingAsync(query, cancellationToken);
                
                // Search for similar entities in the vector store
                var searchResults = await _vectorStore.SearchAsync(queryVector, 5, cancellationToken);
                
                // Log all search results for debugging
                _logger.LogInformation("[SemanticSearch] Raw search results: {Count} total", searchResults.Count);
                foreach (var result in searchResults)
                {
                    var entityType = GetMetadataString(result.Metadata, "Entity") ?? "Unknown";
                    var entityName = GetEntityName(result.Metadata);
                    var entityId = GetMetadataInt(result.Metadata, "EntityId");
                    
                    // Additional debugging for Product entities
                    if (entityType == "Product")
                    {
                        var lineName = GetMetadataString(result.Metadata, "LineName");
                        var lineNickname = GetMetadataString(result.Metadata, "LineNickname");
                        var lineCode = GetMetadataString(result.Metadata, "LineCode");
                        _logger.LogInformation("[SemanticSearch] Product Debug - LineName: '{LineName}', LineNickname: '{LineNickname}', LineCode: '{LineCode}'", 
                            lineName, lineNickname, lineCode);
                    }
                    
                    _logger.LogInformation("[SemanticSearch] Result: {EntityType} '{EntityName}' (ID: {EntityId}) - Score: {Score:F4}", 
                        entityType, entityName, entityId, result.Score);
                }
                
                var relevantEntities = searchResults
                    .Where(r => r.Score > 0.3) // Lowered threshold to include more potential matches
                    .OrderByDescending(r => r.Score) // Prioritize by relevance score
                    .Select(r => new RelevantEntity
                    {
                        EntityType = GetMetadataString(r.Metadata, "Entity") ?? "Unknown",
                        EntityId = GetMetadataInt(r.Metadata, "EntityId"),
                        Name = GetEntityName(r.Metadata),
                        Score = r.Score
                    })
                    .ToList();
                    
                _logger.LogInformation("[SemanticSearch] Found {Count} relevant entities (threshold > 0.3)", relevantEntities.Count);
                return relevantEntities;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[SemanticSearch] Semantic search failed, continuing without context");
                return new List<RelevantEntity>();
            }
        }

        private string? GetMetadataString(IDictionary<string, object> metadata, string key)
        {
            if (!metadata.TryGetValue(key, out var value)) return null;
            
            return value switch
            {
                string s => s,
                System.Text.Json.JsonElement je when je.ValueKind == System.Text.Json.JsonValueKind.String => je.GetString(),
                System.Text.Json.JsonElement je when je.ValueKind == System.Text.Json.JsonValueKind.Null => null,
                null => null,
                _ => value.ToString()
            };
        }

        private int GetMetadataInt(IDictionary<string, object> metadata, string key)
        {
            if (!metadata.TryGetValue(key, out var value)) return 0;
            
            return value switch
            {
                int i => i,
                long l => (int)l,
                string s when int.TryParse(s, out var parsed) => parsed,
                System.Text.Json.JsonElement je when je.ValueKind == System.Text.Json.JsonValueKind.Number => je.GetInt32(),
                _ => 0
            };
        }

        private string GetEntityName(IDictionary<string, object> metadata)
        {
            return GetMetadataString(metadata, "Name") ??
                   GetMetadataString(metadata, "CarrierName") ??
                   $"{GetMetadataString(metadata, "FirstName")} {GetMetadataString(metadata, "LastName")}".Trim() ??
                   GetMetadataString(metadata, "LineName") ?? 
                   "Unknown";
        }
    }

    // Supporting classes
    public class SQLValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    public class QueryResult
    {
        public bool Success { get; set; }
        public object Data { get; set; } = new();
        public int RowCount { get; set; }
        public double ExecutionTimeMs { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class SqlGenerationResult
    {
        public bool Success { get; set; }
        public string Sql { get; set; } = "";
        public string? ErrorMessage { get; set; }
    }
}

// Semantic Kernel Plugins
namespace Surefire.Domain.Agents.Services
{
    public class SqlGenerationPlugin
    {
        [KernelFunction, Description("Generate SQL query from natural language")]
        public async Task<string> GenerateSQL(
            [Description("The natural language question")] string question,
            [Description("Database schema information")] string schema)
        {
            // This would use the AI model to generate SQL
            // For now, return a placeholder that would be processed by the kernel
            return JsonSerializer.Serialize(new
            {
                sql = "SELECT COUNT(*) FROM Clients WHERE Name LIKE '%' + @searchTerm + '%'",
                explanation = "Counts clients matching the search criteria",
                success = true
            });
        }
    }

    public class IntentClassificationPlugin
    {
        [KernelFunction, Description("Classify user intent")]
        public async Task<string> ClassifyIntent(
            [Description("User input to classify")] string input,
            [Description("Context information")] string context)
        {
            return JsonSerializer.Serialize(new IntentClassificationResult
            {
                Intent = IntentType.DatabaseQuery,
                Confidence = 0.9,
                Reasoning = "Input contains question words and asks for data"
            });
        }

        [KernelFunction, Description("Extract parameters from user input")]
        public async Task<string> ExtractParameters([Description("User input")] string input)
        {
            return JsonSerializer.Serialize(new Dictionary<string, object>
            {
                ["client_name"] = "extracted_client_name",
                ["action_type"] = "extracted_action"
            });
        }
    }

    public class BusinessKnowledgePlugin
    {
        [KernelFunction, Description("Answer business domain questions")]
        public async Task<string> AnswerQuestion(
            [Description("The question to answer")] string question,
            [Description("Domain context")] string context)
        {
            return "This is a comprehensive answer about insurance business concepts...";
        }

        [KernelFunction, Description("Explain query results in natural language")]
        public async Task<string> ExplainResults(
            [Description("Original question")] string question,
            [Description("Query results")] string data,
            [Description("Generated SQL")] string sql)
        {
            return "Based on your query, here's what the data shows...";
        }
    }
}