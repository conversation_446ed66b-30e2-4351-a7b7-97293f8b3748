//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Text.Json;
//using System.Threading.Tasks;
//using Microsoft.Extensions.Logging;
//using Microsoft.EntityFrameworkCore;
//using Surefire.Domain.Logs;
//using Surefire.Data;
//using Surefire.Domain.Agents.Models;
//using Surefire.Domain.Agents.Interfaces;

//namespace Surefire.Domain.OpenAI
//{
//    public class OllamaSemanticMemoryService : ISemanticMemoryService
//    {
//        private readonly IOllamaService _ollamaService;
//        private readonly ILogger<OllamaSemanticMemoryService> _logger;
//        private readonly ILoggingService _logService;
//        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
//        private readonly IDatabaseSchemaService _schemaService;
//        private readonly Dictionary<string, List<MemoryItem>> _memoryStore;

//        public OllamaSemanticMemoryService(
//            IOllamaService ollamaService,
//            ILogger<OllamaSemanticMemoryService> logger,
//            ILoggingService logService,
//            IDbContextFactory<ApplicationDbContext> dbContextFactory,
//            IDatabaseSchemaService schemaService)
//        {
//            _ollamaService = ollamaService;
//            _logger = logger;
//            _logService = logService;
//            _dbContextFactory = dbContextFactory;
//            _schemaService = schemaService;
//            _memoryStore = new Dictionary<string, List<MemoryItem>>();
//        }

//        public async Task<string> QueryDatabaseAsync(string naturalLanguageQuery)
//        {
//            try
//            {
//                using var context = await _dbContextFactory.CreateDbContextAsync();
                
//                // Fetch live schema
//                var schema = await _schemaService.GetSchemaAsync();
//                var schemaDescription = BuildSchemaDescriptionForLLM(schema);

//                // Generate a SQL-like query from the natural language query
//                var prompt = $@"Given the following database schema and natural language query, generate a SQL query.
//                Database Schema:
//                {schemaDescription}

//                Natural Language Query: {naturalLanguageQuery}

//                Generate a SQL query that answers this question. Return only the SQL query, nothing else.
//                Remember to:
//                1. Use proper JOINs to connect related tables
//                2. Use appropriate WHERE clauses for filtering
//                3. Use aliases for better readability
//                4. Handle NULL cases appropriately
//                5. Use the correct table and column names from the schema";

//                var sqlQuery = await _ollamaService.GetCompletionAsync(prompt);
//                _logger.LogInformation("Generated SQL query: {SqlQuery}", sqlQuery);

//                // Execute the query and get results
//                object result;
//                if (sqlQuery.Trim().StartsWith("SELECT COUNT", StringComparison.OrdinalIgnoreCase))
//                {
//                    // Handle COUNT queries
//                    var countQuery = $"SELECT COUNT(*) as Count {sqlQuery.Substring(sqlQuery.IndexOf("FROM"))}";
//                    result = await context.Database.SqlQueryRaw<CountResult>(countQuery).FirstOrDefaultAsync();
//                }
//                else if (sqlQuery.Contains("PhoneNumber") || sqlQuery.Contains("Number"))
//                {
//                    // Handle phone number queries
//                    result = await context.Database.SqlQueryRaw<PhoneNumberResult>(sqlQuery).ToListAsync();
//                }
//                else if (sqlQuery.Contains("ClientName") || sqlQuery.Contains("ContactName"))
//                {
//                    // Handle client contact queries
//                    result = await context.Database.SqlQueryRaw<ClientContactResult>(sqlQuery).ToListAsync();
//                }
//                else if (sqlQuery.Contains("CarrierName") && sqlQuery.Contains("COUNT"))
//                {
//                    // Handle carrier policy count queries
//                    result = await context.Database.SqlQueryRaw<CarrierPolicyCountResult>(sqlQuery).ToListAsync();
//                }
//                else
//                {
//                    // For other queries, use a dynamic approach
//                    var rowsAffected = await context.Database.ExecuteSqlRawAsync(sqlQuery);
//                    result = new { RowsAffected = rowsAffected };
//                }

//                // Format the result as a natural language response
//                var responsePrompt = $@"Given the following query results, generate a natural language response.
//                Query: {naturalLanguageQuery}
//                Results: {JsonSerializer.Serialize(result)}

//                Generate a natural language response that answers the original query.
//                If the results are empty or null, say so clearly.
//                If there are multiple results, summarize them appropriately.";

//                return await _ollamaService.GetCompletionAsync(responsePrompt);
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError(ex, "Error querying database: {Message}", ex.Message);
//                await _logService.LogAsync(LogLevel.Error,
//                    $"Error querying database: {ex.Message}",
//                    "OllamaSemanticMemoryService", ex);
//                throw;
//            }
//        }

//        public async Task SaveInformationAsync(string collection, string id, string text, string? description = null, Dictionary<string, object>? metadata = null)
//        {
//            try
//            {
//                // Generate embedding for the text
//                var embedding = await GenerateEmbeddingAsync(text);

//                // Create memory item
//                var item = new MemoryItem
//                {
//                    Id = id,
//                    Text = text,
//                    Description = description,
//                    Metadata = metadata,
//                    Embedding = embedding
//                };

//                // Add to collection
//                if (!_memoryStore.ContainsKey(collection))
//                {
//                    _memoryStore[collection] = new List<MemoryItem>();
//                }

//                // Remove existing item with same ID if it exists
//                _memoryStore[collection].RemoveAll(x => x.Id == id);
//                _memoryStore[collection].Add(item);

//                await _logService.LogAsync(LogLevel.Information,
//                    $"Saved information to collection '{collection}' with ID '{id}'",
//                    "OllamaSemanticMemoryService");
//            }
//            catch (Exception ex)
//            {
//                await _logService.LogAsync(LogLevel.Error,
//                    $"Error saving information to collection '{collection}': {ex.Message}",
//                    "OllamaSemanticMemoryService", ex);
//                throw;
//            }
//        }

//        public async Task<IEnumerable<MemoryQueryResult>> SearchAsync(string collection, string query, int limit = 3)
//        {
//            try
//            {
//                if (!_memoryStore.ContainsKey(collection))
//                {
//                    return Enumerable.Empty<MemoryQueryResult>();
//                }

//                // Generate embedding for the query
//                var queryEmbedding = await GenerateEmbeddingAsync(query);

//                // Calculate cosine similarity and sort results
//                var results = _memoryStore[collection]
//                    .Select(item => new
//                    {
//                        Item = item,
//                        Similarity = CalculateCosineSimilarity(queryEmbedding, item.Embedding)
//                    })
//                    .OrderByDescending(x => x.Similarity)
//                    .Take(limit)
//                    .Select(x => new MemoryQueryResult
//                    {
//                        Id = x.Item.Id,
//                        Text = x.Item.Text,
//                        Description = x.Item.Description,
//                        Metadata = x.Item.Metadata,
//                        Relevance = x.Similarity
//                    });

//                return results;
//            }
//            catch (Exception ex)
//            {
//                await _logService.LogAsync(LogLevel.Error,
//                    $"Error searching collection '{collection}': {ex.Message}",
//                    "OllamaSemanticMemoryService", ex);
//                throw;
//            }
//        }

//        public Task DeleteAsync(string collection, string id)
//        {
//            if (_memoryStore.ContainsKey(collection))
//            {
//                _memoryStore[collection].RemoveAll(x => x.Id == id);
//            }
//            return Task.CompletedTask;
//        }

//        public Task ClearCollectionAsync(string collection)
//        {
//            if (_memoryStore.ContainsKey(collection))
//            {
//                _memoryStore[collection].Clear();
//            }
//            return Task.CompletedTask;
//        }

//        private async Task<float[]> GenerateEmbeddingAsync(string text)
//        {
//            try
//            {
//                return await _ollamaService.GetEmbeddingsAsync(text);
//            }
//            catch (Exception ex)
//            {
//                await _logService.LogAsync(LogLevel.Error,
//                    $"Error generating embedding: {ex.Message}",
//                    "OllamaSemanticMemoryService", ex);
//                throw;
//            }
//        }

//        private float CalculateCosineSimilarity(float[] vectorA, float[] vectorB)
//        {
//            if (vectorA.Length != vectorB.Length)
//                throw new ArgumentException("Vectors must be of the same length");

//            float dotProduct = 0;
//            float normA = 0;
//            float normB = 0;

//            for (int i = 0; i < vectorA.Length; i++)
//            {
//                dotProduct += vectorA[i] * vectorB[i];
//                normA += vectorA[i] * vectorA[i];
//                normB += vectorB[i] * vectorB[i];
//            }

//            return dotProduct / (float)(Math.Sqrt(normA) * Math.Sqrt(normB));
//        }

//        private string BuildSchemaDescriptionForLLM(DatabaseSchema schema)
//        {
//            var sb = new StringBuilder();
//            foreach (var table in schema.Tables)
//            {
//                sb.AppendLine($"Table: {table.Name}");
//                sb.AppendLine($"  Description: {table.Description}");
//                if (table.ExtendedProperties.TryGetValue("LLM_SearchHints", out var hints))
//                {
//                    sb.AppendLine($"  LLM_SearchHints: {hints}");
//                }
//                sb.AppendLine("  Columns:");
//                foreach (var col in table.Columns)
//                {
//                    sb.AppendLine($"    - {col.Name} ({col.DataType}){(string.IsNullOrWhiteSpace(col.Description) ? "" : $": {col.Description}")}");
//                }
//            }
//            return sb.ToString();
//        }

//        private class MemoryItem
//        {
//            public string Id { get; set; } = string.Empty;
//            public string Text { get; set; } = string.Empty;
//            public string? Description { get; set; }
//            public Dictionary<string, object>? Metadata { get; set; }
//            public float[] Embedding { get; set; } = Array.Empty<float>();
//        }

//        private class CountResult
//        {
//            public int Count { get; set; }
//        }

//        private class PhoneNumberResult
//        {
//            public string Number { get; set; } = string.Empty;
//            public string? Extension { get; set; }
//        }

//        private class ClientContactResult
//        {
//            public string ClientName { get; set; } = string.Empty;
//            public string ContactName { get; set; } = string.Empty;
//        }

//        private class CarrierPolicyCountResult
//        {
//            public string CarrierName { get; set; } = string.Empty;
//            public int PolicyCount { get; set; }
//        }
//    }
//} 