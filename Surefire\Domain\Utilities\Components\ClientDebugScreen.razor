@namespace Surefire.Domain.Utilities
@using Surefire.Domain.Shared.Components
@using Surefire.Domain.Shared.Models
@using Surefire.Domain.Clients.Models
@using Surefire.Domain.Contacts.Models
@using Surefire.Domain.Policies.Models
@using Surefire.Domain.Renewals.Services
@using Surefire.Domain.Renewals.Models
@using Icons = Microsoft.FluentUI.AspNetCore.Components.Icons
@inject IJSRuntime JSRuntime
@inject RenewalService RenewalService

<div class="client-debug-container">
    <div class="sf-spacer"></div>
    <FluentStack Class="tabcontents">
        <div style="width:400px;">
            @if (RenewalId.HasValue)
            {
                <span class="txt-section-bar">RENEWAL INFORMATION</span>
                <div class="div-section">
                    @if (renewal != null)
                    {
                        <FluentStack>
                            <div style="margin-bottom:10px;">
                                <div class="txt-small">Renewal Id</div>
                                <div class="txt-small-sub">@renewal.RenewalId</div>
                            </div>
                            <div style="margin-bottom:10px;">
                                <div class="txt-small">Wholesaler Id</div>
                                <div class="txt-small-sub">@renewal.WholesalerId</div>
                            </div>
                            <div style="margin-bottom:10px;">
                                <div class="txt-small">Policy Id</div>
                                <div class="txt-small-sub">@renewal.PolicyId</div>
                            </div>
                            <div style="margin-bottom:10px;">
                                <div class="txt-small">Assigned To Id</div>
                                <div class="txt-small-sub">@renewal.AssignedToId</div>
                            </div>
                            <div style="margin-bottom:10px;">
                                <div class="txt-small">ClientId</div>
                                <div class="txt-small-sub">@renewal.ClientId</div>
                            </div>
                            <div style="margin-bottom:10px;">
                                <div class="txt-small">Product Id</div>
                                <div class="txt-small-sub">@renewal.ProductId</div>
                            </div>
                            <div style="margin-bottom:10px;">
                                <div class="txt-small">Notes</div>
                                <div class="txt-small-sub">@renewal.Notes</div>
                            </div>
                            <div style="margin-bottom:10px;">
                                <div class="txt-small">Date Created</div>
                                <div class="txt-small-sub">@renewal.DateCreated</div>
                            </div>
                            <div style="margin-bottom:10px;">
                                <div class="txt-small">Bill Type</div>
                                <div class="txt-small-sub">@renewal.BillType</div>
                            </div>
                            <div style="margin-bottom:10px;">
                                <div class="txt-small">Renewal Status</div>
                                <div class="txt-small-sub">@renewal.RenewalStatus</div>
                            </div>
                        </FluentStack>
                    }
                    else
                    {
                        <div class="txt-small-sub">Loading renewal information...</div>
                    }
                </div>
                <div class="sf-spacer"></div>
            }
            <span class="txt-section-bar">TOOLS & DATA</span>
            <div class="div-section">
                @if (ClientId.HasValue)
                {
                    <FluentStack>
                        <div style="margin-bottom:10px;">
                            <div class="txt-small">Client Code</div>
                            <div class="txt-small-sub">@Client?.LookupCode</div>
                        </div>
                        <div style="margin-bottom:10px;">
                            <div class="txt-small">Client Id</div>
                            <div class="txt-small-sub">@Client?.ClientId</div>
                        </div>
                        <div style="margin-bottom:10px;">
                            <div class="txt-small">eClient Id</div>
                            <div class="txt-small-sub">@Client?.eClientId</div>
                        </div>
                        <div style="margin-bottom:10px;">
                            <div class="txt-small">API Status</div>
                            @if (utilityLoading)
                            {
                                <div class="txt-small-sub">@utilityLoading.ToString()</div>
                            }
                            else
                            {
                                <div class="txt-small-sub">Loading...</div>
                            }
                        </div>
                    </FluentStack>
                }
                <div class="sf-hr"></div>
                <FluentButton Type="ButtonType.Button" Appearance="Appearance.Outline" OnClick="@(() => ForceImportPolicies())">Sync Policies</FluentButton>

                <div class="sf-spacer-sm"></div>
                <div style="width:100%; text-align:center;">@utilityStatus</div>
                <div class="sf-spacer-sm"></div>
                @if (utilityLoading)
                {
                    <FluentProgressRing Visible="true" Size="ProgressRingSize.Large" Width="50" />
                }
            </div>

            <div class="sf-spacer"></div>

            <span class="txt-section-bar">IMPORT CONTACTS</span>
            <div class="div-section">
                <FluentButton Type="ButtonType.Button" Appearance="Appearance.Outline" OnClick="UtilImportContacts">Import Contacts</FluentButton>

                @if (loadedContacts.Count > 0)
                {
                    <ContactsListImport Contacts="@loadedContacts" ParentType="Client" ParentId="@(ClientId ?? 0)" />
                    <FluentButton Type="ButtonType.Button" Appearance="Appearance.Accent" OnClick="AddLoadedContactsToClient">Import Contacts</FluentButton>
                }
            </div>
        </div>
    </FluentStack>
</div>

@code {
    [Parameter]
    public int? ClientId { get; set; }

    [Parameter]
    public int? PolicyId { get; set; }

    [Parameter]
    public int? ContactId { get; set; }

    [Parameter]
    public int? RenewalId { get; set; }

    private Client? Client { get; set; }
    private bool utilityLoading { get; set; }
    private string utilityStatus { get; set; } = string.Empty;
    private List<Contact> loadedContacts { get; set; } = new();
    private Renewal? renewal { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (ClientId.HasValue)
        {
            // TODO: Load client data
            // Client = await ClientService.GetClientById(ClientId.Value);
        }

        if (RenewalId.HasValue)
        {
            renewal = await RenewalService.GetRenewalByIdAsync(RenewalId.Value);
        }
    }

    private async Task ForceImportPolicies()
    {
        if (!ClientId.HasValue) return;

        utilityLoading = true;
        utilityStatus = "Syncing policies...";
        StateHasChanged();

        try
        {
            // TODO: Implement policy sync logic
            await Task.Delay(1000); // Placeholder for actual sync
            utilityStatus = "Policies synced successfully";
        }
        catch (Exception ex)
        {
            utilityStatus = $"Error syncing policies: {ex.Message}";
        }
        finally
        {
            utilityLoading = false;
            StateHasChanged();
        }
    }

    private async Task UtilImportContacts()
    {
        if (!ClientId.HasValue) return;

        try
        {
            // TODO: Implement contact import logic
            // This would typically involve:
            // 1. Opening a file picker
            // 2. Reading the selected file
            // 3. Parsing contacts
            // 4. Populating loadedContacts
            await Task.Delay(1000); // Placeholder for actual import
        }
        catch (Exception ex)
        {
            utilityStatus = $"Error importing contacts: {ex.Message}";
            StateHasChanged();
        }
    }

    private async Task AddLoadedContactsToClient()
    {
        if (!ClientId.HasValue || !loadedContacts.Any()) return;

        try
        {
            // TODO: Implement adding contacts to client
            // This would typically involve:
            // 1. Validating contacts
            // 2. Adding contacts to the client
            // 3. Saving changes
            await Task.Delay(1000); // Placeholder for actual save
            loadedContacts.Clear();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            utilityStatus = $"Error adding contacts: {ex.Message}";
            StateHasChanged();
        }
    }
}

<style>
.client-debug-container {
    padding: 20px;
}

.txt-section-bar {
    font-size: 14px;
    font-weight: 600;
    color: #666;
    margin-bottom: 10px;
    display: block;
}

.div-section {
    background: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.txt-small {
    font-size: 12px;
    color: #666;
}

.txt-small-sub {
    font-size: 14px;
    color: #333;
    margin-top: 2px;
}

.sf-hr {
    height: 1px;
    background: #ddd;
    margin: 15px 0;
}

.sf-spacer {
    height: 20px;
}

.sf-spacer-sm {
    height: 10px;
}
</style> 