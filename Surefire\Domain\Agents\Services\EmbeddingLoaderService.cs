using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Surefire.Data;
using Surefire.Domain.Agents.Interfaces;
using Surefire.Domain.Agents.Models;

namespace Surefire.Domain.Agents.Services
{
    public class EmbeddingLoaderService
    {
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly IEmbeddingService _embeddingService;
        private readonly IVectorStore _vectorStore;
        private readonly ILogger<EmbeddingLoaderService> _logger;

        public EmbeddingLoaderService(
            IDbContextFactory<ApplicationDbContext> dbContextFactory,
            IEmbeddingService embeddingService,
            IVectorStore vectorStore,
            ILogger<EmbeddingLoaderService> logger)
        {
            _dbContextFactory = dbContextFactory;
            _embeddingService = embeddingService;
            _vectorStore = vectorStore;
            _logger = logger;
        }

        public async Task UpsertAllAsync(CancellationToken cancellationToken = default)
        {
            using var db = _dbContextFactory.CreateDbContext();
            int total = 0;

            // Clients
            var clients = await db.Clients.Select(c => new { c.ClientId, c.Name }).ToListAsync(cancellationToken);
            foreach (var c in clients)
            {
                var text = c.Name?.Trim() ?? "";
                
                // Skip clients with very short or empty names
                if (string.IsNullOrWhiteSpace(text) || text.Length < 2)
                {
                    _logger.LogWarning("Skipping client {ClientId} due to insufficient text: '{Text}'", c.ClientId, text);
                    continue;
                }
                
                var id = (c.ClientId + 1000000).ToString(); // Ensure unique IDs across entities
                
                try
                {
                    var vector = await _embeddingService.GenerateEmbeddingAsync(text, cancellationToken);
                    var metadata = new Dictionary<string, object>
                    {
                        {"Entity","Client"},
                        {"EntityId", c.ClientId},
                        {"Name", c.Name ?? ""}
                    };
                    await _vectorStore.UpsertAsync(id, vector, metadata, cancellationToken);
                    total++;
                    
                    // Add small delay to avoid rate limiting
                    await Task.Delay(50, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process client {ClientId}: {Text}", c.ClientId, text);
                    // Continue processing other clients instead of failing completely
                }
            }

            // Products
            var products = await db.Products.Select(p => new { p.ProductId, p.LineName, p.LineNickname, p.LineCode }).ToListAsync(cancellationToken);
            foreach (var p in products)
            {
                var text = $"{p.LineName} ({p.LineNickname}, code {p.LineCode})".Trim();
                
                // Skip products with very short or empty descriptions
                if (string.IsNullOrWhiteSpace(text) || text.Length < 5)
                {
                    _logger.LogWarning("Skipping product {ProductId} due to insufficient text: '{Text}'", p.ProductId, text);
                    continue;
                }
                
                var id = (p.ProductId + 2000000).ToString(); // Ensure unique IDs across entities
                
                try
                {
                    var vector = await _embeddingService.GenerateEmbeddingAsync(text, cancellationToken);
                    var metadata = new Dictionary<string, object>
                    {
                        {"Entity","Product"},
                        {"EntityId", p.ProductId},
                        {"LineName", p.LineName ?? ""},
                        {"LineNickname", p.LineNickname ?? ""},
                        {"LineCode", p.LineCode ?? ""}
                    };
                    await _vectorStore.UpsertAsync(id, vector, metadata, cancellationToken);
                    total++;
                    
                    // Add small delay to avoid rate limiting
                    await Task.Delay(50, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process product {ProductId}: {Text}", p.ProductId, text);
                    // Continue processing other products instead of failing completely
                }
            }

            // Carriers
            var carriers = await db.Carriers.Select(ca => new { ca.CarrierId, ca.CarrierName }).ToListAsync(cancellationToken);
            foreach (var ca in carriers)
            {
                var text = ca.CarrierName?.Trim() ?? "";
                
                // Skip carriers with very short or empty names
                if (string.IsNullOrWhiteSpace(text) || text.Length < 2)
                {
                    _logger.LogWarning("Skipping carrier {CarrierId} due to insufficient text: '{Text}'", ca.CarrierId, text);
                    continue;
                }
                
                var id = (ca.CarrierId + 3000000).ToString(); // Ensure unique IDs across entities
                
                try
                {
                    var vector = await _embeddingService.GenerateEmbeddingAsync(text, cancellationToken);
                    var metadata = new Dictionary<string, object>
                    {
                        {"Entity","Carrier"},
                        {"EntityId", ca.CarrierId},
                        {"CarrierName", ca.CarrierName ?? ""}
                    };
                    await _vectorStore.UpsertAsync(id, vector, metadata, cancellationToken);
                    total++;
                    
                    // Add small delay to avoid rate limiting
                    await Task.Delay(50, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process carrier {CarrierId}: {Text}", ca.CarrierId, text);
                    // Continue processing other carriers instead of failing completely
                }
            }

            // Contacts
            var contacts = await db.Contacts.Select(ct => new { ct.ContactId, ct.FirstName, ct.LastName }).ToListAsync(cancellationToken);
            foreach (var ct in contacts)
            {
                var text = $"{ct.FirstName} {ct.LastName}".Trim();
                
                // Skip contacts with very short or empty names
                if (string.IsNullOrWhiteSpace(text) || text.Length < 3)
                {
                    _logger.LogWarning("Skipping contact {ContactId} due to insufficient text: '{Text}'", ct.ContactId, text);
                    continue;
                }
                
                var id = (ct.ContactId + 4000000).ToString(); // Ensure unique IDs across entities
                
                try
                {
                    var vector = await _embeddingService.GenerateEmbeddingAsync(text, cancellationToken);
                    var metadata = new Dictionary<string, object>
                    {
                        {"Entity","Contact"},
                        {"EntityId", ct.ContactId},
                        {"FirstName", ct.FirstName ?? ""},
                        {"LastName", ct.LastName ?? ""}
                    };
                    await _vectorStore.UpsertAsync(id, vector, metadata, cancellationToken);
                    total++;
                    
                    // Add small delay to avoid rate limiting
                    await Task.Delay(50, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process contact {ContactId}: {Text}", ct.ContactId, text);
                    // Continue processing other contacts instead of failing completely
                }
            }

            _logger.LogInformation("Upserted {Count} embeddings into Qdrant collection", total); 
        }
    }
}
