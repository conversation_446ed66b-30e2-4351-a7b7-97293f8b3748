using Surefire.Domain.Forms.Models;
using Surefire.Domain.Forms.Services;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Surefire.Interfaces
{
    public interface IEmailTemplateService
    {
        Task<List<EmailTemplate>> GetAllTemplatesAsync();
        Task<EmailTemplate> GetTemplateByIdAsync(int id);
        Task<EmailTemplate> CreateTemplateAsync(EmailTemplate template);
        Task<EmailTemplate> UpdateTemplateAsync(EmailTemplate template);
        Task DeleteTemplateAsync(int id);
        Task<List<EmailTemplate>> GetActiveTemplatesAsync();
    }
}