using Microsoft.AspNetCore.Components;
using Microsoft.FluentUI.AspNetCore.Components;
using Surefire.Domain.Policies.Models;
using Surefire.Domain.Policies.Services;
using Surefire.Domain.Shared.Models;
using Surefire.Domain.Clients.Services;
using Surefire.Domain.Shared.Services;
using Surefire.Domain.Attachments.Models;
using Surefire.Domain.Attachments.Services;
using Surefire.Domain.Ember;
using System.Xml;
using System.Xml.Linq;
using System.Text.Json;
using System.Globalization;
using System.Text.RegularExpressions;

namespace Surefire.Domain.Utilities.Components
{
    public partial class EpicClientPolicySync : ComponentBase
    {
        [Parameter] public int? ClientId { get; set; }

        // UI State
        private bool isProcessing = false;
        private string statusMessage = string.Empty;
        private string lastSyncResult = string.Empty;
        private bool lastSyncSuccess = false;
        private List<DebugMessage> debugMessages = new();

        // Data
        private string xmlData = string.Empty;
        private List<Policy> allPolicies = new();

        protected override async Task OnInitializedAsync()
        {
            await LoadPolicies();
            
            // Start EmberService connection for SignalR communication
            try
            {
                await EmberService.StartConnectionAsync();
                DebugLog("SignalR connection established", "Success");
            }
            catch (Exception ex)
            {
                DebugLog($"Failed to start SignalR connection: {ex.Message}", "Error");
            }
        }

        protected override async Task OnParametersSetAsync()
        {
            if (ClientId.HasValue)
            {
                await LoadPolicies();
            }
        }

        private async Task LoadPolicies()
        {
            if (!ClientId.HasValue) return;

            try
            {
                // Load all policies for the client
                allPolicies = await PolicyService.GetCurrentPoliciesByClientIdAsync(ClientId.Value);
                DebugLog($"Loaded {allPolicies.Count} policies for client {ClientId}", "Info");
            }
            catch (Exception ex)
            {
                DebugLog($"Error loading policies: {ex.Message}", "Error");
            }
        }

        private async Task HandleSyncNow()
        {
            if (!ClientId.HasValue)
            {
                await MessageService.ShowMessageBarAsync("No client selected", MessageIntent.Error);
                return;
            }

            isProcessing = true;
            statusMessage = "Connecting to Word document...";
            lastSyncResult = string.Empty;
            ClearDebugOutput();
            StateHasChanged();

            try
            {
                DebugLog("Starting Epic policy data sync...", "Info");
                
                // Create a cancellation token for timeout handling
                using var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                var responseReceived = false;
                
                // Register a response handler for the GetWordDocContents command
                EmberService.RegisterResponseHandler("GetWordDocContents", async (responseData) =>
                {
                    await InvokeAsync(async () =>
                    {
                        try
                        {
                            responseReceived = true;
                            DebugLog("Received response from tray application", "Info");
                            
                            if (responseData != null && responseData.Count > 0)
                            {
                                var documentContent = responseData[0];
                                
                                if (documentContent.StartsWith("ERROR:"))
                                {
                                    // Handle error
                                    var errorMessage = documentContent.Substring(7); // Remove "ERROR: " prefix
                                    statusMessage = "Error reading Word document";
                                    lastSyncResult = $"Word extraction error: {errorMessage}";
                                    lastSyncSuccess = false;
                                    DebugLog($"Word document error: {errorMessage}", "Error");
                                }
                                else
                                {
                                    // Success - process the XML content
                                    xmlData = documentContent;
                                    DebugLog($"Successfully extracted {documentContent.Length:N0} characters from Word document", "Success");
                                    
                                    // Now process the XML data
                                    await ProcessSyncData();
                                }
                            }
                            else
                            {
                                statusMessage = "No data received";
                                lastSyncResult = "No content received from Word document. Make sure you have a Word document open with policy data.";
                                lastSyncSuccess = false;
                                DebugLog("No content received from Word document", "Warning");
                            }
                        }
                        catch (Exception ex)
                        {
                            statusMessage = "Error processing response";
                            lastSyncResult = $"Error processing Word document response: {ex.Message}";
                            lastSyncSuccess = false;
                            DebugLog($"Error processing Word document response: {ex.Message}", "Error");
                        }
                        
                        isProcessing = false;
                        StateHasChanged();
                    });
                });

                // Send the command to get Word document contents
                await EmberService.RunEmberFunction("GetWordDocContents", new List<string>());
                DebugLog("Word document extraction request sent to tray application", "Info");
                
                // Wait for response with timeout
                statusMessage = "Waiting for response from tray application...";
                StateHasChanged();
                
                // Poll for response with timeout
                var startTime = DateTime.Now;
                while (!responseReceived && !timeoutCts.Token.IsCancellationRequested)
                {
                    await Task.Delay(500, timeoutCts.Token);
                    
                    // Update status message with elapsed time
                    var elapsed = DateTime.Now - startTime;
                    statusMessage = $"Waiting for response... ({elapsed.TotalSeconds:F0}s)";
                    StateHasChanged();
                }
                
                // Handle timeout
                if (!responseReceived && timeoutCts.Token.IsCancellationRequested)
                {
                    statusMessage = "Request timed out";
                    lastSyncResult = "The request timed out. The tray application may have extracted the data but the connection was lost. Please check the tray application logs and try again.";
                    lastSyncSuccess = false;
                    isProcessing = false;
                    DebugLog("Request timed out after 30 seconds", "Error");
                    DebugLog("Note: Tray app may have extracted data but connection was lost during transmission", "Warning");
                    StateHasChanged();
                }
            }
            catch (OperationCanceledException)
            {
                statusMessage = "Request cancelled";
                lastSyncResult = "The request was cancelled due to timeout.";
                lastSyncSuccess = false;
                isProcessing = false;
                DebugLog("Request was cancelled", "Warning");
                StateHasChanged();
            }
            catch (Exception ex)
            {
                statusMessage = "Sync failed";
                lastSyncResult = $"Error during sync: {ex.Message}";
                lastSyncSuccess = false;
                isProcessing = false;
                DebugLog($"Error in HandleSyncNow: {ex.Message}", "Error");
                StateHasChanged();
            }
        }

        private async Task ProcessSyncData()
        {
            try
            {
                statusMessage = "Processing policy data...";
                StateHasChanged();

                DebugLog("Starting XML preprocessing...", "Info");
                DebugLog($"Original XML length: {xmlData.Length:N0} characters", "Info");
                
                // Log first 200 characters for debugging
                var preview = xmlData.Length > 200 ? xmlData.Substring(0, 200) + "..." : xmlData;
                DebugLog($"XML preview: {preview.Replace('\r', ' ').Replace('\n', ' ')}", "Info");
                
                // Preprocess XML to fix common issues (enhanced version)
                var originalLength = xmlData.Length;
                var ampersandCount = xmlData.Count(c => c == '&');
                
                // Fix ampersands first (except XML entities)
                if (ampersandCount > 0)
                {
                    DebugLog($"Fixing {ampersandCount} ampersand characters in XML", "Info");
                    // Only replace & that are not part of XML entities
                    xmlData = System.Text.RegularExpressions.Regex.Replace(xmlData, @"&(?!(?:amp|lt|gt|apos|quot|#\d+|#x[0-9a-fA-F]+);)", "and");
                }

                // Fix greater than and less than symbols with dollar amounts and other common issues
                var replacements = new Dictionary<string, string>
                {
                    { "< $", "&lt; $" },
                    { "> $", "&gt; $" },
                    { "<$", "&lt;$" },
                    { " >$", "&gt;$" },
                    { ">=", "&gt;=" },
                    { "<=", "&lt;=" }
                };

                foreach (var replacement in replacements)
                {
                    var count = (xmlData.Length - xmlData.Replace(replacement.Key, "").Length) / replacement.Key.Length;
                    if (count > 0)
                    {
                        DebugLog($"Fixed {count} instances of '{replacement.Key}'", "Info");
                        xmlData = xmlData.Replace(replacement.Key, replacement.Value);
                    }
                }

                // Remove any null characters or other control characters that might cause issues
                xmlData = System.Text.RegularExpressions.Regex.Replace(xmlData, @"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "");

                // Check if we need to wrap in root element
                var trimmedXml = xmlData.TrimStart();
                bool needsRootWrapper = false;
                
                if (!trimmedXml.StartsWith("<"))
                {
                    DebugLog("XML doesn't start with '<', this doesn't look like XML data", "Error");
                    DebugLog($"First 50 characters: '{trimmedXml.Substring(0, Math.Min(50, trimmedXml.Length))}'", "Error");
                }
                else if (!trimmedXml.StartsWith("<?xml") && !trimmedXml.StartsWith("<root>", StringComparison.OrdinalIgnoreCase))
                {
                    // Check if there are multiple root elements by counting top-level opening tags
                    var topLevelTags = System.Text.RegularExpressions.Regex.Matches(trimmedXml, @"^<[^/?][^>]*>", System.Text.RegularExpressions.RegexOptions.Multiline);
                    if (topLevelTags.Count > 1)
                    {
                        needsRootWrapper = true;
                    }
                    
                    // Also check for XML that doesn't have a single root element
                    try
                    {
                        XDocument.Parse(xmlData);
                    }
                    catch (XmlException ex) when (ex.Message.Contains("multiple root") || ex.Message.Contains("root level"))
                    {
                        needsRootWrapper = true;
                    }
                    catch
                    {
                        // Other XML errors will be caught in the main validation
                    }
                }

                if (needsRootWrapper)
                {
                    DebugLog("Wrapping XML content in root tags", "Info");
                    xmlData = $"<root>{xmlData}</root>";
                }

                DebugLog($"Preprocessed XML length: {xmlData.Length:N0} characters (change: {xmlData.Length - originalLength:+#;-#;0})", "Info");

                // Validate XML format
                if (!IsValidXml(xmlData))
                {
                    statusMessage = "Invalid XML format";
                    lastSyncResult = "The Word document does not contain valid XML data. Please check that you have the correct Epic policy document open.";
                    lastSyncSuccess = false;
                    DebugLog("XML validation failed", "Error");
                    return;
                }

                DebugLog("XML validation passed", "Success");
                statusMessage = "Updating policies...";
                StateHasChanged();

                // Process all matching policies (auto-enabled)
                await ProcessAllMatchingPolicies();

                // Save processed XML as attachment (auto-enabled)
                await SaveXmlAsAttachment();

                statusMessage = "Sync completed successfully";
                lastSyncResult = "Policy data has been successfully synced from Epic to Surefire.";
                lastSyncSuccess = true;
                DebugLog("Sync completed successfully", "Success");

                //await MessageService.ShowMessageBarAsync("✅ Policy sync completed successfully!", MessageIntent.Success);
            }
            catch (Exception ex)
            {
                statusMessage = "Sync failed";
                lastSyncResult = $"Error processing policy data: {ex.Message}";
                lastSyncSuccess = false;
                DebugLog($"Error in ProcessSyncData: {ex.Message}", "Error");
                await MessageService.ShowMessageBarAsync($"❌ Sync failed: {ex.Message}", MessageIntent.Error);
            }
        }

        private async Task ProcessAllMatchingPolicies()
        {
            DebugLog("Starting to process all matching policies...", "Info");

            try
            {
                var doc = XDocument.Parse(xmlData);
                var policies = doc.Descendants()
                    .Where(x => x.Name.LocalName.Equals("Policy", StringComparison.OrdinalIgnoreCase))
                    .ToList();

                DebugLog($"Found {policies.Count} policies in XML data", "Info");

                if (!policies.Any())
                {
                    DebugLog("No policies found in XML data", "Warning");
                    return;
                }

                int processedCount = 0;
                int matchedCount = 0;

                foreach (var xmlPolicy in policies)
                {
                    try
                    {
                        var policyNumber = xmlPolicy.Descendants()
                            .FirstOrDefault(x => x.Name.LocalName.Equals("PolicyNumber", StringComparison.OrdinalIgnoreCase))?.Value?.Trim();

                        if (string.IsNullOrEmpty(policyNumber))
                        {
                            DebugLog($"Policy {processedCount + 1}: No policy number found, skipping", "Warning");
                            processedCount++;
                            continue;
                        }

                        DebugLog($"Policy {processedCount + 1}: Processing policy number {policyNumber}", "Info");

                        // Find matching policy in Surefire
                        var matchingPolicy = allPolicies.FirstOrDefault(p => 
                            !string.IsNullOrEmpty(p.PolicyNumber) && 
                            p.PolicyNumber.Trim().Equals(policyNumber, StringComparison.OrdinalIgnoreCase));

                        if (matchingPolicy != null)
                        {
                            DebugLog($"Found matching Surefire policy: {matchingPolicy.PolicyNumber} (ID: {matchingPolicy.PolicyId})", "Success");
                            
                            // Determine policy type and process accordingly
                            var policyType = DeterminePolicyType(matchingPolicy, xmlPolicy);
                            DebugLog($"Detected policy type: {policyType}", "Info");

                            switch (policyType.ToLower())
                            {
                                case "workcomp":
                                    await ProcessWorkCompPolicyMatch(xmlPolicy, matchingPolicy, processedCount);
                                    break;
                                case "auto":
                                    await ProcessAutoPolicyMatch(xmlPolicy, matchingPolicy, processedCount);
                                    break;
                                case "gl":
                                    await ProcessGLPolicyMatch(xmlPolicy, matchingPolicy, processedCount);
                                    break;
                                default:
                                    DebugLog($"Unknown policy type '{policyType}' for policy {policyNumber}", "Warning");
                                    break;
                            }
                            
                            matchedCount++;
                        }
                        else
                        {
                            DebugLog($"No matching Surefire policy found for policy number: {policyNumber}", "Warning");
                        }

                        processedCount++;
                    }
                    catch (Exception ex)
                    {
                        DebugLog($"Error processing policy {processedCount + 1}: {ex.Message}", "Error");
                        processedCount++;
                    }
                }

                DebugLog($"Processing complete: {processedCount} policies processed, {matchedCount} policies matched and updated", "Success");
            }
            catch (Exception ex)
            {
                DebugLog($"Error in ProcessAllMatchingPolicies: {ex.Message}", "Error");
                throw;
            }
        }

        private string DeterminePolicyType(Policy surefirePolicy, XElement xmlPolicy)
        {
            // First check the Surefire policy product information
            if (surefirePolicy.Product != null)
            {
                var lineName = surefirePolicy.Product.LineName?.ToLower() ?? "";
                
                if (lineName.Contains("work") || lineName.Contains("comp") || lineName.Contains("workers"))
                    return "WorkComp";
                    
                if (lineName.Contains("auto") || lineName.Contains("commercial auto") || lineName.Contains("business auto"))
                    return "Auto";
                    
                if (surefirePolicy.Product.ProductId == 3) // GL Product ID
                    return "GL";
            }

            // Fallback: analyze XML content for policy type indicators
            var xmlContent = xmlPolicy.ToString().ToLower();
            
            if (xmlContent.Contains("workers") || xmlContent.Contains("workcomp") || xmlContent.Contains("work comp"))
                return "WorkComp";
                
            if (xmlContent.Contains("auto") || xmlContent.Contains("vehicle") || xmlContent.Contains("driver"))
                return "Auto";
                
            if (xmlContent.Contains("general liability") || xmlContent.Contains("liability") || xmlContent.Contains("occurrence"))
                return "GL";

            return "Unknown";
        }

        // Simplified policy processing methods that call the existing detailed methods
        private async Task ProcessWorkCompPolicyMatch(XElement xmlPolicy, Policy policy, int policyIndex)
        {
            try
            {
                // Use existing WorkCompPolicyMatched method
                await WorkCompPolicyMatched(xmlPolicy, policyIndex);
                DebugLog($"Work Comp policy {policy.PolicyNumber} updated successfully", "Success");
            }
            catch (Exception ex)
            {
                DebugLog($"Error updating Work Comp policy {policy.PolicyNumber}: {ex.Message}", "Error");
            }
        }

        private async Task ProcessAutoPolicyMatch(XElement xmlPolicy, Policy policy, int policyIndex)
        {
            try
            {
                // Use existing AutoPolicyMatched method  
                await AutoPolicyMatched(xmlPolicy, policyIndex);
                DebugLog($"Auto policy {policy.PolicyNumber} updated successfully", "Success");
            }
            catch (Exception ex)
            {
                DebugLog($"Error updating Auto policy {policy.PolicyNumber}: {ex.Message}", "Error");
            }
        }

        private async Task ProcessGLPolicyMatch(XElement xmlPolicy, Policy policy, int policyIndex)
        {
            try
            {
                // Use existing GLPolicyMatched method
                await GLPolicyMatched(xmlPolicy, policyIndex);
                DebugLog($"GL policy {policy.PolicyNumber} updated successfully", "Success");
            }
            catch (Exception ex)
            {
                DebugLog($"Error updating GL policy {policy.PolicyNumber}: {ex.Message}", "Error");
            }
        }

        private async Task SaveXmlAsAttachment()
        {
            if (!ClientId.HasValue || string.IsNullOrEmpty(xmlData)) return;

            try
            {
                DebugLog("Saving XML data as attachment...", "Info");

                // Create a syncdata folder path
                var syncdataPath = Path.Combine("uploads", "clients", ClientId.Value.ToString(), "syncdata");
                var absoluteSyncdataPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", syncdataPath);
                
                // Ensure directory exists
                if (!Directory.Exists(absoluteSyncdataPath))
                {
                    Directory.CreateDirectory(absoluteSyncdataPath);
                }

                var originalFileName = $"epic_sync_data_{DateTime.Now:yyyyMMdd_HHmmss}.xml";
                
                // Generate hashed filename
                var hash = Surefire.Domain.Shared.Helpers.StringHelper.GenerateFiveCharacterHash(Path.GetFileNameWithoutExtension(originalFileName));
                var hashedFileName = $"{Path.GetFileNameWithoutExtension(originalFileName)}_{hash}.xml";
                
                // Write the XML file
                var filePath = Path.Combine(absoluteSyncdataPath, hashedFileName);
                await File.WriteAllTextAsync(filePath, xmlData);
                
                var fileInfo = new FileInfo(filePath);
                DebugLog($"XML file written to: {filePath} (Size: {fileInfo.Length} bytes)", "Info");

                // Create attachment record
                var attachment = new Attachment
                {
                    OriginalFileName = originalFileName,
                    HashedFileName = hashedFileName,
                    LocalPath = syncdataPath.Replace("\\", "/"),
                    FileFormat = ".xml",
                    FileSize = fileInfo.Length,
                    DateCreated = DateTime.UtcNow,
                    ClientId = ClientId.Value,
                    Description = "Epic policy sync data",
                    IsClientAccessible = false,
                    Status = 1
                };

                // Save attachment to database
                await AttachmentService.SaveAttachmentDirectlyAsync(attachment);
                
                DebugLog($"✅ Successfully saved XML attachment: {originalFileName}", "Success");
            }
            catch (Exception ex)
            {
                DebugLog($"Error saving XML as attachment: {ex.Message}", "Error");
            }
        }

        private bool IsValidXml(string xml)
        {
            if (string.IsNullOrWhiteSpace(xml))
            {
                DebugLog("XML validation failed: Empty or null XML data", "Error");
                return false;
            }

            try
            {
                DebugLog("Attempting to parse XML...", "Info");
                var doc = XDocument.Parse(xml);
                DebugLog($"✅ XML parsed successfully. Root element: {doc.Root?.Name}", "Success");
                DebugLog($"XML contains {doc.Descendants().Count()} total elements", "Info");

                // Log some structure info for debugging
                var rootChildren = doc.Root?.Elements().Select(e => e.Name.LocalName).Distinct().ToList() ?? new List<string>();
                if (rootChildren.Any())
                {
                    DebugLog($"Root level elements: {string.Join(", ", rootChildren)}", "Info");
                }

                // Check for specific Epic data elements that we expect
                var expectedElements = new[] { "MasterAccountMerge", "WorkersCompensation", "BusinessAuto", "GeneralLiability", "Policy" };
                var foundElements = expectedElements.Where(elem => doc.Descendants().Any(d => d.Name.LocalName.Equals(elem, StringComparison.OrdinalIgnoreCase))).ToList();
                
                if (foundElements.Any())
                {
                    DebugLog($"Found expected Epic elements: {string.Join(", ", foundElements)}", "Success");
                }
                else
                {
                    DebugLog("⚠️ Warning: No expected Epic elements found (MasterAccountMerge, WorkersCompensation, BusinessAuto, GeneralLiability, Policy)", "Warning");
                    DebugLog("This might not be Epic policy data, but XML is valid", "Warning");
                }

                // Check for policy elements specifically
                var policyElements = doc.Descendants().Where(d => d.Name.LocalName.Equals("Policy", StringComparison.OrdinalIgnoreCase));
                if (policyElements.Any())
                {
                    DebugLog($"Found {policyElements.Count()} Policy elements", "Info");
                    
                    // Check first policy for policy number
                    var firstPolicy = policyElements.First();
                    var policyNumber = firstPolicy.Descendants()
                        .FirstOrDefault(d => d.Name.LocalName.Equals("PolicyNumber", StringComparison.OrdinalIgnoreCase))?.Value;
                    
                    if (!string.IsNullOrEmpty(policyNumber))
                    {
                        DebugLog($"First policy number: {policyNumber}", "Info");
                    }
                    else
                    {
                        DebugLog("⚠️ Warning: First policy doesn't have a PolicyNumber element", "Warning");
                    }
                }
                else
                {
                    DebugLog("No Policy elements found in XML", "Warning");
                }

                return true;
            }
            catch (XmlException ex)
            {
                DebugLog($"❌ XML validation failed: {ex.Message}", "Error");
                if (ex.LineNumber > 0)
                {
                    DebugLog($"Error at line {ex.LineNumber}, position {ex.LinePosition}", "Error");
                    
                    // Try to show the problematic line
                    try
                    {
                        var lines = xml.Split('\n');
                        if (ex.LineNumber <= lines.Length)
                        {
                            var problematicLine = lines[ex.LineNumber - 1];
                            DebugLog($"Problematic line: '{problematicLine.Trim()}'", "Error");
                        }
                    }
                    catch (Exception lineEx)
                    {
                        DebugLog($"Could not extract problematic line: {lineEx.Message}", "Error");
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                DebugLog($"❌ Unexpected error during XML validation: {ex.GetType().Name}: {ex.Message}", "Error");
                return false;
            }
        }

        private void ClearStatus()
        {
            lastSyncResult = string.Empty;
            lastSyncSuccess = false;
            StateHasChanged();
        }

        private void DebugLog(string message, string type = "Info")
        {
            debugMessages.Add(new DebugMessage
            {
                Message = message,
                Type = type,
                Timestamp = DateTime.Now
            });

            // Keep only the last 100 messages to prevent memory issues
            if (debugMessages.Count > 100)
            {
                debugMessages = debugMessages.TakeLast(100).ToList();
            }

            StateHasChanged();
        }

        private void ClearDebugOutput()
        {
            debugMessages.Clear();
            StateHasChanged();
        }

        // Placeholder methods that would call the existing processing logic
        // These would need to be implemented with the full processing logic from the original DataImport class
        private async Task WorkCompPolicyMatched(XElement xmlPolicy, int policyIndex)
        {
            // This would contain the full Work Comp processing logic
            DebugLog($"Work Comp policy processing logic would be implemented here for policy index {policyIndex}", "Info");
            await Task.CompletedTask;
        }

        private async Task AutoPolicyMatched(XElement xmlPolicy, int policyIndex)
        {
            // This would contain the full Auto processing logic
            DebugLog($"Auto policy processing logic would be implemented here for policy index {policyIndex}", "Info");
            await Task.CompletedTask;
        }

        private async Task GLPolicyMatched(XElement xmlPolicy, int policyIndex)
        {
            // This would contain the full GL processing logic
            DebugLog($"GL policy processing logic would be implemented here for policy index {policyIndex}", "Info");
            await Task.CompletedTask;
        }
    }

    public class DebugMessage
    {
        public string Message { get; set; } = "";
        public string Type { get; set; } = "Info";
        public DateTime Timestamp { get; set; }
    }
}