using Surefire.Data;
using Surefire.Domain.Clients.Models;
using Surefire.Domain.Shared.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System.Data.SqlClient;
using System.Globalization;

namespace Surefire.Domain.Shared.Services
{
    public class SearchService
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly StateService _stateService;

        public SearchService(ApplicationDbContext context, UserManager<ApplicationUser> userManager, IHttpContextAccessor httpContextAccessor, IDbContextFactory<ApplicationDbContext> contextFactory, StateService stateService)
        {
            _context = context;
            _userManager = userManager;
            _httpContextAccessor = httpContextAccessor;
            _contextFactory = contextFactory;
            _stateService = stateService;
        }


        public async Task<List<Product>> GetAllProductsAsync()
        {
            var products = await _context.Products.ToListAsync();
            return products;
        }

        public async Task<List<Client>> FireSearchClients(string str)
        {
            return await _context.Clients
                .Where(c => c.Name.Contains(str) || c.Email.Contains(str) || c.LookupCode.Contains(str))
                .ToListAsync();
        }
       
        public async Task<List<FireSearchResultViewModel>> SearchAllWaitAsync(string searchTerm, CancellationToken cancellationToken)
        {
            var results = new List<FireSearchResultViewModel>();

            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return results; // Return an empty list if the search term is null or whitespace
            }

            // Start queries in parallel with separate DbContext instances
            var clientTask = Task.Run(async () =>
            {
                using var context = _contextFactory.CreateDbContext();
                return await context.Clients.AsNoTracking()
                    .Where(c => c.Name.Contains(searchTerm) || c.Email.Contains(searchTerm) || c.LookupCode.Contains(searchTerm)
                                || c.PhoneNumber.Contains(searchTerm) || c.Website.Contains(searchTerm))
                    .OrderBy(c => c.Name) // Add ordering to prevent EF warning
                    .Select(c => new FireSearchResultViewModel
                    {
                        DataType = "Client",
                        Id = c.ClientId,
                        Primary = c.Name,
                        Parent = "" // Clients have no parent, so this is empty
                    })
                    .Take(10)
                    .ToListAsync(cancellationToken);
            });

            var carrierTask = Task.Run(async () =>
            {
                using var context = _contextFactory.CreateDbContext();
                return await context.Carriers.AsNoTracking()
                    .Where(c => c.CarrierName.Contains(searchTerm) || c.LookupCode.Contains(searchTerm) || c.CarrierNickname.Contains(searchTerm))
                    .OrderBy(c => c.CarrierName) // Add ordering to prevent EF warning
                    .Select(c => new FireSearchResultViewModel
                    {
                        DataType = "Carrier",
                        Id = c.CarrierId,
                        Primary = c.CarrierName,
                        Parent = "" // Carriers have no parent, so this is empty
                    })
                    .Take(10)
                    .ToListAsync(cancellationToken);
            });

            var contactTask = Task.Run(async () =>
            {
                using var context = _contextFactory.CreateDbContext();
                return await context.Contacts.AsNoTracking()
                    .Include(c => c.PhoneNumbers)
                    .Include(c => c.EmailAddresses)
                    .Include(c => c.Client)
                    .Include(c => c.Carrier)
                    .Where(c => c.FirstName.Contains(searchTerm) || c.LastName.Contains(searchTerm)
                                || c.EmailAddresses.Any(e => e.Email.Contains(searchTerm))
                                || c.PhoneNumbers.Any(p => p.Number.Contains(searchTerm)))
                    .OrderBy(c => c.LastName).ThenBy(c => c.FirstName) // Add ordering to prevent EF warning
                    .Select(c => new FireSearchResultViewModel
                    {
                        DataType = "Contact",
                        Id = c.ContactId,
                        Primary = $"{c.FirstName} {c.LastName}",
                        Parent = c.Client != null ? c.Client.Name : c.Carrier != null ? c.Carrier.CarrierName : "" // Parent can be either Client or Carrier
                    })
                    .Take(10)
                    .ToListAsync(cancellationToken);
            });

            var policyTask = Task.Run(async () =>
            {
                using var context = _contextFactory.CreateDbContext();
                return await context.Policies.AsNoTracking()
                    .Where(p => p.PolicyNumber.Contains(searchTerm))
                    .OrderBy(p => p.PolicyNumber) // Add ordering to prevent EF warning
                    .Select(p => new FireSearchResultViewModel
                    {
                        DataType = "Policy",
                        Id = p.ClientId,  // Return the ClientId instead of PolicyId
                        Primary = p.PolicyNumber,
                        Parent = p.Client.Name // Policy is linked to a Client
                    })
                    .Take(10)
                    .ToListAsync(cancellationToken);
            });

            var addressTask = Task.Run(async () =>
            {
                using var context = _contextFactory.CreateDbContext();
                return await (from a in context.Address.AsNoTracking()
                              join c in context.Clients.AsNoTracking() on a.AddressId equals c.Address.AddressId into clientGroup
                              from client in clientGroup.DefaultIfEmpty()
                              join cr in context.Carriers.AsNoTracking() on a.AddressId equals cr.Address.AddressId into carrierGroup
                              from carrier in carrierGroup.DefaultIfEmpty()
                              where a.AddressLine1.Contains(searchTerm) || a.City.Contains(searchTerm) || a.PostalCode.Contains(searchTerm)
                              select new FireSearchResultViewModel
                              {
                                  DataType = "Address",
                                  Id = client != null ? client.ClientId : carrier != null ? carrier.CarrierId : 0,
                                  Primary = $"{a.AddressLine1}, {a.City}, {a.State}",
                                  Parent = client != null ? client.Name : carrier != null ? carrier.CarrierName : ""
                              })
                             .OrderBy(x => x.Primary) // Add ordering to prevent EF warning
                             .Take(10)
                             .ToListAsync(cancellationToken);
            });

            // Await all tasks
            await Task.WhenAll(clientTask, carrierTask, contactTask, policyTask, addressTask);

            // Collect results
            results.AddRange(clientTask.Result);
            results.AddRange(carrierTask.Result);
            results.AddRange(contactTask.Result);
            results.AddRange(policyTask.Result);
            results.AddRange(addressTask.Result);

            return results;
        }

        public async Task<List<FireSearchResultViewModel>> SearchAllUsingSPAsync(string searchTerm, CancellationToken cancellationToken)
        {
            // Use the stored database provider name from StateService
            if (_stateService.DatabaseProvider.Contains("Sqlite", StringComparison.OrdinalIgnoreCase))
            {
                // Fallback to SearchAllWaitAsync for SQLite
                return await SearchAllWaitAsync(searchTerm, cancellationToken);
            }
            try
            {
                using var context = _contextFactory.CreateDbContext();

                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    return new List<FireSearchResultViewModel>();
                }

                searchTerm = searchTerm.ToLower();

                // Execute the stored procedure
                var results = await context.FireSearchResultViewModel
                    .FromSqlRaw("EXEC dbo.SearchAllWithRenewals @SearchTerm = {0}", searchTerm)
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                return results;
            }
            catch (Exception ex) when (
                ex is SqlException ||
                ex is OperationCanceledException ||
                ex is TaskCanceledException ||
                ex is InvalidOperationException)
            {
                // Log the error if needed (e.g., using a logging framework)
                Console.WriteLine($"Error in search: {ex.Message}");

                // Return an indicator to keep typing
                return new List<FireSearchResultViewModel>
                {
                    new FireSearchResultViewModel
                    {
                        DataType = "Info",
                        Primary = "[Keep typing...]", // Message to display in UI
                        Parent = ""
                    }
                };
            }
        }

        /// <summary>
        /// Retrieves associations for a given entity, focusing on Contact relationships.
        /// Includes the primary hard-coded client for a contact, and any loose associations
        /// stored in the EntityAssociations table (primarily Contact->Client and Contact->Contact).
        /// </summary>
        /// <param name="entityType">Currently optimized for "Contact".</param>
        /// <param name="entityId">The ID of the entity.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>A list of associated entities formatted as FireSearchResultViewModel.</returns>
        public async Task<List<FireSearchResultViewModel>> GetAssociationsAsync(string entityType, int entityId, CancellationToken cancellationToken)
        {
            var results = new List<FireSearchResultViewModel>();
            using var context = _contextFactory.CreateDbContext();
            string entityTypeLower = entityType.ToLowerInvariant();

            // --- Step 1: Handle Hard-coded Links (Primarily for Contacts) ---
            if (entityTypeLower == "contact")
            {
                var contact = await context.Contacts.AsNoTracking()
                    .Include(co => co.Client) // Include the primary Client via ClientId FK
                    .FirstOrDefaultAsync(co => co.ContactId == entityId, cancellationToken);

                if (contact?.Client != null)
                {
                    results.Add(new FireSearchResultViewModel
                    {
                        DataType = "Client",
                        Id = contact.ClientId.Value, // Assuming ClientId is nullable int
                        Primary = contact.Client.Name,
                        Parent = "Primary Client (Direct)" // Clearly mark the hard-coded link
                    });
                }
            }
            // Add other hard-coded lookups here if needed for other entity types later

            // --- Step 2: Get Loose Associations from EntityAssociations Table ---
            var looseAssociations = await context.EntityAssociations.AsNoTracking()
                .Where(assoc => (assoc.EntityType1.ToLower() == entityTypeLower && assoc.EntityId1 == entityId) ||
                               (assoc.EntityType2.ToLower() == entityTypeLower && assoc.EntityId2 == entityId))
                .ToListAsync(cancellationToken);

            if (looseAssociations.Any())
            {
                // Identify the 'other' entities we need to look up
                var relatedEntitiesToLookup = looseAssociations
                    .Select(a =>
                    {
                        bool isEntity1Input = a.EntityType1.Equals(entityType, StringComparison.OrdinalIgnoreCase) && a.EntityId1 == entityId;
                        return new
                        {
                            OtherEntityType = (isEntity1Input ? a.EntityType2 : a.EntityType1).ToLowerInvariant(),
                            OtherEntityId = isEntity1Input ? a.EntityId2 : a.EntityId1,
                            Association = a // Keep original association for relationship description
                        };
                    })
                    .GroupBy(x => x.OtherEntityType)
                    .ToList(); // Now have groups by type, e.g., all "client" ids, all "contact" ids

                var nameLookup = new Dictionary<string, Dictionary<int, string>>();

                // Fetch names/display strings in batches
                foreach (var typeGroup in relatedEntitiesToLookup)
                {
                    var otherEntityType = typeGroup.Key;
                    var idsToFetch = typeGroup.Select(x => x.OtherEntityId).Distinct().ToList();
                    var names = new Dictionary<int, string>();

                    switch (otherEntityType)
                    {
                        case "client":
                            var clientNames = await context.Clients.AsNoTracking()
                                .Where(c => idsToFetch.Contains(c.ClientId))
                                .Select(c => new { Id = c.ClientId, Name = c.Name })
                                .ToListAsync(cancellationToken);
                            names = clientNames.ToDictionary(x => x.Id, x => x.Name);
                            break;
                        case "contact":
                            var contactNames = await context.Contacts.AsNoTracking()
                                .Where(c => idsToFetch.Contains(c.ContactId))
                                .Select(c => new { Id = c.ContactId, Name = (c.FirstName + " " + c.LastName).Trim() })
                                .ToListAsync(cancellationToken);
                            names = contactNames.ToDictionary(x => x.Id, x => x.Name);
                            break;
                        // Add other types here if you associate them later
                        default:
                            names = idsToFetch.ToDictionary(id => id, id => $"{CultureInfo.CurrentCulture.TextInfo.ToTitleCase(otherEntityType)} #{id}");
                            break;

                    }
                    nameLookup[otherEntityType] = names;
                }

                // Create ViewModels using the fetched names
                foreach (var typeGroup in relatedEntitiesToLookup)
                {
                    var otherEntityType = typeGroup.Key;
                    if (nameLookup.TryGetValue(otherEntityType, out var namesDict))
                    {
                        foreach (var relatedEntityInfo in typeGroup)
                        {
                            string primaryName = namesDict.TryGetValue(relatedEntityInfo.OtherEntityId, out var name)
                                                   ? name
                                                   : $"{CultureInfo.CurrentCulture.TextInfo.ToTitleCase(otherEntityType)} #{relatedEntityInfo.OtherEntityId}"; // Fallback

                            results.Add(new FireSearchResultViewModel
                            {
                                // Use TitleCase for display consistency
                                DataType = CultureInfo.CurrentCulture.TextInfo.ToTitleCase(otherEntityType),
                                Id = relatedEntityInfo.OtherEntityId,
                                Primary = primaryName,
                                Parent = $"{relatedEntityInfo.Association.RelationshipDescription} (Loose)" // Mark as loose & show description
                            });
                        }
                    }
                }
            }

            // --- Step 3: Combine and Order Results ---
            return results
                .OrderBy(r => r.Parent.Contains("(Direct)") ? 0 : 1) // Show direct links first
                .ThenBy(r => r.Parent)
                .ThenBy(r => r.DataType)
                .ThenBy(r => r.Primary)
                .ToList();
        }
    }
}
