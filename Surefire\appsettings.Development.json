{
  "DetailedErrors": true,
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Warning",
      "Microsoft.Extensions": "Warning",
      "System": "Warning",
      
      // Agent System Debug Logging
      "Surefire.Domain.Agents.Services.TaskAgentRegistry": "Debug",
      "Surefire.Domain.Agents.Services.TaskAgentOrchestrator": "Debug", 
      "Surefire.Domain.Agents.TaskAgents.LossRunRequestAgent": "Debug",
      "Surefire.Domain.Agents.TaskAgents.PaymentLinkAgent": "Debug",
      "Surefire.Domain.Agents.Pages.AgentButtonsSandbox": "Debug",
      "Surefire.Domain.Agents.Components.TaskAgentButtons": "Debug",
      
      // Keep these quiet
      "Surefire.Domain.DocuSign": "None",
      "Surefire.Domain.Agents.Handlers.DatabaseQueryHandler": "None",
      "Surefire.Domain.Agents.Services.UnifiedInputHandlerAdapter": "None"
    }
  }
}
