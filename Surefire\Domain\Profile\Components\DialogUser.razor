﻿@inherits AppComponentBase
@using Surefire.Domain.Users.Services
@using Microsoft.AspNetCore.Components
@implements IDialogContentComponent<ApplicationUser>
@inject UserService UserService

<FluentDialogHeader ShowDismiss="true">
    <FluentStack VerticalAlignment="VerticalAlignment.Center">
        <FluentIcon Value="@(new Icons.Regular.Size24.CardUi())" />
        <FluentLabel Typo="Typography.PaneHeader">
            @Dialog.Instance.Parameters.Title
        </FluentLabel>
    </FluentStack>
</FluentDialogHeader>

<FluentDialogBody>
    <EditForm Model="Content">
        <DataAnnotationsValidator />

        <FluentTextField @bind-Value="Content.FirstName" Placeholder="First Name" Label="First Name" />
        <FluentTextField @bind-Value="Content.LastName" Placeholder="Last Name" Label="Last Name" />
        <FluentTextField @bind-Value="Content.Email" Placeholder="Email" Label="Email" />
        <FluentTextField @bind-Value="Content.PhoneNumber" Placeholder="Phone Number" Label="Phone Number" />
        <FluentTextField @bind-Value="Content.DesktopUsername" Placeholder="Desktop Username" Label="Desktop Username" />

        <div style="color: var(--error);">
            <FluentValidationSummary />
        </div>
    </EditForm>
</FluentDialogBody>

<FluentDialogFooter>
    <FluentButton Appearance="Appearance.Accent" OnClick="@SaveAsync">Save</FluentButton>
    <FluentButton Appearance="Appearance.Neutral" OnClick="@CancelAsync">Cancel</FluentButton>
</FluentDialogFooter>

@code {
    [CascadingParameter]
    public FluentDialog Dialog { get; set; } = default!;

    [Parameter]
    public ApplicationUser Content { get; set; } = default!;

    private async Task SaveAsync()
    {
        await Dialog.CloseAsync(Content);
    }

    private async Task CancelAsync()
    {
        await Dialog.CancelAsync();
    }
}
