using Microsoft.AspNetCore.Components;
using Surefire.Domain.Carriers.Services;

namespace Surefire.Domain.Carriers.Pages
{
    public abstract class CarriersPageBase : AppComponentBase
    {
        [Inject] protected CarrierService CarrierService { get; set; } = default!;
        [Inject] protected NavigationManager Navigation { get; set; } = default!;
        [CascadingParameter] public Action<string>? UpdateHeader { get; set; }

        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();
            UpdateHeader?.Invoke("Carriers");
        }
    }
}