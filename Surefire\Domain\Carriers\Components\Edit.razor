﻿@using Microsoft.AspNetCore.Hosting
@using Microsoft.EntityFrameworkCore
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Surefire.Domain.Carriers.Models
@using Surefire.Domain.Carriers.Services
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Notifications
@using Microsoft.FluentUI.AspNetCore.Components
@using System.IO

@inject CarrierService CarrierService
@inject NavigationManager Navigation
@inject IWebHostEnvironment WebHostEnvironment

@if (isLoading)
{
    <p><em>Loading...</em></p>
}
else if (!IsCreateMode && carrier is null)
{
    <p class="text-danger"><em>Carrier not found.</em></p>
    <FluentButton Appearance="Appearance.Accent" @onclick="@(() => Navigation.NavigateTo("/Carriers"))">Back to Carriers</FluentButton>
}
else
{
    <div class="page-content carrier-edit-page">
        <h1>@(IsCreateMode ? "Create New Carrier" : $"Edit Carrier: {carrier.CarrierName}")</h1>

        @if (!string.IsNullOrEmpty(statusMessage))
        {
            <div class="alert @statusMessageCssClass" role="alert">
                @statusMessage
            </div>
        }

        <EditForm method="post" Model="carrier" OnValidSubmit="HandleValidSubmit" FormName="edit" Enhance>
            <DataAnnotationsValidator />
            <ValidationSummary class="mb-3" />

            <div class="sfpage-carrier-edit">
                <FluentStack>
                    @* Column 1 *@
                    <div style="flex: 1; min-width:300px;">
                        <span class="gb-section-title">BASIC INFO</span>
                        <div class="gb-section-container">
                            <div class="mb-3">
                                <SfTextBox @bind-Value="carrier.CarrierName" Placeholder="Carrier Name" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.CarrierName" />
                            </div>
                            <div class="mb-3">
                                <SfTextBox @bind-Value="carrier.LookupCode" Placeholder="Lookup Code" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.LookupCode" />
                            </div>
                            <div class="mb-3">
                                <SfTextBox @bind-Value="carrier.CarrierNickname" Placeholder="Carrier Nickname (Optional)" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.CarrierNickname" />
                            </div>
                            <div style="height:15px;"></div>
                            <div class="mb-3 d-flex flex-column">
                                <FluentCheckbox @bind-Value="carrier.IssuingCarrier" Label="Issuing Carrier" />
                                <ValidationMessage For="() => carrier.IssuingCarrier" />
                                <FluentCheckbox @bind-Value="carrier.Wholesaler" Label="Wholesaler" />
                                <ValidationMessage For="() => carrier.Wholesaler" />
                            </div>
                        </div>

                        <div style="height:25px;"></div>

                        <span class="gb-section-title">ADDRESS</span>
                        <div class="gb-section-container">
                            <div class="mb-3">
                                <SfTextBox @bind-Value="carrier.StreetAddress" Placeholder="Street Address" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.StreetAddress" />
                            </div>
                            <div class="mb-3">
                                <SfTextBox @bind-Value="carrier.City" Placeholder="City" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.City" />
                            </div>
                            <div class="mb-3">
                                <SfTextBox @bind-Value="carrier.State" Placeholder="State" FloatLabelType="FloatLabelType.Always" MaxLength="2" />
                                <ValidationMessage For="() => carrier.State" />
                            </div>
                            <div class="mb-3">
                                <SfTextBox @bind-Value="carrier.Zip" Placeholder="Zip Code" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.Zip" />
                            </div>
                        </div>
                        <div class="form-actions mt-4">
                            <FluentButton Appearance="Appearance.Accent" Type="Microsoft.FluentUI.AspNetCore.Components.ButtonType.Submit" aria-label="Save Carrier changes">@(IsCreateMode ? "Create Carrier" : "Save Carrier")</FluentButton>
                            <FluentButton Appearance="Appearance.Neutral" @onclick="CancelEdit" aria-label="Cancel editing and return to list">Cancel</FluentButton>
                        </div>
                    </div>
                    @* Column 2 *@
                    <div style="flex: 1; min-width:300px;">

                        <span class="gb-section-title">CHANNELS</span>
                        <div class="gb-section-container">
                            <div class="mb-3">
                                <SfTextBox @bind-Value="carrier.Website" Placeholder="Main Website" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.Website" />
                            </div>
                            <div class="mb-3">
                                <SfTextBox @bind-Value="carrier.QuotingWebsite" Placeholder="Quoting Website" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.QuotingWebsite" />
                            </div>
                            <div class="mb-3">
                                <SfTextBox @bind-Value="carrier.ServicingWebsite" Placeholder="Servicing Website" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.ServicingWebsite" />
                            </div>
                            <div class="mb-3">
                                <SfTextBox @bind-Value="carrier.NewSubmissionEmail" Placeholder="New Submission Email" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.NewSubmissionEmail" />
                            </div>
                            <div class="mb-3">
                                <SfTextBox @bind-Value="carrier.ServicingEmail" Placeholder="Servicing Email" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.ServicingEmail" />
                            </div>

                            <div class="mb-3">
                                <SfTextBox @bind-Value="carrier.Phone" Placeholder="Agency Support Phone" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.ServicingEmail" />
                            </div>
                        </div>
                    </div>

                    @* Column 3 *@
                    <div style="flex: 1; min-width:300px;">
                        <span class="gb-section-title">LOSS RUNS</span>
                        <div class="gb-section-container">
                            <div class="mb-3">
                                <SfTextBox @bind-Value="carrier.LossRunsEmail" Placeholder="Loss Runs Request Email" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.LossRunsEmail" />
                            </div>
                            <div class="mb-3">
                                <SfTextBox @bind-Value="carrier.LossRunsURL" Placeholder="Loss Runs Website URL" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.LossRunsURL" />
                            </div>
                            <div class="mb-3">
                                <SfTextBox Multiline="true" Rows="3" @bind-Value="carrier.LossRunsNote" Placeholder="Loss Runs Request Instructions" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.LossRunsNote" />
                            </div>
                        </div>

                        <div style="height:25px;"></div>

                        @if (!IsCreateMode)
                        {
                            <span class="gb-section-title">LOGO</span>
                            <div class="gb-section-container">
                                @if (!string.IsNullOrEmpty(carrier.LogoFilename))
                                {
                                    <div class="logo-preview mb-2">
                                        <img src="/uploads/logos/carrier/@carrier.LogoFilename" width="100" height="100" alt="@(carrier.CarrierName) Logo" />
                                    </div>
                                    <FluentButton Appearance="Appearance.Lightweight" IconStart="@(new Icons.Regular.Size16.Delete())" OnClick="@(async () => await RemoveLogo())">Remove Logo</FluentButton>
                                }
                                else
                                {
                                    <label>Upload Logo (Max 5MB, .jpg, .png)</label>
                                    <SfUploader AutoUpload="true" AllowedExtensions=".jpg,.jpeg,.png" MaxFileSize="5242880">
                                        <UploaderEvents ValueChange="@OnHeadshotUpload"></UploaderEvents>
                                    </SfUploader>
                                    <ValidationMessage For="() => carrier.LogoFilename" />
                                }
                            </div>
                        }
                    </div>

                    @* Column 4 *@
                    <div style="flex: 1; min-width:300px;">
                        <span class="gb-section-title">NOTES</span>
                        <div class="gb-section-container">

                            <div class="mb-3">
                                <SfTextBox Multiline="true" Rows="5" @bind-Value="carrier.AppetiteJson" Placeholder="Appetite JSON (Optional)" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.AppetiteJson" />
                            </div>
                            <div class="mb-3">
                                <SfTextBox Multiline="true" Rows="5" @bind-Value="carrier.QuotelinesJson" Placeholder="Quote Lines JSON (Optional)" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.QuotelinesJson" />
                            </div>
                            <div class="mb-3">
                                <SfTextBox Multiline="true" Rows="5" @bind-Value="carrier.Notes" Placeholder="Internal Notes (Optional)" FloatLabelType="FloatLabelType.Always" />
                                <ValidationMessage For="() => carrier.Notes" />
                            </div>
                        </div>
                    </div>
                </FluentStack>

            </div>

        </EditForm>
    </div>
}

@code {
    [Parameter]
    public int SelectedCarrierId { get; set; }

    [Parameter]
    public EventCallback<int> SelectedCarrierIdChanged { get; set; }

    [Parameter]
    public bool IsCreateMode { get; set; }

    private Carrier? carrier { get; set; }

    private bool isLoading = true;
    private string? statusMessage;
    private string statusMessageCssClass = "text-danger";

    protected override async Task OnParametersSetAsync()
    {
        if (IsCreateMode)
        {
            // Initialize a new carrier for create mode
            carrier = new Carrier
            {
                DateCreated = DateTime.UtcNow
            };
            isLoading = false;
            return;
        }
        
        if (carrier is null || carrier.CarrierId != SelectedCarrierId)
        {
            if(SelectedCarrierId == 0)
            {
                carrier = null;
                isLoading = false;
                return;
            }

            isLoading = true;
            statusMessage = null;

            carrier = await CarrierService.GetCarrierByIdAsync(SelectedCarrierId);

            isLoading = false;

            if (carrier is null)
            {
                statusMessage = $"Carrier with ID {SelectedCarrierId} not found.";
                statusMessageCssClass = "alert-warning";
            }
        }
    }

    private void CancelEdit()
    {
        if (IsCreateMode)
        {
            Navigation.NavigateTo("/Carriers");
        }
        else
        {
            Navigation.NavigateTo($"/Carriers/{SelectedCarrierId}");
        }
    }

    private async Task HandleValidSubmit()
    {
        if (carrier is null) return;
        
        isLoading = true;
        statusMessage = null;

        try
        {
            if (IsCreateMode)
            {
                // Create a new carrier
                int newCarrierId = await CarrierService.CreateCarrierReturnIdAsync(carrier);
                statusMessage = "Carrier created successfully!";
                statusMessageCssClass = "alert-success";
                Navigation.NavigateTo($"/Carriers/{newCarrierId}", true);
            }
            else
            {
                // Update existing carrier
                await CarrierService.UpdateCarrierAsync(carrier);
                statusMessage = "Carrier updated successfully!";
                statusMessageCssClass = "alert-success";
                Navigation.NavigateTo($"/Carriers/{carrier.CarrierId}");
            }
        }
        catch (DbUpdateConcurrencyException)
        {
            statusMessage = "Error: The carrier was modified by another user after you loaded it. Please reload the page and try again.";
            statusMessageCssClass = "alert-danger";
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Error {(IsCreateMode ? "creating" : "updating")} carrier: {ex.Message}");
            statusMessage = $"An unexpected error occurred while {(IsCreateMode ? "creating" : "saving")} the carrier.";
            statusMessageCssClass = "alert-danger";
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task RemoveLogo()
    {
        if (carrier is null) return;

        try
        {
            await CarrierService.RemoveLogo(carrier.CarrierId);
            carrier.LogoFilename = null;
            statusMessage = "Logo removed.";
            statusMessageCssClass = "alert-info";
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Error removing logo: {ex.Message}");
            statusMessage = "Error removing logo.";
            statusMessageCssClass = "alert-danger";
        }
    }

    private async Task OnHeadshotUpload(UploadChangeEventArgs args)
    {
        if (args.Files != null && args.Files.Count > 0 && carrier != null)
        {
            var file = args.Files[0];
            if (file != null)
            {
                string extension = Path.GetExtension(file.FileInfo.Name);
                string fileName = $"{carrier.CarrierId}{extension}";
                string directoryPath = Path.Combine(WebHostEnvironment.WebRootPath, "uploads", "logos", "carrier");
                Directory.CreateDirectory(directoryPath);
                string uploadPath = Path.Combine(directoryPath, fileName);

                try
                {
                    using (var stream = file.File.OpenReadStream(long.MaxValue))
                    {
                        MemoryStream resizedImage;
                        if (extension.Equals(".png", StringComparison.OrdinalIgnoreCase))
                        {
                            resizedImage = await ImageResizer.ResizeImagePngAsync(stream, 500);
                        }
                        else
                        {
                            resizedImage = await ImageResizer.ResizeImageAsync(stream, 500);
                        }

                        resizedImage.Seek(0, SeekOrigin.Begin);

                        using (var fileStream = new FileStream(uploadPath, FileMode.Create, FileAccess.Write))
                        {
                            await resizedImage.CopyToAsync(fileStream);
                        }
                    }

                    carrier.LogoFilename = fileName;
                    await CarrierService.UpdateCarrierLogoAsync(carrier.CarrierId, fileName);
                    statusMessage = "Logo uploaded successfully.";
                    statusMessageCssClass = "alert-success";
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Error uploading/resizing logo: {ex.Message}");
                    statusMessage = "Error processing logo upload.";
                    statusMessageCssClass = "alert-danger";
                }
                finally
                {
                    StateHasChanged();
                }
            }
        }
    }
}