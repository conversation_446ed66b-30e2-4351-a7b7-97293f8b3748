﻿.sectiontitletab {
    font-family: "montserrat", sans-serif;
    font-size: 1.5em;
    padding-top: 10px;
    padding-bottom: 10px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#e35049+1,201e60+49,3d3d3d+100 */
    background: linear-gradient(to right, #767676 1%,#201e60 49%,#3d3d3d 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#993d51+1,201e60+49,3d3d3d+100 */
    background: linear-gradient(to right, rgba(153,61,81,1) 1%,rgba(32,30,96,1) 49%,rgba(61,61,61,1) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */

    color: #fff;
    text-align: center;
}
.completed-task {
    opacity:.5;
    padding:5px 13px;
}
.daily-container {
    border-radius: 15px;
    box-shadow: 0px 0px 15px #000000b9;
    overflow: hidden;
    position: relative;
    top: -52px;
}
.daily-task {
    font-weight: 600;
    font-family: "montserrat", sans-serif;
    width: 100%;
    font-size: .85em;
    color: #404040;
}
.dailytask-box {
    background-color: #ffffffe2;
}

:root .fluent-sortable-list {
    border:0px !important;
}
.fluent-sortable-list {
    border: 0px !important;

}
:root {
    --stroke-width: 0px;
    --neutral-stroke-input-active: #fff0 !important;
}
:root .sortable-item {
    background-color: #1b8ce3 !important;
    background: none !important;
    border-top: 0px solid #fff !important;
    border-bottom: 1px solid #fff9 !important;
}
.completed-ts {
    position:relative;
    top:-16px
}
.add-field {
    padding-left: 13px;
    padding-right: 13px;
    padding-top: 5px;
    opacity:1;
}
.sortitem {
    display: flex;
    align-items: center;
    height: 10px;
}
