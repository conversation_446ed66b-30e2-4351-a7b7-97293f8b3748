﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Surefire.Migrations
{
    /// <inheritdoc />
    public partial class CleanPhoneNumberLogic : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_EmailAddresses_Clients_ClientId",
                table: "EmailAddresses");

            migrationBuilder.DropForeignKey(
                name: "FK_PhoneNumbers_Clients_ClientId",
                table: "PhoneNumbers");

            migrationBuilder.DropIndex(
                name: "IX_PhoneNumbers_ClientId",
                table: "PhoneNumbers");

            migrationBuilder.DropIndex(
                name: "IX_EmailAddresses_ClientId",
                table: "EmailAddresses");

            migrationBuilder.DropColumn(
                name: "ClientId",
                table: "PhoneNumbers");

            migrationBuilder.DropColumn(
                name: "ClientId",
                table: "EmailAddresses");

            migrationBuilder.AddColumn<string>(
                name: "<PERSON><PERSON><PERSON>",
                table: "Settings",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AgentLlm",
                table: "Settings");

            migrationBuilder.AddColumn<int>(
                name: "ClientId",
                table: "PhoneNumbers",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ClientId",
                table: "EmailAddresses",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_PhoneNumbers_ClientId",
                table: "PhoneNumbers",
                column: "ClientId");

            migrationBuilder.CreateIndex(
                name: "IX_EmailAddresses_ClientId",
                table: "EmailAddresses",
                column: "ClientId");

            migrationBuilder.AddForeignKey(
                name: "FK_EmailAddresses_Clients_ClientId",
                table: "EmailAddresses",
                column: "ClientId",
                principalTable: "Clients",
                principalColumn: "ClientId");

            migrationBuilder.AddForeignKey(
                name: "FK_PhoneNumbers_Clients_ClientId",
                table: "PhoneNumbers",
                column: "ClientId",
                principalTable: "Clients",
                principalColumn: "ClientId");
        }
    }
}
