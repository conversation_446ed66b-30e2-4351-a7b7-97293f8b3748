﻿# Creating DropDowns – **Surefire** Rulebook  
*Version 1.0 – Last updated 2025‑06‑08*

These instructions are intended for AI coding assistants (<PERSON>urs<PERSON>, <PERSON>pilot, etc.) generating **Syncfusion `SfDropDownList`** components inside `.razor` files of the Surefire project.

---
## 1. Always import the correct namespace
```razor
@using Syncfusion.Blazor.DropDowns   <!-- REQUIRED -->
```
Add additional `@using` directives only if they are actually referenced in the file.

---
## 2. Generic type parameters **must** be declared via attributes  
| Attribute | Purpose | Mandatory | Example |
|-----------|---------|-----------|---------|
| `TItem`   | CLR type of each element in `DataSource` | ✅ | `TItem="RelationshipTypeItem"` |
| `TValue`  | CLR type of the bound value (`@bind-Value`) | ✅ | `TValue="RelationshipType"` |

**Do NOT** invent attributes such as `TItemType`, `TData`, `TKey`, etc. They do **not** exist and will break the build.

---
## 3. Minimal valid dropdown
```razor
<SfDropDownList TItem="string" TValue="string"
                DataSource="@names"
                @bind-Value="selectedName"
                Placeholder="Select…">
</SfDropDownList>
```

### Mandatory attributes
* `DataSource` – a **non‑null** `IEnumerable<TItem>`
* `@bind-Value` – a variable of type `TValue`

`Placeholder`, `CssClass`, `AllowFiltering`, etc. are optional.

---
## 4. Binding to complex objects (Display / Value mapping)
When `TItem` is a POCO (custom class/record) **always** add a nested `<DropDownListFieldSettings>` element.

```razor
<SfDropDownList TItem="RelationshipTypeItem" TValue="RelationshipType"
                DataSource="@items"
                @bind-Value="selected">
    <DropDownListFieldSettings Text="DisplayName"
                               Value="Value" />
</SfDropDownList>
```

*Never* write `FieldSettings="..."` or `DropDownListFields` on the root tag.

---
## 5. Templates
Wrap all templates in `<DropDownListTemplates>`; inner markup receives `context` typed as `TItem`.

```razor
<DropDownListTemplates TItem="RelationshipTypeItem">
    <HeaderTemplate>
        <div class="header">
            <span>Category</span><span>Relationship</span>
        </div>
    </HeaderTemplate>

    <ItemTemplate>
        <div class="relationship-type-item">
            <span class="category-badge @context.Category.ToString().ToLower()">
                @context.Category
            </span>
            <span>@context.DisplayName</span>
        </div>
    </ItemTemplate>
</DropDownListTemplates>
```

### Common errors to **avoid**
* Using `<ItemTemplates>` (plural) – correct is **Templates**.
* Writing `@item` instead of `@context`.
* Closing tags out of order.

---
## 6. Searchable dropdowns (optional features)
* Set `AllowFiltering="true"` to enable client‑side filter.
* Choose **one** filtering approach: `Filter` callback **or** `FilterType="FilterType.Contains"`. Do not set both.
* Do not mix `AllowFiltering` with `ShowFilterBar`; pick one.

---
## 7. Styling rules
* `CssClass` expects raw class names (**no leading dot `.`**).
* For transparent buttons next to a dropdown use `BackgroundColor="#fff0"`.

---
## 8. Accessibility & validation
* Always supply `Placeholder` text.
* For required fields, wrap the dropdown in an `EditForm` and add `SfValidator` or `<ValidationMessage For="…" />`.

---
## 9. **Anti‑patterns (NEVER DO THESE)**
1. `TItemType`, `TValueType`, `PlaceholderText` – **invalid attributes**.  
2. Manually inserting `<option>` elements inside `SfDropDownList`.  
3. Passing `null` to `DataSource`. Check for null and use an empty list instead.  
4. Intermixing FluentUI `<FluentSelect>` with `SfDropDownList` in the same control group.  
5. Using kebab‑case (`allow-filtering`) instead of PascalCase (`AllowFiltering`).

---
## 10. Canonical example (used in **Add Association** dialog)
```razor
<SfDropDownList TItem="RelationshipTypeItem" TValue="RelationshipType"
                DataSource="@filteredRelationshipTypes"
                @bind-Value="selectedRelationshipType">
    <DropDownListFieldSettings Text="DisplayName" Value="Value" />
    <DropDownListTemplates TItem="RelationshipTypeItem">
        <HeaderTemplate>
            <div class="relationship-category-header">
                <span>Category</span>
                <span>Relationship Type</span>
            </div>
        </HeaderTemplate>
        <ItemTemplate>
            <div class="relationship-type-item">
                <span class="category-badge @context.Category.ToString().ToLower()">
                    @context.Category
                </span>
                <span>@context.DisplayName</span>
            </div>
        </ItemTemplate>
    </DropDownListTemplates>
</SfDropDownList>
```

Follow this pattern **verbatim**. Any deviation (misspelled attribute, missing field settings, etc.) causes compile‑time errors under .NET 8 Blazor.

---
## 11. Checklist before committing code
- [ ] Namespaces imported?  
- [ ] `TItem` & `TValue` match data types?  
- [ ] `DataSource` non‑null and iterable?  
- [ ] `DropDownListFieldSettings` present when binding complex objects?  
- [ ] No forbidden attributes (`TItemType`, etc.)?  
- [ ] Templates wrapped correctly?  
- [ ] Placeholder provided?  

If any box is unticked, **stop and fix** before merge.
