@* @page "/Forms/LossRunsRequest"
@using Surefire.Domain.Shared.Components
@using Surefire.Components.Layout
@using Surefire.Domain.Policies.Models
@using Surefire.Domain.Policies.Services
@using Surefire.Domain.Clients.Models
@using Surefire.Domain.Clients.Services
@inject PolicyService PolicyService
@inject ClientService ClientService
@inject StateService StateService

<PageTitle>Loss Runs Request</PageTitle>

<_FormsToolbar PageName="LossRunsRequest" />

<div class="page-content">
    <div class="mh1">Loss Runs Request</div>
    
    <div class="client-selection">
        <div class="form-group">
            @if (clients != null)
            {
                <FluentSelect TOption="Client"
                    Label="Client"
                    Items="@clients"
                    @bind-SelectedOption="@selectedClient"
                    OptionText="@(c => c.Name)"
                    OptionValue="@(c => c.ClientId.ToString())" />
            }
            else
            {
                <p><em>Loading clients...</em></p>
            }
        </div>
    </div>
    
    @if (selectedClient != null && currentPolicies != null)
    {
        <LossRunsRequester CurrentPolicies="@currentPolicies" />
    }
    else if (selectedClient != null)
    {
        <p><em>Loading policies...</em></p>
    }
</div>

@code {
    private List<Client> clients;
    private Client selectedClient;
    private List<Policy> currentPolicies;

    protected override async Task OnInitializedAsync()
    {
        await LoadClients();
    }

    private async Task LoadClients()
    {
        // Get recently viewed clients or most active clients
        var clientsList = await ClientService.GetRecentClientsAsync(50);
        clients = clientsList.OrderBy(c => c.Name).ToList();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (selectedClient != null)
        {
            await LoadClientPolicies();
        }
    }

    private async Task LoadClientPolicies()
    {
        if (selectedClient != null)
        {
            var policies = await PolicyService.GetCurrentPoliciesByClientIdAsync(selectedClient.ClientId);
            currentPolicies = policies;
            StateHasChanged();
        }
    }
}

<style>
    .client-selection {
        margin-bottom: 2rem;
    }
</style>  *@