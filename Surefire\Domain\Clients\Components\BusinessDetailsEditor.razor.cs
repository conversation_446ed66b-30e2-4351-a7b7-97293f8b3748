﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.JSInterop;
using Surefire.Domain.Attachments.Models;
using Surefire.Domain.Attachments.Services;
using Surefire.Domain.Clients.Helpers;
using Surefire.Domain.Clients.Models;
using Surefire.Domain.Clients.Services;
using Surefire.Domain.OpenAI;
using Surefire.Domain.Ember;
using Surefire.Domain.Policies.Services;
using Surefire.Domain.Shared.Services;
using Syncfusion.Blazor.Inputs;
using System.Text.Json;


public partial class BusinessDetailsEditorBase : ComponentBase
{
    [Parameter]
    public int ClientId { get; set; }

    [Inject] protected IJSRuntime JS { get; set; } = null!;
    [Inject] protected ClientService ClientService { get; set; } = null!;
    [Inject] protected OpenAiService OpenAiService { get; set; } = null!;
    [Inject] protected StateService StateService { get; set; } = null!; 
    [Inject] protected PolicyExtractorService PolicyExtractor { get; set; } = null!;
    [Inject] protected AttachmentService AttachmentService { get; set; } = null!;
    [Inject] protected LLMWhispererService LLMWhispererService { get; set; } = null!;
    [Inject] protected OpenAIPro OpenAIPro { get; set; } = null!;
    [Inject] protected AzureFormService AzureFormService { get; set; } = null!;
    [Inject] protected EmberService EmberService { get; set; } = null!;

    public BusinessDetails? BusinessDetails { get; set; }
    public EditContext? MyEditContext { get; set; }
    public JsonDocument? CurrentJsonDocument { get; set; }
    public Attachment? SupplementalAttachment { get; set; }
    public Attachment? TargetAttachment { get; set; }
    public SfUploader UploaderSupplemental;
    public SfUploader UploaderTargetForm;
    public IEnumerable<LocalEnumData<LegalEntityType>> LegalEntityTypeList { get; set; } = Enumerable.Empty<LocalEnumData<LegalEntityType>>();
    public IEnumerable<LocalEnumData<BusinessType>> BusinessTypeList { get; set; } = Enumerable.Empty<LocalEnumData<BusinessType>>();
    public IEnumerable<LocalEnumData<LicenseType>> LicenseTypeList { get; set; } = Enumerable.Empty<LocalEnumData<LicenseType>>();
    // Da 1s and 0s
    public bool IsExtractingFields { get; set; } = false;
    public bool showFirebar { get; set; } = false;
    public bool advancedMode = false;
    private int? supplementalGroupId;
    public double firebarProgress { get; set; } = 0;
    // Strings
    public string XmlContent { get; set; } = string.Empty;
    public string JsonContent { get; set; } = string.Empty;
    public string JsonResponse { get; set; } = string.Empty;
    public string ExtractStatusMessage { get; set; } = string.Empty;
    public string ExtractedTextAttachmentUrl { get; set; } = string.Empty;
    public string ExtractedJsonAttachmentUrl { get; set; } = string.Empty;
    public string firebarStatus { get; set; } = string.Empty;
    public string selectedForm { get; set; } = "Kinsale";

    
    //                                                                 |
    // MAIN METHODS ---------------------------------------------------|
    //                                                                 |
    protected override async Task OnParametersSetAsync()
    {
        await LoadBusinessDetailsAsync();
        LoadEnumData();
        if (BusinessDetails != null)
        {
            MyEditContext = new EditContext(BusinessDetails);
        }
        await LoadSupplementalAttachment();
        
        // Start EmberService connection for SignalR communication
        try
        {
            await EmberService.StartConnectionAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to start EmberService connection: {ex.Message}");
        }
    }
    private async Task LoadBusinessDetailsAsync()
    {
        BusinessDetails = await ClientService.GetBusinessDetailsByClientId(ClientId);
        if (BusinessDetails == null)
        {
            BusinessDetails = new BusinessDetails { ClientId = ClientId };
            await ClientService.AddBusinessDetailsAsync(BusinessDetails);
            BusinessDetails = await ClientService.GetBusinessDetailsByClientId(ClientId);
        }
    }

    // Form Fields UI                                                --|
    public async Task OnFieldChanged(string fieldName)
    {
        await SaveChanges();
    }
    public async Task SaveChanges()
    {
        if (BusinessDetails != null)
        {
            bool isValid = MyEditContext?.Validate() ?? false;
            await ClientService.UpdateBusinessDetailsAsync(BusinessDetails);
            StateHasChanged();
        }
    }
    public async Task CopyItem(string? textToCopy)
    {
        if (!string.IsNullOrEmpty(textToCopy))
        {
            await JS.InvokeVoidAsync("navigator.clipboard.writeText", textToCopy);
            // Optionally, add a notification "Copied!"
        }
    }
    
    // (Extractor)                                                   --|
    public async Task ParseGenius()
    {
        if (BusinessDetails == null) return;

        StateService.UpdateStatus("Parsing (Genius Function Calling)...", true);
        await InvokeAsync(StateHasChanged);

        var jresp = await OpenAiService.ParseXmlToBusinessDetailsWithFunction(XmlContent, ClientId);
        // Create a fake CurrentJsonDocument for us to parse


        CurrentJsonDocument?.Dispose(); // Dispose previous document
        CurrentJsonDocument = JsonDocument.Parse(jresp);

        string prettyJson = System.Text.Json.JsonSerializer.Serialize(CurrentJsonDocument.RootElement, new JsonSerializerOptions { WriteIndented = true });
        JsonResponse = StringHelper.ColorizeJSON(prettyJson); // Assuming StringHelper.ColorizeJSON exists

        PolicyExtractor.MapSpecificJsonProperties(CurrentJsonDocument.RootElement, BusinessDetails);
        StateService.UpdateStatus("Done.", false);
        await InvokeAsync(StateHasChanged); // Final state update 
    }

    // Extract Word document contents and populate XML field
    public async Task ExtractWordDocument()
    {
        try
        {
            Console.WriteLine("[BusinessDetailsEditor] ========== ExtractWordDocument START ==========");
            StateService.UpdateStatus("Extracting Word document contents...", true);
            await InvokeAsync(StateHasChanged);

            // Register a response handler for the GetWordDocContents command
            EmberService.RegisterResponseHandler("GetWordDocContents", async (responseData) =>
            {
                await InvokeAsync(async () =>
                {
                    try
                    {
                        Console.WriteLine("[BusinessDetailsEditor] ========== Response Handler START ==========");
                        Console.WriteLine($"[BusinessDetailsEditor] Response data count: {responseData?.Count ?? 0}");
                        
                        if (responseData != null && responseData.Count > 0)
                        {
                            var documentContent = responseData[0];
                            Console.WriteLine($"[BusinessDetailsEditor] Document content length: {documentContent?.Length ?? 0}");
                            
                            if (documentContent != null)
                            {
                                var preview = documentContent.Length > 100 ? documentContent.Substring(0, 100) + "..." : documentContent;
                                Console.WriteLine($"[BusinessDetailsEditor] Content preview: '{preview.Replace('\r', ' ').Replace('\n', ' ')}'");
                            }
                            
                            if (documentContent.StartsWith("ERROR:"))
                            {
                                // Handle error
                                var errorMessage = documentContent.Substring(7); // Remove "ERROR: " prefix
                                StateService.UpdateStatus($"Word extraction error: {errorMessage}", false);
                                Console.WriteLine($"[BusinessDetailsEditor] ❌ Word document error: {errorMessage}");
                            }
                            else
                            {
                                // Success - populate the XML content field
                                XmlContent = documentContent;
                                StateService.UpdateStatus("Word document extracted successfully. You can now click 'Extract Data from ACORD XML'.", false);
                                Console.WriteLine($"[BusinessDetailsEditor] ✅ Word document content extracted: {documentContent.Length} characters");
                                
                                // Log XML structure info for debugging
                                if (documentContent.Contains("<") && documentContent.Contains(">"))
                                {
                                    Console.WriteLine("[BusinessDetailsEditor] Content appears to be XML format");
                                    
                                    // Check for expected Epic elements
                                    var expectedElements = new[] { "MasterAccountMerge", "WorkersCompensation", "BusinessAuto", "GeneralLiability", "Policy" };
                                    var foundElements = expectedElements.Where(elem => documentContent.Contains(elem)).ToList();
                                    
                                    if (foundElements.Any())
                                    {
                                        Console.WriteLine($"[BusinessDetailsEditor] Found expected Epic elements: {string.Join(", ", foundElements)}");
                                    }
                                    else
                                    {
                                        Console.WriteLine("[BusinessDetailsEditor] ⚠️ No expected Epic elements found in content");
                                    }
                                }
                                else
                                {
                                    Console.WriteLine("[BusinessDetailsEditor] ⚠️ Content does not appear to be XML format");
                                }
                            }
                        }
                        else
                        {
                            StateService.UpdateStatus("No content received from Word document.", false);
                            Console.WriteLine("[BusinessDetailsEditor] ❌ No content received from Word document");
                        }
                        
                        Console.WriteLine("[BusinessDetailsEditor] ========== Response Handler END ==========");
                    }
                    catch (Exception ex)
                    {
                        StateService.UpdateStatus($"Error processing Word document response: {ex.Message}", false);
                        Console.WriteLine($"[BusinessDetailsEditor] ❌ Error processing Word document response: {ex.Message}");
                        Console.WriteLine($"[BusinessDetailsEditor] Exception type: {ex.GetType().Name}");
                        Console.WriteLine($"[BusinessDetailsEditor] Stack trace: {ex.StackTrace}");
                    }
                    
                    StateHasChanged();
                });
            });

            // Send the command to get Word document contents
            Console.WriteLine("[BusinessDetailsEditor] Sending GetWordDocContents command...");
            await EmberService.RunEmberFunction("GetWordDocContents", new List<string>());
            Console.WriteLine("[BusinessDetailsEditor] GetWordDocContents command sent successfully");
            Console.WriteLine("[BusinessDetailsEditor] ========== ExtractWordDocument END (Command Sent) ==========");
        }
        catch (Exception ex)
        {
            StateService.UpdateStatus($"Error extracting Word document: {ex.Message}", false);
            Console.WriteLine($"[BusinessDetailsEditor] ❌ Error in ExtractWordDocument: {ex.Message}");
            Console.WriteLine($"[BusinessDetailsEditor] Exception type: {ex.GetType().Name}");
            Console.WriteLine($"[BusinessDetailsEditor] Stack trace: {ex.StackTrace}");
            Console.WriteLine("[BusinessDetailsEditor] ========== ExtractWordDocument END (Error) ==========");
            await InvokeAsync(StateHasChanged);
        }
    }
    private async Task LoadSupplementalAttachment()
    {
        supplementalGroupId = await AttachmentService.EnsureSupplementalAttachmentGroupAsync(ClientId);
        var attachments = await AttachmentService.GetAttachmentsByGroupIdAsync(supplementalGroupId.Value);
        SupplementalAttachment = attachments?
            .Where(a => a.IsSupplemental && (a.FileFormat?.ToLower() == ".pdf"))
            .OrderByDescending(a => a.DateCreated)
            .FirstOrDefault();
        var extractedText = attachments?
            .Where(a => a.FileFormat?.ToLower() == ".txt")
            .OrderByDescending(a => a.DateCreated)
            .FirstOrDefault();
        if (extractedText != null)
        {
            ExtractedTextAttachmentUrl = $"/{extractedText.LocalPath}/{extractedText.HashedFileName}";
        }
        else
        {
            ExtractedTextAttachmentUrl = string.Empty;
        }
        // Find the most recent .json attachment (not .txt or .pdf)
        var extractedJson = attachments?
            .Where(a => a.FileFormat?.ToLower() == ".json")
            .OrderByDescending(a => a.DateCreated)
            .FirstOrDefault();
        if (extractedJson != null)
        {
            ExtractedJsonAttachmentUrl = $"/{extractedJson.LocalPath}/{extractedJson.HashedFileName}";
        }
        else
        {
            ExtractedJsonAttachmentUrl = string.Empty;
        }
    }
    public string GetThumbnailPath(Attachment attachment)
    {
        if (attachment == null || string.IsNullOrEmpty(attachment.HashedFileName) || string.IsNullOrEmpty(attachment.LocalPath))
            return string.Empty;
        var baseName = attachment.HashedFileName;
        var thumbName = baseName;
        var lastDot = baseName.LastIndexOf('.');
        if (lastDot >= 0)
        {
            thumbName = baseName.Substring(0, lastDot) + "_thumb.jpg";
        }
        else
        {
            thumbName = baseName + "_thumb.jpg";
        }
        var localPath = attachment.LocalPath.Replace("\\", "/").TrimEnd('/');
        return $"/{localPath}/{thumbName}";
    }
    //                                                                 |
    // SUPPLEMENTAL WORKFLOW ------------------------------------------|
    //                                                                 |
    public async Task RunSupplementalWorkflowAsync()
    {
        // If user selected "Autodetect", run the special flow and exit early
        if (selectedForm == "Autodetect")
        {
            await RunAutodetectWorkflowAsync();
            return;
        }
        showFirebar = true;
        firebarProgress = 0;
        firebarStatus = "Extracting text from PDF...";
        StateHasChanged();
        var (pdfFile, fieldsFile, promptFile) = GetFormFiles(selectedForm);
        await ExtractSupplementalFormTextAsync(pdfFile, progressCallback: p => { firebarProgress = p; StateHasChanged(); });
        firebarProgress = 20;
        firebarStatus = "Extracting fields with AI...";
        StateHasChanged();
        // Simulate progress for OpenAI call
        double openAIProgress = 20;
        var openAITask = ExtractSupplementalFieldsFromTextAndSaveAsync(fieldsFile, promptFile, progressCallback: p => { firebarProgress = 20 + p * 0.7; StateHasChanged(); });
        while (!openAITask.IsCompleted && openAIProgress < 90)
        {
            await Task.Delay(20000); // every 20 seconds
            openAIProgress += 10;
            firebarProgress = Math.Min(90, openAIProgress);
            StateHasChanged();
        }
        await openAITask;
        firebarProgress = 90;
        firebarStatus = "Filling PDF and attaching...";
        StateHasChanged();
        await FillSupplementalPdfAndAttachAsync(pdfFile, progressCallback: p => { firebarProgress = 90 + p * 0.1; StateHasChanged(); });
        firebarProgress = 100;
        firebarStatus = "Done!";
        StateHasChanged();
        await Task.Delay(1200);
        showFirebar = false;
        firebarStatus = string.Empty;
        firebarProgress = 0;
        StateHasChanged();
    }
    // ---------------------------------------------------
    // AUTODETECT WORKFLOW ------------------------------
    // ---------------------------------------------------
    private async Task RunAutodetectWorkflowAsync()
    {
        if (TargetAttachment == null)
        {
            ExtractStatusMessage = "Please upload a target PDF before running the workflow.";
            StateHasChanged();
            return;
        }
        // Ensure we have a group ID to save all generated files
        if (supplementalGroupId == null)
        {
            supplementalGroupId = await AttachmentService.EnsureSupplementalAttachmentGroupAsync(ClientId);
        }

        showFirebar = true;
        firebarProgress = 0;
        firebarStatus = "Extracting text from target PDF...";
        StateHasChanged();

        // 1) Work on SUPPLEMENTAL form (not target) ------------------------------------
        if (SupplementalAttachment == null)
        {
            ExtractStatusMessage = "Please upload a supplemental PDF first.";
            StateHasChanged();
            return;
        }
        // 1a. Extract raw text via LLMWhisperer
        var txtAttachment = await LLMWhispererService.ProcessPdfAttachmentAsync(SupplementalAttachment, "form", supplementalGroupId);

        // 1b. Run Azure Form Recognizer on supplemental and get raw JSON
        firebarProgress = 25;
        firebarStatus = "Running Azure Document Intelligence...";
        StateHasChanged();
        await AzureFormService.ProcessPdfAttachmentAsync(SupplementalAttachment);

        // Retrieve latest RAW json (description contains "raw")
        var attachments = await AttachmentService.GetAttachmentsByGroupIdAsync(supplementalGroupId.Value);
        var rawJsonAttachment = attachments?.Where(a => a.FileFormat?.ToLower() == ".json" && a.Description?.Contains("raw") == true)
                                           .OrderByDescending(a => a.DateCreated)
                                           .FirstOrDefault();
        if (rawJsonAttachment == null)
        {
            ExtractStatusMessage = "Could not locate Azure raw JSON.";
            StateHasChanged();
            return;
        }
        var rootPath = Directory.GetCurrentDirectory();
        var rawJsonPath = Path.Combine(rootPath, "wwwroot", rawJsonAttachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()), rawJsonAttachment.HashedFileName);
        var rawJsonContent = await File.ReadAllTextAsync(rawJsonPath);

        // Append llmWhispererDump
        var llmDumpPath = Path.Combine(rootPath, "wwwroot", txtAttachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()), txtAttachment.HashedFileName);
        var llmDumpText = await File.ReadAllTextAsync(llmDumpPath);
        var docJsonDict = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(rawJsonContent) ?? new Dictionary<string, object>();
        docJsonDict["llmWhispererDump"] = llmDumpText;
        var mergedDocJson = System.Text.Json.JsonSerializer.Serialize(docJsonDict);

        // Build fielddefs array from TARGET PDF -----------------------------------------
        var targetPath = Path.Combine(rootPath, "wwwroot", TargetAttachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()), TargetAttachment.HashedFileName);
        var fieldDefs = new List<object>();
        using (var targetStream = new FileStream(targetPath, FileMode.Open, FileAccess.Read))
        {
            using var loadedDoc = new Syncfusion.Pdf.Parsing.PdfLoadedDocument(targetStream);
            foreach (Syncfusion.Pdf.Parsing.PdfLoadedField fld in loadedDoc.Form.Fields)
            {
                var tooltipProp = fld.GetType().GetProperty("Tooltip") ?? fld.GetType().GetProperty("ToolTip");
                var tooltipVal = tooltipProp?.GetValue(fld)?.ToString();
                fieldDefs.Add(new { name = fld.Name, tooltip = tooltipVal });
            }
        }
        var fieldDefsJson = System.Text.Json.JsonSerializer.Serialize(fieldDefs);
// Dump field list to console for verification
Console.WriteLine("===== Target Form Fields =====");
foreach (dynamic f in fieldDefs)
{
    Console.WriteLine(f.name);
}
Console.WriteLine("==============================");

        firebarProgress = 45;
        firebarStatus = "Inferring JSON values with OpenAI...";
        StateHasChanged();

        // 2) Send to OpenAI
        var jsonAttachment = await OpenAIPro.AutoDetectJsonFromDocJsonAsync(mergedDocJson, fieldDefsJson, supplementalGroupId.Value, ClientId, 0, "autodetect-prompt.txt");
        // Log detected form field names
        try
        {
            var jsonPath = Path.Combine(rootPath, "wwwroot", jsonAttachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()), jsonAttachment.HashedFileName);
            if (File.Exists(jsonPath))
            {
                var jsonContent = await File.ReadAllTextAsync(jsonPath);
                var dict = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(jsonContent);
                if (dict != null)
                {
                    Console.WriteLine("===== Detected Form Field Names =====");
                    foreach (var key in dict.Keys)
                    {
                        Console.WriteLine(key);
                    }
                    Console.WriteLine("====================================");
                }
            }
        }
        catch (Exception logEx)
        {
            Console.WriteLine($"Failed to log field names: {logEx.Message}");
        }
        firebarProgress = 70;
        firebarStatus = "Filling target PDF and attaching...";
        StateHasChanged();

        // 3) Fill the uploaded target PDF with JSON values and attach filled copy
        await FillTargetPdfAndAttachAsync(progressCallback: p => { firebarProgress = 70 + p * 0.3; StateHasChanged(); });

        firebarProgress = 100;
        firebarStatus = "Done!";
        StateHasChanged();
        await Task.Delay(1200);
        showFirebar = false;
        firebarStatus = string.Empty;
        firebarProgress = 0;
        StateHasChanged();
    }

    private async Task FillTargetPdfAndAttachAsync(Action<double>? progressCallback = null)
    {
        try
        {
            progressCallback?.Invoke(10);
            if (TargetAttachment == null || supplementalGroupId == null)
            {
                ExtractStatusMessage = "Missing target PDF or attachment group.";
                await InvokeAsync(StateHasChanged);
                return;
            }

            // Locate latest JSON produced in autodetect flow
            var attachments = await AttachmentService.GetAttachmentsByGroupIdAsync(supplementalGroupId.Value);
            var jsonAttachment = attachments?.Where(a => a.FileFormat?.ToLower() == ".json")
                                            .OrderByDescending(a => a.DateCreated)
                                            .FirstOrDefault();
            if (jsonAttachment == null)
            {
                ExtractStatusMessage = "No JSON data found to fill the PDF.";
                await InvokeAsync(StateHasChanged);
                return;
            }

            // Read JSON into dictionary
            var rootPath = Directory.GetCurrentDirectory();
            var jsonPath = Path.Combine(rootPath, "wwwroot", jsonAttachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()), jsonAttachment.HashedFileName);
            var jsonContent = await File.ReadAllTextAsync(jsonPath);
            var dict = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(jsonContent);
            if (dict == null)
            {
                ExtractStatusMessage = "Unable to parse JSON content.";
                await InvokeAsync(StateHasChanged);
                return;
            }

            // Open the uploaded target PDF
            var pdfPath = Path.Combine(rootPath, "wwwroot", TargetAttachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()), TargetAttachment.HashedFileName);
            using var pdfStream = new FileStream(pdfPath, FileMode.Open, FileAccess.Read);
            using var loadedDoc = new Syncfusion.Pdf.Parsing.PdfLoadedDocument(pdfStream);
            var form = loadedDoc.Form;
            foreach (Syncfusion.Pdf.Parsing.PdfLoadedField field in form.Fields)
            {
                if (dict.TryGetValue(field.Name, out var value))
                {
                    if (field is Syncfusion.Pdf.Parsing.PdfLoadedTextBoxField textField)
                    {
                        textField.Text = value?.ToString() ?? string.Empty;
                    }
                    else if (field is Syncfusion.Pdf.Parsing.PdfLoadedCheckBoxField checkBoxField)
                    {
                        var strVal = value?.ToString()?.Trim().ToLowerInvariant();
                        checkBoxField.Checked = strVal == "true" || strVal == "yes" || strVal == "on" || strVal == "1";
                    }
                }
            }
            progressCallback?.Invoke(60);

            // Save new filled pdf copy next to target file
            var uploadsDir = Path.GetDirectoryName(pdfPath);
            var outputFileName = $"filled_{Path.GetFileNameWithoutExtension(TargetAttachment.OriginalFileName)}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
            var hash = Surefire.Domain.Shared.Helpers.StringHelper.GenerateFiveCharacterHash(outputFileName);
            var hashedFileName = $"{Path.GetFileNameWithoutExtension(outputFileName)}_{hash}.pdf";
            var outputPath = Path.Combine(uploadsDir, hashedFileName);
            using (var outStream = new FileStream(outputPath, FileMode.Create, FileAccess.Write))
            {
                loadedDoc.Save(outStream);
            }

            // Generate thumbnail via private method on AttachmentService
            var thumbnailPath = Path.Combine(uploadsDir, Path.GetFileNameWithoutExtension(hashedFileName) + "_thumb.jpg");
            AttachmentService.GetType().GetMethod("GeneratePdfThumbnail", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                             ?.Invoke(AttachmentService, new object[] { outputPath, thumbnailPath });

            // Create attachment record for filled PDF
            var fileInfo = new System.IO.FileInfo(outputPath);
            var attachment = new Attachment
            {
                OriginalFileName = outputFileName,
                HashedFileName = hashedFileName,
                LocalPath = TargetAttachment.LocalPath, // same directory
                FileFormat = ".pdf",
                FileSize = fileInfo.Length,
                DateCreated = DateTime.UtcNow,
                AttachmentGroupId = supplementalGroupId,
                ClientId = ClientId,
                Description = "Filled target PDF (Autodetect)",
                IsSupplemental = true
            };
            await AttachmentService.SaveAttachmentDirectlyAsync(attachment);
            progressCallback?.Invoke(100);
            await LoadSupplementalAttachment();
        }
        catch (Exception ex)
        {
            ExtractStatusMessage = $"Error filling target PDF: {ex.Message}";
        }
        await InvokeAsync(StateHasChanged);
    }

    public void ToggleAdvancedMode()
    {
        advancedMode = !advancedMode;
    }
    // (1) Upload                                                    --|
    public async void OnUploadSlotSuccess(ActionCompleteEventArgs args, string slotType)
    {
        if (slotType == "supplemental")
        {
            var file = args.FileData.FirstOrDefault();
            if (file != null)
            {
                var newAttachment = new Attachment
                {
                    OriginalFileName = file.Name,
                    FileFormat = System.IO.Path.GetExtension(file.Name),
                    FileSize = file.Size,
                    DateCreated = DateTime.Now,
                    AttachmentGroupId = null,
                    IsSupplemental = true,
                    Description = file.Name,
                    ClientId = ClientId,
                };
                var groupId = await AttachmentService.EnsureSupplementalAttachmentGroupAsync(ClientId);
                newAttachment.AttachmentGroupId = groupId;
                await AttachmentService.SaveDropZoneAttachmentAsync(newAttachment);
                await LoadSupplementalAttachment();
                StateHasChanged();
            }
        }
        if (slotType == "target")
        {
            var file = args.FileData.FirstOrDefault();
            if (file != null)
            {
                var newAttachment = new Attachment
                {
                    OriginalFileName = file.Name,
                    FileFormat = System.IO.Path.GetExtension(file.Name),
                    FileSize = file.Size,
                    DateCreated = DateTime.Now,
                    AttachmentGroupId = null,
                    IsSupplemental = false,
                    Description = "Target Supplemental Form (Autodetect)",
                    ClientId = ClientId,
                };
                // Re-use the supplemental group so all related files stay together
                var groupId = await AttachmentService.EnsureSupplementalAttachmentGroupAsync(ClientId);
                newAttachment.AttachmentGroupId = groupId;
                await AttachmentService.SaveDropZoneAttachmentAsync(newAttachment);

                TargetAttachment = newAttachment; // keep reference for later workflow steps
                supplementalGroupId = groupId;   // make sure later methods can still find the group
                StateHasChanged();
            }
        }
    }
    public async void OnDeleteAttachmentClicked(Attachment? attachment)
    {
        if (attachment != null)
        {
            await AttachmentService.DeleteAttachmentAsync(attachment.AttachmentId);
            await LoadSupplementalAttachment();
            StateHasChanged();
        }
    }
    public async Task OnChange(UploadChangeEventArgs args)
    {

        try
        {
            foreach (var file in args.Files)
            {
                var path = $"wwwroot/uploads/temp/{file.FileInfo.Name}";
                var fileName = file.FileInfo.Name;
                FileStream filestream = new FileStream(path, FileMode.Create, FileAccess.Write);
                await file.File.OpenReadStream(long.MaxValue).CopyToAsync(filestream);
                filestream.Close();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error during file upload: {ex.Message}");
        }
    }
    public void OnUploadFailure(FailureEventArgs args)
    {
        Console.WriteLine("File upload failed: " + args.File.Name);
    }
    // (2) Extract Text                                              --|
    public (string pdfFile, string fieldsFile, string promptFile) GetFormFiles(string form)
    {
        const string promptFile = "supp-prompt.txt";
        switch (form)
        {
            case "Kinsale":
                return ("supp-contractors-kinsale.pdf", "supp-contractors-kinsale.txt", promptFile);
            case "Amwins":
                return ("supp-contractors-amwins.pdf", "supp-contractors-amwins.txt", promptFile);
            case "CS":
            default:
                return ("supp-contractors-cs.pdf", "supp-contractors-cs.txt", promptFile);
        }
    }
    public async Task ExtractSupplementalFormTextAsync(string pdfFile, Action<double>? progressCallback = null)
    {
        if (SupplementalAttachment == null)
        {
            ExtractStatusMessage = "No supplemental attachment to process.";
            await InvokeAsync(StateHasChanged);
            return;
        }
        try
        {
            ExtractStatusMessage = "Extracting text from supplemental PDF...";
            ExtractedTextAttachmentUrl = string.Empty;
            progressCallback?.Invoke(5);
            await InvokeAsync(StateHasChanged);
            // Use the selected PDF file for extraction if needed (future-proofing)
            var txtAttachment = await LLMWhispererService.ProcessPdfAttachmentAsync(SupplementalAttachment, "form", supplementalGroupId);
            progressCallback?.Invoke(15);
            if (txtAttachment != null)
            {
                ExtractStatusMessage = "Extraction complete.";
                ExtractedTextAttachmentUrl = $"/{txtAttachment.LocalPath}/{txtAttachment.HashedFileName}";
                progressCallback?.Invoke(20);
            }
            else
            {
                ExtractStatusMessage = "Extraction failed: No attachment returned.";
            }
        }
        catch (Exception ex)
        {
            ExtractStatusMessage = $"Extraction failed: {ex.Message}";
        }
        await InvokeAsync(StateHasChanged);
    }
    // (3) Fill Schema                                               --|
    public async Task ExtractSupplementalFieldsFromTextAndSaveAsync(string fieldsFile, string promptFile, Action<double>? progressCallback = null)
    {
        if (supplementalGroupId == null)
        {
            IsExtractingFields = false;
            ExtractStatusMessage = "No supplemental group found.";
            await InvokeAsync(StateHasChanged);
            return;
        }
        var attachments = await AttachmentService.GetAttachmentsByGroupIdAsync(supplementalGroupId.Value);
        var txtAttachment = attachments?
            .Where(a => a.FileFormat?.ToLower() == ".txt")
            .OrderByDescending(a => a.DateCreated)
            .FirstOrDefault();
        if (txtAttachment == null)
        {
            IsExtractingFields = false;
            ExtractStatusMessage = "No extracted text file found. Run ExtractFormText first.";
            await InvokeAsync(StateHasChanged);
            return;
        }
        try
        {
            IsExtractingFields = true;
            ExtractStatusMessage = "Extracting fields from supplemental text...";
            progressCallback?.Invoke(10);
            await InvokeAsync(StateHasChanged);
            // Pass the fields and prompt file to OpenAIPro (update OpenAIPro if needed)
            var jsonAttachment = await OpenAIPro.ExtractSupplementalFieldsFromTextAsync(txtAttachment, supplementalGroupId.Value, ClientId, 0, fieldsFile, promptFile);
            progressCallback?.Invoke(80);
            if (jsonAttachment != null)
            {
                ExtractStatusMessage = $"Field extraction complete. <a href='/{jsonAttachment.LocalPath}/{jsonAttachment.HashedFileName}' target='_blank'>View Extracted JSON</a>";
                progressCallback?.Invoke(100);
            }
            else
            {
                ExtractStatusMessage = "Field extraction failed: No attachment returned.";
            }
        }
        catch (Exception ex)
        {
            ExtractStatusMessage = $"Field extraction failed: {ex.Message}";
        }
        finally
        {
            IsExtractingFields = false;
            await InvokeAsync(StateHasChanged);
        }
    }
    // (4) Work the Data                                             --|
    public async Task FillSupplementalPdfAndAttachAsync(string pdfFile, Action<double>? progressCallback = null)
    {
        try
        {
            ExtractStatusMessage = "Filling PDF from JSON...";
            progressCallback?.Invoke(10);
            await InvokeAsync(StateHasChanged);
            if (supplementalGroupId == null)
            {
                ExtractStatusMessage = "No supplemental group found.";
                await InvokeAsync(StateHasChanged);
                return;
            }
            // Find the most recent .json attachment
            var attachments = await AttachmentService.GetAttachmentsByGroupIdAsync(supplementalGroupId.Value);
            progressCallback?.Invoke(20);
            var jsonAttachment = attachments?
                .Where(a => a.FileFormat?.ToLower() == ".json")
                .OrderByDescending(a => a.DateCreated)
                .FirstOrDefault();
            if (jsonAttachment == null)
            {
                ExtractStatusMessage = "No cleaned JSON attachment found.";
                await InvokeAsync(StateHasChanged);
                return;
            }
            // Load JSON content
            var rootPath = Directory.GetCurrentDirectory();
            var jsonPath = Path.Combine(rootPath, "wwwroot", jsonAttachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()), jsonAttachment.HashedFileName);
            if (!File.Exists(jsonPath))
            {
                ExtractStatusMessage = $"JSON file not found: {jsonPath}";
                await InvokeAsync(StateHasChanged);
                return;
            }
            var jsonContent = await File.ReadAllTextAsync(jsonPath);
            progressCallback?.Invoke(40);
            var dict = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(jsonContent);
            if (dict == null)
            {
                ExtractStatusMessage = "Failed to parse JSON.";
                await InvokeAsync(StateHasChanged);
                return;
            }
            // Load the PDF template dynamically
            var pdfTemplatePath = Path.Combine(rootPath, "wwwroot", "forms", pdfFile);
            if (!File.Exists(pdfTemplatePath))
            {
                ExtractStatusMessage = $"PDF template not found: {pdfTemplatePath}";
                await InvokeAsync(StateHasChanged);
                return;
            }
            using var pdfStream = new FileStream(pdfTemplatePath, FileMode.Open, FileAccess.Read);
            using var loadedDoc = new Syncfusion.Pdf.Parsing.PdfLoadedDocument(pdfStream);
            var form = loadedDoc.Form;
            // Fill text fields and checkboxes
            foreach (Syncfusion.Pdf.Parsing.PdfLoadedField field in form.Fields)
            {
                if (dict.TryGetValue(field.Name, out var value))
                {
                    if (field is Syncfusion.Pdf.Parsing.PdfLoadedTextBoxField textField)
                    {
                        textField.Text = value?.ToString() ?? string.Empty;
                    }
                    else if (field is Syncfusion.Pdf.Parsing.PdfLoadedCheckBoxField checkBoxField)
                    {
                        var strVal = value?.ToString()?.Trim().ToLowerInvariant();
                        checkBoxField.Checked = strVal == "true" || strVal == "yes" || strVal == "on" || strVal == "1";
                    }
                }
            }
            progressCallback?.Invoke(70);
            // Save the filled PDF to the correct uploads folder
            var uploadsDir = Path.Combine(rootPath, "wwwroot", "uploads", "clients", ClientId.ToString(), supplementalGroupId.Value.ToString());
            if (!Directory.Exists(uploadsDir))
                Directory.CreateDirectory(uploadsDir);
            var outputFileName = $"{Path.GetFileNameWithoutExtension(pdfFile)}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
            var hash = Surefire.Domain.Shared.Helpers.StringHelper.GenerateFiveCharacterHash(outputFileName);
            var hashedFileName = $"{Path.GetFileNameWithoutExtension(outputFileName)}_{hash}.pdf";
            var outputPath = Path.Combine(uploadsDir, hashedFileName);
            using (var outStream = new FileStream(outputPath, FileMode.Create, FileAccess.Write))
            {
                loadedDoc.Save(outStream);
            }
            // Generate thumbnail for the filled PDF
            var thumbnailPath = Path.Combine(uploadsDir, Path.GetFileNameWithoutExtension(hashedFileName) + "_thumb.jpg");
            AttachmentService.GetType().GetMethod("GeneratePdfThumbnail", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.Invoke(AttachmentService, new object[] { outputPath, thumbnailPath });
            // Create and save the attachment
            var fileInfo = new System.IO.FileInfo(outputPath);
            var attachment = new Attachment
            {
                OriginalFileName = outputFileName,
                HashedFileName = hashedFileName,
                LocalPath = $"uploads/clients/{ClientId}/{supplementalGroupId}",
                FileFormat = ".pdf",
                FileSize = fileInfo.Length,
                DateCreated = DateTime.UtcNow,
                AttachmentGroupId = supplementalGroupId,
                ClientId = ClientId,
                Description = "Filled supplemental contractors PDF",
                IsSupplemental = true
            };
            await AttachmentService.SaveAttachmentDirectlyAsync(attachment);
            ExtractStatusMessage = "Filled PDF saved and attached successfully.";
            progressCallback?.Invoke(100);
            await LoadSupplementalAttachment();
        }
        catch (Exception ex)
        {
            ExtractStatusMessage = $"Error filling PDF: {ex.Message}";
        }
        await InvokeAsync(StateHasChanged);
    }
    public async Task PopulateDetails(Dictionary<string, object> dict)
    {
        if (BusinessDetails == null || dict == null)
            return;

        // YearsOfExperience -> YearsExperience (int?)
        if ((!BusinessDetails.YearsExperience.HasValue || BusinessDetails.YearsExperience == 0) && dict.TryGetValue("YearsOfExperience", out var yearsExpObj) && int.TryParse(yearsExpObj?.ToString(), out var yearsExp))
            BusinessDetails.YearsExperience = yearsExp;

        // ContractorsLicenseNumber -> LicenseNumber (string)
        if (string.IsNullOrWhiteSpace(BusinessDetails.LicenseNumber) && dict.TryGetValue("ContractorsLicenseNumber", out var licObj))
            BusinessDetails.LicenseNumber = licObj?.ToString();

        // DescriptionOfOperations -> ShortDescription (string)
        if (string.IsNullOrWhiteSpace(BusinessDetails.ShortDescription) && dict.TryGetValue("DescriptionOfOperations", out var descOpObj))
            BusinessDetails.ShortDescription = descOpObj?.ToString();

        // DescriptionOfOperations -> LongDescription (string)
        if (string.IsNullOrWhiteSpace(BusinessDetails.LongDescription) && dict.TryGetValue("DescriptionOfOperations", out var descOpObj2))
            BusinessDetails.LongDescription = descOpObj2?.ToString();

        // DirectPayroll_NextYearProjected -> EstimatedAnnualPayroll0 (decimal?)
        if ((!BusinessDetails.EstimatedAnnualPayroll0.HasValue || BusinessDetails.EstimatedAnnualPayroll0 == 0) && dict.TryGetValue("DirectPayroll_NextYearProjected", out var payroll0Obj) && decimal.TryParse(payroll0Obj?.ToString(), out var payroll0))
            BusinessDetails.EstimatedAnnualPayroll0 = payroll0;

        // DirectPayroll_CurrentYear -> EstimatedAnnualPayroll1 (decimal?)
        if ((!BusinessDetails.EstimatedAnnualPayroll1.HasValue || BusinessDetails.EstimatedAnnualPayroll1 == 0) && dict.TryGetValue("DirectPayroll_CurrentYear", out var payroll1Obj) && decimal.TryParse(payroll1Obj?.ToString(), out var payroll1))
            BusinessDetails.EstimatedAnnualPayroll1 = payroll1;

        // DirectPayroll_2ndYearPrior -> EstimatedAnnualPayroll2 (decimal?)
        if ((!BusinessDetails.EstimatedAnnualPayroll2.HasValue || BusinessDetails.EstimatedAnnualPayroll2 == 0) && dict.TryGetValue("DirectPayroll_2ndYearPrior", out var payroll2Obj) && decimal.TryParse(payroll2Obj?.ToString(), out var payroll2))
            BusinessDetails.EstimatedAnnualPayroll2 = payroll2;

        // DirectPayroll_3rdYearPrior -> EstimatedAnnualPayroll3 (decimal?)
        if ((!BusinessDetails.EstimatedAnnualPayroll3.HasValue || BusinessDetails.EstimatedAnnualPayroll3 == 0) && dict.TryGetValue("DirectPayroll_3rdYearPrior", out var payroll3Obj) && decimal.TryParse(payroll3Obj?.ToString(), out var payroll3))
            BusinessDetails.EstimatedAnnualPayroll3 = payroll3;

        // DirectPayroll_4thYearPrior -> EstimatedAnnualPayroll4 (decimal?)
        if ((!BusinessDetails.EstimatedAnnualPayroll4.HasValue || BusinessDetails.EstimatedAnnualPayroll4 == 0) && dict.TryGetValue("DirectPayroll_4thYearPrior", out var payroll4Obj) && decimal.TryParse(payroll4Obj?.ToString(), out var payroll4))
            BusinessDetails.EstimatedAnnualPayroll4 = payroll4;

        // GrossReciepts_NextYearProjected -> EstimatedGrossSales0 (decimal?)
        if ((!BusinessDetails.EstimatedGrossSales0.HasValue || BusinessDetails.EstimatedGrossSales0 == 0) && dict.TryGetValue("GrossReciepts_NextYearProjected", out var gross0Obj) && decimal.TryParse(gross0Obj?.ToString(), out var gross0))
            BusinessDetails.EstimatedGrossSales0 = gross0;

        // GrossReciepts_CurrentYear -> AnnualGrossSalesRevenueReceipts (decimal?)
        if ((!BusinessDetails.AnnualGrossSalesRevenueReceipts.HasValue || BusinessDetails.AnnualGrossSalesRevenueReceipts == 0) && dict.TryGetValue("GrossReciepts_CurrentYear", out var grossCurObj) && decimal.TryParse(grossCurObj?.ToString(), out var grossCur))
            BusinessDetails.AnnualGrossSalesRevenueReceipts = grossCur;

        // GrossReciepts_CurrentYear -> EstimatedGrossSales1 (decimal?)
        if ((!BusinessDetails.EstimatedGrossSales1.HasValue || BusinessDetails.EstimatedGrossSales1 == 0) && dict.TryGetValue("GrossReciepts_CurrentYear", out var gross1Obj) && decimal.TryParse(gross1Obj?.ToString(), out var gross1))
            BusinessDetails.EstimatedGrossSales1 = gross1;

        // GrossReciepts_2ndYearPrior -> EstimatedGrossSales2 (decimal?)
        if ((!BusinessDetails.EstimatedGrossSales2.HasValue || BusinessDetails.EstimatedGrossSales2 == 0) && dict.TryGetValue("GrossReciepts_2ndYearPrior", out var gross2Obj) && decimal.TryParse(gross2Obj?.ToString(), out var gross2))
            BusinessDetails.EstimatedGrossSales2 = gross2;

        // GrossReciepts_3rdYearPrior -> EstimatedGrossSales3 (decimal?)
        if ((!BusinessDetails.EstimatedGrossSales3.HasValue || BusinessDetails.EstimatedGrossSales3 == 0) && dict.TryGetValue("GrossReciepts_3rdYearPrior", out var gross3Obj) && decimal.TryParse(gross3Obj?.ToString(), out var gross3))
            BusinessDetails.EstimatedGrossSales3 = gross3;

        // GrossReciepts_4thYearPrior -> EstimatedGrossSales4 (decimal?)
        if ((!BusinessDetails.EstimatedGrossSales4.HasValue || BusinessDetails.EstimatedGrossSales4 == 0) && dict.TryGetValue("GrossReciepts_4thYearPrior", out var gross4Obj) && decimal.TryParse(gross4Obj?.ToString(), out var gross4))
            BusinessDetails.EstimatedGrossSales4 = gross4;

        // SubcontractorCosts_NextYearProjected -> EstimatedSubcontractingExpenses (decimal?)
        if ((!BusinessDetails.EstimatedSubcontractingExpenses.HasValue || BusinessDetails.EstimatedSubcontractingExpenses == 0) && dict.TryGetValue("SubcontractorCosts_NextYearProjected", out var subObj) && decimal.TryParse(subObj?.ToString(), out var sub))
            BusinessDetails.EstimatedSubcontractingExpenses = sub;

        // NumFullTimeEmployees -> NumFullTimeEmployees (int?)
        if ((!BusinessDetails.NumFullTimeEmployees.HasValue || BusinessDetails.NumFullTimeEmployees == 0) && dict.TryGetValue("NumFullTimeEmployees", out var empObj) && int.TryParse(empObj?.ToString(), out var emp))
            BusinessDetails.NumFullTimeEmployees = emp;

        // PercentageOfWork_Commercial -> PercentCommercial (string)
        if (string.IsNullOrWhiteSpace(BusinessDetails.PercentCommercial) && dict.TryGetValue("PercentageOfWork_Commercial", out var pcObj))
            BusinessDetails.PercentCommercial = pcObj?.ToString();

        // PercentageOfWork_Residential -> PercentResidential (string)
        if (string.IsNullOrWhiteSpace(BusinessDetails.PercentResidential) && dict.TryGetValue("PercentageOfWork_Residential", out var prObj))
            BusinessDetails.PercentResidential = prObj?.ToString();

        // PercentageOfWork_PublicWorksOrGovernment -> PercentPublic (string)
        if (string.IsNullOrWhiteSpace(BusinessDetails.PercentPublic) && dict.TryGetValue("PercentageOfWork_PublicWorksOrGovernment", out var ppObj))
            BusinessDetails.PercentPublic = ppObj?.ToString();

        // PercentageOfWork_NewConstruction -> PercentNewConstruction (string)
        if (string.IsNullOrWhiteSpace(BusinessDetails.PercentNewConstruction) && dict.TryGetValue("PercentageOfWork_NewConstruction", out var pncObj))
            BusinessDetails.PercentNewConstruction = pncObj?.ToString();

        // PercentageOfWork_RemodelOrRepair -> PercentRemodelRepair (string)
        if (string.IsNullOrWhiteSpace(BusinessDetails.PercentRemodelRepair) && dict.TryGetValue("PercentageOfWork_RemodelOrRepair", out var prrObj))
            BusinessDetails.PercentRemodelRepair = prrObj?.ToString();

        // PercentageOfWork_Interior -> PercentInterior (string)
        if (string.IsNullOrWhiteSpace(BusinessDetails.PercentInterior) && dict.TryGetValue("PercentageOfWork_Interior", out var piObj))
            BusinessDetails.PercentInterior = piObj?.ToString();

        // PercentageOfWork_Exterior -> PercentExterior (string)
        if (string.IsNullOrWhiteSpace(BusinessDetails.PercentExterior) && dict.TryGetValue("PercentageOfWork_Exterior", out var peObj))
            BusinessDetails.PercentExterior = peObj?.ToString();

        await SaveChanges();
    }
    public async Task PopulateDetailsFromLatestJsonAsync()
    {
        try
        {
            ExtractStatusMessage = "Populating details from JSON...";
            await InvokeAsync(StateHasChanged);
            if (supplementalGroupId == null)
            {
                ExtractStatusMessage = "No supplemental group found.";
                await InvokeAsync(StateHasChanged);
                return;
            }
            // Find the most recent .json attachment
            var attachments = await AttachmentService.GetAttachmentsByGroupIdAsync(supplementalGroupId.Value);
            var jsonAttachment = attachments?
                .Where(a => a.FileFormat?.ToLower() == ".json")
                .OrderByDescending(a => a.DateCreated)
                .FirstOrDefault();
            if (jsonAttachment == null)
            {
                ExtractStatusMessage = "No cleaned JSON attachment found.";
                await InvokeAsync(StateHasChanged);
                return;
            }
            // Load JSON content
            var rootPath = Directory.GetCurrentDirectory();
            var jsonPath = Path.Combine(rootPath, "wwwroot", jsonAttachment.LocalPath.Replace("/", Path.DirectorySeparatorChar.ToString()), jsonAttachment.HashedFileName);
            if (!File.Exists(jsonPath))
            {
                ExtractStatusMessage = $"JSON file not found: {jsonPath}";
                await InvokeAsync(StateHasChanged);
                return;
            }
            var jsonContent = await File.ReadAllTextAsync(jsonPath);
            var dict = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(jsonContent);
            if (dict == null)
            {
                ExtractStatusMessage = "Failed to parse JSON.";
                await InvokeAsync(StateHasChanged);
                return;
            }
            await PopulateDetails(dict);
            ExtractStatusMessage = "Business details populated from JSON.";
        }
        catch (Exception ex)
        {
            ExtractStatusMessage = $"Error populating details: {ex.Message}";
        }
        await InvokeAsync(StateHasChanged);
    }

    //                                                                 |
    // MISC STUFF -----------------------------------------------------|
    //                                                                 |
    private void LoadEnumData()
    {
        LegalEntityTypeList = Enum.GetValues(typeof(LegalEntityType)).Cast<LegalEntityType>().Select(e => new LocalEnumData<LegalEntityType> { Value = e, Text = e.ToString().Replace("_", " ") });
        BusinessTypeList = Enum.GetValues(typeof(BusinessType)).Cast<BusinessType>().Select(e => new LocalEnumData<BusinessType> { Value = e, Text = e.ToString().Replace("_", " ") });
        LicenseTypeList = Enum.GetValues(typeof(LicenseType)).Cast<LicenseType>().Select(e => new LocalEnumData<LicenseType> { Value = e, Text = e.ToString().Replace("_", " ") });
    }
    public class LocalEnumData<T> where T : struct, Enum
    {
        public T Value { get; set; }
        public string Text { get; set; } = string.Empty;
    }
    public void Dispose()
    {
        CurrentJsonDocument?.Dispose();
        EmberService?.DisposeAsync();
    }
}