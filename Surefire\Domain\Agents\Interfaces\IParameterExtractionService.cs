using Surefire.Domain.Agents.Models;

namespace Surefire.Domain.Agents.Interfaces
{
    /// <summary>
    /// Service for extracting and validating parameters from user input
    /// </summary>
    public interface IParameterExtractionService
    {
        /// <summary>
        /// Extract parameters from user input using AI analysis
        /// </summary>
        /// <param name="userInput">The user's natural language input</param>
        /// <param name="parameterDefinitions">Definitions of parameters to extract</param>
        /// <param name="context">Additional context that might help with extraction</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Validation result with extracted parameters</returns>
        Task<AgentParameterValidation> ExtractParametersAsync(
            string userInput, 
            List<AgentParameterDefinition> parameterDefinitions,
            Dictionary<string, object> context = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Validate provided parameters against definitions
        /// </summary>
        /// <param name="parameters">Parameters to validate</param>
        /// <param name="parameterDefinitions">Parameter definitions to validate against</param>
        /// <returns>Validation result</returns>
        Task<AgentParameterValidation> ValidateParametersAsync(
            Dictionary<string, object> parameters,
            List<AgentParameterDefinition> parameterDefinitions);

        /// <summary>
        /// Generate clarification questions for missing or ambiguous parameters
        /// </summary>
        /// <param name="validation">Current validation state</param>
        /// <param name="userInput">Original user input for context</param>
        /// <returns>Human-friendly clarification message</returns>
        Task<string> GenerateClarificationMessageAsync(
            AgentParameterValidation validation,
            string userInput);

        /// <summary>
        /// Attempt to resolve ambiguous parameters using additional context
        /// </summary>
        /// <param name="parameterName">Name of the ambiguous parameter</param>
        /// <param name="userInput">User's input</param>
        /// <param name="possibleValues">List of possible values</param>
        /// <param name="context">Additional context</param>
        /// <returns>Best guess value and confidence score</returns>
        Task<(string? Value, double Confidence)> ResolveAmbiguousParameterAsync(
            string parameterName,
            string userInput,
            List<string> possibleValues,
            Dictionary<string, object> context = null);
    }

    /// <summary>
    /// Service for entity extraction using embeddings and vectorized search
    /// This is a placeholder for the future embedding-based entity extraction system
    /// </summary>
    public interface IEntityExtractionService
    {
        /// <summary>
        /// Extract client names from user input
        /// PLACEHOLDER: This will be replaced with the embedding-based extraction system
        /// </summary>
        /// <param name="userInput">User's input text</param>
        /// <param name="topResults">Maximum number of results</param>
        /// <returns>List of matching client names with confidence scores</returns>
        Task<List<(string ClientName, double Confidence)>> ExtractClientNamesAsync(string userInput, int topResults = 5);

        /// <summary>
        /// Extract carrier names from user input
        /// PLACEHOLDER: This will be replaced with the embedding-based extraction system
        /// </summary>
        /// <param name="userInput">User's input text</param>
        /// <param name="topResults">Maximum number of results</param>
        /// <returns>List of matching carrier names with confidence scores</returns>
        Task<List<(string CarrierName, double Confidence)>> ExtractCarrierNamesAsync(string userInput, int topResults = 5);

        /// <summary>
        /// Extract policy types from user input
        /// PLACEHOLDER: This will be replaced with the embedding-based extraction system
        /// </summary>
        /// <param name="userInput">User's input text</param>
        /// <param name="topResults">Maximum number of results</param>
        /// <returns>List of matching policy types with confidence scores</returns>
        Task<List<(string PolicyType, double Confidence)>> ExtractPolicyTypesAsync(string userInput, int topResults = 5);

        /// <summary>
        /// Generic entity extraction for custom entity types
        /// PLACEHOLDER: This will be replaced with the embedding-based extraction system
        /// </summary>
        /// <param name="userInput">User's input text</param>
        /// <param name="entityType">Type of entity to extract</param>
        /// <param name="topResults">Maximum number of results</param>
        /// <returns>List of matching entities with confidence scores</returns>
        Task<List<(string Entity, double Confidence)>> ExtractEntitiesAsync(string userInput, string entityType, int topResults = 5);

        /// <summary>
        /// Check if entity extraction is available for a given entity type
        /// </summary>
        /// <param name="entityType">Entity type to check</param>
        /// <returns>True if extraction is supported</returns>
        bool SupportsEntityType(string entityType);
    }
} 