﻿using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using iText.Kernel.Pdf;
using Microsoft.EntityFrameworkCore;

namespace Surefire.Domain.Proposals
{
    public partial class ProposalService
    {
        /// <summary>
        /// Given the original PDF bytes and a list of selected pages (1-indexed),
        /// returns a new PDF containing only those pages.
        /// </summary>
        public byte[] ExtractSelectedPages(byte[] originalPdfBytes, int[] selectedPages)
        {
            using var inputStream = new MemoryStream(originalPdfBytes);
            using var reader = new PdfReader(inputStream);
            using var inputPdf = new PdfDocument(reader);
            using var outputStream = new MemoryStream();
            using var writer = new PdfWriter(outputStream);
            using var outputPdf = new PdfDocument(writer);

            int totalPages = inputPdf.GetNumberOfPages();
            foreach (int page in selectedPages)
            {
                if (page >= 1 && page <= totalPages)
                {
                    inputPdf.CopyPagesTo(page, page, outputPdf);
                }
                else
                {
                    throw new Exception($"Page number {page} is out of range. Total pages: {totalPages}");
                }
            }
            outputPdf.Close();
            return outputStream.ToArray();
        }

        /// <summary>
        /// Saves the extracted PDF to the proposal and updates the proposal's metadata.
        /// </summary>
        public async Task<bool> SaveExtractedPdfToProposal(int proposalId, byte[] pdfBytes, string fileName)
        {
            await _log.LogAsync(LogLevel.Information, $"Saving extracted PDF to proposal: {proposalId}", "ProposalService");
            
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                
                var proposal = await context.Proposals
                    .FirstOrDefaultAsync(p => p.ProposalId == proposalId);
                
                if (proposal == null)
                {
                    throw new Exception($"Proposal with ID {proposalId} not found");
                }
                
                // Generate a unique file name if one is not provided
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = $"proposal_{proposalId}_{DateTime.UtcNow:yyyyMMddHHmmss}.pdf";
                }
                
                string storagePath = Path.Combine("Proposals", fileName);
                
                // Get the current user ID for the modified by field
                var userId = _userManager.GetUserId(_httpContextAccessor.HttpContext.User);
                if (!string.IsNullOrEmpty(userId))
                {
                    proposal.ModifiedById = userId;
                }
                
                // Save the changes to the database
                await context.SaveChangesAsync();
                
                await _log.LogAsync(LogLevel.Information, $"Successfully saved PDF to proposal: {proposalId}", "ProposalService");
                
                return true;
            }
            catch (Exception ex)
            {
                await _log.LogAsync(LogLevel.Error, $"Error saving PDF to proposal {proposalId}: {ex.Message}", "ProposalService");
                throw;
            }
        }

        /// <summary>
        /// Updates a proposal with an attachment ID.
        /// </summary>
        public async Task<bool> UpdateProposalAttachmentAsync(int proposalId, int attachmentId)
        {
            await _log.LogAsync(LogLevel.Information, $"Updating proposal {proposalId} with attachment {attachmentId}", "ProposalService");
            
            try
            {
                using var context = await _dbContextFactory.CreateDbContextAsync();
                
                var proposal = await context.Proposals
                    .FirstOrDefaultAsync(p => p.ProposalId == proposalId);
                
                if (proposal == null)
                {
                    throw new Exception($"Proposal with ID {proposalId} not found");
                }
                
                // Update the proposal with the attachment ID
                proposal.AttachmentId = attachmentId;
                proposal.DateModified = DateTime.UtcNow;
                
                // Get the current user ID for the modified by field
                var userId = _userManager.GetUserId(_httpContextAccessor.HttpContext.User);
                if (!string.IsNullOrEmpty(userId))
                {
                    proposal.ModifiedById = userId;
                }
                
                // Save the changes to the database
                await context.SaveChangesAsync();
                
                await _log.LogAsync(LogLevel.Information, $"Successfully updated proposal {proposalId} with attachment {attachmentId}", "ProposalService");
                
                return true;
            }
            catch (Exception ex)
            {
                await _log.LogAsync(LogLevel.Error, $"Error updating proposal {proposalId} with attachment {attachmentId}: {ex.Message}", "ProposalService");
                throw;
            }
        }

        /// <summary>
        /// Extracts specified pages from a PDF and saves them as filedataforai.pdf in the same folder.
        /// </summary>
        /// <param name="originalPdfPath">Path to the original PDF file</param>
        /// <param name="pageNumbers">Comma-delimited list of page numbers to extract (1-indexed)</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> ExtractPages(string originalPdfPath, string pageNumbers)
        {
            try
            {
                await _log.LogAsync(LogLevel.Information, $"Starting page extraction from {originalPdfPath}", "ProposalService");

                // Parse the page numbers
                var selectedPages = pageNumbers.Split(',')
                    .Select(p => int.TryParse(p.Trim(), out int page) ? page : 0)
                    .Where(p => p > 0)
                    .ToArray();

                if (selectedPages.Length == 0)
                {
                    throw new Exception("No valid page numbers provided");
                }

                // Read the original PDF
                byte[] originalPdfBytes = await File.ReadAllBytesAsync(originalPdfPath);

                // Extract the selected pages
                byte[] extractedPdfBytes = ExtractSelectedPages(originalPdfBytes, selectedPages);

                // Get the directory of the original file
                string directory = Path.GetDirectoryName(originalPdfPath);
                string outputPath = Path.Combine(directory, "filedataforai.pdf");

                // Save the extracted pages
                await File.WriteAllBytesAsync(outputPath, extractedPdfBytes);

                await _log.LogAsync(LogLevel.Information, $"Successfully extracted pages to {outputPath}", "ProposalService");
                return true;
            }
            catch (Exception ex)
            {
                await _log.LogAsync(LogLevel.Error, $"Error extracting pages: {ex.Message}", "ProposalService", ex);
                return false;
            }
        }
    }
}
