﻿
.page-content-client {
    display: flex;
    flex-direction: row;
}

.tinyexpander {
    position: absolute;
    top: 0px;
    left: 0px;
    opacity: .4;
    transition: all .4s ease;
}

    .tinyexpander:hover {
        opacity: .8;
        cursor: pointer;
        left: -3px;
    }

.sf-collapse {
    width: 15px;
    background-color: #e2e2e2;
    height: 100%;
    float: right;
}

.client-main {
    background-color: #fff;
    height: auto;
    position: relative; /* This allows absolute positioning of child elements */
}

.btn-notestog {
    position: absolute;
    top: 0px;
    right: 0px;
    z-index: 100;
    cursor: pointer;
    opacity: 0.4;
    transition: all 0.4s ease;
}

.btn-notestog:hover {
    opacity: 1;
    top:-3px;
}

h3 {
    font-family: "montserrat", sans-serif;
    font-weight: 600;
    font-style: normal;
    font-size: 14px !important;
    color: #8d8d8d;
    padding: 0 !important;
    position: relative;
    top: 3px;
}

h4 {
    font-family: "montserrat", sans-serif;
    font-weight: 400;
    font-style: normal;
    font-size: 14px !important;
    color: #8d8d8d;
    padding: 0 !important;
    position: relative;
    top: 3px;
}

:root .smallbutton {
    font-size: .6em !important;
    width: 50px;
}

.policy-number {
    font-size: .75em;
    color: #ccc;
}

.policy-price {
    font-family: "montserrat", sans-serif;
    font-size: .9em;
    font-style: italic;
    color: #bebebe;
}

.policy-details {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
}

.past-policy {
    display: flex;
    justify-content: space-between;
    font-size: 10px;
    flex-direction: row;
    width: 100%;
    padding-right: 17px;
    padding: 0px;
    padding-left: 7px;
    margin-bottom: 7px;
    border-left: 5px solid #e5e5e5;
    border-radius: 5px;
}

    .past-policy div {
        text-align: left;
    }

.txt-cursive-sm {
    position: relative;
    top: -6px;
}

.sf-toppad {
    margin-top: 25px;
    margin-bottom: 12px !important;
}

.past-policy-header {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
}

.past-policy-details {
    display: flex;
    justify-content: space-between;
    font-size: 10px;
    color: #696969;
}

.client-details-1 {
    font-family: "montserrat", sans-serif;
    font-weight: 200;
    font-style: normal;
    padding-bottom: 20px;
}



.client-top {
    font-family: "montserrat", sans-serif;
    background: linear-gradient(to bottom, rgba(249,249,249,1) 0%,rgba(234,234,234,1) 100%);
    padding-top: 2px;
    padding-bottom: 4px;
    padding-left: 15px;
    padding-right: 15px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}


.top-rowtwo {
    position: relative;
    top: -4px;
    color: #454545;
}





.client-header {
    font-family: "montserrat", sans-serif;
    font-weight: 800;
    font-style: normal;
    font-size: 2.5em;
    flex: 2;
    line-height: 30px;
}

    .client-header h1 {
        font-family: "montserrat", sans-serif;
        font-weight: 800;
        font-style: normal;
        font-size: 2.5em;
        margin-bottom: 20px;
    }

.client-header-2 {
    flex: 1;
}

.client-header-3 {
    flex: 1;
    line-height: 20px;
    text-align: right;
    align-content: center;
}

.sf-col h3 {
}



.client-email {
    font-family: "montserrat", sans-serif;
    font-weight: 400;
    font-style: italic;
    font-size: 1em;
}
/*main content*/
.sf-col-container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    background-color: #fff;
}

.contactcard {
    margin-left: 10px;
    font-size: 1rem;
}

.sf-col {
}

.tabcontents {
    padding: 10px 20px;
}

.tabcontentsnopad {
    padding: 5px;
}

.sf-col-1 {
    flex: 1.75;
    padding: 0px 20px 0px 0px;
    min-width: 500px;
}

.sf-col-2 {
    flex: 1.5;
    font-family: "montserrat", sans-serif;
    padding: 0px 10px 0px 20px;
}
.sf-gright-col {

}
.sf-col-3 {
    flex: 1.35;
    padding: 0px 10px 0px 20px;
    font-family: "montserrat", sans-serif;
    font-size: 12px;
}


.sf-selected {
    width: 100%;
    box-shadow: 0px 0px 6px #0000002b;
    position: relative;
    background-color: #fff;
    -webkit-border-top-left-radius: inherit;
    border-top-left-radius: 10px;
}
.client-vspacer {
    height: 30px;
}
.sf-header {
    font-family: "montserrat", sans-serif;
    display: flex;
    justify-content: normal;
    flex-direction: row;
    background: linear-gradient(to bottom, rgba(249,249,249,1) 0%,rgba(234,234,234,1) 100%);
    padding-top: 12px;
    padding-bottom: 8px;
    padding-left: 15px;
    padding-right: 15px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}
.client-logo-sf {
    max-height: 100px;
    /*max-width: 120px;
    max-height: 60px;
    width: auto;
    height: auto;
    object-fit: contain;
    border-radius: 6px;
    transition: all 0.3s ease;*/
}



.client-details {
    display: flex;
    justify-content: start;
    flex-direction: column;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    background-color: #fff;
    padding: 15px;
}

.sf-detail {
    width: 100%;
    padding: 20px;
}

.column:first-child {
    padding-right: 20px;
}

.column:nth-child(2) {
    padding-left: 10px;
    padding-right: 10px;
}

.column:last-child {
    padding-left: 20px;
}

.column {
    flex: 1;
    padding: 0;
}

    .column h3 {
        padding-bottom: 10px;
        margin-bottom: 10px;
    }

    .column p, .column ul {
        margin-bottom: 10px;
    }


.client-list {
    margin-top: 8px;
    width: 225px;
    overflow: hidden;
}

.client-list-item {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 3px 10px;
    white-space: nowrap;
    font-size: .9em;
    color: #6c6c6c;
    transition: all 0.1s ease-out;
}

    .client-list-item:hover {
        background-color: #ffffff;
        color: #272727;
        cursor: pointer;
    }

.syncme {
    transition: all 5s ease-out;
}

.syncmeTrue {
    display: block;
    opacity: 1;
}

.syncmeFalse {
    opacity: .2;
}

.ai-summary-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px;
    gap: 16px;
}

.ai-summary-content {
    height: 100%;
    overflow-y: auto;
}

    .ai-summary-content h3 {
        margin-top: 0;
        color: var(--neutral-foreground-rest);
    }

    .ai-summary-content h4 {
        margin-top: 16px;
        color: var(--neutral-foreground-rest);
    }

    .ai-summary-content ul {
        margin: 8px 0;
        padding-left: 24px;
    }

    .ai-summary-content li {
        margin: 4px 0;
    }

    .ai-summary-content strong {
        color: var(--neutral-foreground-rest);
        font-weight: bold;
    }

.sf-tab {
    overflow-y: scroll;
    height: calc(100vh - 300px);
}

.pol-tweak {
    position: relative;
    top:-65px;
    padding-right:2px;
}
.quickstats-col-1 {
    border-left: 1px solid #ccc;
    padding-left:10px;
    font-size:.85em;
    color: #000;
    font-family: 'Segoe UI';
}
.quickstats-col-1 b {
    text-transform: uppercase;
    color:#5a5a5a;
    font-weight: 900;
}

/* Clean minimal styles for stats and fallback */
.no-logo-fallback {
    display: flex;
    gap: 20px;
    align-items: center;
}

.fallback-logo-container {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
}

.fallback-logo-img {
    width: 78px;
    height: 78px;
    object-fit: contain;
    flex-shrink: 0;
}

.fallback-business-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    line-height: 1.1;
}

.business-name-line1 {
    font-size: 18px;
    font-weight: 700;
    color: #212529;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 2px;
}

.business-name-line2 {
    font-size: 14px;
    font-weight: 400;
    color: #6c757d;
    text-transform: capitalize;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 250px;
}

.enhanced-stats {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
    margin: 0 20px;
    max-width: 220px;
}

.stat-row {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
}

.stat-row:last-child {
    margin-bottom: 0;
}

.stat-icon {
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-width: 0;
}

.stat-content b {
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6c757d;
    font-weight: 600;
    flex-shrink: 0;
}

.stat-number {
    font-weight: 700;
    color: #6c757d;
    font-size: 13px;
    text-align: right;
    min-width: 80px;
}

.stat-number.highlight {
    color: #6c757d;
    font-weight: 700;
}
:root {
    --dialog-width: 60vw !important;
    --dialog-height: 80vh !important;
}

.ai-summary-d {
    width: 60vw !important;
    min-width: 60vw !important;
}
.dialog-header-custom {
    background-color: #d9d9d9 !important;
    padding: 5px 0px 5px 10px !important;
    color: #585858 !important;
    border-radius: 14px !important;
    font-family: "montserrat" !important;
    font-size: 1.3em !important;
    font-weight: 500 !important;
    box-shadow: 3px 3px 5px #ffffff !important;
}
.dialog-header-texter {
    position: relative;
    top: 4px;
}

/* Transcription Confirmation Dialog Styles */
.transcription-confirm-content {
    padding: 16px 0;
}

.transcription-confirm-content p {
    margin-bottom: 16px;
    line-height: 1.5;
    color: #323130;
}

.transcription-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
}

.option-card {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    border: 1px solid #e1dfdd;
    border-radius: 8px;
    background-color: #faf9f8;
    transition: all 0.2s ease;
}

.option-card:hover {
    border-color: #0078d4;
    background-color: #f0f8ff;
    cursor: pointer;
}

.option-card strong {
    display: block;
    font-weight: 600;
    color: #323130;
    margin-bottom: 4px;
}

.option-card p {
    margin: 0;
    font-size: 14px;
    color: #605e5c;
    line-height: 1.4;
}