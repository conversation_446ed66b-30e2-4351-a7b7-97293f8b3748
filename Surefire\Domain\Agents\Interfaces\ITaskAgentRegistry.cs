using Surefire.Domain.Agents.Models;

namespace Surefire.Domain.Agents.Interfaces
{
    /// <summary>
    /// Registry service for managing task agents
    /// </summary>
    public interface ITaskAgentRegistry
    {
        /// <summary>
        /// Register a task agent instance
        /// </summary>
        /// <param name="agent">The agent to register</param>
        void RegisterAgent(ITaskAgent agent);

        /// <summary>
        /// Get all registered agents
        /// </summary>
        /// <returns>List of all registered agent definitions</returns>
        IEnumerable<TaskAgentDefinition> GetAllAgents();

        /// <summary>
        /// Get available agents (alias for GetAllAgents for compatibility)
        /// </summary>
        /// <returns>List of available agent definitions</returns>
        List<TaskAgentDefinition> GetAvailableAgents();

        /// <summary>
        /// Get agents by category
        /// </summary>
        /// <param name="category">Category to filter by</param>
        /// <returns>Agents in the specified category</returns>
        IEnumerable<TaskAgentDefinition> GetAgentsByCategory(string category);

        /// <summary>
        /// Get agent by ID
        /// </summary>
        /// <param name="agentId">Agent identifier</param>
        /// <returns>Agent definition if found</returns>
        TaskAgentDefinition? GetAgentById(string agentId);

        /// <summary>
        /// Get a specific agent definition by ID
        /// </summary>
        /// <param name="agentId">Agent identifier</param>
        /// <returns>Agent definition if found</returns>
        TaskAgentDefinition? GetAgentDefinition(string agentId);

        /// <summary>
        /// Get the type of an agent by ID
        /// </summary>
        /// <param name="agentId">Agent identifier</param>
        /// <returns>Agent type if found</returns>
        Type? GetAgentType(string agentId);

        /// <summary>
        /// Get agent instance by ID for execution
        /// </summary>
        /// <param name="agentId">Agent identifier</param>
        /// <returns>Agent instance if found</returns>
        ITaskAgent? GetAgentInstance(string agentId);

        /// <summary>
        /// Find the best matching agent for user input
        /// </summary>
        /// <param name="userInput">User's natural language input</param>
        /// <param name="context">Execution context (AI chat or action button)</param>
        /// <returns>Match result with agent and confidence score</returns>
        AgentMatchResult FindBestMatch(string userInput, AgentExecutionContext context);

        /// <summary>
        /// Find agents that might match the given user input
        /// </summary>
        /// <param name="userInput">User's natural language input</param>
        /// <param name="topResults">Maximum number of results to return</param>
        /// <returns>Matching agents with confidence scores</returns>
        Task<List<(TaskAgentDefinition Agent, double Confidence)>> FindMatchingAgentsAsync(string userInput, int topResults = 5);

        /// <summary>
        /// Check if an agent exists and supports the given execution context
        /// </summary>
        /// <param name="agentId">Agent identifier</param>
        /// <param name="context">Execution context (AI chat or action button)</param>
        /// <returns>True if agent exists and supports the context</returns>
        bool SupportsContext(string agentId, AgentExecutionContext context);
    }
} 