﻿namespace Surefire.Domain.Graph
{
    public class EmailMessage
    {
        public string Subject { get; set; }
        public string Sender { get; set; }
        public string SenderEmail { get; set; }
        public DateTime ReceivedDateTime { get; set; }
        public string Preview { get; set; }
        public string Mailbox { get; set; }
        
        // Unique identifiers
        public string MessageId { get; set; }
        public string ConversationId { get; set; }
        public string InternetMessageId { get; set; } // Standard email ID format like <id@domain>
        
        // Recipients info
        public List<Recipient> ToRecipients { get; set; } = new List<Recipient>();
        public List<Recipient> CcRecipients { get; set; } = new List<Recipient>();
        
        // Message classification for UI display
        public bool FromClient { get; set; }
        public bool IsInternal { get; set; }
        
        // Information about which mailbox has this email as direct recipient (not cc)
        public string DirectRecipientMailbox { get; set; }
        
        // Get all mailboxes that received this email (for deduplication)
        public List<string> AllRecipientMailboxes { get; set; } = new List<string>();
        
        // Determine if this message is a duplicate of another one
        public bool IsDuplicate(EmailMessage other)
        {
            // First check InternetMessageId as it's most reliable for deduplication
            if (!string.IsNullOrEmpty(InternetMessageId) && 
                !string.IsNullOrEmpty(other.InternetMessageId) &&
                InternetMessageId == other.InternetMessageId)
            {
                return true;
            }
            
            // Fall back to Graph message ID
            return !string.IsNullOrEmpty(MessageId) && 
                   !string.IsNullOrEmpty(other.MessageId) && 
                   MessageId == other.MessageId;
        }
        
        // Check if this message was sent to a mailbox directly (in To: field)
        public bool IsDirectRecipient(string mailbox)
        {
            return ToRecipients.Any(r => r.EmailAddress?.Equals(mailbox, StringComparison.OrdinalIgnoreCase) == true);
        }
        
        // Check if this message was CC'd to a mailbox
        public bool IsCcRecipient(string mailbox)
        {
            return CcRecipients.Any(r => r.EmailAddress?.Equals(mailbox, StringComparison.OrdinalIgnoreCase) == true);
        }
        
        // Check if a recipient is an internal email address
        public bool HasInternalRecipient(List<string> internalMailboxes)
        {
            return ToRecipients.Any(r => internalMailboxes.Contains(r.EmailAddress?.ToLowerInvariant())) ||
                   CcRecipients.Any(r => internalMailboxes.Contains(r.EmailAddress?.ToLowerInvariant()));
        }
        
        // Check if email is from an internal sender
        public bool IsFromInternalSender(List<string> internalMailboxes)
        {
            return internalMailboxes.Contains(SenderEmail?.ToLowerInvariant());
        }
    }
    
    public class Recipient
    {
        public string Name { get; set; }
        public string EmailAddress { get; set; }
        
        public override string ToString()
        {
            if (string.IsNullOrEmpty(Name))
                return EmailAddress;
                
            return $"{Name} <{EmailAddress}>";
        }
    }
}
