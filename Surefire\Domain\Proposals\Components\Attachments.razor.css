/* Attachments.razor.css */

.proposal-details-stack {
    display: flex;
    flex-direction: row;
    gap: 24px;
    margin-top: 10px;
    align-items: flex-start;
    position: relative;
}

.proposal-details-col {
    min-width: 340px;
    max-width: 375px;
    display: flex;
    flex-direction: column;
    gap: 18px;
    position: relative;
}

@media (max-width: 1200px) {
    .proposal-details-stack {
        flex-direction: column;
        gap: 18px;
    }
    .proposal-details-col {
        max-width: 100%;
        min-width: 0;
    }
}

.thumb-icon {
    max-width: 160px;
    box-shadow: 1px 1px 9px #ccc;
}

.attachment-thumbnail {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    margin-bottom: 0;
    margin-top: 30px;
}

.attachment-actions {
    display: flex;
    flex-direction: row;
    gap: 6px;
    margin-top: 5px;
    margin-bottom: 2px;
    justify-content: flex-end !important;
    width: 100%;
    transition: all .5s;
    opacity: .6;
}
    .attachment-actions:hover {
        opacity: 1;
    }
.para {
    height:12px;
}
.delbtn, .folderbtn, .openbtn {
    cursor: pointer;
    font-size: 14px !important;
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    transition: background 0.15s;
}

.delbtn:hover, .folderbtn:hover, .openbtn:hover {
    background: #e8f0fe;
    cursor: pointer;
}

.drop-zone-quote, .drop-zone-acord, .drop-zone-supplemental, .drop-zone-proposal, .drop-zone-enclosures, .drop-zone-sl2, .drop-zone-binder, .drop-zone-invoice, .drop-zone-lossruns {
    outline: 0px;
    outline-offset: 0px !important;
    padding: 0 !important;
    background: none !important;
}

.flat-card {
    font-family: "montserrat", sans-serif;
    font-size: 1.6em;
    font-weight: 300;
    color: #828282;
    position: relative;
    top: 25px;
}

.flat-class-container {
    position: relative;
    top: -25px;
    margin-left: 10px;
    width:160px;
}

.flat-up {
    position: relative;
    top: -75px;
}

.docbutton:hover {
    cursor: pointer;
} 