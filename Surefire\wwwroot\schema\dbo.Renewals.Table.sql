USE [surefire-x1]
GO
/****** Object:  Table [dbo].[Renewals]    Script Date: 6/3/2025 10:26:32 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Renewals](
	[RenewalId] [int] IDENTITY(1,1) NOT NULL,
	[RenewalDate] [datetime2](7) NOT NULL,
	[CarrierId] [int] NULL,
	[WholesalerId] [int] NULL,
	[PolicyId] [int] NULL,
	[AssignedToId] [nvarchar](450) NOT NULL,
	[ClientId] [int] NOT NULL,
	[ProductId] [int] NOT NULL,
	[ExpiringPolicyNumber] [nvarchar](max) NULL,
	[ExpiringPremium] [decimal](18, 2) NULL,
	[Notes] [nvarchar](max) NULL,
	[DateCreated] [datetime2](7) NULL,
	[DateModified] [datetime2](7) NULL,
	[BillType] [nvarchar](max) NOT NULL,
	[RenewalStatus] [nvarchar](max) NOT NULL,
 CONSTRAINT [PK_Renewals] PRIMARY KEY CLUSTERED 
(
	[RenewalId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
ALTER TABLE [dbo].[Renewals] ADD  DEFAULT (N'') FOR [AssignedToId]
GO
ALTER TABLE [dbo].[Renewals] ADD  DEFAULT ((0)) FOR [ClientId]
GO
ALTER TABLE [dbo].[Renewals] ADD  DEFAULT ((0)) FOR [ProductId]
GO
ALTER TABLE [dbo].[Renewals] ADD  DEFAULT (N'') FOR [BillType]
GO
ALTER TABLE [dbo].[Renewals] ADD  DEFAULT (N'') FOR [RenewalStatus]
GO
ALTER TABLE [dbo].[Renewals]  WITH CHECK ADD  CONSTRAINT [FK_Renewals_AspNetUsers_AssignedToId] FOREIGN KEY([AssignedToId])
REFERENCES [dbo].[AspNetUsers] ([Id])
GO
ALTER TABLE [dbo].[Renewals] CHECK CONSTRAINT [FK_Renewals_AspNetUsers_AssignedToId]
GO
ALTER TABLE [dbo].[Renewals]  WITH CHECK ADD  CONSTRAINT [FK_Renewals_Carriers_CarrierId] FOREIGN KEY([CarrierId])
REFERENCES [dbo].[Carriers] ([CarrierId])
GO
ALTER TABLE [dbo].[Renewals] CHECK CONSTRAINT [FK_Renewals_Carriers_CarrierId]
GO
ALTER TABLE [dbo].[Renewals]  WITH CHECK ADD  CONSTRAINT [FK_Renewals_Carriers_WholesalerId] FOREIGN KEY([WholesalerId])
REFERENCES [dbo].[Carriers] ([CarrierId])
GO
ALTER TABLE [dbo].[Renewals] CHECK CONSTRAINT [FK_Renewals_Carriers_WholesalerId]
GO
ALTER TABLE [dbo].[Renewals]  WITH CHECK ADD  CONSTRAINT [FK_Renewals_Clients_ClientId] FOREIGN KEY([ClientId])
REFERENCES [dbo].[Clients] ([ClientId])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[Renewals] CHECK CONSTRAINT [FK_Renewals_Clients_ClientId]
GO
ALTER TABLE [dbo].[Renewals]  WITH CHECK ADD  CONSTRAINT [FK_Renewals_Policies_PolicyId] FOREIGN KEY([PolicyId])
REFERENCES [dbo].[Policies] ([PolicyId])
GO
ALTER TABLE [dbo].[Renewals] CHECK CONSTRAINT [FK_Renewals_Policies_PolicyId]
GO
ALTER TABLE [dbo].[Renewals]  WITH CHECK ADD  CONSTRAINT [FK_Renewals_Products_ProductId] FOREIGN KEY([ProductId])
REFERENCES [dbo].[Products] ([ProductId])
GO
ALTER TABLE [dbo].[Renewals] CHECK CONSTRAINT [FK_Renewals_Products_ProductId]
GO
-- LLM hints for AI: note that Renewals table is for processing data only
EXEC sys.sp_addextendedproperty 
    @name = N'LLM_SearchHints', 
    @value = N'Renewals table contains processing and marketing information for upcoming renewals. It is NOT the source of truth for actual policies written. For counts of policies and their details, always use the Policies table. Use Renewals only for renewal status operations.', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE',  @level1name = N'Renewals';
GO
