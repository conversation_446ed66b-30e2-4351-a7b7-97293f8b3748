﻿@using System.IO
@using Newtonsoft.<PERSON><PERSON>
@using Newtonsoft.Json.Linq
@using Syncfusion.Pdf
@using Syncfusion.Pdf.Parsing
@using Syncfusion.Blazor.SfPdfViewer
@using Surefire.Domain.Forms.Models
@using Surefire.Domain.Forms.Services
@inject FormService FormService
@inject IWebHostEnvironment Environment
@inject IJSRuntime JS

<SfPdfViewer2 @ref=pdfViewer DocumentPath="@DocumentPath"  Height="784px" Width="100%">
    <PdfViewerEvents DocumentLoaded="LoadJsonDataIntoPdfAsync"></PdfViewerEvents>
</SfPdfViewer2>

@code {
    [Parameter] public int FormDocId { get; set; }

    public FormPdf FormPdf { get; set; } = new FormPdf();
    public FormDoc FormDoc { get; set; } = new FormDoc();
    public List<FormDocRevision> Revisions { get; set; } = new List<FormDocRevision>();
    private string DocumentPath { get; set; } = "";
    private SfPdfViewer2 pdfViewer;
    private Stream stream;

    protected override async Task OnInitializedAsync()
    {
        FormDoc = await FormService.GetFormDocByIdAsync(FormDocId);

        // Load the data
        if(FormDoc != null)
        {
            FormPdf = FormDoc.FormPdf;
            DocumentPath = Path.Combine(Environment.WebRootPath, "forms", FormPdf.Filepath);
            Revisions = await FormService.GetFormDocRevisionsAsync(FormDocId);
        }
    }

    //Methods
    private async Task LoadJsonDataIntoPdfAsync()
    {
        if (string.IsNullOrEmpty(FormDoc.JSONData) || FormDoc.JSONData.Length < 10)
        {
            Console.WriteLine("No JSON data available to load into the PDF.");
            return;
        }

        JObject jsonData = JObject.Parse(FormDoc.JSONData);

        // Check if the FormDocId is 14 and if so fill in some fields
        if (FormDoc.FormPdfId == 14)
        {
            string currentDate = DateTime.Now.ToString("MM/yyyy");
            jsonData["Insurer 1 Date"] = currentDate;
            jsonData["Insurer 2 Date"] = currentDate;
            jsonData["Insurer 3 Date"] = currentDate;
        }

        // Convert updated JSON back to string and load into the PDF form
        FormDoc.JSONData = jsonData.ToString();
        byte[] byteArray = System.Text.Encoding.UTF8.GetBytes(FormDoc.JSONData);
        using (MemoryStream jsonStream = new MemoryStream(byteArray))
        {
            await pdfViewer.ImportFormFieldsAsync(jsonStream, FormFieldDataFormat.Json);
        }
    }

    public async Task SaveToDatabase(bool? exit)
    {
        var jsondata = await ExtractJson();

        if (!string.IsNullOrEmpty(jsondata))
        {
            FormDoc.JSONData = jsondata;
            await FormService.UpdateFormDoc(FormDoc);

            // Refresh revisions list
            Revisions = await FormService.GetFormDocRevisionsAsync(FormDocId);
        }
        else
        {
            Console.WriteLine("Failed to extract JSON data.");
        }
    }


    public async Task<string> ExtractJson()
    {
        stream = await pdfViewer.ExportFormFieldsAsync(FormFieldDataFormat.Json);

        using (StreamReader reader = new StreamReader(stream))
        {
            // Read the stream into a JSON string
            string jsonString = await reader.ReadToEndAsync();

            try
            {
                return jsonString;
            }
            catch (JsonException ex)
            {
                Console.WriteLine($"Error parsing JSON: {ex.Message}");
                return null;
            }
        }
    }
}
