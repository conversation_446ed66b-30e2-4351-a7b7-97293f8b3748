using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Surefire.Domain.Shared.Services;
using Surefire.Domain.Chat.Services;
using Surefire.Domain.Shared.Helpers;
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.SignalR;
using Surefire.Hubs;

namespace Surefire.Domain.Chat.Services
{
    public class SmsBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<SmsBackgroundService> _logger;
        private readonly IHubContext<MessagingHub> _hubContext;
        private readonly TimeSpan _checkInterval = TimeSpan.FromSeconds(30); // Check every 30 seconds
        private DateTime _lastCheckTime = DateTime.MinValue;

        public SmsBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<SmsBackgroundService> logger,
            IHubContext<MessagingHub> hubContext)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _hubContext = hubContext;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("SMS Background Service started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await CheckForNewSmsMessagesAsync();
                    await Task.Delay(_checkInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Service is being stopped
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in SMS background service");
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken); // Wait longer on error
                }
            }

            _logger.LogInformation("SMS Background Service stopped");
        }

        private async Task CheckForNewSmsMessagesAsync()
        {
            try
            {
                // Create a scope to resolve scoped services
                using var scope = _serviceProvider.CreateScope();
                var chatService = scope.ServiceProvider.GetRequiredService<ChatService>();
                var stateService = scope.ServiceProvider.GetRequiredService<StateService>();

                _logger.LogInformation($"SmsBackgroundService: Checking for new messages since {_lastCheckTime} (UTC: {_lastCheckTime.ToUniversalTime()})");
                
                // Check for new messages since last check
                await chatService.ProcessNewSmsMessagesAsync(_lastCheckTime, (message) => OnNewSmsMessage(message, stateService));
                _lastCheckTime = DateTime.UtcNow;
                
                _logger.LogInformation($"SmsBackgroundService: Updated last check time to {_lastCheckTime} (UTC: {_lastCheckTime.ToUniversalTime()})");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking for new SMS messages");
            }
        }

        private async void OnNewSmsMessage(SmsMessage message, StateService stateService)
        {
            try
            {
                _logger.LogInformation($"SmsBackgroundService: Processing new SMS message - ID: {message.Id}, Phone: {message.PhoneNumber}, Inbound: {message.IsInbound}, Text: {message.Text?.Substring(0, Math.Min(message.Text?.Length ?? 0, 50))}...");
                
                // Only process inbound messages for unread counts and broadcasting
                if (message.IsInbound)
                {
                    // Store the message in the database first
                    using var scope = _serviceProvider.CreateScope();
                    var smsMessageService = scope.ServiceProvider.GetRequiredService<SmsMessageService>();
                    var storedMessage = await smsMessageService.StoreSmsMessageAsync(message);
                    
                    if (storedMessage != null)
                    {
                        // Update unread counts from database state
                        var unconfirmedCounts = await smsMessageService.GetUnconfirmedCountsByPhoneAsync();
                        stateService.UpdateUnconfirmedSmsCounts(unconfirmedCounts);
                        
                        _logger.LogInformation($"SmsBackgroundService: Broadcasting inbound SMS message via SignalR");
                        
                        // Broadcast the new message via SignalR to update conversation list in real-time
                        await _hubContext.Clients.All.SendAsync("ReceiveSmsMessage", message);
                        
                                            // Also broadcast to specific SMS chat group for this phone number (using normalized phone number)
                    var normalizedPhone = StringHelper.NormalizePhoneNumber(message.PhoneNumber);
                    await _hubContext.Clients.Group($"SmsChat_{normalizedPhone}").SendAsync("ReceiveSmsMessage", message);
                        
                        _logger.LogInformation($"SmsBackgroundService: Successfully broadcasted SMS message via SignalR to all clients and SmsChat_{normalizedPhone} group");
                    }
                    else
                    {
                        _logger.LogWarning($"SmsBackgroundService: Failed to store SMS message {message.Id} in database");
                    }
                }
                else
                {
                    _logger.LogInformation($"SmsBackgroundService: Skipping outbound message (not broadcasting)");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing new SMS message");
            }
        }
    }
} 