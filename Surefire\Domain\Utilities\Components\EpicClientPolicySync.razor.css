.epic-sync-container {
    margin: 0 auto;
    padding: 0px 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.epic-sync-header {
    text-align: center;
    margin-bottom: 32px;
}

.epic-sync-header h2 {
    margin: 16px 0 8px 0;
    color: #323130;
    font-size: 28px;
    font-weight: 600;
}
.main-head {
    font-family: "montserrat", sans-serif;
    font-size: 2em;
}
.sync-description {
    color: #605e5c;
    font-size: 16px;
    line-height: 1.5;
    margin: 0;
}

.sync-instructions {
    background: #f8f9fa;
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 32px;
}

.sync-instructions h4 {
    margin: 0 0 16px 0;
    color: #323130;
    font-size: 18px;
    font-weight: 600;
}

.sync-instructions ol {
    margin: 0;
    padding-left: 20px;
}

.sync-instructions li {
    margin-bottom: 12px;
    line-height: 1.5;
    color: #323130;
}

.sync-status {
    text-align: center;
    margin-bottom: 32px;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.processing-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.status-text {
    margin: 0;
    font-size: 16px;
    color: #605e5c;
    font-weight: 500;
}

.sync-result {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
}

.sync-result.success {
    background: #dff6dd;
    border: 1px solid #107c10;
    color: #107c10;
}

.sync-result.error {
    background: #fde7e9;
    border: 1px solid #d13438;
    color: #d13438;
}

.sync-result p {
    margin: 0;
}

.sync-actions {
    text-align: center;
    margin-bottom: 32px;
}

.debug-panel {
    border: 1px solid #e1e4e8;
    border-radius: 8px;
    overflow: hidden;
}

.debug-header {
    background: #f6f8fa;
    padding: 12px 16px;
    border-bottom: 1px solid #e1e4e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.debug-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #323130;
}

.debug-content {
    max-height: 300px;
    overflow-y: auto;
    padding: 8px;
    background: #fafbfc;
}

.debug-message {
    display: flex;
    gap: 8px;
    padding: 4px 8px;
    border-radius: 4px;
    margin-bottom: 2px;
    font-size: 12px;
    font-family: 'Consolas', 'Monaco', monospace;
}

.debug-message.info {
    background: #e3f2fd;
    color: #1976d2;
}

.debug-message.success {
    background: #e8f5e8;
    color: #2e7d32;
}

.debug-message.warning {
    background: #fff3e0;
    color: #f57c00;
}

.debug-message.error {
    background: #ffebee;
    color: #d32f2f;
}

.debug-time {
    font-weight: 600;
    min-width: 60px;
}

.debug-text {
    flex: 1;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .debug-panel {
        width: 350px;
    }
}

@media (max-width: 768px) {
    .data-import-container {
        padding: 15px;
        height: auto;
    }
    
    .data-import-layout {
        flex-direction: column;
        height: auto;
    }
    
    .data-import-main {
        overflow-y: visible;
    }
    
    .debug-panel {
        width: 100%;
        max-height: 300px;
    }
    
    .import-actions {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .import-actions fluent-button {
        width: 100%;
        margin-left: 0 !important;
    }
} 