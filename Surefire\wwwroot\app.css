/*---- Root Layout ----*/
html, body {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    margin: 0 !important;
    padding: 0 !important;
    overflow:hidden;
}
:root html, body {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    margin: 0px !important;
    padding: 0px !important;
}
main {
    flex: 1;
    background-color: #e8e8e8;
    padding: 0px;
    margin: 0;
    margin-left: 50px;
    margin-top: 50px;
}
.article {
    padding: 10px;
    margin: 0;
}
.sf-top-bar {
    background: linear-gradient(to right, #153f82 0%,#89263d 100%);
}
.fluent-data-grid {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: .8rem;

}
    .fluent-data-grid thead {
        padding: 0px !important;
        margin: 0px !important;
        font-size: .2em !important;
        padding-top: 10px !important;
        margin-top: 10px !important;
    }
    .fluent-data-grid th {
        padding: 0px !important;
        margin: 0px !important;
        font-size: .2em !important;
        background-color: #fbfbfb;
    }
    .resize-handle {
        height:24px !important;
    }
    .fluent-data-grid th {
        padding: 0px !important;
        margin: 0px !important;
        min-height: 35px !important;
        max-height: 70px;
        padding-top: 1px !important;
        margin-top: 2px !important;
    }
    .fluent-data-grid tr td {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        margin: 0 !important;
    }
.chat-btn {

}
    .chat-btn:hover {
        cursor:pointer;
    }
chopper {
    transition: all cubic-bezier(.44,-0.36,.44,1) .5s;
    padding: 0px;
    margin: 0;
    background-color: #ccc;
    position: absolute;
    right: 0px;
    top: 0px;
    height: calc(100vh - 20px);
    background-color: #000000e1;
    z-index: 501;
}
.chopper-expand-True {
    width: 400px;
    min-height: 100px;
}
.chopper-expand-False {
    width: 0px;
    min-height: 100px;
}
.breadcrum {
    padding-top: 0px;
    padding-bottom: 0px;
    padding-left: 5px;
}
.page-content {
    padding-left: 10px;
    height: calc(100vh - 55px);
    max-width: 100vw;
    overflow-y:scroll;
}
.page-content-client {
    padding-left: 10px;
    max-width: 100vw;
    height: calc(100vh - 135px);
}
#blazor-error-ui {
    text-align: center;
    font-family: "montserrat", sans-serif;
    background: #cf4640;
    bottom: 20px;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
    height: 100px;
    font-size: 3em;
    opacity:.9;

}
    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }
.status-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #1f1e60;
    color: white;
    text-align: right;
    padding: 0px 0px;
    font-size: 12px;
    z-index: 9999;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.3);
    height:25px;
}
.status-update {
    font-size: 12px;
    font-family: "montserrat", sans-serif;
    position: relative;
    top: 4px;
    left: -10px;
    opacity: .8;
    float: right;
}
.status-ring {
    float: right;
    position:relative;
    top:1px;
    left:-5px;
}
.surefire-main-logo {
    height: 50px;
    width: 50px;
    min-height: 50px;
    min-width: 50px;
    max-height: 50px;
    max-width: 50px;
    background-color: #0622386f;
    border-right: 1px solid #000000e1;
    box-shadow: 0px 0px 5px #ffffff7c;
    cursor: pointer;
    position: relative;
    transition: opacity 0.2s ease;
    padding: 0;
    margin: 0;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.surefire-main-logo:hover {
    box-shadow: 0px 0px 8px #ffffff9c;
}

/* Recording state - no opacity change */

/* Custom mic spinner animation handled by JavaScript */

.recording-indicator {
    position: absolute;
    top: 2px;
    right: 2px;
    background-color: #e74c3c;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 1.5s infinite;
    z-index: 10;
    pointer-events: none;
}

.recording-indicator i {
    color: white;
    font-size: 8px;
}

/* Logo spin animation replaced by custom mic spinner sequence */

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
    70% { box-shadow: 0 0 0 6px rgba(231, 76, 60, 0); }
    100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
}

.e-spin-overlay {
    z-index: 10000;
    background-color: #ffffffc2 !important;
}
.hnum {
    background-color: #ffffffa7;
    color: #6755c2;
    padding: 2px 5px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    font-size: .7em;
    font-family: "montserrat", sans-serif;
    font-weight: 800;
    font-style: normal;
    position:relative;
    top:-1px;
    left:-3px;
}
.page-content-datagrid {
    padding: 10px;
    background: linear-gradient(135deg, #f9f9f9 0%,#e8e8e8 54%,#f7f7f7 76%,#e2e2e2 100%);
    height: calc(100vh - 150px);
    width: calc(100vw - 50px);
    overflow-y: scroll;
}
.inactive-icon {
    fill: #5e5e5e !important;
    fill: #5e5e5e00 !important;
}

.inactive-icon- {
    fill: #5e5e5e !important;
    fill: #5e5e5e00 !important;
}

.inactive-icon-ro-on {
    fill: #1b8ce3 !important;
    fill: #1b8ce3 !important;
}

.inactive-icon-ro-off {
    fill: #5e5e5e !important;
    fill: #5e5e5e !important;
}
.nb-blueline {
    width: 2px;
    height: 37px;
    float: left;
    position: relative;
    top: -6px;
    left: 4px;
}
.what {
    color: #808080;
}
.toolbar-disabled {
    opacity: .3 !important;
    pointer-events: none;
}
/*--------------------------Side and Top Bars----------------------------*/
.sidebar {
    padding-top: 20px;
    position: fixed;
    top: 50px;
    width: 50px;
    height: calc(100vh - 70px);
    border-right: 1px solid #cecece;
    background-color: #e8e8e8;
    z-index: 1000;
    box-shadow:0px 0px 8px #0000003d;
}
.sfnav {
    position:relative;
    z-index:1000;
}
.sfnav::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: 10px;
    bottom: -15px;
    z-index: 999;
}
    .sfnav:hover {
        cursor:pointer;
    }
.ro-container {
    display: inline;
    padding: 6px;
    height: 27px;
    background-color: #f6f6f6;
    position: absolute;
    z-index: 500 !important;
    transition: all 0.2s ease-out;
    overflow: hidden;
    white-space: nowrap;
    border-right: 5px solid #1b8ce3;
    border-top: 1px solid #1b8ce3;
    border-bottom: 1px solid #1b8ce3;
    color: #1b8ce3 !important;
}
:root .nb-bluelinenb-active {
    transition: all 0.5s ease;
    width: 2px;
    height: 37px;
    float: left;
    position: relative;
    top: -6px;
    left: 4px;
    background-color: #036ac4 !important;
    transform-origin: center;
    transform: scaleY(1);
}
:root .nb-bluelinenb-inactive {
    transition: all 0.5s ease;
    width: 2px;
    height: 37px;
    float: left;
    position: relative;
    top: -6px;
    left: 4px;
    background-color: #5e5e5e00 !important;
    transform-origin: center;
    transform: scaleY(0.1);
}
/*
:root .inactive-icon {
    fill: #808080 !important;
}*/

/* Each menu item is 46px vertically seperated */
.ro-home {
    top: 62px;
    width: 66px;
}
.ro-clients {
    top: 107px;
    width: 74px;
}
.ro-renewals {
    top: 153px;
    width: 103px;
}
.ro-leads {
    top: 220px;
    width: 83px;
}
.ro-carriers {
    top: 266px;
    width: 83px;
}
.ro-contacts {
    top: 312px;
    width: 93px;
}
.ro-locations {
    top: 358px;
    width: 102px;
}
.ro-policies {
    top:404px;
    width: 81px;
}
.ro-agents {
    top: 450px;
    width: 81px;
}
.ro-on {
    display: block;
    left: 49px;
}
.ro-off {
    left: -120px;
}
.ro-off2 {
    left: -300px;
    top: 56px;
    opacity: .5;
}
.ro-mi-name {
    font-family: "montserrat", sans-serif;
    font-weight: 500;
    font-size: 1.3em;
    position: relative;
    top: 1px;
}

.nav-item {
    position: relative;
}

.nav-link {
    padding: 0px !important;
}

.nav-sub {
    padding-left: 60px;
    font-size: 12px;
    height: 35px !important;
}

.top-bar {
    width: 100%;
    height: 50px;
    background-image: url('/img/bg-grad.jpg');
    background-size:cover;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0px;
    z-index: 1000;
}

/*------------ Toolbar Styles ---------------*/
.page-toolbar {
    display: flex;
    margin-left: 10px;
    margin-bottom:20px;
    margin-right:0px;
    background-color: #f8f8f8;
    padding: 4px;
    box-shadow: 0px 0px 10px #ccc;
    border-radius: 5px;
    align-items: center;
    position:relative;
    top:10px;
}
.toolbar-link {
    color: #000;
    height: 24px;
    font-size: 13px;
    text-decoration: none;
    margin-right: 15px;
    opacity:.7;
}
.toolbar-link:hover {
    opacity: 1;
    cursor: pointer;
}
.toolbar-link svg {
    fill: #000 !important;
}
:root .toolbar-link-cal svg {
    fill: #636363 !important;
    position: relative;
    top: 2px;
}
.sf-chevron {
    opacity:.7;
}
    .sf-chevron:hover {
        cursor: pointer;
        opacity: 1;
    }
.toolbar-text {
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
    font-family: "montserrat", sans-serif;
    position: relative;
    top: -7px;
    left: -2px;
}
    :root .toolbar-text a {
        color: #5f5f5f !important;
    }
.tworowgroup__container {
    margin-right:15px;
}
.tworowgroup__toprow {

}
.tworowgroup__toprow-button {
    font-family: "montserrat", sans-serif;
    font-size: .8em;
    font-weight: 500;
    padding:0px 4px;
    display:inline-flex;
    position:relative;
    top:3px;
}
    .tworowgroup__toprow-button:hover {
        background-color:#0000000b;
        cursor:pointer;
    }
    .tworowgroup__toprow-button span {
        position: relative;
        top: 4px;
        left: 1px;
        color: #454545 !important;
    }
    .tworowgroup__toprow-button a {
        color:#454545 !important;
    }

.tworowgroup__bottomrow {
    font-family: "montserrat", sans-serif;
    font-size: .7em;
    position: relative;
    top: -5px;
    width: 100%;
    text-align: center;
    color:#878787;
    text-shadow: 1px 1px 1px #fff;
    height:5px;
}
.group-left-title {
    font-family: "montserrat", sans-serif;
    font-size: .7em;
    text-align: right;
    line-height: 12px;
    color: #b9b9b9;
    font-weight: 600;
    text-shadow: 1px 1px 1px #fff, 1px 1px 1px #fff;
    margin-right: 4px;
    margin-left: 4px;
    /*color: #fff;
    text-shadow: 
        0px 0px 3px rgba(0, 0, 0, .95),
        0px 0px 8px rgba(0, 0, 0, .95);*/
}
.sf-useremail {
    font-size: .7em;
    color: #fff;
    opacity: .4;
    -webkit-text-decoration: none;
    text-decoration: none;
    transition: opacity .5s;
}

    .sf-useremail:hover {
        opacity: .9;
    }

.search-popper {
    font-family: "montserrat", sans-serif;
    position: absolute;
    top: -10px;
    left: -54px;
    width: 300px;
    box-shadow: 0px 0px 10px #000;
    background-color: #ffffffe7;
    z-index: -1;
    border-top: 50px solid #593293;
    padding-left: 12px;
    padding-bottom: 12px;
    word-wrap: normal;
    white-space: nowrap;
    color: #000;
    overflow: hidden;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    transition: all 0.5s ease-in-out;
}
.search-category {
    font-size: .65em;
    font-weight: 600;
    letter-spacing: 1.5px;
    color: #036ac4;
    text-transform: uppercase;
    padding-top: 6px;
    margin-top:6px;
    padding-bottom:2px;
    border-top:1px solid #d3d3d3;
}
.search-item {
    font-weight: 400;
    font-size: .85em;
    padding-left: 0px;
    opacity:.7;
}
.search-item:hover {
    opacity: 1;
    cursor:pointer;
    background-color:#036ac431;
}
.search-item-parent {
    font-size: .6em;
    font-weight: 600;
    color: #6b6b6b;
    padding-left: 25px;
    opacity: .7;
}
.fsearch-hide {
    display: none;
    pointer-events: none;
}
.fsearch-show {
    display: block;
}
.fsearch-on {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.3s ease-in-out;
}

.fsearch-off {
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease-in-out;
    pointer-events:none;
}
.fsearch-selecteditem {
    background-color: #2b2b2b;
    color:#e8e8e8;
    cursor: pointer;
    font-weight:bold;
}
/*--------------------------Main Layout Items----------------------------*/
.left-icons {
    display: flex;
    align-items: center;
    width:300px;
}
.fsearch-bar {
    flex-grow: 1;
    display: flex;
    justify-content: center;
    z-index: 100 !important;
}
:root .fsearch-quick {
    opacity: .8;
}
:root .fsearch-quick:hover {
    opacity: 1;
}
    :root .fsearch-quick:active {
        opacity: 1;
    }
    :root .fsearch-quick:focus {
        opacity: 1;
    }
.search-bar {
    flex-grow: 1;
    display: flex;
    justify-content: center;
    z-index:100 !important;
}
.fsearch-container {
    position:relative;
}
.main-content {
    flex-grow: 1;
    padding: 20px;
    overflow: auto;
}
.search-input {
    width: 100%;
    max-width: 400px;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}
.sf-search-input {
    font-family: "montserrat", sans-serif;
    font-size: 15px;
    padding: 4px 8px;
    padding-left: 30px;
    border-radius: 3px;
    border: 1px solid #745ea1;
    width: 300px;
    background-color: #ffffffde;
}

    .sf-search-input:active {
        border-color: #745ea1;
    }

:root .sf-search-input:focus-visible {
    outline: 0;
    background-color: #fff;
}

.sf-search-icon {
    position: relative;
    left: -337px;
    top: 4px;
    pointer-events: none;
    font-size: 20px;
    color: #745ea1;
}
.right-icons {
    align-items: center;
    width:10vw;
}
.right-other-icons {
    position:relative;
    top:3px;
}
.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.sf-content {
    background: linear-gradient(135deg, #f9f9f9 0%,#e8e8e8 54%,#f7f7f7 76%,#e2e2e2 100%);
    height: calc(100vh - 150px);
    max-height: 90vh;
    max-width: 100vw;
    overflow-y: scroll;
}
.sf-extrapad {
    padding-left:7px !important;
}
.sf-fluenttabs {
    padding: 0px 10px;
}
.sf-head {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    width: 100%;
    z-index: 300;
}
.sf-header-col-name {
    white-space: nowrap; 
    overflow: hidden; 
    text-overflow: ellipsis;
    min-width: 200px;
    max-width:50%;
    margin-left:16px;
}
.sf-header-col-tab {
    height: 78px;
    min-width: 200px;
    max-width: 50%;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    position: relative;
    top: -2px;
    box-shadow: 0 10px 10px rgba(0, 0, 0, 0.2), 0 10px 15px rgba(0, 0, 0, 0.1), 0 25px 20px rgba(0, 0, 0, 0.05), 0 30px 25px rgba(0, 0, 0, 0.02);
    display:flex;
    flex-direction:row;
    /*background: linear-gradient(to bottom, rgba(249,249,249,1) 0%,rgba(249,249,249,0.89) 12%,rgba(0,0,0,0.05) 100%);*/
}
.sf-header-col-tab__leftcol {
    width: 100px;
    text-align: center;
    padding-top:10px;
    padding-left:5px;
}
.sf-header-col-tab__col {
    padding-top: 4px;
    text-align: left;
    width: auto;
    line-height: 15px;
    padding-left: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.sf-header__vertline {
    height:60px;
    width:1px;
    margin-left:7px;
    margin-right:7px;
    margin-top:8px;
    background-color:#d7d7d7;
    border-right:1px solid #ffffffdc;
}
.sf-header-col__spacer {
    margin-bottom:4px;
}
.sf-header__spacer {
    width:10px;
}
.sf-tab-pad {
    background-color: #fff;
    border-radius: 5px;
    padding:10px;
    min-height: calc(100vh - 268px);
}
.sf-tab-bg {
    background-color: #fff;
    border-radius: 5px;
}
.sf-tab {
    border-radius: 5px;
    /*line-height: 1.1;*/
}
.sf-tab-container {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0px 0px 5px #ccc;
}
.sf-tab-client {
    background-color: #fff;
    border-radius: 5px;
    line-height: 1.1;
    height: calc(100vh - 256px) !important;
    overflow-y: scroll !important;
    padding-bottom:15px;
}
.subm-title {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    color:#3e3e3e;
}
.teldialphone {
    color:inherit;
    text-decoration:none;
}
.teldialphone:hover {
    color: #036ac4;
    cursor:pointer;
    text-decoration: none;
}

.subm-title__listing {
    font-weight: 700;
}
.subm-title__listing-crop {
   
   
}
.subm-byline {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-weight: 400;
    font-size:.9em;
}
.subm-byline__bold {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-weight: 700;
    color:#7e7e7e;
}
.subm-byline__special {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-weight: 700;
    font-size: 1.1em;
    color:#494949;
    float:right;
}
.subm-preview {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size:.85em;
    color:#818181;
}
.subm {
    border-bottom: 1px solid #ccc;
    line-height:20px;
    padding: 5px 10px;
}
.subm:hover {
    cursor:pointer;
    background-color:#f6f6f6;
}
.subm-selected {
    background-color:#cfe4fa;
}
.subm-arrow {
    position:relative;
    top:3px;
    left:-1px;
    opacity:.6;
}
.sf-navlist {
    float: left;
    position: relative;
    left: -5px;
    background-color: #fff;
    border-radius: 5px;
    width: 300px;
    height: calc(100vh - 290px);
    box-shadow: 0px 0px 7px #00000013;
    margin-right:5px;
}

.sf-viewpanel {
    padding:0px 10px;
    float:left;
    background-color: #fff;
    border-radius: 5px;
    width: calc(100% - 340px);
    min-height: calc(100vh - 280px);
    box-shadow:0px 0px 7px #00000013;
}
.app-logo {
    width: 50px;
    height: 50px;
    display: block;
    object-fit: contain;
    opacity: 1;
}
.left-company {
    font-family: "montserrat", sans-serif;
    font-weight: 400;
    color: #ffffff91;
    font-size: 1.2em;
    margin-left: 5px;
    pointer-events: none;
}
.left-section {
    font-family: "montserrat", sans-serif;
    font-weight: 600;
    color: #ffffffb9;
    font-size: 1.2em;
    margin-left: 10px;
    position: relative;
    left: -5px;
    pointer-events:none;
}
.left-dot {
    font-family: "montserrat", sans-serif;
    font-weight: 600;
    color: #ffffff9c;
    position: relative;
    left: 3px;
    top: 1px;
    pointer-events: none;
}
.profile-gear {
    position:relative;
    top:2px;
    left:-5px;
    opacity:.7;
}
.profile-gear:hover {
    opacity: 1;
    cursor:pointer;
}
.copied {
    animation: blink-text 2s linear forwards;
}


/* Keyframes for blinking text color animation */
@keyframes blink-text {
    0% {
        color: inherit; /* Start with the original color */
    }

    16.67% {
        color: #F2C5BD; /* Fade to transparent */
    }

    33.33% {
        color: #CF4740; /* Blink to red */
    }

    50% {
        color: #FF6600; /* Fade back to transparent */
    }

    66.67% {
        color: #593294; /* Blink to red again */
    }

    83.33% {
        color: #745DA1; /* Fade back to transparent */
    }

    100% {
        color: inherit; /* Return to the original color */
    }
}
/*--------------------------Contacts----------------------------*/
.sf-verthr {
    width: 1px;
    height: 30px;
    background-color: #c6c6c6 !important;
    margin-left: 15px;
    margin-right: 10px;
}
.sf-verthr2 {
    width: 1px;
    height: 30px;
    background-color: #c6c6c6 !important;
    margin-right: 15px;
}

/*--------------------------Global Icons----------------------------*/

/*--------------------------Contacts----------------------------*/
.m-contact {
    padding: 5px;
    border: 1px solid #e4e4e4;
    border-left: 5px solid #b74c02;
    border-radius: 3px;
    width:100%;
    margin-bottom: 11px;
    font-family: "montserrat", sans-serif;
}
.m-contact-small {
    padding: 5px;
    font-family: "montserrat", sans-serif;
    font-size:.85em;
    line-height:11px;
}

.m-contact-True {
    padding: 5px;
    border: 1px solid #1b8ce3;
    border-left: 5px solid #1b8ce3;
    border-radius: 3px;
    width: 100%;
    margin-bottom: 11px;
    font-family: "montserrat", sans-serif;
    box-shadow: 0px 0px 7px #1b8ce323;
}

.m-contact-False {
    padding: 5px;
    border: 1px solid #e4e4e4;
    border-left: 5px solid #b74c02;
    border-radius: 3px;
    width: 100%;
    margin-bottom: 11px;
    font-family: "montserrat", sans-serif;
    background-color:#fcfcfc;
}

.m-name {
    font-size: 1.25em;
    font-weight: 700;
}
.m-title {
    font-size: .9em;
    font-style: italic;
    color:#868686
}
.m-email {
    font-size: 1em;
    margin-top:4px;
    margin-bottom:4px;
}
.m-email:hover {
    color:#036ac4;
    cursor:pointer;
}
.m-phone {
    font-size: 1em;
    font-weight: 300;
}
.m-phonetype {
    font-size: .75em;
    font-weight: 300;
}
.m-padafter {
    margin-right:20px;
}
/* Contact navigation container */
.contact-navigation {
    display: flex;
    align-items: center;
}

/* Navigation buttons */
.contact-nav-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    padding: 10px;
}

    .contact-nav-button:focus {
        outline: none;
    }

/* Contact display area */
.contact-display {
    flex: 1;
    overflow: hidden;
    position: relative;
}

/* Contact card */
.contact-card {
    animation-duration: 0.5s;
    animation-fill-mode: forwards;
    width:320px;
}

/* Sliding animations */
@keyframes slide-in-left {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }

    to {
        transform: translateX(0%);
        opacity: 1;
    }
}

@keyframes slide-in-right {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0%);
        opacity: 1;
    }
}

.slide-in-left {
    animation-name: slide-in-left;
}

.slide-in-right {
    animation-name: slide-in-right;
}

/*--------------------------Flex----------------------------*/
.mf-flex {
    display: flex;
}
.mf-flexrow {
    display: flex;
    flex-direction: row;
}
.mf-flexcolumn {
    display: flex;
    flex-direction: column;
}
.mf-flexrowspaced {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
/* Flex Columns Spaced, Left Justified */
.mf-flexleft {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: flex-start;
    align-items: flex-start;
    width: auto;
    color:#1a4d16
}
.mf-flexleft-col {
    flex: 0 1 auto;
}
/* Two Columns, 50-50 Weighted */
.mf-flextwocolumn {
    display: flex;
    gap:20px;
}
.mf-flextwocolumn-col {
    flex: 1;
    min-width: calc(50% - 20px);
    box-sizing: border-box;
    padding: 10px;
    max-width: calc(50% - 20px);
}
.mf-flextwocolumn .mf-flextwocolumn-col:nth-of-type(2) {
    border-left: 1px solid #ccc;
}
/* Two Columns, Right Weighted */
.mf-flextwocolumnrs {
    display: flex;
    gap: 20px;
    box-sizing: border-box;
}

.mf-flextwocolumnrs-col:nth-of-type(1) {
    flex: 1;
    max-width: 33%;
}

.mf-flextwocolumnrs-col:nth-of-type(2) {
    flex: 2;
    max-width: 66%;
}

.mf-flextwocolumnrs-col {
    box-sizing: border-box;
    padding: 10px;
}
/* Two Columns, Left Weighted */
.mf-flextwocolumnls {
    display: flex;
    gap: 20px; 
    box-sizing: border-box;
}

.mf-flextwocolumnls-col:nth-of-type(1) {
    flex: 2;
    max-width: 66%;
}

.mf-flextwocolumnls-col:nth-of-type(2) {
    flex: 1;
    max-width: 33%;
}

.mf-flextwocolumnls-col {
    box-sizing: border-box;
    padding: 10px;
}
@media (max-width: 800px) {
    .mf-flextwocolumn-col {
        min-width: 400px;
    }
}
.mf-row {
    -ms-flex-direction: row;
    -webkit-flex-direction: row;
    flex-direction: row;
    justify-content: flex-start;
}
.mf-flex-editpage {
    display: flex;
    flex-direction: row;
    margin-bottom:10px;
}
:root .mf-flex-row {
    display: flex !important;
    -ms-flex-direction: row;
    -webkit-flex-direction: row;
    flex-direction: row;
    justify-content: flex-start;
}
.mf-item-half {
    flex-basis: 50%;
    margin-right: 20px;
}
.mf-item-space {
    margin-bottom: 10px;
}
.mf-sm {
    flex: 0 1 auto;
}
.mf-item-xs {
    flex-basis: 10%
}
.mf-item-sm {
    flex-basis: 20%
}
.mf-item-md {
    flex-basis: 35%
}
.mf-item-xmd {
    flex-basis: 40%
}
.mf-item-lg {
    flex-basis: 50%;
    margin-right:10px;
    margin-left:10px;
}
.mf-pad-left {
    margin-left:25px;
}
.mf-row-container {
    max-width:900px !important;
}
/*--------------------------Forms Styles----------------------------*/
.cb {
    padding:5px 0px;
}
:root .text-danger {
    font-size: 10px !important;
    color: #f00 !important;
}
/*--------------------------Homepage----------------------------*/

.tbg {
    height: 20px;
    color: #ffffff64;
    text-align: left;
    font-size: .7em;
}
    .tbg th {
        padding-left: 4px !important;
    }
.highlighted {
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#f3c5bd+0,e86c57+50,ea2803+51,ff6600+75,c72200+100;Red+Gloss */
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#f3c5bd+0,e86c57+26,ea2803+51,ff6600+75,f3c5bd+100 */
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#f3c5bd+0,e86c57+26,e8998b+51,ff6600+75,f3c5bd+100 */
    background: linear-gradient(to right, #f3c5bd 0%,#e86c57 26%,#e8998b 51%,#ff6600 75%,#f3c5bd 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */

    background-size: 300% 300%;
    animation: gradientAnimation 12s ease-in-out infinite;
}
.home-box {
    padding: 3px;
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#6858c7+0,593293+19,4e1359+47,ac283a+77,e55249+100 */
    border-bottom-right-radius: 15px;
    border-bottom-left-radius: 15px;
}
@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }
    10% {
        background-position: 0% 10%;
    }
    13% {
        background-position: 0% 25%;
    }
    15% {
        background-position: 0% 15%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}
    .home-box > * {
        background: white;
        padding: 10px;
        border-radius: inherit;
    }

.sectiontitlecloser {
    font-family: "montserrat", sans-serif;
    font-size: 1.5em;
    padding-top: 5px;
    background-color: #ffffff76;
    color: #b6b6b6;
    text-align: center;
    border-bottom: 5px solid #e55249;
    border-right: 5px solid #1f1e60;
    border-left: 1px solid #3d3d3d;
    border-bottom-left-radius: 10px !important;
    border-bottom-right-radius: 10px !important;
    height: 10px;
}
/*--------------------------Business Top Bar ----------------------------*/
.biz-top {
    font-family: "montserrat", sans-serif;
    background: linear-gradient(to bottom, rgba(249,249,249,1) 0%,rgba(234,234,234,1) 100%);
    padding-top: 2px;
    padding-bottom: 4px;
    padding-left: 15px;
    padding-right: 15px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}
.biz-rowone {
    width: 100%;
    height: 50px;
    overflow: hidden;
}

.biz-rowtwo {
    position: relative;
    top: -4px;
    color: #454545;
}

.biz-name {
    font-weight: 800;
    font-style: normal;
    font-size: 2.5em;
}

.biz-phone {
    float: right;
}

.biz-phonenumber {
    font-weight: 300;
    font-style: normal;
    font-size: 2.5em;
    position: relative;
    top: -10px;
    left: -5px;
    color: #8d8d8d;
}

.biz-data {
    font-weight: 300;
    position: relative;
    left: -4px;
    font-style: normal;
    font-size: 1.125em;
    margin-right: 25px;
    text-decoration: none;
    color: #1e1e1e;
}

    .biz-data:hover {
        color: #0f6cbd;
        cursor: pointer;
    }

.data-icon {
    position: relative;
    top: 3px;
    color: #8d8d8d;
}
.biz-main {
    background-color: #fff;
    height: auto;
}
.phone-icon {
    position: relative;
    top: 2px;
    color: #8d8d8d;
}

.email-icon {
    position: relative;
    top: 2px;
    color: #8d8d8d;
}

.map-icon {
    position: relative;
    top: 2px;
    color: #8d8d8d;
}
/*--------------------------Policy Screen----------------------------*/
:root .rating-basis-table tbody tr {
    border-bottom: 1px solid #ccc !important;
}
.sf-da {
    width: 50px;
}
.sf-pp {
    width: 130px;
    overflow: hidden;
}
.pr-td {
    border-bottom: 1px solid #ccc;
    border-left: 1px solid #ccc;
}
.pol-name {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: .8em;
    width: 130px;
    display: inline-block;
    font-weight: bold;
    color: #484848;
    height: 30px;
    text-align: right;
    position: relative;
    top: 4px;
}

.pol-value {
    width: 200px;
    display: inline-block;
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: 14px;
    color: #000;
    height: 30px;
}
.pol-rate-title {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: .7em;
    font-weight: bold;
    color: #484848;
    text-align: center;
    text-transform: uppercase;
}
.pol-static {
    position: relative;
    top: 4px;
    left: 5px;
}

.pol-section-title {
    font-family: "montserrat", sans-serif;
    font-size: 1em;
    font-weight:400;
    color:#6b6b6b;
    text-transform: uppercase;
    letter-spacing:2px;
}

.gb-section-title {
    font-family: "montserrat", sans-serif;
    font-size: 1.25em;
    font-weight: 400;
    color: #6b6b6b;
    text-transform: uppercase;
    letter-spacing: 2px;
    padding: 5px 20px 3px 5px;
    border-top-right-radius: 20px;
    text-shadow: 1px 1px 1px #fff;
}

.gb-section-container {
    border-left: 5px solid #c8c8c8;
    padding-left: 15px;
    padding-right: 15px;
    padding-top: 10px;
    padding-bottom: 20px;
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#ffffff+0,ffffff+35,ffffff+99&1+0,0.26+35,0.89+99 */
    background: linear-gradient(to bottom, rgba(255,255,255,1) 0%,rgba(255,255,255,0.26) 35%,rgba(255,255,255,0.89) 99%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.pol-exist {
    font-family: "montserrat", sans-serif;
    font-size: .8em;
    font-weight: 400;
    color: #6b6b6b;
    text-transform: uppercase;
    letter-spacing: 1px;
    background-color:#fff;
    width:auto;
    height:80px;
    border:1px dashed #ccc;
    position:relative;
    left:8px;
    clear:both;
}
.pdf-icon {
    width:60px;
}
/*--------------------------Primary Default Text Styles----------------------------*/
.txt-small {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: .7em;
    font-weight: bold;
    color: #484848;
    text-transform: uppercase;
    text-align:left;
}
.txt-small-sub {
    font-family: "Segoe UI";
    font-size: .7em;
    color: #484848;
    text-align: left;
}
.txt-cursive {
    font-family: montserrat, sans-serif;
    font-size: .7em;
    color: #484848;
    font-weight: 400;
    font-style: italic;
}
.txt-cursive-sm {
    font-family: montserrat, sans-serif;
    font-size: .6em;
    color: #484848;
    font-weight: 400;
    font-style: italic;
}
.txt-p {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: 14px;
    color: #000;
}

.txt-p-md {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: 1.5rem;
    color: #000;
}
.txt-reg-head {
    font-size: 1.2em;
    font-weight: 800;
    color: #036ac4;
}
.txt-reg-head2 {
    font-size: 1.2em;
    font-weight: 800;
    color: #d44444;
}
.txt-reg-bold {
    font-size:1em;
    font-weight:800;
    color:#000;
}
.txt-reg {
    font-size: 1em;
    color: #000;
}
.txt-reg-sm {
    font-size: .8em;
    color: #464646;
}
.txt-reg-xs {
    font-size: .6em;
    color: #464646;
}
.txt-bold {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: .8em;
    font-weight: bold;
    color: #484848;
}
.txt-bold-md {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: 1em;
    font-weight: bold;
    color: #484848;
}
.txt-section {
    font-family: "montserrat", sans-serif;
    font-size: 1rem;
    font-weight: 400;
    color: #6b6b6b;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom:7px;
    margin-top:3px;
}
.txt-alert {
    font-family: "montserrat", sans-serif;
    font-size: 1.5em;
    font-weight: 800;
    margin-bottom:10px;
    color: #00000073;
}
.txt-alert-box {
    text-align: center;
    width: calc(100% - 100px);
    padding:30px;
    background-color:#d54b4161;
}
.txt-alert-box-sm {
    text-align: left;
    width:100%;
    padding: 10px;
    background-color: #d54b4161;
    border-radius:6px;
}
.txt-persona {
    font-size: 1.5em;
    font-weight: 100;
    font-family: montserrat, sans-serif;
}
.txt-label {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-size: .8em;
    color: #484848;
    margin-bottom:7px;
}
.sf-link {
    color: #000 !important;
    text-decoration: none !important;
}
    .sf-link:hover {
        color: #036ac4 !important;
        cursor: pointer !important;
    }
.icon-nonefound-text {
    font-size: 1em;
    color: #b7b7b7;
    position: relative;
    top: -8px;
}
.icon-nonefound-text a {
    color:#5689b5;
    text-decoration:none;
}
    .icon-nonefound-text a:hover {
        color: #036ac4;
        text-decoration: none;
    }
/*--------------------------Other Styles----------------------------*/
.hr {
    width:100%;
    height:1px;
    margin-top:15px;
    margin-bottom:15px;
    background-color:#eeeeee;
}
.sf-header {
    display: flex;
    justify-content: normal;
    flex-direction: row;
    padding-top: 12px;
    padding-bottom: 8px;
    padding-left: 15px;
    padding-right: 15px;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.default-header {
    font-family: "montserrat", sans-serif;
    font-weight: 400;
    font-style: normal;
    font-size: 2.5em;
    line-height: 30px;
}
.default-subheader {
    font-family: "montserrat", sans-serif;
    font-weight: 600;
    font-style: normal;
    font-size: .6em;
    color:#a3a3a3;
}
.div-section {
    padding: 10px;
    border-top: 1px solid #ccc;
    border-left: 5px solid #ccc;
    background: linear-gradient(165deg, rgba(204,204,204,1) 0%,rgba(242,242,242,1) 13%,rgba(0,0,0,0) 100%);
}

.txt-section-bar {
    background-color: #ccc;
    font-family: "montserrat", sans-serif;
    font-size: .9rem;
    font-weight: 400;
    color: #626262;
    text-shadow:1px 1px 1px #ffffffb9;
    text-transform: uppercase;
    padding: 4px 15px 4px 12px;
    border-top-right-radius: 10px;
    letter-spacing: .2em;
    position: relative;
    top: -2px;
}
.e-toolbar-spacer {
    flex-grow: 1;
}
:root .mh1 {
    font-family: "montserrat", sans-serif;
    font-weight: 200;
    font-size: 2em;
    font-style: normal;
    color: #b2b2b2 !important;
    padding: 0px;
    padding-bottom: 10px !important;
    margin: 0px;
}
:root .e-btnhover {
    opacity:.5;
    text-decoration:none;
}
:root .e-btnhover:hover {
    opacity:1;
    cursor:pointer;
}

:root .sf-quicklist .e-listview {
    border: 0px;
}
    :root .sf-quicklist .e-listview .e-list-item {
        border: 0px;
    }

.sf-detail {
    background-color: #fff;
    padding: 10px;
    margin-left: 10px;
    border-radius: 4px;
    box-shadow: 0px 0px 3px #00000040;
    max-height: 80vh;
}
a, .btn-link {
    color: #006bb7;
}

.btn-primary {
    color: #fff;
    background-color: #1b6ec2;
    border-color: #1861ac;
}
.sf-table {
    width:100%;
    text-decoration:none;
}
.sf-table a {
    text-decoration: none;
}
.sf-table tbody td {
    padding:0px 0px;
}
.sf-table tbody {
    font-size:1.3em;
}
    .sf-table th {
        border-bottom: 3px solid #ccc;
    }
    .sf-table tbody {
        font-size: 1.3em;
    }
    .sf-table tbody td {
        border-bottom:1px solid #ccc;
    }
        .sf-table tbody tr > :first-child {
            text-align: right;
            padding-right: 6px;
        }
.rdate {
    font-family: "Segoe UI", -apple-system, blinkMacSystemfont, "Roboto", "Helvetica Neue", sans-serif;
    font-family: "mono45-headline", monospace;
    font-family: "montserrat", sans-serif;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-weight: 700;
    color: #7e7e7e;
}
.sf-td-bold {
    font-weight: bold;
}
.sf-td-sm {
    font-size:15px;
}
.sf-td-sm2 {
    font-size: 15px;
    font-weight:bold;
}
.sf-td-mono {
    font-family: "mono45-headline", monospace;
    font-weight: 500;
    font-style: normal;
    color: #909090 !important;
}
.sf-thead {
    font-size: 12px;
    font-family: "montserrat", sans-serif;
    font-weight: 600;
    text-align: left;
    color: #7a7a7a;
}
.sf-rentable > :first-child {
    width:40px;
    text-align:right;
    padding-right:15px;
}
.sf-rentable > :nth-child(2) {
    width: 50px;
}

.content {
    padding-top: 1.1rem;
}

h1:focus {
    outline: none;
}

.valid.modified:not([type=checkbox]) {
    outline: 1px solid #26b050;
}

.invalid {
    outline: 1px solid #e50000;
}

.validation-message {
    color: #e50000;
}

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

    .blazor-error-boundary::after {
        content: "An error has occurred."
    }

.darker-border-checkbox.form-check-input {
    border-color: #929292;
}

.b-navicon {
    color: white;
    margin-right: 8px;
    padding-right: 5px;
    font-size: 25px !important;
}
.credentials-table {
    border-spacing: 0px !important;
    padding: 0px;
    margin: 0px;
    width: 100%;
    border-collapse: collapse;
}
    .credentials-table th {
        border-bottom: 2px solid #ccc;
    }
    .credentials-table .last-of-set {
        border-bottom: 1px solid #ccc;
    }
a:hover {
    text-decoration: none !important;
}
.colu {
    width:60%;
    text-align:left;
}
.colp {
    width: 40%;
    text-align: left;
}

.submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out;
}

    .submenu.expanded {
        max-height: 500px;
    }

hr {
    color: #ffffff82;
    margin: 0;
    width: 90%;
    margin-left: auto;
    margin-right: auto;
}
.toolbar-link {
    color:#000;
}

.sfdg-title {
    font-weight: bold;
}

.fluent-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.fluent-list-item {
    display: flex;
    align-items: center;
    padding: 0.5em;
    border-bottom: 1px solid #ccc;
}

    .fluent-list-item span {
        margin-left: 0.5em;
    }

.sf-initials {
    font-family: "montserrat", sans-serif;
    font-weight:500;
    color:#8a8a8a;
}
.img-fluid {
    max-width: 100%;
    height: auto;
}
.fluent-data-grid {
    background-color:#fff;
    border:1px solid #ccc;
    box-shadow: 0px 0px 7px #ccc;
}
.fluent-data-grid__row {
    background-color:#000;
}
.selected-row {
    background-color: #000;
}
:root .selected-row {
    background-color: #000 !important;
}
.toolbar__fluentui-selectedname {
    font-weight: bold;
    margin-right: 5px;
    font-family: "montserrat", sans-serif;
    font-weight: 500;
    font-size:.7em;
}
.fluent-data-grid__bottombar {
    position:relative;
}
.fluent-data-grid__pagination {
    position: absolute;
    top:0px;
    left:0px;
    width:100%;
}
.fluent-data-grid__search {
    position: absolute;
    top: 10px;
    left: 100px;
    text-align:center;
}

.sf-bold {
    font-weight:bold;
}
.sf-bold a {
    text-decoration: none;
    color:#1f1e60
}
    .sf-bold a:hover {
        color: #036ac4;
    }

.sf-table-txtsmall {
    font-size:1.1rem;
}
.sf-spacer-lg {
    height: 30px;
}
.sf-spacer-sm {
    height:8px;
}
.sf-spacer {
    height: 20px;
}
.no-tasks {
    width: 100%;
    height: 100px;
    padding: 10px;
    padding-top: 80px;
    font-family: "montserrat", sans-serif;
    font-size: 1.3em;
    color: #9b9b9b;
    background-color: #fff;
    text-align: center;
}
@media (max-width: 1700px) {
    .md-hide {
        display: none;
    }
}
.sf-warningboxblank {
    width: 400px;
    margin-left: auto;
    margin-right: auto;
    height: calc(100vh - 300px);
}

.sf-warningbox {
    border: 1px solid #ffcc00; 
    background-color: #fff8e1; 
    padding: 16px;
    border-radius: 8px;
    margin: 20px 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.sf-warning__header {
    font-weight: bold;
    font-size: 1.2em;
    color: #e65100;
    margin-bottom: 8px;
}

.sf-warning__body {
    font-size: 1em;
    color: #5d4037;
    margin-bottom: 16px;
}

.sf-warningbox FluentButton {
    padding: 10px 20px;
    background-color: #d32f2f;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-weight: bold;
}

    .sf-warningbox FluentButton:hover {
        background-color: #b71c1c; 
    }

    .sf-warningbox FluentButton:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.5); 
    }

.normal-box {
    padding: 3px 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-bottom: 5px;
    font-family: "montserrat", sans-serif;
    font-size: 1em;
}
.trans-show {
    display:block;
}
.trans-hide {
    display:none;
}
.sf-make-button:hover {
    cursor:pointer;
}
.status-container {
    text-align:left;
}
.rotate-90 {
    transform: rotate(90deg) !important;
    transition: transform 0.3s ease;
}
.rotate-0 {
    transform: rotate(0deg);
    transition: transform 0.3s ease;
}
.sf-quicklist {
    width: 290px !important;
    overflow: hidden;
    transition: all 0.5s ease;
    height: calc(100vh - 137px);
}

.sf-quicklist-close {
    width: 1px !important;
    overflow: hidden;
    transition: all 0.5s ease;
}

.sf-quicklist .e-listview {
    padding: 0px !important;
    margin: 0px !important;
}

.e-listview .e-list-item {
    padding: 0px !important;
    margin: 0px !important;
    height: 25px !important;
}
.sf-searchquick {
    width: 240px !important;
}
.smallicon {
    position: relative !important;
    top: 3px !important;
    left: -2px !important;
}
.additional-links {
    transition: all .4s ease-in-out;
}
.nothingfound {
    font-family: "montserrat", sans-serif;
    font-size:.8em;
    text-align: center;
    padding-top: 15px !important;
    padding-bottom: 15px !important;
    color: #8a8a8a;
    font-style: italic;
    width: 100%;
}
.sf-add-button {
    color: #036ac4;
    font-size: .8em;
    font-weight: bold;
    filter: saturate(50%);
}
.sf-add-button svg {
    position:relative;
    top:4px;
}
    .sf-add-button:hover {
        filter: saturate(100%);
        cursor:pointer;
    }
.icotwe {
    position:relative;
    top:2px;
}
.tdtwe {
    padding: 0 !important;
    margin: 0 !important;
    border-bottom: 0 !important;
    filter: saturate(70%);
}
.csm {
    font-size: 10px;
    padding-top: 2px;
    padding-bottom: 2px;
}

.tag-container {
    height: 29px;
    overflow: hidden;
}
.emberToolBtn {
    border: 2px solid #ccc;
    display: inline-block;
    padding: 0px 10px;
    text-align: center;
    font-size: 10px;
    margin-right: 10px;
    color: #484848;
    background-color: #efefef;
}

    .emberToolBtn:hover {
        border: 2px solid #036ac4;
        background-color: #fff;
        color: #036ac4;
        box-shadow:0px 0px 8px #0003;
    }
.client-data {
    font-weight: 300;
    position: relative;
    left: -4px;
    top: -4px;
    font-style: normal;
    font-size: 1.125em;
    margin-right: 25px;
    text-decoration: none !important;
    color: #1e1e1e;
}
    
    .client-data:hover {
        color: #0f6cbd;
        cursor: pointer;
    }
.client-phonenumber {
    font-weight: 300;
    font-style: normal;
    font-size: 2.5em;
    position: relative;
    top: -10px;
    left: -5px;
    color: #8d8d8d;
    text-decoration: none !important;
}
.client-phonenumber a {
    text-decoration: none !important;
}
/*POssible Trash*/
.sjks {
    color: #c70000
}

.rotate-scale-down-hor {
    -webkit-animation: rotate-scale-down-hor 1s cubic-bezier(0.600, -0.280, 0.735, 0.045) infinite;
    animation: rotate-scale-down-hor 1.5s cubic-bezier(0.600, -0.280, 0.735, 0.545) infinite;
}
/* ----------------------------------------------
 * Generated by Animista on 2024-11-24 14:48:25
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation rotate-scale-down-hor
 * ----------------------------------------
 */
@-webkit-keyframes rotate-scale-down-hor {
    0% {
        -webkit-transform: scale(1) rotateX(0);
        transform: scale(1) rotateX(0);
        -webkit-filter: blur(0);
        filter: blur(0);
    }

    50% {
        -webkit-transform: scale(0.5) rotateX(-180deg);
        transform: scale(0.5) rotateX(-180deg);
        -webkit-filter: blur(20px);
        filter: blur(20px);
    }

    100% {
        -webkit-transform: scale(1) rotateX(-360deg);
        transform: scale(1) rotateX(0deg);
        -webkit-filter: blur(10px);
        filter: blur(10px);
    }
}

@keyframes rotate-scale-down-hor {
    0% {
        -webkit-transform: scale(1) rotateX(0);
        transform: scale(1) rotateX(0) rotateZ(0);
        -webkit-filter: blur(0);
        filter: blur(0px);
    }

    50% {
        -webkit-transform: scale(0.5) rotateX(-100deg);
        transform: scale(0.5) rotateY(0deg) rotateZ(360deg);
        -webkit-filter: blur(20px);
        filter: blur(20px);
    }

    100% {
        -webkit-transform: scale(1) rotateX(-180);
        transform: scale(1) rotateX(0) rotateZ(720deg);
        -webkit-filter: blur(0);
        filter: blur(0px);
    }
}

.top-loader {
    width: 100%;
    height: 50px;
    overflow: hidden;
    transition: all ease .25s;
    pointer-events: none;
}

.top-loader-True {
    opacity: 1;
    pointer-events: all;
}

.top-loader-False {
    opacity: 0;
    pointer-events: none;
}

.emptyLoader {
    width: 100%;
    height: 100vh;
    text-align: center;
    min-width: 700px;
}
#Layer_1 {
    width:400px;
    position:relative;
    top:25%;
}

.sftb-disabled {
    opacity: .4;
    pointer-events: none;
}
.spcr {
    width: 15px;
}
.toolbar-link-cal {
    text-decoration: none;
}
.toolbar-text-cal {
    color: #3c3c3c;
    font-family: "montserrat", sans-serif;
    font-size: .85em;
    width: 115px;
    text-align: center;
    -webkit-user-select: none; 
    -moz-user-select: none;
    -ms-user-select: none; 
    user-select: none;
}
.e-dropdownlist-disabled {
    font-size: .8em !important;
    font-weight: 400;
    font-weight: normal !important;
    background-color: transparent !important;
    border: 0px !important;
    padding-left: 3px !important;
    padding-right: 0px !important;
    padding-top: 2px !important;
    padding-bottom: 2px !important;
    font-family: "montserrat", sans-serif;
    font-size: .8em !important;
}
.drop-icon {
    opacity:.7;
}
.drop-icon:hover {
    opacity:1;
}
.quickfile {
    width: 100px;
    height: 140px;
    float: left;
    box-shadow: 0px 0px 6px #0009;
    padding: 5px;
    display: ruby;
    position: relative;
    border-radius: 5px;
    margin-left: 5px;
    margin-right: 5px;
}
.quickfile-name {
    position: absolute;
    z-index: 2;
    top: 5px;
    left: 12px;
    background-color: #ffffffd7;
    width: 86px;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    font-size: .8em;
    display: block;
    line-height: 13px;
    text-align: center;
    color: #484848;
    line-break:anywhere;
    pointer-events:none;
}
.quickfile-image {
    position:absolute;
    z-index:1;
    top:5px;
    left:5px;
    width:100px;
    height:129px;
}
.quickfile-buttons {
    position: absolute;
    z-index: 1;
    top: 130px;
    left: 5px;
    text-align:center;
    width:100px;
}
.quickfile-buttons__icon {
    opacity: .5;
    filter: saturate(0);
    margin-right: 1px;
    margin-left: 1px;
}
.quickfile-buttons__icon:hover {
    opacity: 1;
    filter: saturate(1);
    margin-right: 1px;
    margin-left: 1px;
    cursor:pointer;
}
    .quickfile-buttons__icon:active {
        position:relative;
        top:1px;
        left:1px;
    }
.warn {
    font-size: 1.3em;
    font-weight: bold;
    color:#dd4f45;
    z-index: 2;
    text-align:center;
}
.num {
    font-size: 1.7em;
    font-weight: bold;
    z-index: 2;
    position: relative;
    text-align: right;
}

.desc {
    /* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#000000+0,1f1d5e+100&0.84+0,0.87+54,0+100 */
    background: linear-gradient(to right, rgba(0,0,0,0.5) 0%,rgba(17,16,51,0.87) 54%,rgba(31,29,94,0) 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    text-align: left;
    position: relative;
    top: -11px;
    z-index: 1;
    padding-top: 10px;
    padding-left: 12px;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: #ffffff97;
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px;
    border-bottom: 1px solid #ffffff50;
    font-size: .8em;
}
.drop-icon {
    width: 100px;
    float: left;
}
.mhis {
    font-size: .8em;
    line-height: 11px;
    position: relative;
    top: -50px;
    left:10px;
    color: #7e9cb5;
    text-align:right;
    opacity:.4;
    transition: all ease .5s;
}
.mhis:hover {
    opacity:1;
}
.mhis u {
    text-decoration:none;
    color:#7d1fa8;
    font-size:.8em;
    font-weight:bold;
}

    .mhis i {
        font-stretch: condensed;
        font-size: .8em;
        font-style: normal !important;
        color: #757575;
    }
    .mhis b {
        font-weight:500;
        color:#e4500f;
        font-size: 1.3em;
    }
.hidden-uploader {
    width:100px !important;
    height: 150px !important;
    opacity: 1;
    border: 0px !important;

}
    .hidden-uploader input[type="file"] {
        opacity: .1;
        font-size: 2em;
    }

.chevron-indicator {
    position: absolute;
    font-size: 11px;
    font-weight: bold;
    color: #c03939;
    margin-left: -6px;
    margin-top: -8px;
}

.sf-button-small {
    font-size: .8em;
    padding: 5px 6px;
    font-family: 'Segoe UI';
    color: #0473ce;
    transition: all 0.2s ease-out;
    display: inline-flex;
    gap: 3px;
}

    .sf-button-small:hover {
        color: #004a87;
        background-color: #0473ce13;
        cursor: pointer;
    }

.sf-button-md {
    font-size: .95em;
    padding: 5px 6px;
    font-family: 'Segoe UI';
    color: #0473ce;
    transition: all 0.2s ease-out;
    display: inline-flex;
    gap: 3px;
}

    .sf-button-md:hover {
        color: #004a87;
        background-color: #0473ce13;
        cursor: pointer;
    }
.txt-column {
    font-family: "montserrat", sans-serif;
    font-weight: 300;
    font-size: .75em;
    color: #494949;
    text-transform: uppercase;
}
.fdg-stretch {
    height: calc(100% - 3rem);
    min-height: 300px;
    overflow-x: auto;
    overflow-y: hidden;
}
.home-list-stretch {
    height: calc(100% - 100px);
}
.fdg-link {
    text-decoration: none;
    font-weight: bold;
}

.fdg-link:hover {
    text-decoration: underline !important;
    font-weight: bold;
}
.fdg-col-id {
    font-family: "mono45-headline", monospace;
    font-size:1.1em;
    font-weight: normal;
    text-align: center;
    color: #9a9a9a;
}
.fdg-tinyicon {
    position: relative;
    top: 1px;
    left: 2px;
}
.fdg-btn-tiny {
    text-decoration: none;
    font-size: .9em;
    background-color: #e6e6e6;
    color:#282828;
    margin-right: 3px;
    padding-right:2px;
    border-radius: 4px;
    border:1px solid #ccc;
    opacity:.6;
}
.fdg-btn-tiny:hover {
    opacity:1;
}
.fdg-tinytxt {
    position: relative;
    top: -1px;
    left: -2px;
}

/* Added .toolbar-disabled styles */
.tb-link {
    color: #000;
    height: 24px;
    font-size: 13px;
    text-decoration: none;
    margin-right: 15px;
    opacity: .7;
}

    .tb-link:hover {
        opacity: 1;
        cursor: pointer;
    }

    .tb-link svg {
        fill: #000 !important;
    }

.tb-link-first {
    padding-left: 15px;
}
.tb-disabled {
    color: #0473ce !important; /* Set active color and use !important to override base */
    opacity: 1 !important; /* Ensure full opacity */
    cursor: default !important; /* Change cursor */
    pointer-events: none; /* Disable click events */
}

.tb-disabled svg {
    fill: #0473ce !important; /* Set active icon color */
}
.pricrazy {
    font-weight: bold;
    color: #e63946;
}

.prilate {
    font-weight: bold;
    color: #d97706;
}

.pritoday {
    font-weight: bold;
    color: #f59e0b;
}

.prinormal {
    color: #6b7280;
}

.prilow {
    color: #9ca3af;
}

.prino {
    color: #b2b2b2;
}

/* Top Bar Fire Animation System */
@keyframes topbar-infinite-scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-5760px);
    }
}

/* Standardized sections */
.std-module-title {
    /*Font*/
    font-family: "montserrat", sans-serif;
    font-size: 1rem;
    font-weight: 400;
    color: #6b6b6b;
    text-transform: uppercase;
    letter-spacing: 0em;
    /*Position*/
    position: relative;
    top: 8px;
    margin-bottom: 7px;
    font-style: italic; /* Force italic */
}
    .std-module-title b {
        font-weight: 900;
        letter-spacing: .1em;
        color: #898989;
        font-style: normal;
    }
.std-module-container {
    padding: 10px;
    border-left: 5px solid #ccc;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    border-top-right-radius: 8px;
    box-shadow: 0px 3px 8px #ccc;
    border-top: 1px solid #ccc;
    background: linear-gradient(165deg, rgba(204,204,204,1) 0%,rgba(242,242,242,1) 13%,rgba(0,0,0,0) 100%);
}

.std-module-container-alt {
    padding: 10px;
    background: linear-gradient(195deg, rgba(0,0,0,0.35) 0%,rgba(0,0,0,0) 50%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
    background-color: #fff;
    position: relative;
    top: 10px;
    left: -6px;
    width: calc(100% + 9px);
    padding: 10px 10px;
    transition: all 1s ease-out;
    border-top-right-radius: 100px;
    box-shadow: inset -10px 10px 15px #00000024;
    opacity: .4;
    min-height: 180px;
    overflow: hidden;
}

    .std-module-container-alt:hover {
        opacity: 1;
        background-color: #fff;
    }

/* Standardized contacts */
.m-contact-buttons {
    margin-left: auto;
}
.m-contact {
    font-family: "montserrat", sans-serif;
    width: calc(100% - 20px);
    padding: 5px;
    border-radius: 3px;
    box-shadow: 0px 0px 7px #1b8ce323;
}
.m-primary-True {
    border-left: 5px solid #1b8ce3;
    background-color: #fff;
}
.m-primary-False {
    border-left: 5px solid #bebebe;
    background-color: #f8f8f8;
}

/* Status Pills for Renewals */
.status-pill {
    display: inline-block;
    padding: 5px 8px 0px 8px;
    border-radius: 10px;
    font-size: 0.7em;
    font-weight: 600;
    font-family: "montserrat", sans-serif;
    height:15px;
}
.status-pill-text {
    width:55px;
    text-align: center;
    position: relative;
    left: -2px;
    top: 1px;
}
.status-none {
    background-color: #f5f5f5;
    color: #999;
}
.status-nonren {
    background-color: #c6b9b9;
    color: #857979;
    width: 124px;
    text-align: center;
}
.status-created {
    background-color: #e0e0e0;
    color: #666;
}

.status-started {
    background-color: #909aa3;
    color: white;
}

.status-submitted {
    background-color: #78828b;
    color: white;
}

.status-quoted {
    background-color: #3498db;
    color: white;
}

.status-proposed {
    background-color: #eb6d19;
    color: white;
}

.status-bound {
    background-color: #27ae60;
    color: white;
}

.status-issued {
    background-color: #1d8732;
    color: white;
}

.status-unknown {
    background-color: #95a5a6;
    color: white;
}
.unread-counter {
    position: relative;
    top: -18px;
    left: -14px !important;
    height: 10px;
    border: 1px solid #fff !important;
    box-shadow: 0px 0px 10px #000;
    animation: unread-pulse 2s infinite;
}

@keyframes unread-pulse {
    0% {
        opacity: 1;
    }
    25% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 1;
    }
}

.chat-btn-container {
    position: relative;
    top:2px;
    left: -8px;
    opacity: .8;
}
.chat-btn-container:hover {
    opacity: 1;
}