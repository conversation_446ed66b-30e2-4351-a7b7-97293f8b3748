﻿@inherits AppComponentBase
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.FluentUI.AspNetCore.Components
@using Surefire.Domain.Carriers.Models
@using Surefire.Domain.Carriers.Services
@using Surefire.Domain.Carriers.Components.Dialogs
@using Surefire.Domain.Shared.Services
@using Surefire.Domain.Shared.Components
@inject CarrierService CarrierService
@inject IJSRuntime JSRuntime
@inject SurefireDialogService DialogService

<div>
    <table class="credentials-table">
        <thead>
            <tr>
                <th class="txt-small" style="padding-right:15px; color:#ccc;">Staff</th>
                <th class="txt-small colu" style="color:#ccc;">Username</th>
                <th class="txt-small colp" style="color:#ccc;">Password</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            @foreach (var credential in Credentials)
            {
                string headshotImage = "/img/staff/" + credential.CreatedBy?.PictureUrl;
                <tr style="border-top:1px solid #ccc;">
                    <td rowspan="2" style="padding-right:15px;"><FluentPersona Initials="--" ImageSize="50px" Class="txt-persona" Image="@headshotImage" /></td>
                    <td style="font-size:14px;">
                        @credential.Username
                        <FluentIcon Value="@(new Icons.Regular.Size20.Copy())" OnClick="() => CopyToClipboard(credential.Username)" />
                    </td>
                    <td style="font-size:14px;">
                        @("*".PadLeft(credential.Password?.Length ?? 0, '*'))
                        <FluentIcon Value="@(new Icons.Regular.Size20.Copy())" OnClick="() => CopyToClipboard(credential.Password)" />
                    </td>
                    <td rowspan="2">
                        <FluentStack>
                            <FluentIcon Value="@(new Icons.Regular.Size24.Delete())" OnClick="() => ShowDeleteConfirmation(credential.CredentialId)" />
                            <FluentIcon Value="@(new Icons.Regular.Size24.Edit())" OnClick="() => ShowEditDialog(credential)" />
                        </FluentStack>
                    </td>
                </tr>
                <tr class="last-of-set">
                    <td colspan="2" class="txt-reg-sm" style="vertical-align: top;">@credential.Notes</td>
                </tr>
            }
        </tbody>
    </table>
</div>

<div style="height:8px;"></div>
<a class="sf-add-button" @onclick="ShowAddDialog"><FluentIcon Value="@(new Icons.Regular.Size20.AddCircle())" Color="Color.Custom" CustomColor="#036ac4" /> Add Credential</a>

<!-- Credential Dialog Component -->
<CredentialDialog @ref="credentialDialog"
@bind-Hidden="credentialDialogHidden"
@bind-Hidden:after="() => HandleCredentialDialogHiddenChanged(credentialDialogHidden)"
DialogId="carrier-credential-dialog" 
CarrierId="@CarrierId"
CredentialForEdit="@credentialToEdit"
OnSave="HandleCredentialSaved" />

<!-- Confirmation Dialog Component -->
<ConfirmationDialog @ref="confirmationDialog"
@bind-Hidden="confirmationDialogHidden"
@bind-Hidden:after="() => HandleConfirmationDialogHiddenChanged(confirmationDialogHidden)"
DialogId="credential-delete-confirmation"
Title="Delete Credential"
Message="Are you sure you want to delete this credential?"
ConfirmText="Delete"
CancelText="Cancel"
OnConfirm="ConfirmDelete" />

@code {

    [Parameter]
    public int CarrierId { get; set; }

    [Parameter]
    public ICollection<Credential> Credentials { get; set; }

    [Parameter]
    public EventCallback OnCredentialChanged { get; set; }

    private CredentialDialog credentialDialog;
    private ConfirmationDialog confirmationDialog;
    private bool confirmationDialogHidden = true;
    private bool credentialDialogHidden = true;
    private int credentialToDelete;
    private Credential? credentialToEdit;

    private void ShowDeleteConfirmation(int credentialId)
    {
        credentialToDelete = credentialId;
        confirmationDialogHidden = false;
        StateHasChanged();
    }

    private async Task ConfirmDelete(bool confirmed)
    {
        confirmationDialogHidden = true;
        if (confirmed)
        {
            try
            {
                await CarrierService.DeleteCredentialAsync(credentialToDelete);
                var credential = Credentials.FirstOrDefault(c => c.CredentialId == credentialToDelete);
                if (credential != null)
                {
                    Credentials.Remove(credential);
                }
                await OnCredentialChanged.InvokeAsync();
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error deleting credential: {ex.Message}");
            }
        }
        credentialToDelete = 0;
    }

    private void ShowAddDialog()
    {
        credentialToEdit = null;
        credentialDialogHidden = false;
        StateHasChanged();
    }

    private void ShowEditDialog(Credential credential)
    {
        credentialToEdit = credential;
        credentialDialogHidden = false;
        StateHasChanged();
    }

    private async Task HandleCredentialSaved(Credential credential)
    {
        credentialDialogHidden = true;
        credentialToEdit = null;
        await OnCredentialChanged.InvokeAsync();
    }

    private void HandleCredentialDialogHiddenChanged(bool isHidden)
    {

        Console.WriteLine($"Test: {isHidden}");
        credentialDialogHidden = isHidden;
        if (isHidden)
        {
            credentialToEdit = null;
        }
    }
    
    protected void HandleConfirmationDialogHiddenChanged(bool isHidden)
    {
        Console.WriteLine($"222Test: {isHidden}");
        confirmationDialogHidden = isHidden;
        if (isHidden)
        {
           credentialToDelete = 0;
        }
    }

    private async Task CopyToClipboard(string text)
    {
        await JSRuntime.InvokeVoidAsync("navigator.clipboard.writeText", text);
    }
}
