﻿@page "/businessdetailseditor/{ClientId:int}"
@inherits BusinessDetailsEditorBase

@* Keep @using statements relevant to the Razor markup *@
@using Surefire.Domain.OpenAI
@using Surefire.Domain.Clients.Models
@using Surefire.Domain.Shared.Models
@using Syncfusion.Blazor.InPlaceEditor
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.SmartComponents
@using Microsoft.FluentUI.AspNetCore.Components
@using Microsoft.FluentUI.AspNetCore.Components.Icons
@using Syncfusion.Blazor.Buttons

@if (BusinessDetails != null)
{
    <EditForm EditContext="@MyEditContext">
        <FluentStack Wrap="true" HorizontalGap="30">
            <div xs="3" Class="pol-section">
                <!--=====----- BASICS -----=====-->
                <span class="pol-section-title" style="border-left: 5px solid #046BC4;">Basics</span>
                <div class="pol-section-container">
                    <!-- Date Started -->
                    <span class="pol-name">Business Started<span class="copy-spcr" /></span>
                    <span class="pol-value">
                        <SfDatePicker TValue="DateTime?" @bind-Value="BusinessDetails.DateStarted" Placeholder="Date Started" FloatLabelType="FloatLabelType.Never" OnChange="@(args => SaveChanges())" />
                    </span><br />

                    <!-- Years Experience -->
                    <span class="pol-name">
                        Years of Experience
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.YearsExperience.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="n0" @bind-Value="@BusinessDetails.YearsExperience" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />

                    <!-- FEIN -->
                    <span class="pol-name">
                        FEIN
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.FEIN))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfTextBox @bind-Value="@BusinessDetails.FEIN" @onblur="SaveChanges"></SfTextBox>
                    </span><br />

                    <!-- Coverage History -->
                    <span class="pol-name">
                        Coverage History
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.InsuranceHistory))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfTextBox @bind-Value="@BusinessDetails.InsuranceHistory" @onblur="SaveChanges"></SfTextBox>
                    </span><br />

                    <!-- Number of Claims -->
                    <span class="pol-name">
                        Number of Claims
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.NumClaims.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="n0" @bind-Value="@BusinessDetails.NumClaims" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />
                </div>

                <!--=====----- CLASSIFICATIONS -----=====-->
                <div class="pol-column-spacer"></div>
                <span class="pol-section-title" style="border-left: 5px solid #1B8CE3;">Classifications</span><br />
                <div class="pol-section-container">
                    <!-- Legal Entity Type -->
                    <span class="pol-name">Legal Entity Type<span class="copy-spcr" /></span>
                    <span class="pol-value">
                        <SfDropDownList TValue="LegalEntityType?" TItem="LocalEnumData<LegalEntityType>" @bind-Value="@BusinessDetails.LegalEntityType" DataSource="@LegalEntityTypeList"
                                        FloatLabelType="FloatLabelType.Never" AllowFiltering="true" ShowClearButton="true">
                            <DropDownListEvents TValue="LegalEntityType?" TItem="LocalEnumData<LegalEntityType>" ValueChange="@(args => OnFieldChanged(nameof(BusinessDetails.LegalEntityType)))" />
                            <DropDownListFieldSettings Value="Value" Text="Text"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </span><br />

                    <!-- Business Type -->
                    <span class="pol-name">Business Type<span class="copy-spcr" /></span>
                    <span class="pol-value">
                        <SfDropDownList TValue="BusinessType?" TItem="LocalEnumData<BusinessType>" @bind-Value="@BusinessDetails.BusinessType" DataSource="@BusinessTypeList"
                                        FloatLabelType="FloatLabelType.Never" AllowFiltering="true" ShowClearButton="true">
                            <DropDownListEvents TValue="BusinessType?" TItem="LocalEnumData<BusinessType>" ValueChange="@(args => OnFieldChanged(nameof(BusinessDetails.BusinessType)))" />
                            <DropDownListFieldSettings Value="Value" Text="Text"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </span><br />

                    <!-- Short Description -->
                    <span class="pol-name">
                        Short Description
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.ShortDescription))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfTextBox @bind-Value="@BusinessDetails.ShortDescription" @onblur="SaveChanges"></SfTextBox>
                    </span><br />

                    <!-- Long Description -->
                    <span class="pol-name">
                        Long Description
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.LongDescription))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfTextBox @bind-Value="@BusinessDetails.LongDescription" Multiline="true" @onblur="SaveChanges"></SfTextBox>
                    </span><br />

                    <!-- Business Industry -->
                    <span class="pol-name">
                        SIC / Industry
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.BusinessIndustry))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfTextBox @bind-Value="@BusinessDetails.BusinessIndustry" @onblur="SaveChanges"></SfTextBox>
                    </span><br />

                    <!-- Business Specialty -->
                    <span class="pol-name">
                        NCAIS / Specialty
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.BusinessSpecialty))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfTextBox @bind-Value="@BusinessDetails.BusinessSpecialty" @onblur="SaveChanges"></SfTextBox>
                    </span>
                </div>

                <!--=====----- PRIMARY BUILDING -----=====-->
                <span class="pol-section-title" style="border-left: 5px solid #CF4740;">Primary Building</span>
                <div class="pol-section-container">
                    <!-- Year Built -->
                    <span class="pol-name">
                        Year Built
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.BuildingLocationYearBuilt.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="n0" @bind-Value="@BusinessDetails.BuildingLocationYearBuilt" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />

                    <!-- Square Footage -->
                    <span class="pol-name">
                        Square Footage
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.BuildingLocationSquareFootage.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="n0" @bind-Value="@BusinessDetails.BuildingLocationSquareFootage" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />

                    <!-- Number of Stories -->
                    <span class="pol-name">
                        Number of Stories
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.BuildingLocationNumberOfStories.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="n0" @bind-Value="@BusinessDetails.BuildingLocationNumberOfStories" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />

                    <!-- Sprinklered -->
                    <span class="pol-name">Sprinklered<span class="copy-spcr" /></span>
                    <span class="pol-value">
                        <SfCheckBox @bind-Checked="@BusinessDetails.BuildingLocationSprinklered" ValueChange="@( (Syncfusion.Blazor.Buttons.ChangeEventArgs<bool?> args) => OnFieldChanged(nameof(BusinessDetails.BuildingLocationSprinklered)) )"></SfCheckBox>
                    </span><br />

                    <!-- Monitored Security -->
                    <span class="pol-name">Monitored Security<span class="copy-spcr" /></span>
                    <span class="pol-value">
                        <SfCheckBox @bind-Checked="@BusinessDetails.BuildingLocationMonitoredSecurity" ValueChange="@((Syncfusion.Blazor.Buttons.ChangeEventArgs<bool?> args) => OnFieldChanged(nameof(BusinessDetails.BuildingLocationMonitoredSecurity)))"></SfCheckBox>
                    </span><br />
                </div>

            </div>

            <div xs="3">
                <!--=====----- EMPLOYEES -----=====-->
                <span class="pol-section-title" style="border-left: 5px solid #593294;">Employees</span>
                <div class="pol-section-container">
                    <!-- Number of Part-Time Employees -->
                    <span class="pol-name">
                        Part-Time
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.NumPartTimeEmployees.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="n0" @bind-Value="@BusinessDetails.NumPartTimeEmployees" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />

                    <!-- Number of Full-Time Employees -->
                    <span class="pol-name">
                        Full-Time
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.NumFullTimeEmployees.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="n0" @bind-Value="@BusinessDetails.NumFullTimeEmployees" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />
                </div>


                <!--=====----- PAYROLL -----=====-->
                <span class="pol-section-title" style="border-left: 5px solid #745DA1;">Payroll</span><br />
                <div class="pol-section-container">
                    <span class="pol-name">
                        Next Year
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedAnnualPayroll0.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedAnnualPayroll0" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />
                    <!-- Other Payroll fields follow the same pattern -->
                    <span class="pol-name">
                        Current
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedAnnualPayroll1.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedAnnualPayroll1" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />

                    <span class="pol-name">
                        Previous Year
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedAnnualPayroll2.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedAnnualPayroll2" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />

                    <span class="pol-name">
                        2 Years Ago
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedAnnualPayroll3.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedAnnualPayroll3" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />

                    <span class="pol-name">
                        3 Years Ago
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedAnnualPayroll4.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedAnnualPayroll4" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />
                </div>

                <!--=====----- GROSS SALES -----=====-->
                <span class="pol-section-title" style="border-left: 5px solid #FF6600;">Gross Sales</span><br />
                <div class="pol-section-container">
                    <span class="pol-name">
                        Next Year
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedGrossSales0.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedGrossSales0" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />
                    <!-- Other Gross Sales fields follow the same pattern -->
                    <span class="pol-name">
                        Current Year
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedGrossSales1.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedGrossSales1" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />

                    <span class="pol-name">
                        Last Year
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedGrossSales2.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedGrossSales2" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />

                    <span class="pol-name">
                        2 Years Ago
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedGrossSales3.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedGrossSales3" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />

                    <span class="pol-name">
                        3 Years Ago
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedGrossSales4.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.EstimatedGrossSales4" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />
                </div>
            </div>

            <div xs="3">

                <!--=====----- CONTRACTORS -----=====-->
                <span class="pol-section-title" style="border-left: 5px solid #1F1E61;">Contractors</span>
                <div class="pol-section-container">
                    <!-- License Type -->
                    <span class="pol-name">License Type<span class="copy-spcr" /></span>
                    <span class="pol-value">
                        <SfDropDownList TValue="LicenseType?" TItem="LocalEnumData<LicenseType>" @bind-Value="@BusinessDetails.LicenseType" DataSource="@LicenseTypeList"
                                        Placeholder="Select License Type" FloatLabelType="FloatLabelType.Never" AllowFiltering="true" ShowClearButton="true">
                            <DropDownListEvents TValue="LicenseType?" TItem="LocalEnumData<LicenseType>" ValueChange="@(args => OnFieldChanged(nameof(BusinessDetails.LicenseType)))" />
                            <DropDownListFieldSettings Value="Value" Text="Text"></DropDownListFieldSettings>
                        </SfDropDownList>
                    </span><br />

                    <!-- License Number -->
                    <span class="pol-name">
                        License Number
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.LicenseNumber))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfTextBox @bind-Value="@BusinessDetails.LicenseNumber" @onblur="SaveChanges"></SfTextBox>
                    </span><br />

                    <!-- Estimated Subcontracting Expenses -->
                    <span class="pol-name">
                        Sub Costs
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.EstimatedSubcontractingExpenses.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="c0" @bind-Value="@BusinessDetails.EstimatedSubcontractingExpenses" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />

                    <div class="pol-row">
                        <FluentStack HorizontalGap="0">
                            <span>Res</span>
                            <SfTextBox @bind-Value="@BusinessDetails.PercentResidential" @onblur="SaveChanges"></SfTextBox>
                            <span>Com</span>
                            <SfTextBox @bind-Value="@BusinessDetails.PercentCommercial" @onblur="SaveChanges"></SfTextBox>
                            <span>Public</span>
                            <SfTextBox @bind-Value="@BusinessDetails.PercentPublic" @onblur="SaveChanges"></SfTextBox>
                        </FluentStack>
                    </div><br />

                    <div class="pol-row">
                        <FluentStack HorizontalGap="0">
                            <span>New</span>
                            <SfTextBox @bind-Value="@BusinessDetails.PercentNewConstruction" @onblur="SaveChanges"></SfTextBox>
                            <span>Repair</span>
                            <SfTextBox @bind-Value="@BusinessDetails.PercentRemodelRepair" @onblur="SaveChanges"></SfTextBox>
                        </FluentStack>
                    </div><br />

                    <div class="pol-row">
                        <FluentStack HorizontalGap="0">
                            <span>Interior</span>
                            <SfTextBox @bind-Value="@BusinessDetails.PercentInterior" @onblur="SaveChanges"></SfTextBox>
                            <span>Exterior</span>
                            <SfTextBox @bind-Value="@BusinessDetails.PercentRemodelRepair" @onblur="SaveChanges"></SfTextBox>
                        </FluentStack>
                    </div>

                </div>

                <!--=====----- FINANCIALS -----=====-->
                <span class="pol-section-title" style="border-left: 5px solid #CF4740;">FINANCIALS</span>
                <div class="pol-section-container">
                    <!-- Annual Gross Sales -->
                    <span class="pol-name">
                        Annual Gross Sales
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.AnnualGrossSalesRevenueReceipts.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.AnnualGrossSalesRevenueReceipts" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />

                    <!-- Annual Payroll -->
                    <span class="pol-name">
                        Annual Payroll
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.AnnualPayrollHazardExposure.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.AnnualPayrollHazardExposure" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />

                    <!-- Business Property -->
                    <span class="pol-name">
                        Business Property
                        <span class="copy-btn" @onclick="@(x => CopyItem(BusinessDetails.BusinessPersonalPropertyBPP.ToString()))"><FluentIcon Value="@(new Icons.Regular.Size20.Copy())" Slot="start" Color="Color.Custom" CustomColor="#4a4a4a" /></span>
                    </span>
                    <span class="pol-value">
                        <SfNumericTextBox Format="c2" @bind-Value="@BusinessDetails.BusinessPersonalPropertyBPP" @onblur="SaveChanges"></SfNumericTextBox>
                    </span><br />
                </div>
            </div>
            <div style="width: 500px;">
                <div class="txt-section">POLICY DATA EXTRACTOR</div>

                <FluentTextField Placeholder="Paste XML here" @bind-Value="XmlContent" Multiline="true" Rows="5" Style="width:100%;" /><br />
                <FluentStack HorizontalGap="10">
                    <FluentButton OnClick="ExtractWordDocument">Word Doc Extract</FluentButton>
                    <FluentButton OnClick="ParseGenius">Extract Data from ACORD XML</FluentButton>
                    <FluentButton OnClick="SaveChanges">Save Form</FluentButton>
                </FluentStack>

                @if (!string.IsNullOrEmpty(JsonResponse))
                {
                    <div style="max-height: 475px; overflow-y: scroll; border: 1px solid #ccc; padding: 10px; color:#959595; width:100%; margin-top:10px;">
                        @((MarkupString)JsonResponse)
                    </div>
                }
                <div style="height: 25px;"></div>
                <div class="txt-section">SUPPLEMENTAL APP TRANSFER</div>
                <div class="flat-class-container flat-up">
                    <FluentStack>
                        <div class="drop-zone-acord" id="dzsupp">
                            @if (SupplementalAttachment != null)
                            {
                                <div class="attachment-thumbnail">
                                    <a href="/@SupplementalAttachment.LocalPath/@SupplementalAttachment.HashedFileName" target="_blank">
                                        <img src="@GetThumbnailPath(SupplementalAttachment)" class="thumb-icon" />
                                    </a>
                                    <div class="attachment-actions">
                                        <a @onclick="() => OnDeleteAttachmentClicked(SupplementalAttachment)"><FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" /></a>
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" />
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" />
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="attachment-thumbnail">
                                    <img src="/img/icons/document-upload.png" class="thumb-icon" />
                                </div>
                            }
                            <SfUploader @ref="UploaderSupplemental" DropEffect="DropEffect.Copy" DropArea="#dzsupp">
                                <UploaderTemplates><Template Context="file"></Template></UploaderTemplates>
                                <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "supplemental"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                            </SfUploader>
                        </div>
                        <div class="drop-zone-acord" id="dztarget">
                            @if (TargetAttachment != null)
                            {
                                <div class="attachment-thumbnail">
                                    <a href="/@TargetAttachment.LocalPath/@TargetAttachment.HashedFileName" target="_blank">
                                        <img src="@GetThumbnailPath(TargetAttachment)" class="thumb-icon" />
                                    </a>
                                    <div class="attachment-actions">
                                        <a @onclick="() => OnDeleteAttachmentClicked(TargetAttachment)"><FluentIcon Value="@(new Icons.Regular.Size20.Delete())" Color="Color.Custom" CustomColor="#036ac4" Class="delbtn" /></a>
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Folder())" Color="Color.Custom" CustomColor="#036ac4" Class="folderbtn" />
                                        <FluentIcon Value="@(new Icons.Regular.Size20.Open())" Color="Color.Custom" CustomColor="#036ac4" Class="openbtn" />
                                    </div>
                                </div>
                            }
                            else
                            {
                                <div class="attachment-thumbnail">
                                    <img src="/img/icons/document-upload.png" class="thumb-icon" />
                                </div>
                            }
                            <SfUploader @ref="UploaderTargetForm" DropEffect="DropEffect.Copy" DropArea="#dztarget">
                                <UploaderTemplates><Template Context="file"></Template></UploaderTemplates>
                                <UploaderEvents OnActionComplete="@(args => OnUploadSlotSuccess(args, "target"))" OnFailure="OnUploadFailure" ValueChange="OnChange"></UploaderEvents>
                            </SfUploader>
                        </div>
                    </FluentStack>
                    <span style="margin-left: 10px;">
                        <select @bind="selectedForm" style="height:32px; border-radius:4px;">
                            <option value="Autodetect">Autodetect</option>
                            <option value="Kinsale">Kinsale</option>
                            <option value="Amwins">Amwins</option>
                            <option value="CS">CS</option>
                        </select>
                    </span>
                    @if (showFirebar)
                    {
                        <Surefire.Domain.Shared.Components.FireBar Progress="@firebarProgress" Width="100px" Height="100px" />
                        <div style="margin-top: 10px; padding: 8px; background-color: #f0f8ff; border-radius: 4px; font-size: 0.9em;">@firebarStatus</div>
                    }
                    @if (advancedMode)
                    {
                        <button class="extract-btn" @onclick="async () => await ExtractSupplementalFormTextAsync(GetFormFiles(selectedForm).pdfFile)" disabled="@(SupplementalAttachment == null)">ExtractFormText</button>
                        <button class="extract-btn" @onclick="async () => await ExtractSupplementalFieldsFromTextAndSaveAsync(GetFormFiles(selectedForm).fieldsFile, GetFormFiles(selectedForm).promptFile)" disabled="@(SupplementalAttachment == null)">ExtractFieldsAI</button>
                        <button class="extract-btn" @onclick="async () => await FillSupplementalPdfAndAttachAsync(GetFormFiles(selectedForm).pdfFile)" disabled="@(SupplementalAttachment == null)">Fill PDF & Attach</button>
                        <button class="extract-btn" @onclick="async () => await PopulateDetailsFromLatestJsonAsync()">Populate Details from JSON</button>
                    }
                    <button class="extract-btn" @onclick="RunSupplementalWorkflowAsync" disabled="@(SupplementalAttachment == null)">Auto Extract & Attach</button>
                    
                    @if (IsExtractingFields)
                    {
                        <span style="margin-left:10px;"><FluentProgressRing Size="ProgressRingSize.Small" /></span>
                    }
                    @if (!string.IsNullOrEmpty(ExtractedTextAttachmentUrl))
                    {
                        <div><a href="@ExtractedTextAttachmentUrl" target="_blank">View Extracted Text</a></div>
                    }
                    @if (!string.IsNullOrEmpty(ExtractedJsonAttachmentUrl))
                    {
                        <div><a href="@ExtractedJsonAttachmentUrl" target="_blank">View Cleaned JSON</a></div>
                    }
                    <br />
                    <div class="copy-btn" @onclick="ToggleAdvancedMode">Advanced Mode</div>
                </div>
            </div>
        </FluentStack>
    </EditForm>
}
else if (BusinessDetails == null)
{
    <p><em>Loading business details...</em></p>
}