﻿using System;
using System.Collections.Generic;

namespace Surefire.Domain.DocuSign
{
    /// <summary>
    /// Represents a DocuSign envelope
    /// </summary>
    public class DocuSignEnvelope
    {
        /// <summary>
        /// Unique identifier for the envelope
        /// </summary>
        public string EnvelopeId { get; set; } = string.Empty;
        
        /// <summary>
        /// Email subject of the envelope
        /// </summary>
        public string Subject { get; set; } = string.Empty;
        
        /// <summary>
        /// Current status of the envelope (e.g., "sent", "delivered", "completed", "declined")
        /// </summary>
        public string Status { get; set; } = string.Empty;
        
        /// <summary>
        /// Date and time when the envelope was sent
        /// </summary>
        public DateTime? SentDateTime { get; set; }
        
        /// <summary>
        /// Date and time when the envelope was completed (null if not completed)
        /// </summary>
        public DateTime? CompletedDateTime { get; set; }
        
        /// <summary>
        /// Name of the envelope sender
        /// </summary>
        public string SenderName { get; set; } = string.Empty;
        
        /// <summary>
        /// Email of the envelope sender
        /// </summary>
        public string SenderEmail { get; set; } = string.Empty;
        
        /// <summary>
        /// Name of the primary recipient
        /// </summary>
        public string RecipientName { get; set; } = string.Empty;
        
        /// <summary>
        /// Email of the primary recipient
        /// </summary>
        public string RecipientEmail { get; set; } = string.Empty;
        
        /// <summary>
        /// Name of the folder containing this envelope
        /// </summary>
        public string FolderName { get; set; } = string.Empty;
        
        /// <summary>
        /// List of all recipients for this envelope
        /// </summary>
        public List<DocuSignRecipient> Recipients { get; set; } = new List<DocuSignRecipient>();
        
        /// <summary>
        /// Local copy of documents (if downloaded)
        /// </summary>
        public List<DocuSignDocument> Documents { get; set; } = new List<DocuSignDocument>();
        
        /// <summary>
        /// Date and time when the envelope will expire (null if no expiration)
        /// </summary>
        public DateTime? ExpirationDateTime { get; set; }
        
        /// <summary>
        /// Any custom metadata associated with the envelope
        /// </summary>
        public Dictionary<string, string> CustomFields { get; set; } = new Dictionary<string, string>();
        
        /// <summary>
        /// Time when the envelope data was last refreshed from the API
        /// </summary>
        public DateTime LastRefreshed { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// Gets a formatted string with sent date and status
        /// </summary>
        public string StatusWithDate => $"{Status} on {SentDateTime?.ToLocalTime().ToString("g") ?? "N/A"}";
        
        /// <summary>
        /// Gets whether the envelope is in a terminal state (completed, declined, voided)
        /// </summary>
        public bool IsTerminalState => 
            Status.Equals("completed", StringComparison.OrdinalIgnoreCase) || 
            Status.Equals("declined", StringComparison.OrdinalIgnoreCase) || 
            Status.Equals("voided", StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Represents a recipient of a DocuSign envelope
    /// </summary>
    public class DocuSignRecipient
    {
        /// <summary>
        /// Unique identifier for the recipient
        /// </summary>
        public string RecipientId { get; set; } = string.Empty;
        
        /// <summary>
        /// Name of the recipient
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// Email of the recipient
        /// </summary>
        public string Email { get; set; } = string.Empty;
        
        /// <summary>
        /// Type of recipient (e.g., "signer", "cc", "inpersonSigner")
        /// </summary>
        public string Type { get; set; } = string.Empty;
        
        /// <summary>
        /// Current status of the recipient (e.g., "sent", "delivered", "completed")
        /// </summary>
        public string Status { get; set; } = string.Empty;
        
        /// <summary>
        /// Routing order for the recipient
        /// </summary>
        public int RoutingOrder { get; set; } = 1;
        
        /// <summary>
        /// Date and time when the recipient signed (null if not signed)
        /// </summary>
        public DateTime? SignedDateTime { get; set; }
        
        /// <summary>
        /// Date and time when the envelope was delivered to the recipient
        /// </summary>
        public DateTime? DeliveredDateTime { get; set; }
    }

    /// <summary>
    /// Represents a document in a DocuSign envelope
    /// </summary>
    public class DocuSignDocument
    {
        /// <summary>
        /// Unique identifier for the document
        /// </summary>
        public string DocumentId { get; set; } = string.Empty;
        
        /// <summary>
        /// Name of the document
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// MIME type of the document
        /// </summary>
        public string Type { get; set; } = string.Empty;
        
        /// <summary>
        /// Local path to the document file if downloaded
        /// </summary>
        public string LocalPath { get; set; } = string.Empty;
        
        /// <summary>
        /// Order of the document in the envelope
        /// </summary>
        public int Order { get; set; } = 1;
        
        /// <summary>
        /// Number of pages in the document
        /// </summary>
        public int PageCount { get; set; }
    }
} 