@page "/Forms/CertificateRequest/{RequestId:int}"
@using Surefire.Domain.Forms.Models
@using Surefire.Domain.Forms.Services
@using Syncfusion.Blazor.Notifications
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Spinner
@inject FormService FormService
@inject NavigationManager NavigationManager
@inject StateService StateService
@inherits AppComponentBase

<PageTitle>Certificate Request Details</PageTitle>

<div class="row">
    <div class="col-md-8">
        <h2 class="page-title">
            Certificate Request 
            <span class="certificate-id">@RequestId</span>
            <span class="status-badge status-@(certificateRequest?.Status.ToLower() ?? "pending")">@(certificateRequest?.Status ?? "Loading...")</span>
        </h2>
    </div>
    <div class="col-md-4 text-right">
        <div class="button-group">
            @if (certificateRequest != null && certificateRequest.Status != "Completed" && certificateRequest.Status != "Approved")
            {
                <SfButton CssClass="e-success" @onclick='() => UpdateStatus(certificateRequest.CertificateRequestId, "Approved")'>
                    <span class="material-icons">check_circle</span>&nbsp;Approve
                </SfButton>
                <SfButton CssClass="e-warning" @onclick='() => UpdateStatus(certificateRequest.CertificateRequestId, "Review")'>
                    <span class="material-icons">pending</span>&nbsp;Mark for Review
                </SfButton>
                <SfButton CssClass="e-danger" @onclick='() => UpdateStatus(certificateRequest.CertificateRequestId, "Rejected")'>
                    <span class="material-icons">cancel</span>&nbsp;Reject
                </SfButton>
            }
            <SfButton @onclick="NavigateBack">
                <span class="material-icons">arrow_back</span>&amp;nbsp;Back
            </SfButton>
        </div>
    </div>
</div>

@if (isLoading)
{
    <div class="loading-container">
        <SfSpinner Size="50" Type="SpinnerType.Material"></SfSpinner>
        <p>Loading certificate request...</p>
    </div>
}
else if (hasError)
{
    <div class="error-container">
        <span class="material-icons error-icon">error_outline</span>
        <h3>Error Loading Certificate Request</h3>
        <p>@errorMessage</p>
        <div class="error-actions">
            <SfButton @onclick="RetryLoading" CssClass="e-primary">
                <span class="material-icons">refresh</span>&amp;nbsp;Retry
            </SfButton>
            <SfButton @onclick="NavigateBack">
                <span class="material-icons">arrow_back</span>&amp;nbsp;Back
            </SfButton>
        </div>
    </div>
}
else if (certificateRequest == null)
{
    <div class="alert alert-danger">
        Certificate request not found or could not be loaded.
    </div>
}
else
{
    <div class="certificate-container">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>Certificate Holder Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="info-group">
                            <label>Holder Name:</label>
                            <div>@certificateRequest.HolderName</div>
                        </div>
                        @if (!string.IsNullOrEmpty(certificateRequest.HolderAttention))
                        {
                            <div class="info-group">
                                <label>Attention:</label>
                                <div>@certificateRequest.HolderAttention</div>
                            </div>
                        }
                        <div class="info-group">
                            <label>Address:</label>
                            <div>
                                @certificateRequest.HolderAddress<br />
                                @certificateRequest.HolderCity, @certificateRequest.HolderState @certificateRequest.HolderZip
                            </div>
                        </div>
                        @if (!string.IsNullOrEmpty(certificateRequest.HolderEmail))
                        {
                            <div class="info-group">
                                <label>Email:</label>
                                <div><a href="mailto:@certificateRequest.HolderEmail">@certificateRequest.HolderEmail</a></div>
                            </div>
                        }
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>Client Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="info-group">
                            <label>Company:</label>
                            <div>@certificateRequest.ClientCompany</div>
                        </div>
                        <div class="info-group">
                            <label>Contact:</label>
                            <div>@certificateRequest.ClientName</div>
                        </div>
                        @if (!string.IsNullOrEmpty(certificateRequest.ClientEmail))
                        {
                            <div class="info-group">
                                <label>Email:</label>
                                <div><a href="mailto:@certificateRequest.ClientEmail">@certificateRequest.ClientEmail</a></div>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(certificateRequest.ClientPhone))
                        {
                            <div class="info-group">
                                <label>Phone:</label>
                                <div>@certificateRequest.ClientPhone</div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        @if (certificateRequest.HasProject)
        {
            <div class="row mt-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3>Project Information</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-group">
                                        <label>Project Name:</label>
                                        <div>@certificateRequest.ProjectName</div>
                                    </div>
                                    @if (!string.IsNullOrEmpty(certificateRequest.ProjectNumber))
                                    {
                                        <div class="info-group">
                                            <label>Project Number:</label>
                                            <div>@certificateRequest.ProjectNumber</div>
                                        </div>
                                    }
                                    @if (!string.IsNullOrEmpty(certificateRequest.ProjectAddress))
                                    {
                                        <div class="info-group">
                                            <label>Project Address:</label>
                                            <div>@certificateRequest.ProjectAddress</div>
                                        </div>
                                    }
                                </div>
                                <div class="col-md-6">
                                    @if (certificateRequest.StartDate.HasValue)
                                    {
                                        <div class="info-group">
                                            <label>Start Date:</label>
                                            <div>@certificateRequest.StartDate.Value.ToShortDateString()</div>
                                        </div>
                                    }
                                    @if (certificateRequest.EndDate.HasValue)
                                    {
                                        <div class="info-group">
                                            <label>End Date:</label>
                                            <div>@certificateRequest.EndDate.Value.ToShortDateString()</div>
                                        </div>
                                    }
                                    @if (!string.IsNullOrEmpty(certificateRequest.Description))
                                    {
                                        <div class="info-group">
                                            <label>Description:</label>
                                            <div>@certificateRequest.Description</div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>Certificate Options</h3>
                    </div>
                    <div class="card-body">
                        <div class="checkbox-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="primaryNonContributary" checked="@certificateRequest.PrimaryNonContributary" disabled>
                                <label class="form-check-label" for="primaryNonContributary">
                                    Primary &amp; Non-Contributary
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="waiverSubrogationWC" checked="@certificateRequest.WaiverSubrogationWC" disabled>
                                <label class="form-check-label" for="waiverSubrogationWC">
                                    Waiver of Subrogation (Workers Comp)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="waiverSubrogationGL" checked="@certificateRequest.WaiverSubrogationGL" disabled>
                                <label class="form-check-label" for="waiverSubrogationGL">
                                    Waiver of Subrogation (General Liability)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="emailToHolder" checked="@certificateRequest.EmailToHolder" disabled>
                                <label class="form-check-label" for="emailToHolder">
                                    Email to Certificate Holder
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="emailCopyToClient" checked="@certificateRequest.EmailCopyToClient" disabled>
                                <label class="form-check-label" for="emailCopyToClient">
                                    Email Copy to Client
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>Status Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="info-group">
                            <label>Request Date:</label>
                            <div>@certificateRequest.RequestDate.ToString("g")</div>
                        </div>
                        @if (certificateRequest.ImportedDate.HasValue)
                        {
                            <div class="info-group">
                                <label>Imported Date:</label>
                                <div>@certificateRequest.ImportedDate.Value.ToString("g")</div>
                            </div>
                        }
                        <div class="info-group">
                            <label>Current Status:</label>
                            <div><span class="status-badge <EMAIL>()">@certificateRequest.Status</span></div>
                        </div>
                        <div class="info-group">
                            <label>Notes:</label>
                            <div class="notes-container">
                                <textarea id="notesEditor" class="form-control" @bind="notes" rows="4"></textarea>
                                <button class="btn btn-primary mt-2" @onclick="SaveNotes">Save Notes</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<SfToast ID="statusToast" Timeout="3000" @ref="ToastObj">
    <ToastPosition X="Right" Y="Bottom"></ToastPosition>
</SfToast>

<style>
    .page-title {
        font-size: 1.8rem;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }

    .certificate-id {
        background-color: #e9ecef;
        padding: 4px 8px;
        border-radius: 4px;
        margin-left: 10px;
        font-size: 1rem;
        color: #495057;
    }

    .card {
        margin-bottom: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        background: linear-gradient(to right, rgba(61,61,61,0.8) 0%,rgba(32,30,96,0.8) 49%,rgba(61,61,61,0.8) 100%);
        color: #b8adef;
        border-top-left-radius: 8px !important;
        border-top-right-radius: 8px !important;
        padding: 10px 15px;
    }

    .card-header h3 {
        margin: 0;
        font-size: 1.2rem;
    }

    .card-body {
        padding: 15px;
    }

    .info-group {
        margin-bottom: 15px;
    }

    .info-group label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 3px;
        display: block;
    }

    .button-group {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        margin-top: 10px;
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 300px;
    }

    .error-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 300px;
        padding: 20px;
        text-align: center;
    }

    .error-container .error-icon {
        font-size: 48px;
        color: #dc3545;
        margin-bottom: 15px;
    }

    .error-container h3 {
        margin-bottom: 10px;
        color: #721c24;
    }

    .error-container p {
        margin-bottom: 20px;
        color: #6c757d;
        max-width: 600px;
    }

    .error-actions {
        display: flex;
        gap: 10px;
    }

    .status-badge {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8rem;
        color: white;
        margin-left: 10px;
    }

    .status-approved {
        background-color: #036ac4;
    }

    .status-rejected {
        background-color: #d94c43;
    }

    .status-review {
        background-color: #72174a;
    }

    .status-completed {
        background-color: #2ca55e;
    }

    .status-pending {
        background-color: #6c757d;
    }

    .notes-container {
        margin-top: 5px;
    }

    .form-check {
        margin-bottom: 10px;
    }

    .form-check-input:disabled {
        opacity: 0.7;
    }

    .form-check-label {
        padding-left: 5px;
    }
</style>

@code {
    [Parameter]
    public int RequestId { get; set; }

    private CertificateRequest certificateRequest;
    private bool isLoading = true;
    private string notes;
    private SfToast ToastObj;
    private bool hasError = false;
    private string errorMessage;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            isLoading = true;
            certificateRequest = await FormService.GetExternalCertificateRequestByIdAsync(RequestId);
            
            if (certificateRequest != null)
            {
                notes = certificateRequest.Notes;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading certificate request: {ex.Message}");
            hasError = true;
            errorMessage = ex.Message;
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task UpdateStatus(int requestId, string status)
    {
        try
        {
            bool success = await FormService.UpdateCertificateRequestStatusAsync(requestId, status, notes);
            
            if (success)
            {
                certificateRequest.Status = status;
                
                if (status == "Approved" || status == "Completed")
                {
                    certificateRequest.ImportedDate = DateTime.UtcNow;
                }
                
                await ToastObj.ShowAsync(new ToastModel
                {
                    Title = "Success",
                    Content = $"Certificate request status updated to {status}",
                    CssClass = "e-toast-success"
                });
            }
            else
            {
                await ToastObj.ShowAsync(new ToastModel
                {
                    Title = "Error",
                    Content = "Failed to update certificate request status",
                    CssClass = "e-toast-danger"
                });
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error updating status: {ex.Message}");
            await ToastObj.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = $"Error: {ex.Message}",
                CssClass = "e-toast-danger"
            });
        }
    }

    private async Task SaveNotes()
    {
        try
        {
            bool success = await FormService.UpdateCertificateRequestStatusAsync(
                RequestId, 
                certificateRequest.Status, 
                notes);
            
            if (success)
            {
                certificateRequest.Notes = notes;
                
                await ToastObj.ShowAsync(new ToastModel
                {
                    Title = "Success",
                    Content = "Notes updated successfully",
                    CssClass = "e-toast-success"
                });
            }
            else
            {
                await ToastObj.ShowAsync(new ToastModel
                {
                    Title = "Error",
                    Content = "Failed to save notes",
                    CssClass = "e-toast-danger"
                });
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error saving notes: {ex.Message}");
            await ToastObj.ShowAsync(new ToastModel
            {
                Title = "Error",
                Content = $"Error: {ex.Message}",
                CssClass = "e-toast-danger"
            });
        }
    }

    private void NavigateBack()
    {
        NavigationManager.NavigateTo("/");
    }

    private async Task RetryLoading()
    {
        await OnInitializedAsync();
    }
} 