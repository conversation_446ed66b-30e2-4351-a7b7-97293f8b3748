﻿.normal-box {
    padding: 3px 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin-bottom: 5px;
    font-family: "montserrat", sans-serif;
    font-size: 1em;
}
.m-contact {
    padding: 5px;
    border: 1px solid #e4e4e4;
    border-left: 5px solid #ccc;
    border-radius: 3px;
    width: 100%;
    margin-bottom: 11px;
    font-family: "montserrat", sans-serif;
}

.m-contact-small {
    padding: 5px;
    font-family: "montserrat", sans-serif;
    font-size: .85em;
    line-height: 11px;
}

.sf-calltable {
    line-height: 10px;
    border-collapse: collapse;
}

    .sf-calltable td {
        text-align: left;
        padding: 5px 0px;
        padding: 5px 0px;
    }
.phone-icon {
    position: relative;
    top: 0px;
    left: 5px;
}

.phone-icon-cell {
    width: 40px;
}
.phone-longago {
    font-size: .8em;
    position: relative;
    top: -3px;
    left: 10px;
    color: #808080;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.phonetxt {
    font-size: .8em;
    position: relative;
    top: -3px;
    left: 10px;
    color: #808080;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.pay-lognum {
    font-size: 1.8em;
    font-weight: 300;
    color: #0f6cbd;
    min-width: 155px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.phone-none {
    font-size: 1em;
    color: #b7b7b7;
    position: relative;
    top: -8px;
}