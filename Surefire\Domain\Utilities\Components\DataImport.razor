@namespace Surefire.Domain.Utilities.Components
@using Surefire.Domain.Shared.Components
@using Surefire.Domain.Shared.Models
@using Surefire.Domain.Shared.Services
@using Surefire.Domain.Policies.Models
@using Surefire.Domain.Policies.Services
@using Surefire.Domain.Clients.Models
@using Surefire.Domain.Clients.Services
@using Surefire.Domain.Attachments.Models
@using Surefire.Domain.Attachments.Services
@using Microsoft.FluentUI.AspNetCore.Components
@using System.Xml
@using System.Xml.Linq
@using System.Text.Json
@inject PolicyService PolicyService
@inject ClientService ClientService
@inject AttachmentService AttachmentService
@inject IMessageService MessageService
@inject IJSRuntime JS

<div class="data-import-container">
    <div class="message-bar-container">
        <FluentMessageBarProvider />
    </div>

    <div class="data-import-layout">
        <div class="data-import-main">
            <div class="import-section">
                <div class="xml-input-section">
                    <FluentLabel Typo="Typography.Body">XML Data</FluentLabel>
                    <FluentTextArea @bind-Value="xmlData"
                                    Placeholder="Paste your XML data here..."
                                    Rows="10"
                                    Style="width: 100%; margin-bottom: 20px;" />
                </div>

                <div class="import-options">
                    <h4>Import Options</h4>

                    <div class="option-group">
                        <FluentCheckbox @bind-Value="workCompEnabled"
                                        Label="Work Comp" />

                        @if (workCompEnabled && workCompPolicies.Any())
                        {
                            <div class="policy-selection">
                                <FluentLabel Typo="Typography.Body">Select Work Comp Policy</FluentLabel>
                                <FluentSelect Items="@workCompPolicies" @bind-SelectedOption="selectedWorkCompPolicy" TOption="Policy" OptionText="@(tg => tg.PolicyNumber)" />
                            </div>
                        }
                        else if (workCompEnabled && !workCompPolicies.Any())
                        {
                            <div class="no-policies-message">
                                <p class="warning-text">No Work Comp policies found for this client.</p>
                            </div>
                        }
                    </div>

                    <div class="option-group">
                        <FluentCheckbox @bind-Value="autoEnabled"
                                        Label="Auto" />

                        @if (autoEnabled && autoPolicies.Any())
                        {
                            <div class="policy-selection">
                                <FluentLabel Typo="Typography.Body">Select Auto Policy</FluentLabel>
                                <FluentSelect Items="@autoPolicies" @bind-SelectedOption="selectedAutoPolicy" TOption="Policy" OptionText="@(tg => tg.PolicyNumber)" />
                            </div>
                        }
                        else if (autoEnabled && !autoPolicies.Any())
                        {
                            <div class="no-policies-message">
                                <p class="warning-text">No Auto policies found for this client.</p>
                            </div>
                        }
                    </div>

                    <div class="option-group">
                        <FluentCheckbox @bind-Value="glEnabled"
                                        Label="General Liability" />

                        @if (glEnabled && glPolicies.Any())
                        {
                            <div class="policy-selection">
                                <FluentLabel Typo="Typography.Body">Select GL Policy</FluentLabel>
                                <FluentSelect Items="@glPolicies" @bind-SelectedOption="selectedGLPolicy" TOption="Policy" OptionText="@(tg => tg.PolicyNumber)" />
                            </div>
                        }
                        else if (glEnabled && !glPolicies.Any())
                        {
                            <div class="no-policies-message">
                                <p class="warning-text">No General Liability policies found for this client.</p>
                            </div>
                        }
                    </div>

                    <div class="option-group">
                        <FluentCheckbox @bind-Value="updateAllMatchingPolicies"
                                        Label="Update All Matching Policies" />
                        <div class="option-description">
                            <small>Automatically find and update all policies in the XML that match client policy numbers (overrides individual selections above)</small>
                        </div>
                    </div>

                    <div class="option-group">
                        <FluentCheckbox @bind-Value="saveAsAttachment"
                                        Label="Save Data as Attachment" />
                        <div class="option-description">
                            <small>Save the processed XML data as an attachment in the client's syncdata folder</small>
                        </div>
                    </div>
                </div>
                <div class="import-actions">
                    <FluentButton Appearance="Appearance.Accent"
                                  OnClick="HandleImport"
                                  Disabled="@(!CanImport)">
                        <FluentIcon Value="@(new Icons.Filled.Size20.ArrowUpload())" />
                        Import Data
                    </FluentButton>

                    <FluentButton Appearance="Appearance.Lightweight"
                                  OnClick="ClearForm"
                                  Style="margin-left: 10px;">
                        <FluentIcon Value="@(new Icons.Filled.Size20.Dismiss())" />
                        Clear
                    </FluentButton>
                </div>
            </div>
        </div>

        <div class="debug-panel">
            <div class="debug-header">
                <h4>Debug Output</h4>
                <FluentButton Appearance="Appearance.Lightweight"
                              OnClick="ClearDebugOutput"
                              Style="padding: 4px 8px; font-size: 12px;">
                    Clear
                </FluentButton>
            </div>
            <div class="debug-content">
                @if (debugMessages.Any())
                {
                    @foreach (var message in debugMessages)
                    {
                        <div class="debug-message @message.Type.ToLower()">
                            <span class="debug-time">@message.Timestamp.ToString("HH:mm:ss.fff")</span>
                            <span class="debug-text">@message.Message</span>
                        </div>
                    }
                }
                else
                {
                    <div class="debug-empty">No debug output yet...</div>
                }
            </div>
        </div>
    </div>
</div> 