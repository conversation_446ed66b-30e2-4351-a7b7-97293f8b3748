@namespace Surefire.Domain.Chat.Components
@using Surefire.Data
@using Surefire.Domain.Chat
@using Surefire.Domain.Chat.Services
@using Surefire.Domain.Shared.Helpers
@using Surefire.Domain.Shared.Services
@using Surefire.Domain.Ember
@using System.Threading.Tasks
@using System.Collections.ObjectModel
@using Microsoft.AspNetCore.SignalR.Client
@using Microsoft.FluentUI.AspNetCore.Components
@using Microsoft.EntityFrameworkCore
@using Microsoft.Extensions.DependencyInjection
@implements IAsyncDisposable
@inject NavigationManager NavigationManager
@inject StateService StateService
@inject ChatService ChatService
@inject IJSRuntime JSRuntime
@inject SharedService SharedService
@inject EmberService EmberService
@inject PhoneLookupService PhoneLookupService
@inject SmsMessageService SmsMessageService
@inject IServiceProvider ServiceProvider

<div class="chopper-messaging">
    <div class="chopper-header">
        <div class="chopper-actions">
            <a class="flu-btn flu-btn-md accent @(_staffChatVis ? "active" : "")" @onclick="ShowStaffChat">
                <FluentIcon Value="@(new Icons.Regular.Size24.Organization())" Color="Color.Fill" />
                <span class="flu-btn-text">Staff</span>
                @if (StateService.UnreadStaffMessageCount > 0)
                {
                    <span class="unread-badge">@StateService.UnreadStaffMessageCount</span>
                }
            </a>
            <a class="flu-btn flu-btn-md accent @(_webChatVisible ? "active" : "")" @onclick="ShowWebChat">
                <FluentIcon Value="@(new Icons.Regular.Size24.Globe())" Color="Color.Fill" />
                <span class="flu-btn-text">Web</span>
            </a>
            <a class="flu-btn flu-btn-md accent @(_smsChatVisible ? "active" : "")" @onclick="LoadSmsConversations">
                <FluentIcon Value="@(new Icons.Regular.Size24.PeopleChat())" Color="Color.Fill" />
                <span class="flu-btn-text">Clients</span>
                @if (StateService.UnreadSmsMessageCount > 0)
                {
                    <span class="unread-badge">@StateService.UnreadSmsMessageCount</span>
                }
            </a>
            <a class="flu-btn flu-btn-md accent" @onclick="ToggleNewConversation">
                <FluentIcon Value="@(new Icons.Regular.Size24.Add())" Color="Color.Fill" />
                <span class="flu-btn-text">New</span>
            </a>
        </div>
        
        <div class="chopper-title">
            @if (_staffChatVis)
            {
                <h3>Staff Chat</h3>
            }
            else if (_webChatVisible)
            {
                <h3>Web Chat</h3>
            }
            else if (_smsChatVisible)
            {
                @if (showConversationList)
                {
                    <h3>Conversation List</h3>
                }
                else if (currentConversationLookup?.IsFound == true)
                {
                    <h3>Chat with @currentConversationLookup.DisplayName</h3>
                }
                else
                {
                    <h3>Chat with @StringHelper.FormatPhoneNumber(targetPhoneNumber)</h3>
                }
            }
        </div>
    </div>

    <div class="message-container">
        
        @if (showNewConversationInput)
        {
            <div class="new-conversation-panel">
                <h5>New Conversation</h5>
                <div class="new-conversation-input">
                    <FluentStack Orientation="Orientation.Horizontal" Gap="8">
                        <FluentTextArea @bind-Value="newConversationPhoneNumber" 
                                       Placeholder="Enter phone number (e.g., +15551234567)" 
                                       @onkeydown="HandleNewConversationKeyDown" />
                        <a class="flu-btn flu-btn-md accent" @onclick="StartNewConversation">
                            <FluentIcon Value="@(new Icons.Regular.Size16.Send())" Color="Color.Custom" CustomColor="#9d9999" />
                            Start
                        </a>
                        <a class="flu-btn flu-btn-md neutral" @onclick="ToggleNewConversation">
                            <FluentIcon Value="@(new Icons.Regular.Size16.Dismiss())" Color="Color.Custom" CustomColor="#9d9999" />
                            Cancel
                        </a>
                    </FluentStack>
                </div>
            </div>
        }
        
        <div class="chat-content @(showNewConversationInput ? "pushed-down" : "")">
        
        @if (_staffChatVis == true)
        {
            <div class="message-container-insidepadding">
                <div class="chat-header">
                    <div class="chat-title">
                        <div class="chat-info">
                            <small>Last updated: @lastStaffChatUpdate.ToString("HH:mm:ss")</small>
                        </div>
                    </div>
                    <div class="chat-actions">
                        <a class="flu-btn-sm neutral" @onclick="RefreshStaffChat">
                            <FluentIcon Value="@(new Icons.Regular.Size16.ArrowSync())" Color="Color.Custom" CustomColor="#9d9999" />
                            Refresh
                        </a>
                        <a class="flu-btn-sm neutral" @onclick="ClearStaffChat">
                            <FluentIcon Value="@(new Icons.Regular.Size16.Delete())" Color="Color.Custom" CustomColor="#9d9999" />
                            Clear
                        </a>
                    </div>
                </div>
                @if (isLoading)
                {
                    <div class="loading-messages">
                        <FluentProgressRing />
                        <span>Loading messages...</span>
                    </div>
                }
                else if (messages.Count == 0)
                {
                    <div class="no-messages">
                        <p>No messages yet</p>
                    </div>
                }
                else
                {
                    <div class="messages-list" style="margin-bottom: 6px;">
                        @foreach (var message in messages)
                        {
                            <div class="@(message.IsFromCurrentUser ? "message-item outgoing" : "message-item incoming")">
                                @if (!message.IsFromCurrentUser)
                                {
                                    <div class="message-avatar">
                                        @if (!string.IsNullOrEmpty(message.UserPictureUrl))
                                        {
                                            <img src="/img/staff/@message.UserPictureUrl" alt="@message.UserFullName" title="@message.UserFullName" />
                                        }
                                        else
                                        {
                                            <div class="avatar-placeholder">
                                                @(string.IsNullOrEmpty(message.UserFullName) ? "?" : message.UserFullName.Substring(0, 1).ToUpper())
                                            </div>
                                        }
                                    </div>
                                }
                                <div class="message-content">
                                    @if (!message.IsFromCurrentUser && !string.IsNullOrEmpty(message.UserFullName))
                                    {
                                        <div class="message-sender">@message.UserFullName</div>
                                    }
                                    <p>@message.Content</p>
                                    <span class="message-time">@message.Timestamp.ToString("g")</span>
                                </div>
                            </div>
                        }
                    </div>
                }
            </div>
            <div class="message-input">
                <FluentStack>
                    <span class="message-input-textarea"><input type="text" @bind-value="newMessage" @bind-value:event="oninput" placeholder="Type a message..." @onkeydown="HandleKeyDown" /></span>
                    <a class="flu-btn" @onclick="SendMessage">
                        <FluentIcon Value="@(new Icons.Regular.Size16.Send())" Color="Color.Custom" CustomColor="#9d9999" />
                    </a>
                </FluentStack>
            </div>
        }

        @if (_webChatVisible == true)
        {
            <div class="web-chat-placeholder">
                <div class="placeholder-content">
                    <FluentIcon Value="@(new Icons.Regular.Size48.Globe())" Color="Color.Custom" CustomColor="#9d9999" />
                    <h4>Web Chat</h4>
                    <p>Web chat functionality coming soon...</p>
                </div>
            </div>
        }

        @if (_smsChatVisible == true)
        {
            @if (showConversationList)
            {
                <div class="conversation-list">
                    @if (showDebug)
                    {
                        <div class="debug-info">
                            <p>Debug: Conversations count: @conversations.Count, Loading: @isLoadingConversations</p>
                            @foreach (var conv in conversations.Take(3))
                            {
                                <p>Debug: @conv.PhoneNumber -> @(conv.ContactName ?? "No contact")</p>
                            }
                        </div>
                    }
                    @if (isLoadingConversations)
                    {
                        <div class="loading-messages">
                            <FluentProgressRing />
                            <span>Loading conversations...</span>
                        </div>
                    }
                    else if (conversations.Count == 0)
                    {
                        <div class="no-messages">
                            <p>No conversations found (@conversations.Count total)</p>
                            <a class="flu-btn flu-btn-md neutral" @onclick="ForceRefreshConversations">
                                Try Again
                            </a>
                        </div>
                    }
                    else
                    {
                        <div class="conversation-grid">
                            @foreach (var conv in FilteredConversations())
                            {
                                <div class="conversation-item @(conv.UnreadCount > 0 ? "unread" : "")" @onclick="() => SelectConversation(conv.PhoneNumber)">
                                    <div class="conversation-details">
                                        <div class="conversation-phone">
                                            <FluentIcon Value="@(new Icons.Regular.Size20.Phone())" Color="Color.Custom" CustomColor="#9d9999" />
                                            <div class="conversation-phone-info">
                                                <div class="conversation-name">
                                                    @if (conv.IsContactFound)
                                                    {
                                                        <span class="contact-name">@conv.DisplayName</span>
                                                        @if (!string.IsNullOrEmpty(conv.ContactTitle))
                                                        {
                                                            <span class="contact-title">@conv.ContactTitle</span>
                                                        }
                                                    }
                                                    else
                                                    {
                                                        <span class="phone-number">@StringHelper.FormatPhoneNumber(conv.PhoneNumber)</span>
                                                    }
                                                </div>
                                                <div class="conversation-phone-number">
                                                    <small>@StringHelper.FormatPhoneNumber(conv.PhoneNumber)</small>
                                                </div>
                                            </div>
                                            @if (conv.UnreadCount > 0)
                                            {
                                                <span class="unread-badge">@conv.UnreadCount</span>
                                            }
                                        </div>
                                        <div class="conversation-preview">
                                            @(string.IsNullOrEmpty(conv.LastMessageText) ? "No messages" : StringHelper.TruncateText(conv.LastMessageText, 40))
                                        </div>
                                    </div>
                                    <div class="conversation-time">
                                        <small>@StringHelper.FormatDateTimeDifference(conv.LastMessageTime)</small>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                </div>
            }

            @if (isLoadingSms && !showConversationList)
            {
                <div class="loading-messages">
                    <FluentProgressRing />
                    <span>Loading SMS messages...</span>
                </div>
            }
            else if (!showConversationList)
            {
                <div class="current-conversation">
                    <div class="conversation-phone-number">
                        <FluentIcon Value="@(new Icons.Regular.Size20.Phone())" Color="Color.Custom" CustomColor="#9d9999" />
                        <small>@StringHelper.FormatPhoneNumber(targetPhoneNumber)</small>
                    </div>
                    <div class="current-conversation-info">
                        @if (currentConversationLookup?.IsFound == true)
                        {
                            <div class="conversation-name">
                                <span class="contact-name">@currentConversationLookup.ContactName</span>
                            </div>
                        }
                    </div>
                </div>
                <div class="action-buttons-conv">
                    <a class="flu-btn-sm neutral" @onclick="@(() => showDebug = !showDebug)">
                        <FluentIcon Value="@(new Icons.Regular.Size16.Bug())" Color="Color.Custom" CustomColor="#9d9999" />
                        @(showDebug ? "Hide Debug" : "Debug")
                    </a>
                </div>
                <div class="messages-list" style="margin-bottom: -12px;">
                    @if (smsMessages.Count == 0)
                    {
                        <div class="no-messages">
                            <p>No messages with @targetPhoneNumber</p>
                        </div>
                    }
                    else
                    {
                        @foreach (var message in smsMessages.OrderBy(m => m.Timestamp))
                        {
                            <div class="@(message.IsInbound ? "message-item incoming" : "message-item outgoing")">
                                <div class="message-content">
                                    <p>@message.Text</p>
                                    <span class="message-time">@message.Timestamp.ToString("g")</span>
                                </div>
                                @if (message.IsInbound)
                                {
                                    <div class="message-confirmation">
                                        @if (message.ConfirmedBy != null)
                                        {
                                            <div class="confirmation-circle confirmed" title="Confirmed by @(message.ConfirmedByUser?.FullName ?? "Unknown")">
                                                @if (!string.IsNullOrEmpty(message.ConfirmedByUser?.PictureUrl))
                                                {
                                                    <img src="/img/staff/@message.ConfirmedByUser.PictureUrl" alt="@message.ConfirmedByUser.FullName" />
                                                }
                                                else
                                                {
                                                    <span>@(message.ConfirmedByUser?.FullName?.Substring(0, 1).ToUpper() ?? "?")</span>
                                                }
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="confirmation-circle unconfirmed" @onclick="() => ConfirmMessage(message.RingCentralId)" title="Click to confirm">
                                                <FluentIcon Value="@(new Icons.Regular.Size12.Checkmark())" Color="Color.Custom" CustomColor="#9d9999" />
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                        }
                    }
                </div>
                <div class="message-input">
                    <FluentStack>
                        <span class="message-input-textarea"><input type="text" @bind-value="newSmsMessage" @bind-value:event="oninput" placeholder="Type a message..." @onkeydown="HandleSmsSendKeyDown" /></span>
                        <a class="flu-btn" @onclick="SendSmsMessage">
                            <FluentIcon Value="@(new Icons.Regular.Size16.Send())" Color="Color.Custom" CustomColor="#9d9999" />
                        </a>
                    </FluentStack>
                </div>
            }

            @if (showDebug && apiDebugInfo.Count > 0)
            {
                <div class="debug-panel">
                    <h5>Debug Info</h5>
                    @foreach (var info in apiDebugInfo.OrderBy(i => i.Key))
                    {
                        <div class="debug-item">
                            <strong>@info.Key:</strong> @info.Value
                        </div>
                    }
                    <a class="flu-btn flu-btn-md neutral" @onclick="@(() => apiDebugInfo.Clear())" Size="Size.Small">
                        Clear
                    </a>
                </div>
            }
        }
        </div>
    </div>
</div>

@code {
    [Parameter] public EventCallback OnMinimize { get; set; }

    private bool isLoading = true;
    private bool showDebug = false;
    private bool isLoadingSms = false;
    protected bool _smsChatVisible = false;
    protected bool _staffChatVis = true;
    protected bool _webChatVisible = false;
    private bool showPhoneInput = false;
    private bool showConversationList = false;
    private bool isLoadingConversations = false;
    private bool showNewConversationInput = false;
    private bool isComponentRendered = false;
    private bool isDisposed = false;
    private HubConnection? hubConnection;
    private List<SmsMessageEntity> smsMessages = new();
    private List<ConversationInfo> conversations = new();
    private ObservableCollection<MessageItem> messages = new();
    private Dictionary<string, string> apiDebugInfo = new Dictionary<string, string>();
    private string newSmsMessage = "";
    private string newMessage = "";
    private string newPhoneNumber = "";
    private string conversationFilter = "";
    private string newConversationPhoneNumber = "";
    private string targetPhoneNumber = "17146187735";
    private DateTime lastStaffChatUpdate = DateTime.Now;
    private DateTime lastNotificationTime = DateTime.MinValue;
    private static readonly TimeSpan NotificationCooldown = TimeSpan.FromMinutes(5);
    private PhoneLookupResult? currentConversationLookup;
    private Dictionary<string, PhoneLookupResult> phoneLookupCache = new();
    private Timer? smsRefreshTimer; // SMS auto-refresh timer
    private static readonly TimeSpan SmsRefreshInterval = TimeSpan.FromSeconds(15);
    private DateTime lastSmsRefresh = DateTime.MinValue;
    private HashSet<string> sentMessageIds = new();
    private static readonly int MaxSentMessageIds = 100;
    private Dictionary<string, DateTime> optimisticMessageTimes = new();
    private static readonly TimeSpan OptimisticMessageTimeout = TimeSpan.FromMinutes(2);

    // PRIMARY MAINS --------------------------------------------------------------------------/
    protected override async Task OnInitializedAsync()
    {
        Console.WriteLine("ChopperMessaging: OnInitializedAsync started");

        // Set staff chat as default view
        _staffChatVis = true;
        _smsChatVisible = false;
        _webChatVisible = false;

        await LoadMessages();
        await InitializeSignalR();
        StartSmsRefreshTimer();
        
        // Load initial unread counts for SMS messages
        await LoadInitialUnreadCounts();
    }
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            isComponentRendered = true;

            // Test if JavaScript functions are available
            try
            {
                await JSRuntime.InvokeVoidAsync("eval", "console.log('ChopperMessaging: JavaScript runtime is ready')");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ChopperMessaging: JavaScript runtime not ready: {ex.Message}");
            }
        }
        await base.OnAfterRenderAsync(firstRender);
    }
    private async Task InitializeSignalR()
    {
        try
        {
            // Initialize SignalR connection for real-time messaging
            hubConnection = new HubConnectionBuilder()
                .WithUrl(NavigationManager.ToAbsoluteUri("/messagingHub"))
                .WithAutomaticReconnect()
                .Build();

            // Handler for receiving staff messages
            hubConnection.On<MessageItem>("ReceiveStaffMessage", (message) =>
            {
                var processingId = Guid.NewGuid().ToString()[..8];
                // Skip processing if component is disposed
                if (isDisposed) 
                {
                    return;
                }

                var currentUser = StateService.CurrentUser;
                message.IsFromCurrentUser = message.UserId == currentUser?.Id;
                messages.Add(message);
                lastStaffChatUpdate = DateTime.Now;

                // Increment unread count if message is from another user
                if (!message.IsFromCurrentUser && (!StateService.IsChopperVisible || !_staffChatVis))
                {
                    StateService.IncrementUnreadStaffMessages();
                }

                // Only call StateHasChanged if not disposed
                if (!isDisposed)
                {
                    InvokeAsync(StateHasChanged);
                }

                // Send Windows notification for messages from other users (with cooldown)
                if (!message.IsFromCurrentUser && !isDisposed)
                {
                    _ = Task.Run(async () =>
                    {
                        if (isDisposed) return;

                        await InvokeAsync(async () => 
                        {
                            if (isDisposed) return;

                            // Check if enough time has passed since last notification
                            var now = DateTime.Now;
                            if (now - lastNotificationTime >= NotificationCooldown)
                            {
                                try
                                {
                                    // Send Windows notification via EmberService
                                    var notificationParams = new List<string>
                                    {
                                        message.UserName ?? "Unknown",
                                        message.Content ?? "New message",
                                        message.UserFullName ?? "Staff Member"
                                    };
                                    
                                    await EmberService.RunEmberFunction("ShowStaffChat", notificationParams);
                                    lastNotificationTime = now;
                                    
                                    Console.WriteLine($"Staff chat notification sent for message from {message.UserFullName}");
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Error sending staff chat notification: {ex.Message}");
                                }
                            }
                            else
                            {
                                var timeRemaining = NotificationCooldown - (now - lastNotificationTime);
                                Console.WriteLine($"Staff chat notification skipped - cooldown active for {timeRemaining:mm\\:ss} more");
                            }
                        });
                    });
                }

                // Play notification sound
                if (!message.IsFromCurrentUser && !isDisposed)
                {
                    _ = Task.Run(async () =>
                    {
                        if (isDisposed) return;

                        await InvokeAsync(async () => 
                        {
                            if (isDisposed) return;

                            await PlayNotificationSoundWithRetry(processingId);
                        });
                    });
                }

                // Scroll to bottom after receiving message
                if (_staffChatVis && isComponentRendered && !isDisposed)
                {
                    _ = Task.Run(async () =>
                    {
                        if (isDisposed) return;

                        await Task.Delay(100);

                        if (isDisposed) return;

                        await InvokeAsync(async () => 
                        {
                            if (!isDisposed)
                            {
                                await ScrollToBottom();
                            }
                        });
                    });
                }
            });

            // Handler for receiving SMS messages
            hubConnection.On<SmsMessage>("ReceiveSmsMessage", async (message) =>
            {
                var processingId = Guid.NewGuid().ToString()[..8];
                
                // Skip processing if component is disposed
                if (isDisposed) 
                {
                    return;
                }

                // Check if this is a duplicate message we already sent
                if (!message.IsInbound && sentMessageIds.Contains(message.Id))
                {
                    Console.WriteLine($"Skipping duplicate outbound message with ID: {message.Id}");
                    return;
                }

                // Store message in database first
                var storedMessage = await SmsMessageService.StoreSmsMessageAsync(message);
                if (storedMessage == null)
                {
                    Console.WriteLine($"Failed to store SMS message in database: {message.Id}");
                    return;
                }

                // Check if we already have a similar message (to prevent duplicates from background service)
                var existingMessage = smsMessages.FirstOrDefault(m => 
                    m.PhoneNumber == message.PhoneNumber && 
                    m.Text == message.Text && 
                    Math.Abs((m.Timestamp - message.Timestamp).TotalSeconds) < 30); // Within 30 seconds
                
                if (existingMessage != null)
                {
                    Console.WriteLine($"Replacing optimistic message with real message - ID: {existingMessage.RingCentralId} -> {message.Id}");
                    // Replace the optimistic message with the real one
                    var index = smsMessages.IndexOf(existingMessage);
                    if (index >= 0)
                    {
                        smsMessages[index] = storedMessage;
                        
                        // Remove from optimistic tracking since it's been replaced
                        optimisticMessageTimes.Remove(existingMessage.RingCentralId);
                        
                        // Clean up old optimistic messages
                        CleanupOptimisticMessages();
                        
                        if (!isDisposed)
                        {
                            InvokeAsync(StateHasChanged);
                        }
                    }
                    return;
                }

                Console.WriteLine($"Received SMS message: ID={message.Id}, Phone={message.PhoneNumber}, Inbound={message.IsInbound}, Text={message.Text?.Substring(0, Math.Min(message.Text?.Length ?? 0, 30))}");

                // Normalize phone numbers for consistent comparison
                var normalizedReceivedPhone = StringHelper.NormalizePhoneNumber(message.PhoneNumber);
                var normalizedTargetPhone = StringHelper.NormalizePhoneNumber(targetPhoneNumber);
                
                // Add message to current conversation if it matches (using normalized phone numbers)
                if (normalizedReceivedPhone == normalizedTargetPhone && _smsChatVisible)
                {
                    Console.WriteLine($"ChopperMessaging: Adding message to UI - ID: {message.Id}, Phone: {message.PhoneNumber}, Inbound: {message.IsInbound}, Current target: {targetPhoneNumber}, SMS visible: {_smsChatVisible}");
                    smsMessages.Add(storedMessage);
                    
                    // Clean up old optimistic messages
                    CleanupOptimisticMessages();
                    
                    // Only call StateHasChanged if not disposed
                    if (!isDisposed)
                    {
                        InvokeAsync(StateHasChanged);
                    }

                    // Scroll to bottom after receiving message
                    if (isComponentRendered && !isDisposed)
                    {
                        _ = Task.Run(async () =>
                        {
                            if (isDisposed) return;

                            await Task.Delay(100);

                            if (isDisposed) return;

                            await InvokeAsync(async () => 
                            {
                                if (!isDisposed)
                                {
                                    await ScrollToBottom();
                                }
                            });
                        });
                    }
                }

                // Update conversation list if it's visible
                if (showConversationList && !isDisposed)
                {
                    _ = Task.Run(async () =>
                    {
                        if (isDisposed) return;

                        await InvokeAsync(async () =>
                        {
                            if (!isDisposed)
                            {
                                await RefreshConversationList();
                            }
                        });
                    });
                }
                
                // Also update conversation list if SMS chat is visible but conversation list is not shown
                // This ensures new conversations appear when they come in
                if (_smsChatVisible && !showConversationList && !isDisposed)
                {
                    _ = Task.Run(async () =>
                    {
                        if (isDisposed) return;

                        await InvokeAsync(async () =>
                        {
                            if (!isDisposed)
                            {
                                // Invalidate cache to force refresh on next load
                                ChatService.InvalidateAllSmsCaches();
                            }
                        });
                    });
                }

                // Update unconfirmed counts in StateService if message is inbound
                if (message.IsInbound)
                {
                    var unconfirmedCounts = await SmsMessageService.GetUnconfirmedCountsByPhoneAsync();
                    StateService.UpdateUnconfirmedSmsCounts(unconfirmedCounts);
                }

                // Play notification sound for inbound messages
                if (message.IsInbound && !isDisposed)
                {
                    _ = Task.Run(async () =>
                    {
                        if (isDisposed) return;

                        await InvokeAsync(async () => 
                        {
                            if (isDisposed) return;

                            await PlayNotificationSoundWithRetry(processingId);
                        });
                    });
                }
            });

            // Handle connection state changes
            hubConnection.Closed += async (error) =>
            {
                // Don't try to reconnect if component is disposed
                if (isDisposed) return;
            };

            hubConnection.Reconnecting += async (error) =>
            {
                Console.WriteLine($"SignalR reconnecting: {error?.Message}");
            };

            hubConnection.Reconnected += async (connectionId) =>
            {
                // Rejoin the group if not disposed
                if (!isDisposed)
                {
                    try
                    {
                        await hubConnection.SendAsync("JoinStaffChat");
                        // Also rejoin SMS chat if currently viewing one
                        if (_smsChatVisible && !string.IsNullOrEmpty(targetPhoneNumber))
                        {
                            await hubConnection.SendAsync("JoinSmsChat", targetPhoneNumber);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error rejoining chat groups: {ex.Message}");
                    }
                }
            };

            await hubConnection.StartAsync();

            // Join the StaffChat group
            await hubConnection.SendAsync("JoinStaffChat");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error initializing SignalR for messaging: {ex.Message}");
        }
    }

    // Message Handling -----------------------------------------------------------------------/
    private async Task LoadMessages()
    {
        try
        {
            isLoading = true;

            // Load messages from the chat service
            var chatMessages = ChatService.GetStaffChatMessages();
            var currentUser = StateService.CurrentUser;

            // Convert to ObservableCollection and set IsFromCurrentUser correctly
            messages = new ObservableCollection<MessageItem>(
                chatMessages.Select(m => new MessageItem
                {
                    Content = m.Content,
                    Timestamp = m.Timestamp,
                    IsFromCurrentUser = m.UserId == currentUser?.Id,
                    UserId = m.UserId,
                    UserName = m.UserName,
                    UserFullName = m.UserFullName,
                    UserPictureUrl = m.UserPictureUrl
                })
            );

            lastStaffChatUpdate = DateTime.Now;
            isLoading = false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading messages: {ex.Message}");
            isLoading = false;
        }
    }
    private async Task SendMessage()
    {
        if (string.IsNullOrWhiteSpace(newMessage))
        {
            return;
        }

        try
        {
            var currentUser = StateService.CurrentUser;
            var sentMessage = newMessage;

            // Clear input after sending
            newMessage = "";

            // Send via SignalR if connection is active
            if (hubConnection?.State == HubConnectionState.Connected)
            {
                await hubConnection.SendAsync("SendStaffMessage", sentMessage, 
                    currentUser?.Id, 
                    currentUser?.UserName, 
                    currentUser?.FullName, 
                    currentUser?.PictureUrl);

                // Scroll to bottom after sending
                await Task.Delay(100);
                await ScrollToBottom();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error sending message: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
    private async Task LoadSmsMessages()
    {
        try
        {
            isLoadingSms = true;
            showConversationList = false;
            apiDebugInfo.Clear();

            // Get messages from database with confirmation status
            var dbMessages = await SmsMessageService.GetConversationMessagesAsync(targetPhoneNumber);
            
            // Convert to SmsMessage for compatibility with existing code
            var apiMessages = await ChatService.GetAllSmsMessagesAsync();
            
            // Merge database messages with API messages to ensure we have all messages
            var allMessages = new List<SmsMessageEntity>();
            
            // Add database messages first
            allMessages.AddRange(dbMessages);
            
            // Add any API messages that aren't in the database
            if (apiMessages?.Messages != null)
            {
                foreach (var apiMessage in apiMessages.Messages.Where(m => m.PhoneNumber == targetPhoneNumber))
                {
                    var existingDbMessage = dbMessages.FirstOrDefault(m => m.RingCentralId == apiMessage.Id);
                    if (existingDbMessage == null)
                    {
                        // Store new message in database
                        var storedMessage = await SmsMessageService.StoreSmsMessageAsync(apiMessage);
                        if (storedMessage != null)
                        {
                            allMessages.Add(storedMessage);
                        }
                    }
                }
            }

            // Sort by timestamp and update UI
            smsMessages = allMessages.OrderBy(m => m.Timestamp).ToList();
            
            // Ensure all confirmed messages have their ConfirmedByUser properly loaded
            // This is needed because messages added from API might not have this data loaded
            await EnsureAllConfirmedUsersLoaded(smsMessages);
            
            apiDebugInfo["Source"] = "Database + API";
            apiDebugInfo["Database Messages"] = dbMessages.Count.ToString();
            apiDebugInfo["Total Messages"] = smsMessages.Count.ToString();
            apiDebugInfo["Target Number"] = targetPhoneNumber;

            isLoadingSms = false;
            
            // Look up contact information for the current conversation
            await LookupCurrentConversationContact();
        }
        catch (Exception ex)
        {
            apiDebugInfo["Exception"] = ex.Message;
            Console.WriteLine($"Error loading SMS messages: {ex.Message}");
            isLoadingSms = false;
        }
    }
    private async Task SendSmsMessage()
    {
        if (string.IsNullOrWhiteSpace(newSmsMessage))
            return;

        try
        {
            var messageText = newSmsMessage;

            // Clear input after sending
            newSmsMessage = "";

            // Create optimistic message for immediate UI update
            var optimisticMessage = new SmsMessageEntity
            {
                RingCentralId = Guid.NewGuid().ToString(),
                PhoneNumber = StringHelper.NormalizePhoneNumber(targetPhoneNumber),
                Text = messageText,
                Timestamp = DateTime.UtcNow, // Use UTC to match RingCentral timestamps
                IsInbound = false,
                ConfirmedBy = null,
                ConfirmedOn = null
            };

            // Track this message ID to prevent duplicates
            sentMessageIds.Add(optimisticMessage.RingCentralId);
            CleanupSentMessageIds();

            // Track optimistic message for cleanup
            optimisticMessageTimes[optimisticMessage.RingCentralId] = DateTime.UtcNow;

            // Add to UI immediately
            smsMessages.Add(optimisticMessage);
            StateHasChanged();

            // Send via SignalR if connection is active
            if (hubConnection?.State == HubConnectionState.Connected)
            {
                await hubConnection.SendAsync("SendSmsMessage", StringHelper.NormalizePhoneNumber(targetPhoneNumber), messageText, optimisticMessage.RingCentralId);
                
                // Scroll to bottom after sending
                await Task.Delay(100);
                await ScrollToBottom();
            }
            else
            {
                // Fallback to direct service call
                var success = await ChatService.SendSmsAsync(targetPhoneNumber, messageText);
                if (!success)
                {
                    Console.WriteLine("Failed to send SMS message");
                    // Remove optimistic message if failed
                    smsMessages.Remove(optimisticMessage);
                    StateHasChanged();
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error sending SMS message: {ex.Message}");
        }
    }
    private async Task LoadSmsConversations()
    {
        try
        {
            // Switch to SMS chat view if currently on staff chat or web chat
            if (_staffChatVis || _webChatVisible)
            {
                _smsChatVisible = true;
                _staffChatVis = false;
                _webChatVisible = false;
                
                // Reset unread SMS message count when opening SMS chat
                StateService.ResetUnreadSmsMessages();
            }
            
            isLoadingConversations = true;
            showConversationList = true;
            StateHasChanged();

            // Check cache first
            var cachedConversations = ChatService.GetCachedConversationList();
            var isCacheValid = ChatService.IsConversationListCacheValid();
            
            if (cachedConversations != null && isCacheValid)
            {
                conversations = cachedConversations;
                isLoadingConversations = false;
                return;
            }

            // Use a very old date to get all messages (same as background service)
            var allMessages = await ChatService.GetAllSmsMessagesAsync(DateTime.MinValue);
            
            if (allMessages?.Messages != null)
            {                
                // Show all unique phone numbers found
                var uniquePhones = allMessages.Messages.Select(m => m.PhoneNumber).Distinct().ToList();
                for (int i = 0; i < Math.Min(uniquePhones.Count, 10); i++)
                {
                    var phone = uniquePhones[i];
                    var messageCount = allMessages.Messages.Count(m => m.PhoneNumber == phone);
                }
            }

            // Group messages by normalized phone number to find unique conversation partners
            var conversationPartners = new Dictionary<string, ConversationInfo>();

            if (allMessages != null && allMessages.Messages != null)
            {
                // Group messages by normalized phone number
                var groupedMessages = allMessages.Messages
                    .Where(m => !string.IsNullOrEmpty(m.PhoneNumber))
                    .GroupBy(m => StringHelper.NormalizePhoneNumber(m.PhoneNumber))
                    .ToList();

                foreach (var group in groupedMessages)
                {
                    string normalizedPhoneNumber = group.Key;
                    var messages = group.OrderBy(m => m.Timestamp).ToList();
                    
                    // Store all inbound messages from API into database first
                    foreach (var message in messages.Where(m => m.IsInbound))
                    {
                        await SmsMessageService.StoreSmsMessageAsync(message);
                    }
                    
                    // Get the most recent message and unconfirmed count
                    var lastMessage = messages.Last();
                    var unconfirmedCount = await SmsMessageService.GetUnconfirmedCountAsync(normalizedPhoneNumber);
                    
                    // Debug: Log unconfirmed count for this phone number
                    Console.WriteLine($"LoadSmsConversations: Phone {normalizedPhoneNumber} has {unconfirmedCount} unconfirmed messages (after storing {messages.Count(m => m.IsInbound)} inbound messages)");
                    
                    var convInfo = new ConversationInfo
                    {
                        PhoneNumber = normalizedPhoneNumber,
                        LastMessageTime = lastMessage.Timestamp,
                        LastMessageText = lastMessage.Text,
                        UnreadCount = unconfirmedCount
                    };
                    
                    conversationPartners[normalizedPhoneNumber] = convInfo;
                }
            }

            // Convert to list and sort by most recent message first
            conversations = conversationPartners.Values.OrderByDescending(c => c.LastMessageTime).ToList();

            // Look up contact information for all phone numbers
            await EnrichConversationsWithContactInfo();

            // Update cache
            ChatService.UpdateConversationListCache(conversations);

            isLoadingConversations = false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading conversations: {ex.Message}");
            isLoadingConversations = false;
        }
    }
    private async Task StartNewConversation()
    {
        if (string.IsNullOrWhiteSpace(newConversationPhoneNumber))
        {
            return;
        }

        try
        {
            // Normalize the phone number
            var normalizedPhone = StringHelper.NormalizePhoneNumber(newConversationPhoneNumber);
            targetPhoneNumber = normalizedPhone;

            // Hide the new conversation input
            showNewConversationInput = false;
            newConversationPhoneNumber = "";

            // Switch to SMS chat
            _smsChatVisible = true;
            _staffChatVis = false;
            _webChatVisible = false;

            StateService.ResetUnreadSmsMessages();
            showConversationList = false;
            smsMessages.Clear();

            await LookupCurrentConversationContact();

            // Join SMS chat group for this phone number
            if (hubConnection?.State == HubConnectionState.Connected)
            {
                try
                {
                    await hubConnection.SendAsync("JoinSmsChat", StringHelper.NormalizePhoneNumber(normalizedPhone));
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error joining SMS chat group: {ex.Message}");
                }
            }

            // Update UI
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error starting new conversation: {ex.Message}");
        }
    }
    private async Task ConfirmMessage(string ringCentralId)
    {
        try
        {
            var currentUser = StateService.CurrentUser;
            if (currentUser == null)
            {
                return;
            }

            // Check if message is already confirmed to prevent double-clicks
            var message = smsMessages.FirstOrDefault(m => m.RingCentralId == ringCentralId);
            if (message?.ConfirmedBy != null)
            {
                Console.WriteLine($"Message {ringCentralId} is already confirmed by {message.ConfirmedBy}");
                return;
            }

            var success = await SmsMessageService.ConfirmSmsMessageAsync(ringCentralId, currentUser.Id);
            if (success)
            {
                // Update the message in the UI with the confirmed user info
                if (message != null)
                {
                    message.ConfirmedBy = currentUser.Id;
                    message.ConfirmedOn = DateTime.UtcNow;
                    message.ConfirmedByUser = currentUser;
                    
                    // Force UI refresh for this specific message
                    StateHasChanged();
                }

                // Update conversation list and unread counts
                await UpdateConversationUnreadCounts(targetPhoneNumber);
                
                // Refresh conversation list if it's visible
                if (showConversationList)
                {
                    await RefreshConversationList();
                }

                StateHasChanged();
                Console.WriteLine($"Successfully confirmed message {ringCentralId}");
            }
            else
            {
                Console.WriteLine($"Failed to confirm message {ringCentralId}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error confirming message: {ex.Message}");
        }
    }

    // Message UI Handlers --------------------------------------------------------------------/
    private async Task SelectConversation(string phoneNumber)
    {
        targetPhoneNumber = phoneNumber;
        showConversationList = false;
        
        // Look up contact information for the selected conversation
        await LookupCurrentConversationContact();
        
        // Join SMS chat group for this phone number
        if (hubConnection?.State == HubConnectionState.Connected)
        {
            try
            {
                await hubConnection.SendAsync("JoinSmsChat", StringHelper.NormalizePhoneNumber(phoneNumber));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error joining SMS chat group: {ex.Message}");
            }
        }
        
        await LoadSmsMessages();
    }
    private async Task HandleKeyDown(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SendMessage();
        }
    }
    private async Task RefreshMessages()
    {
        if (_staffChatVis)
        {
            await LoadMessages();
        }
        else if (_smsChatVisible)
        {
            await LoadSmsMessages();
        }
    }
    private async Task RefreshStaffChat()
    {
        await LoadMessages();
        await Task.Delay(100);
        await ScrollToBottom();
    }
    private async Task ClearStaffChat()
    {
        ChatService.ClearStaffChatMessages();
        messages.Clear();
        StateHasChanged();
    }
    private async Task HandleSmsSendKeyDown(KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !e.ShiftKey)
        {
            await SendSmsMessage();
        }
    }
    private async Task HandleNewConversationKeyDown(KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !e.ShiftKey)
        {
            await StartNewConversation();
        }
    }
    private void ChangePhoneNumber()
    {
        if (!string.IsNullOrWhiteSpace(newPhoneNumber))
        {
            targetPhoneNumber = StringHelper.NormalizePhoneNumber(newPhoneNumber);

            // Reset UI state
            showPhoneInput = false;
            newPhoneNumber = "";

            // Load messages for this number
            LoadSmsMessages();
        }
    }
    private void CleanupSentMessageIds()
    {
        // Keep only the last MaxSentMessageIds message IDs to prevent memory leaks
        if (sentMessageIds.Count > MaxSentMessageIds)
        {
            var idsToRemove = sentMessageIds.Take(sentMessageIds.Count - MaxSentMessageIds).ToList();
            foreach (var id in idsToRemove)
            {
                sentMessageIds.Remove(id);
            }
        }
    }
    private void CleanupOptimisticMessages()
    {
        var now = DateTime.UtcNow;
        var expiredIds = optimisticMessageTimes
            .Where(kvp => now - kvp.Value > OptimisticMessageTimeout)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var id in expiredIds)
        {
            optimisticMessageTimes.Remove(id);

            // Remove the optimistic message from the UI if it still exists
            var optimisticMessage = smsMessages.FirstOrDefault(m => m.RingCentralId == id);
            if (optimisticMessage != null)
            {
                Console.WriteLine($"Removing expired optimistic message: {id}");
                smsMessages.Remove(optimisticMessage);
            }
        }

        if (expiredIds.Count > 0)
        {
            StateHasChanged();
        }
    }

    // UI Handling ----------------------------------------------------------------------------/
    private async Task ShowStaffChat()
    {
        _staffChatVis = true;
        _smsChatVisible = false;
        _webChatVisible = false;
        
        StateService.ResetUnreadStaffMessages();
        
        await LoadMessages();
        await Task.Delay(100);
        await ScrollToBottom();
    }
    private async Task ShowWebChat()
    {
        _webChatVisible = true;
        _staffChatVis = false;
        _smsChatVisible = false;
        
        StateService.ResetUnreadSmsMessages();
    }
    private async Task ToggleNewConversation()
    {
        showNewConversationInput = !showNewConversationInput;
        
        if (showNewConversationInput)
        {
            newConversationPhoneNumber = "";
        }
        
        StateHasChanged();
    }
    private async Task ShowActiveSmsChat()
    {
        _smsChatVisible = true;
        _staffChatVis = false;
        _webChatVisible = false;

        StateService.ResetUnreadSmsMessages();

        // Don't auto-load messages
        showConversationList = false;

        if (smsMessages.Count == 0)
        {
            await LoadSmsMessages();
        }
        else
        {
            await LookupCurrentConversationContact();
        }
    }
    private async Task MinimizeChopper()
    {
        await OnMinimize.InvokeAsync();
    }
    private async Task HandlePhoneInputKeyDown(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            ChangePhoneNumber();
        }
    }
    private async Task RefreshConversationList()
    {
        try
        {
            ChatService.InvalidateAllSmsCaches();

            // Reload conversations with fresh unread counts
            await LoadSmsConversations();
            
            // Also refresh the current conversation's unread count if we're viewing one
            if (!showConversationList && !string.IsNullOrEmpty(targetPhoneNumber))
            {
                var unconfirmedCount = await SmsMessageService.GetUnconfirmedCountAsync(targetPhoneNumber);
                var conversation = conversations.FirstOrDefault(c => c.PhoneNumber == targetPhoneNumber);
                if (conversation != null)
                {
                    conversation.UnreadCount = unconfirmedCount;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error refreshing conversation list: {ex.Message}");
        }
    }

    private async Task UpdateConversationUnreadCounts(string phoneNumber)
    {
        try
        {
            if (string.IsNullOrEmpty(phoneNumber))
                return;

            // Update the specific conversation's unread count
            var unconfirmedCount = await SmsMessageService.GetUnconfirmedCountAsync(phoneNumber);
            var conversation = conversations.FirstOrDefault(c => c.PhoneNumber == phoneNumber);
            if (conversation != null)
            {
                conversation.UnreadCount = unconfirmedCount;
                Console.WriteLine($"Updated unread count for {phoneNumber}: {unconfirmedCount}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error updating conversation unread counts: {ex.Message}");
        }
    }

    private async Task LoadInitialUnreadCounts()
    {
        try
        {
            // Load initial unread counts from the database
            var unconfirmedCounts = await SmsMessageService.GetUnconfirmedCountsByPhoneAsync();
            
            // Update StateService with current unconfirmed counts
            StateService.UpdateUnconfirmedSmsCounts(unconfirmedCounts);
            
            Console.WriteLine($"Loaded initial unread counts: {unconfirmedCounts.Count} phone numbers with unconfirmed messages");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading initial unread counts: {ex.Message}");
        }
    }
    private async Task ForceRefreshConversations()
    {
        try
        {
            // Switch to SMS chat view if currently on staff chat or web chat
            if (_staffChatVis || _webChatVisible)
            {
                _smsChatVisible = true;
                _staffChatVis = false;
                _webChatVisible = false;
                
                // Reset unread SMS message count when opening SMS chat
                StateService.ResetUnreadSmsMessages();
            }
            
            ChatService.InvalidateAllSmsCaches();
            showConversationList = true;
            isLoadingConversations = true;
            StateHasChanged();
            
            await LoadSmsConversations();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error force refreshing conversations: {ex.Message}");
            isLoadingConversations = false;
            StateHasChanged();
        }
    }
    private IEnumerable<ConversationInfo> FilteredConversations()
    {
        var filtered = string.IsNullOrWhiteSpace(conversationFilter)
            ? conversations
            : conversations.Where(c =>
                c.PhoneNumber.Contains(conversationFilter) ||
                (c.LastMessageText?.Contains(conversationFilter, StringComparison.OrdinalIgnoreCase) ?? false));

        return filtered;
    }

    // Misc -----------------------------------------------------------------------------------/
    private async Task PlayNotificationSoundWithRetry(string processingId = "")
    {
        const int maxRetries = 3;
        const int delayMs = 500;
        
        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            if (isDisposed) return;
            
            try
            {                
                // First check if the function exists
                var functionExists = await JSRuntime.InvokeAsync<bool>("eval", "typeof window.playStaffMessageSound === 'function'");
                
                if (functionExists)
                {
                    await JSRuntime.InvokeVoidAsync("playStaffMessageSound");
                    return; // Success, exit retry loop
                }
                else
                {                    
                    if (attempt < maxRetries)
                    {
                        await Task.Delay(delayMs);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ChopperMessaging: [{processingId}] Error playing notification sound (attempt {attempt}): {ex.Message}");
                if (attempt < maxRetries)
                {
                    await Task.Delay(delayMs);
                }
            }
        }
    }
    private async Task ScrollToBottom()
    {
        if (!isComponentRendered || isDisposed)
            return;
            
        try
        {
            await JSRuntime.InvokeVoidAsync("scrollToBottom", "chopper-messaging");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error scrolling to bottom: {ex.Message}");
        }
    }
    public async ValueTask DisposeAsync()
    {
        isDisposed = true;
        smsRefreshTimer?.Dispose();
        
        if (hubConnection != null)
        {
            try
            {
                // Leave the StaffChat group before disposing
                if (hubConnection.State == HubConnectionState.Connected)
                {
                    await hubConnection.SendAsync("LeaveStaffChat");
                    Console.WriteLine("Left StaffChat group");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error leaving StaffChat group: {ex.Message}");
            }
            
            try
            {
                await hubConnection.DisposeAsync();
                Console.WriteLine("SignalR hub connection disposed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error disposing SignalR hub connection: {ex.Message}");
            }
        }
    }
    private void StartSmsRefreshTimer()
    {
        smsRefreshTimer = new Timer(async _ => await RefreshSmsMessagesIfNeeded(), null, SmsRefreshInterval, SmsRefreshInterval);
    }
    private async Task RefreshSmsMessagesIfNeeded()
    {
        if (isDisposed || !_smsChatVisible || !isComponentRendered)
            return;

        try
        {
            // Only refresh if enough time has passed
            if (DateTime.Now - lastSmsRefresh >= SmsRefreshInterval)
            {
                lastSmsRefresh = DateTime.Now;
                
                await InvokeAsync(async () =>
                {
                    if (!isDisposed && _smsChatVisible)
                    {
                        await LoadSmsMessages();
                        CleanupOptimisticMessages(); // Clean up old optimistic messages
                    }
                });
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in SMS auto-refresh: {ex.Message}");
        }
    }

    // Helper Methods --------------------------------------------------------------------------/
    private async Task EnsureAllConfirmedUsersLoaded(List<SmsMessageEntity> messages)
    {
        try
        {
            // Get all unique user IDs that need to be loaded
            var userIdsToLoad = messages
                .Where(m => m.ConfirmedBy != null && m.ConfirmedByUser == null)
                .Select(m => m.ConfirmedBy)
                .Distinct()
                .ToList();

            if (!userIdsToLoad.Any())
                return;

            // Load all users in a single query
            using var scope = ServiceProvider.CreateScope();
            var dbContextFactory = scope.ServiceProvider.GetRequiredService<IDbContextFactory<ApplicationDbContext>>();
            using var context = await dbContextFactory.CreateDbContextAsync();
            
            var users = await context.Users
                .Where(u => userIdsToLoad.Contains(u.Id))
                .ToDictionaryAsync(u => u.Id, u => u);

            // Assign users to messages
            foreach (var message in messages.Where(m => m.ConfirmedBy != null && m.ConfirmedByUser == null))
            {
                if (users.TryGetValue(message.ConfirmedBy, out var user))
                {
                    message.ConfirmedByUser = user;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading confirmed by user data: {ex.Message}");
        }
    }

    // Phone Lookup Methods --------------------------------------------------------------------/
    private async Task EnrichConversationsWithContactInfo()
    {
        try
        {
            // Get unique phone numbers and batch lookup
            var phoneNumbers = conversations.Select(c => c.PhoneNumber).Distinct().ToList();            
            var lookupResults = await PhoneLookupService.LookupPhoneNumbersAsync(phoneNumbers);
                        
            // Update conversations with contact information
            foreach (var conversation in conversations)
            {
                if (lookupResults.TryGetValue(conversation.PhoneNumber, out var lookupResult))
                {
                    conversation.ContactName = lookupResult.ContactName;
                    conversation.ContactTitle = lookupResult.ContactTitle;
                    conversation.ContactId = lookupResult.ContactId;
                    conversation.ClientName = lookupResult.ClientName;
                    conversation.ClientId = lookupResult.ClientId;
                    conversation.CarrierName = lookupResult.CarrierName;
                    conversation.CarrierId = lookupResult.CarrierId;
                    conversation.IsContactFound = lookupResult.IsFound;
                }
                
                // Cache the lookup result for future use
                phoneLookupCache[conversation.PhoneNumber] = lookupResult;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error enriching conversations with contact info: {ex.Message}");
        }
    }
    private async Task LookupCurrentConversationContact()
    {
        try
        {
            // Check cache first
            if (phoneLookupCache.TryGetValue(targetPhoneNumber, out var cachedResult))
            {
                currentConversationLookup = cachedResult;
                return;
            }
            
            // Perform lookup
            currentConversationLookup = await PhoneLookupService.LookupPhoneNumberAsync(targetPhoneNumber);
            
            // Cache the result
            phoneLookupCache[targetPhoneNumber] = currentConversationLookup;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error looking up current conversation contact: {ex.Message}");
            currentConversationLookup = new PhoneLookupResult { PhoneNumber = targetPhoneNumber, IsFound = false };
        }
    }
}
<script>
    window.scrollToBottom = (containerId) => {
        const messagesList = document.querySelector('.messages-list');
        if (messagesList) {
            messagesList.scrollTop = messagesList.scrollHeight;
        }
    };
</script>