using Microsoft.AspNetCore.Components;
using Microsoft.FluentUI.AspNetCore.Components;
using Surefire.Domain.Policies.Models;
using Surefire.Domain.Policies.Services;
using Surefire.Domain.Shared.Models;
using Surefire.Domain.Clients.Services;
using Surefire.Domain.Shared.Services;
using Surefire.Domain.Attachments.Models;
using Surefire.Domain.Attachments.Services;
using System.Xml;
using System.Xml.Linq;
using System.Text.Json;
using System.Globalization;
using System.Text.RegularExpressions;

namespace Surefire.Domain.Utilities.Components
{
    public partial class DataImport : ComponentBase
    {
        [Parameter] public int? ClientId { get; set; }
        [Parameter] public int? PolicyId { get; set; }
        [Parameter] public int? RenewalId { get; set; }

        private string xmlData = string.Empty;
        private bool workCompEnabled = false;
        private int? selectedWorkCompPolicyId;
        private Policy? selectedWorkCompPolicy;
        private List<Policy> workCompPolicies = new();
        private bool autoEnabled = false;
        private int? selectedAutoPolicyId;
        private Policy? selectedAutoPolicy;
        private List<Policy> autoPolicies = new();
        private bool glEnabled = false;
        private int? selectedGLPolicyId;
        private Policy? selectedGLPolicy;
        private List<Policy> glPolicies = new();
        private bool saveAsAttachment = false;
        private bool updateAllMatchingPolicies = false;
        private List<Policy> allPolicies = new();
        private List<DebugMessage> debugMessages = new();

        private bool CanImport =>
            !string.IsNullOrWhiteSpace(xmlData) &&
            (updateAllMatchingPolicies || 
             ((!workCompEnabled || (workCompEnabled && selectedWorkCompPolicy != null)) &&
              (!autoEnabled || (autoEnabled && selectedAutoPolicy != null)) &&
              (!glEnabled || (glEnabled && selectedGLPolicy != null))));

        protected override async Task OnInitializedAsync()
        {
            await LoadPolicies();
        }

        protected override async Task OnParametersSetAsync()
        {
            if (ClientId.HasValue)
            {
                await LoadPolicies();
            }
        }

        private async Task LoadPolicies()
        {
            if (!ClientId.HasValue) return;

            try
            {
                // Load all policies for the client
                allPolicies = await PolicyService.GetCurrentPoliciesByClientIdAsync(ClientId.Value);
                
                // Filter for Work Comp policies
                workCompPolicies = allPolicies
                    .Where(p => p.Product?.LineName?.ToLower().Contains("work") == true ||
                               p.Product?.LineName?.ToLower().Contains("comp") == true ||
                               p.Product?.LineName?.ToLower().Contains("workers") == true)
                    .OrderByDescending(p => p.EffectiveDate)
                    .ToList();

                // Auto-select the most recent Work Comp policy
                if (workCompPolicies.Any())
                {
                    selectedWorkCompPolicy = workCompPolicies.First();
                    selectedWorkCompPolicyId = selectedWorkCompPolicy.PolicyId;
                    DebugLog($"Auto-selected work comp policy: {selectedWorkCompPolicy.PolicyNumber}", "Info");
                }

                // Filter for Auto policies
                autoPolicies = allPolicies
                    .Where(p => p.Product?.LineName?.ToLower().Contains("auto") == true ||
                               p.Product?.LineName?.ToLower().Contains("commercial auto") == true ||
                               p.Product?.LineName?.ToLower().Contains("business auto") == true)
                    .OrderByDescending(p => p.EffectiveDate)
                    .ToList();

                // Auto-select the most recent Auto policy
                if (autoPolicies.Any())
                {
                    selectedAutoPolicy = autoPolicies.First();
                    selectedAutoPolicyId = selectedAutoPolicy.PolicyId;
                    DebugLog($"Auto-selected auto policy: {selectedAutoPolicy.PolicyNumber}", "Info");
                }

                // Filter for GL policies (ProductId = 3)
                glPolicies = allPolicies
                    .Where(p => p.Product?.ProductId == 3)
                    .OrderByDescending(p => p.EffectiveDate)
                    .ToList();

                // Auto-select the most recent GL policy
                if (glPolicies.Any())
                {
                    selectedGLPolicy = glPolicies.First();
                    selectedGLPolicyId = selectedGLPolicy.PolicyId;
                    DebugLog($"Auto-selected GL policy: {selectedGLPolicy.PolicyNumber}", "Info");
                }

                DebugLog($"Loaded {allPolicies.Count} total policies: {workCompPolicies.Count} work comp, {autoPolicies.Count} auto, and {glPolicies.Count} GL policies for client {ClientId}", "Info");
            }
            catch (Exception ex)
            {
                await MessageService.ShowMessageBarAsync($"Error loading policies: {ex.Message}", MessageIntent.Error);
            }
        }

        private async Task HandleImport()
        {
            if (!CanImport) return;

            try
            {
                DebugLog("Starting import process...", "Info");

                // Clear previous messages
                MessageService.Clear();

                await MessageService.ShowMessageBarAsync("Processing import...", MessageIntent.Info);

                                // Preprocess XML to fix common issues
                DebugLog("=== XML PREPROCESSING START ===", "Info");
                var originalLength = xmlData.Length;
                var ampersandCount = xmlData.Count(c => c == '&');
                
                if (ampersandCount > 0)
                {
                    DebugLog($"Found {ampersandCount} ampersand characters (&) in XML", "Warning");
                    DebugLog("Replacing all '&' with 'and' to fix XML validation issues...", "Info");
                    xmlData = xmlData.Replace("&", "and");
                    DebugLog($"XML length changed from {originalLength} to {xmlData.Length} characters", "Info");
                }
                else
                {
                    DebugLog("No ampersand characters found in XML", "Info");
                }

                // Fix greater than and less than symbols with dollar amounts
                var replacements = new Dictionary<string, string>
                {
                    { "< $", "&lt; $" },
                    { "> $", "&gt; $" },
                    { "<$", "&lt;$" },
                    { " >$", "&gt;$" },
                    { ">=", "&gt;=" },
                    { "<=", "&lt;=" }
                };

                foreach (var replacement in replacements)
                {
                    var count = (xmlData.Length - xmlData.Replace(replacement.Key, "").Length) / replacement.Key.Length;
                    if (count > 0)
                    {
                        DebugLog($"Found {count} instances of '{replacement.Key}' - replacing with '{replacement.Value}'", "Info");
                        xmlData = xmlData.Replace(replacement.Key, replacement.Value);
                    }
                }

                if (xmlData.Length != originalLength)
                {
                    DebugLog($"XML length after all preprocessing: {xmlData.Length} characters (changed by {xmlData.Length - originalLength})", "Info");
                }
                
                // Wrap XML in root tags if not already wrapped
                if (!xmlData.TrimStart().StartsWith("<root>", StringComparison.OrdinalIgnoreCase))
                {
                    DebugLog("Wrapping XML content in <root></root> tags", "Info");
                    xmlData = $"<root>{xmlData}</root>";
                    DebugLog($"XML length after root wrapping: {xmlData.Length} characters", "Info");
                }
                else
                {
                    DebugLog("XML already has root tags", "Info");
                }

                // Validate XML format
                //DebugLog("=== XML VALIDATION START ===", "Info");
                DebugLog($"XML data length: {xmlData.Length} characters", "Info");
                DebugLog($"XML starts with: {xmlData.Substring(0, Math.Min(100, xmlData.Length))}...", "Info");

                if (!IsValidXml(xmlData))
                {
                    DebugLog("=== XML VALIDATION FAILED ===", "Error");
                    await MessageService.ShowMessageBarAsync("Invalid XML format. Check debug panel for details.", MessageIntent.Error);
                    return;
                }
                DebugLog("=== XML VALIDATION PASSED ===", "Success");

                if (updateAllMatchingPolicies)
                {
                    // Process all matching policies from XML
                    await ProcessAllMatchingPolicies();
                }
                else
                {
                    // Process Work Comp import if enabled
                    if (workCompEnabled && selectedWorkCompPolicy != null)
                    {
                        //DebugLog($"Processing Work Comp import for policy: {selectedWorkCompPolicy.PolicyNumber}", "Info");
                        await ProcessWorkCompImport();
                    }

                    // Process Auto import if enabled
                    if (autoEnabled && selectedAutoPolicy != null)
                    {
                        //DebugLog($"Processing Auto import for policy: {selectedAutoPolicy.PolicyNumber}", "Info");
                        await ProcessAutoImport();
                    }

                    // Process GL import if enabled
                    if (glEnabled && selectedGLPolicy != null)
                    {
                        //DebugLog($"Processing GL import for policy: {selectedGLPolicy.PolicyNumber}", "Info");
                        await ProcessGLImport();
                    }
                }

                // Save processed XML as attachment if enabled
                if (saveAsAttachment && ClientId.HasValue)
                {
                    await SaveXmlAsAttachment();
                }

                //DebugLog("Import completed successfully", "Success");
                MessageService.ShowMessageBar(options =>
                {
                    options.Title = "✅ Import Completed";
                    options.Body = "Data import completed successfully!";
                    options.Intent = MessageIntent.Success;
                    options.ClearAfterNavigation = false;
                });
            }
            catch (Exception ex)
            {
                DebugLog($"Import failed: {ex.Message}", "Error");
                MessageService.ShowMessageBar(options =>
                {
                    options.Title = "❌ Import Failed";
                    options.Body = ex.Message;
                    options.Intent = MessageIntent.Error;
                    options.ClearAfterNavigation = false;
                });
            }
        }

        private async Task ProcessWorkCompImport()
        {
            DebugLog("Parsing XML document...", "Info");
            var xmlDoc = XDocument.Parse(xmlData);
            DebugLog($"XML parsed successfully. Root element: {xmlDoc.Root?.Name}", "Success");

            if (selectedWorkCompPolicy == null)
            {
                throw new InvalidOperationException("Selected Work Comp policy not found.");
            }

            DebugLog($"Processing policy: {selectedWorkCompPolicy.PolicyNumber} (ID: {selectedWorkCompPolicy.PolicyId})", "Info");

            // Step 1: Find WorkersCompensation root element
            DebugLog("Step 1: Looking for <WorkersCompensation> root element...", "Info");

            var workersCompElement = xmlDoc.Descendants("WorkersCompensation").FirstOrDefault();

            if (workersCompElement == null)
            {
                DebugLog("❌ <WorkersCompensation> element NOT found in XML", "Error");
                throw new InvalidOperationException("WorkersCompensation element not found in XML");
            }
            else
            {
                DebugLog("✅ <WorkersCompensation> element found successfully", "Success");

                // Step 2: Count Policy nodes within WorkersCompensation
                var policyNodes = workersCompElement.Elements("Policy").ToList();
                DebugLog($"Found {policyNodes.Count} <Policy> nodes in <WorkersCompensation>", "Info");

                // Step 3: List all Policy details
                if (policyNodes.Any())
                {
                    DebugLog("Extracting Policy details:", "Info");

                    for (int i = 0; i < policyNodes.Count; i++)
                    {
                        var policy = policyNodes[i];
                        var basicDetails = policy.Element("BasicDetails");

                                                if (basicDetails != null)
                        {
                            var policyNum = basicDetails.Element("policynum")?.Value ?? "N/A";
                            var polEffDate = basicDetails.Element("poleffdate")?.Value ?? "N/A";
                            var polExpDate = basicDetails.Element("polexpdate")?.Value ?? "N/A";
                            
                            DebugLog($"Found:[{i}] {policyNum} ({polEffDate} - {polExpDate})", "Info");
                            
                            // Check if this policy matches the selected one
                            if (policyNum == selectedWorkCompPolicy.PolicyNumber)
                            {
                                DebugLog($"✅ POLICY MATCH FOUND: {policyNum}", "Success");
                                await WorkCompPolicyMatched(policy, i);
                            }
                        }
                        else
                        {
                            DebugLog($"WorkersCompensation.Policy[{i}]: BasicDetails element not found", "Warning");
                        }
                    }
                }
                else
                {
                    DebugLog("No <Policy> nodes found within <WorkersCompensation>", "Warning");
                }
            }

            DebugLog("Work Comp XML analysis completed", "Success");

            // Simulate processing time
            await Task.Delay(500);
        }

        private bool IsValidXml(string xml)
        {
            if (string.IsNullOrWhiteSpace(xml))
            {
                DebugLog("XML validation failed: Empty or null XML data", "Error");
                return false;
            }

            try
            {
                var doc = XDocument.Parse(xml);
                DebugLog($"XML parsed successfully. Root element: {doc.Root?.Name}", "Success");
                DebugLog($"XML contains {doc.Descendants().Count()} total elements", "Info");

                // Log some structure info
                var workersCompElements = doc.Descendants("WorkersCompensation");
                if (workersCompElements.Any())
                {
                    DebugLog($"Found {workersCompElements.Count()} WorkersCompensation elements", "Info");
                }
                else
                {
                    DebugLog("No WorkersCompensation elements found in XML", "Warning");
                }

                return true;
            }
            catch (XmlException ex)
            {
                DebugLog($"XML validation failed: {ex.Message}", "Error");
                DebugLog($"Error at line {ex.LineNumber}, position {ex.LinePosition}", "Error");
                return false;
            }
            catch (Exception ex)
            {
                DebugLog($"Unexpected error during XML validation: {ex.Message}", "Error");
                return false;
            }
        }

        private void ClearForm()
        {
            xmlData = string.Empty;
            workCompEnabled = false;
            selectedWorkCompPolicyId = null;
            selectedWorkCompPolicy = null;
            autoEnabled = false;
            selectedAutoPolicyId = null;
            selectedAutoPolicy = null;
            glEnabled = false;
            selectedGLPolicyId = null;
            selectedGLPolicy = null;
            updateAllMatchingPolicies = false;
            saveAsAttachment = false;
            MessageService.Clear();
            DebugLog("Form cleared", "Info");
            StateHasChanged();
        }

        private void DebugLog(string message, string type = "Info")
        {
            // Insert at the beginning (index 0) to show most recent at top
            debugMessages.Insert(0, new DebugMessage
            {
                Message = message,
                Type = type,
                Timestamp = DateTime.Now
            });

            // Keep only the last 100 messages to prevent memory issues
            if (debugMessages.Count > 100)
            {
                debugMessages.RemoveAt(debugMessages.Count - 1); // Remove from the end (oldest)
            }

            StateHasChanged();
        }

        private void ClearDebugOutput()
        {
            debugMessages.Clear();
            StateHasChanged();
        }

        private async Task WorkCompPolicyMatched(XElement matchedPolicy, int policyIndex)
        {
            DebugLog($"=== ANALYZING MATCHED POLICY [{policyIndex}] ===", "Success");
            
            // 1. Find and count Locations.Loc[] nodes
            var locationsElement = matchedPolicy.Element("Locations");
            if (locationsElement != null)
            {
                var locNodes = locationsElement.Elements("Loc").ToList();
                DebugLog($"WorkersCompensation.Policy[{policyIndex}].Locations.Loc[] count: {locNodes.Count}", "Info");
                
                // Process each Loc node
                for (int locIndex = 0; locIndex < locNodes.Count; locIndex++)
                {
                    var locNode = locNodes[locIndex];
                    DebugLog($"--- Processing Location [{locIndex}] ---", "Info");
                    
                    // 1a. Find and count RatingInfo.FieldSet[] nodes
                    var ratingInfoElement = locNode.Element("RatingInfo");
                    if (ratingInfoElement != null)
                    {
                        var fieldSetNodes = ratingInfoElement.Elements("FieldSet").ToList();
                        DebugLog($"WorkersCompensation.Policy[{policyIndex}].Locations.Loc[{locIndex}].RatingInfo.FieldSet[] count: {fieldSetNodes.Count}", "Info");
                        
                        // 1b. Log each FieldSet's locstateclsscod
                        for (int fieldSetIndex = 0; fieldSetIndex < fieldSetNodes.Count; fieldSetIndex++)
                        {
                            var fieldSetNode = fieldSetNodes[fieldSetIndex];
                            var classCode = fieldSetNode.Element("locstateclsscod")?.Value ?? "N/A";
                            var classDescription = fieldSetNode.Element("locstatecatdutclss")?.Value ?? "N/A";
                            var payroll = fieldSetNode.Element("locstateestannrempay")?.Value ?? "N/A";
                            var rate = fieldSetNode.Element("locstaterate")?.Value ?? "N/A";
                            
                            DebugLog($"WorkersCompensation.Policy[{policyIndex}].Locations.Loc[{locIndex}].RatingInfo.FieldSet[{fieldSetIndex}].locstateclsscod: {classCode}", "Info");
                            DebugLog($"  Description: {classDescription}", "Info");
                            DebugLog($"  Payroll: {payroll}, Rate: {rate}", "Info");
                            
                            // Process this FieldSet for database update/insert
                            await ProcessWorkCompRatingBasis(fieldSetNode, locIndex, fieldSetIndex);
                        }
                    }
                    else
                    {
                        DebugLog($"WorkersCompensation.Policy[{policyIndex}].Locations.Loc[{locIndex}]: RatingInfo element not found", "Warning");
                    }
                }
            }
            else
            {
                DebugLog($"WorkersCompensation.Policy[{policyIndex}]: Locations element not found", "Warning");
            }
            
            //2. Update coverage limits
            DebugLog("--- Processing Work Comp Coverage Limits ---", "Info");
            await ProcessWorkCompCoverageLimits(matchedPolicy, policyIndex);
            
            DebugLog($"=== POLICY [{policyIndex}] ANALYSIS COMPLETE ===", "Success");
        }

        private async Task ProcessWorkCompRatingBasis(XElement fieldSetNode, int locIndex, int fieldSetIndex)
        {
            try
            {
                DebugLog($"--- Processing FieldSet[{fieldSetIndex}] for Database Update ---", "Info");
                
                // Extract all the XML values
                var locationNumber = fieldSetNode.Element("locationnumber")?.Value ?? "";
                var locationState = fieldSetNode.Element("locstate")?.Value ?? "";
                var classCode = fieldSetNode.Element("locstateclsscod")?.Value ?? "";
                var classDescription = fieldSetNode.Element("locstatecatdutclss")?.Value ?? "";
                var partTimeEmpStr = fieldSetNode.Element("locstatenumparttimeemp")?.Value ?? "0";
                var fullTimeEmpStr = fieldSetNode.Element("locstatenumfulltimeemp")?.Value ?? "0";
                var baseRateStr = fieldSetNode.Element("locstaterate")?.Value ?? "0";
                var premiumStr = fieldSetNode.Element("locstateestannmanprem")?.Value ?? "$0.00";
                var payrollStr = fieldSetNode.Element("locstateestannrempay")?.Value ?? "$0.00";

                DebugLog($"Raw XML Data - ClassCode: {classCode}, State: {locationState}, Location: {locationNumber}", "Info");

                // Convert values with error handling
                var partTimeEmployees = ConvertToInt(partTimeEmpStr, "PartTimeEmployees");
                var fullTimeEmployees = ConvertToInt(fullTimeEmpStr, "FullTimeEmployees");
                var baseRate = ConvertToDecimal(baseRateStr, "BaseRate");
                var premium = ConvertCurrencyToDecimal(premiumStr, "Premium");
                var payroll = ConvertCurrencyToDecimal(payrollStr, "Payroll");

                DebugLog($"Converted Values - PT Emp: {partTimeEmployees}, FT Emp: {fullTimeEmployees}, Rate: {baseRate}, Premium: {premium:C}, Payroll: {payroll:C}", "Info");

                // Create or update WorkCompRatingBasis
                var workCompRatingBasis = new WorkCompRatingBasis
                {
                    PolicyId = selectedWorkCompPolicy.PolicyId,
                    LocationNumberNote = locationNumber,
                    LocationState = locationState,
                    ClassCode = classCode,
                    ClassDescription = classDescription,
                    PartTimeEmployees = partTimeEmployees,
                    FullTimeEmployees = fullTimeEmployees,
                    BaseRate = baseRate,
                    Premium = premium,
                    Payroll = payroll,
                    DateModified = DateTime.UtcNow
                };

                // Check if this ClassCode already exists for this policy
                await PolicyService.UpsertWorkCompRatingBasisAsync(workCompRatingBasis);
                
                DebugLog($"✅ Successfully processed ClassCode: {classCode}", "Success");
            }
            catch (Exception ex)
            {
                DebugLog($"❌ Error processing FieldSet[{fieldSetIndex}]: {ex.Message}", "Error");
            }
        }

        private int? ConvertToInt(string value, string fieldName)
        {
            if (string.IsNullOrWhiteSpace(value)) return null;
            
            if (int.TryParse(value, out int result))
            {
                return result;
            }
            
            DebugLog($"Warning: Could not convert '{value}' to int for {fieldName}", "Warning");
            return null;
        }

        private decimal? ConvertToDecimal(string value, string fieldName)
        {
            if (string.IsNullOrWhiteSpace(value)) return null;
            
            if (decimal.TryParse(value, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                return result;
            }
            
            DebugLog($"Warning: Could not convert '{value}' to decimal for {fieldName}", "Warning");
            return null;
        }

        private decimal? ConvertCurrencyToDecimal(string value, string fieldName)
        {
            if (string.IsNullOrWhiteSpace(value)) return null;
            
            // Remove currency symbols, commas, and spaces
            string cleanValue = Regex.Replace(value, @"[$,\s]", "");
            
            if (decimal.TryParse(cleanValue, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                DebugLog($"Converted currency '{value}' to {result:C} for {fieldName}", "Info");
                return result;
            }
            
            DebugLog($"Warning: Could not convert currency '{value}' to decimal for {fieldName}", "Warning");
            return null;
        }

        private async Task ProcessWorkCompCoverageLimits(XElement matchedPolicy, int policyIndex)
        {
            try
            {
                DebugLog($"--- Processing Coverage Limits for Policy [{policyIndex}] ---", "Info");
                
                // Extract coverage limits from BasicDetails
                var basicDetails = matchedPolicy.Element("BasicDetails");
                if (basicDetails == null)
                {
                    DebugLog("BasicDetails element not found - cannot process coverage limits", "Warning");
                    return;
                }

                var limit1Str = basicDetails.Element("limit1")?.Value ?? "";
                var limit2Str = basicDetails.Element("limit2")?.Value ?? "";
                var limit3Str = basicDetails.Element("limit3")?.Value ?? "";

                DebugLog($"Raw XML Limits - limit1: {limit1Str}, limit2: {limit2Str}, limit3: {limit3Str}", "Info");

                // Convert limits to integers (removing commas and currency symbols)
                var eachAccident = ConvertLimitToInt(limit1Str, "EachAccident");
                var diseaseEachEmployee = ConvertLimitToInt(limit2Str, "DiseaseEachEmployee");
                var diseasePolicyLimit = ConvertLimitToInt(limit3Str, "DiseasePolicyLimit");

                DebugLog($"Converted Limits - EachAccident: {eachAccident:N0}, DiseaseEachEmployee: {diseaseEachEmployee:N0}, DiseasePolicyLimit: {diseasePolicyLimit:N0}", "Info");

                // Create or update WorkCompCoverage
                var workCompCoverage = new WorkCompCoverage
                {
                    PolicyId = selectedWorkCompPolicy.PolicyId,
                    EachAccident = eachAccident,
                    DiseaseEachEmployee = diseaseEachEmployee,
                    DiseasePolicyLimit = diseasePolicyLimit,
                    DateModified = DateTime.UtcNow
                };

                // Upsert the coverage record
                await PolicyService.UpsertWorkCompCoverageAsync(workCompCoverage);
                
                DebugLog($"✅ Successfully processed Work Comp Coverage limits", "Success");
            }
            catch (Exception ex)
            {
                DebugLog($"❌ Error processing coverage limits: {ex.Message}", "Error");
            }
        }

        private int? ConvertLimitToInt(string value, string fieldName)
        {
            if (string.IsNullOrWhiteSpace(value)) return null;
            
            // Remove currency symbols, commas, and spaces
            string cleanValue = Regex.Replace(value, @"[$,\s]", "");
            
            if (int.TryParse(cleanValue, NumberStyles.Any, CultureInfo.InvariantCulture, out int result))
            {
                DebugLog($"Converted limit '{value}' to {result:N0} for {fieldName}", "Info");
                return result;
            }
            
            DebugLog($"Warning: Could not convert limit '{value}' to int for {fieldName}", "Warning");
            return null;
        }

        private async Task ProcessAutoImport()
        {
            DebugLog("Parsing XML document for Auto import...", "Info");
            var xmlDoc = XDocument.Parse(xmlData);
            DebugLog($"XML parsed successfully. Root element: {xmlDoc.Root?.Name}", "Success");

            if (selectedAutoPolicy == null)
            {
                throw new InvalidOperationException("Selected Auto policy not found.");
            }

            DebugLog($"Processing Auto policy: {selectedAutoPolicy.PolicyNumber} (ID: {selectedAutoPolicy.PolicyId})", "Info");

            // Step 1: Find businessauto root element (case sensitive)
            DebugLog("Step 1: Looking for <businessauto> root element...", "Info");

            var businessAutoElement = xmlDoc.Descendants("businessauto").FirstOrDefault();

            if (businessAutoElement == null)
            {
                DebugLog("❌ <businessauto> element NOT found in XML", "Error");
                throw new InvalidOperationException("businessauto element not found in XML");
            }
            else
            {
                DebugLog("✅ <businessauto> element found successfully", "Success");

                // Step 2: Count policy nodes within businessauto
                var policyNodes = businessAutoElement.Elements("policy").ToList();
                DebugLog($"Found {policyNodes.Count} <policy> nodes in <businessauto>", "Info");

                // Step 3: List all Policy details
                if (policyNodes.Any())
                {
                    DebugLog("Extracting Auto Policy details:", "Info");

                    for (int i = 0; i < policyNodes.Count; i++)
                    {
                        var policy = policyNodes[i];
                        var basicDetails = policy.Element("basicdetails");

                        if (basicDetails != null)
                        {
                            var policyNum = basicDetails.Element("policynum")?.Value ?? "N/A";
                            var polEffDate = basicDetails.Element("policyeff")?.Value ?? "N/A";
                            var polExpDate = basicDetails.Element("policyexp")?.Value ?? "N/A";
                            
                            DebugLog($"Found Auto Policy[{i}] {policyNum} ({polEffDate} - {polExpDate})", "Info");
                            
                            // Check if this policy matches the selected one
                            if (policyNum == selectedAutoPolicy.PolicyNumber)
                            {
                                DebugLog($"✅ AUTO POLICY MATCH FOUND: {policyNum}", "Success");
                                await AutoPolicyMatched(policy, i);
                            }
                        }
                        else
                        {
                            DebugLog($"businessauto.policy[{i}]: basicdetails element not found", "Warning");
                        }
                    }
                }
                else
                {
                    DebugLog("No <Policy> nodes found within <BusinessAuto>", "Warning");
                }
            }

            DebugLog("Auto XML analysis completed", "Success");
        }

        private async Task AutoPolicyMatched(XElement matchedPolicy, int policyIndex)
        {
            DebugLog($"=== ANALYZING MATCHED AUTO POLICY [{policyIndex}] ===", "Success");
            
            // 1. Process basic policy information and update coverage limits
            DebugLog("--- Processing Auto Coverage Limits ---", "Info");
            await ProcessAutoCoverageLimits(matchedPolicy, policyIndex);
            
            // 2. Process Drivers
            DebugLog("--- Processing Auto Drivers ---", "Info");
            await ProcessAutoDrivers(matchedPolicy, policyIndex);
            
            // 3. Process Vehicles
            DebugLog("--- Processing Auto Vehicles ---", "Info");
            await ProcessAutoVehicles(matchedPolicy, policyIndex);
            
            DebugLog($"=== AUTO POLICY [{policyIndex}] ANALYSIS COMPLETE ===", "Success");
        }

        private async Task ProcessAutoCoverageLimits(XElement matchedPolicy, int policyIndex)
        {
            try
            {
                DebugLog($"--- Processing Auto Coverage Limits for Policy [{policyIndex}] ---", "Info");
                
                // Extract coverage limits from coverages.liabilitycsl
                var coverages = matchedPolicy.Element("coverages");
                if (coverages == null)
                {
                    DebugLog("coverages element not found - cannot process coverage limits", "Warning");
                    return;
                }

                var liabilityCSL = coverages.Element("liabilitycsl");
                if (liabilityCSL == null)
                {
                    DebugLog("liabilitycsl element not found - cannot process coverage limits", "Warning");
                    return;
                }

                var limit1Str = liabilityCSL.Element("limit1")?.Value ?? "";
                var coverageSymbol1 = liabilityCSL.Element("coveragesymbol1")?.Value ?? "";
                var coverageSymbol2 = liabilityCSL.Element("coveragesymbol2")?.Value ?? "";
                var coverageSymbol7 = liabilityCSL.Element("coveragesymbol7")?.Value ?? "";
                var coverageSymbol8 = liabilityCSL.Element("coveragesymbol8")?.Value ?? "";
                var coverageSymbol9 = liabilityCSL.Element("coveragesymbol9")?.Value ?? "";

                DebugLog($"Raw XML Coverage - limit1: {limit1Str}", "Info");
                DebugLog($"Coverage Symbols - 1: {coverageSymbol1}, 2: {coverageSymbol2}, 7: {coverageSymbol7}, 8: {coverageSymbol8}, 9: {coverageSymbol9}", "Info");

                // Convert limit to integer
                var combinedLimit = ConvertLimitToInt(limit1Str, "CombinedLimit");

                DebugLog($"Converted Combined Limit: {combinedLimit:N0}", "Info");

                // Create or update AutoCoverage
                var autoCoverage = new AutoCoverage
                {
                    PolicyId = selectedAutoPolicy.PolicyId,
                    CombinedLimit = combinedLimit,
                    ForAny = !string.IsNullOrWhiteSpace(coverageSymbol1),
                    ForOwned = !string.IsNullOrWhiteSpace(coverageSymbol2),
                    ForScheduled = !string.IsNullOrWhiteSpace(coverageSymbol7),
                    ForHired = !string.IsNullOrWhiteSpace(coverageSymbol8),
                    ForNonOwned = !string.IsNullOrWhiteSpace(coverageSymbol9),
                    DateModified = DateTime.UtcNow
                };

                // Upsert the coverage record
                await PolicyService.UpsertAutoCoverageAsync(autoCoverage);
                
                DebugLog($"✅ Successfully processed Auto Coverage limits", "Success");
            }
            catch (Exception ex)
            {
                DebugLog($"❌ Error processing auto coverage limits: {ex.Message}", "Error");
            }
        }

        private async Task ProcessAutoDrivers(XElement matchedPolicy, int policyIndex)
        {
            try
            {
                DebugLog($"--- Processing Auto Drivers for Policy [{policyIndex}] ---", "Info");
                
                var driversElement = matchedPolicy.Element("drivers");
                if (driversElement == null)
                {
                    DebugLog("drivers element not found", "Warning");
                    return;
                }

                var driverNodes = driversElement.Elements("driver").ToList();
                DebugLog($"Found {driverNodes.Count} driver nodes", "Info");

                for (int driverIndex = 0; driverIndex < driverNodes.Count; driverIndex++)
                {
                    var driverNode = driverNodes[driverIndex];
                    DebugLog($"--- Processing Driver [{driverIndex}] ---", "Info");
                    
                    var driverNumber = driverNode.Element("drvrnumb")?.Value ?? "";
                    var driverName = driverNode.Element("drvrname")?.Value ?? "";
                    var driverDOBStr = driverNode.Element("drvrdob")?.Value ?? "";
                    var driverLicenseNumber = driverNode.Element("drvrdlnum")?.Value ?? "";
                    var driverLicenseState = driverNode.Element("drvrstlic")?.Value ?? "";
                    var driverGender = driverNode.Element("gender")?.Element("code")?.Value ?? "";
                    var driverMarried = driverNode.Element("married")?.Element("code")?.Value ?? "";
                    var driverDateOfHireStr = driverNode.Element("drvrdatehire")?.Value ?? "";

                    DebugLog($"Driver Data - Number: {driverNumber}, Name: {driverName}, DOB: {driverDOBStr}", "Info");

                    // Convert dates
                    var driverDOB = ConvertToDateTime(driverDOBStr, "DateOfBirth");
                    var driverDateOfHire = ConvertToDateTime(driverDateOfHireStr, "DateOfHire");

                    // Create or update Driver
                    var driver = new Driver
                    {
                        PolicyId = selectedAutoPolicy.PolicyId,
                        DriverNumberNote = driverNumber,
                        FullName = driverName,
                        DateOfBirth = driverDOB,
                        LicenseNumber = driverLicenseNumber,
                        LicenseState = driverLicenseState,
                        Gender = driverGender,
                        Married = driverMarried,
                        DateOfHire = driverDateOfHire,
                        IsPrimaryDriver = driverIndex == 0 // First driver is primary
                    };

                    await PolicyService.UpsertDriverAsync(driver);
                    DebugLog($"✅ Successfully processed Driver: {driverName}", "Success");
                }
            }
            catch (Exception ex)
            {
                DebugLog($"❌ Error processing auto drivers: {ex.Message}", "Error");
            }
        }

        private async Task ProcessAutoVehicles(XElement matchedPolicy, int policyIndex)
        {
            try
            {
                DebugLog($"--- Processing Auto Vehicles for Policy [{policyIndex}] ---", "Info");
                
                var vehiclesElement = matchedPolicy.Element("vehicles");
                if (vehiclesElement == null)
                {
                    DebugLog("vehicles element not found", "Warning");
                    return;
                }

                var vehicleNodes = vehiclesElement.Elements("vehicle").ToList();
                DebugLog($"Found {vehicleNodes.Count} vehicle nodes", "Info");

                for (int vehicleIndex = 0; vehicleIndex < vehicleNodes.Count; vehicleIndex++)
                {
                    var vehicleNode = vehicleNodes[vehicleIndex];
                    DebugLog($"--- Processing Vehicle [{vehicleIndex}] ---", "Info");
                    
                    var dataElement = vehicleNode.Element("data");
                    if (dataElement == null)
                    {
                        DebugLog($"vehicle[{vehicleIndex}]: data element not found", "Warning");
                        continue;
                    }

                    var vehicleNumber = dataElement.Element("vehnum")?.Value ?? "";
                    var vehicleYear = dataElement.Element("vehyear")?.Value ?? "";
                    var vehicleMake = dataElement.Element("vehmake")?.Value ?? "";
                    var vehicleModel = dataElement.Element("vehmodel")?.Value ?? "";
                    var vehicleVIN = dataElement.Element("vehvin")?.Value ?? "";

                    var garagedElement = dataElement.Element("garaged");
                    var garagedAddress = garagedElement?.Element("address1")?.Value ?? "";
                    var garagedCity = garagedElement?.Element("city")?.Value ?? "";
                    var garagedState = garagedElement?.Element("statecode")?.Value ?? "";
                    var garagedPostalCode = garagedElement?.Element("postalcode")?.Value ?? "";

                    DebugLog($"Vehicle Data - Number: {vehicleNumber}, Year: {vehicleYear}, Make: {vehicleMake}, Model: {vehicleModel}", "Info");

                    // Create or update Vehicle
                    var vehicle = new Vehicle
                    {
                        PolicyId = selectedAutoPolicy.PolicyId,
                        VehicleNumberNote = vehicleNumber,
                        Year = vehicleYear,
                        Make = vehicleMake,
                        Model = vehicleModel,
                        VIN = vehicleVIN,
                        GaragedAddress = garagedAddress,
                        GaragedCity = garagedCity,
                        GaragedState = garagedState,
                        GaragedPostalCode = garagedPostalCode
                    };

                    await PolicyService.UpsertVehicleAsync(vehicle);
                    DebugLog($"✅ Successfully processed Vehicle: {vehicleYear} {vehicleMake} {vehicleModel}", "Success");
                }
            }
            catch (Exception ex)
            {
                DebugLog($"❌ Error processing auto vehicles: {ex.Message}", "Error");
            }
        }

        private DateTime? ConvertToDateTime(string value, string fieldName)
        {
            if (string.IsNullOrWhiteSpace(value)) return null;
            
            if (DateTime.TryParse(value, out DateTime result))
            {
                DebugLog($"Converted date '{value}' to {result:yyyy-MM-dd} for {fieldName}", "Info");
                return result;
            }
            
            DebugLog($"Warning: Could not convert '{value}' to DateTime for {fieldName}", "Warning");
            return null;
        }

        private async Task ProcessGLImport()
        {
            DebugLog("Parsing XML document for GL import...", "Info");
            var xmlDoc = XDocument.Parse(xmlData);
            DebugLog($"XML parsed successfully. Root element: {xmlDoc.Root?.Name}", "Success");

            if (selectedGLPolicy == null)
            {
                throw new InvalidOperationException("Selected GL policy not found.");
            }

            DebugLog($"Processing GL policy: {selectedGLPolicy.PolicyNumber} (ID: {selectedGLPolicy.PolicyId})", "Info");

            // Step 1: Find GeneralLiability root element
            DebugLog("Step 1: Looking for <GeneralLiability> root element...", "Info");

            var glElement = xmlDoc.Descendants("GeneralLiability").FirstOrDefault();

            if (glElement == null)
            {
                DebugLog("❌ <GeneralLiability> element NOT found in XML", "Error");
                throw new InvalidOperationException("GeneralLiability element not found in XML");
            }
            else
            {
                DebugLog("✅ <GeneralLiability> element found successfully", "Success");

                // Step 2: Count policy nodes within GeneralLiability
                var policyNodes = glElement.Elements("policy").ToList();
                DebugLog($"Found {policyNodes.Count} <policy> nodes in <GeneralLiability>", "Info");

                // Step 3: List all Policy details
                if (policyNodes.Any())
                {
                    DebugLog("Extracting GL Policy details:", "Info");

                    for (int i = 0; i < policyNodes.Count; i++)
                    {
                        var policy = policyNodes[i];
                        var basicDetails = policy.Element("basicdetails");

                        if (basicDetails != null)
                        {
                            var policyNum = basicDetails.Element("policynumber")?.Value ?? "N/A";
                            var polEffDate = basicDetails.Element("effectivedate")?.Value ?? "N/A";
                            var polExpDate = basicDetails.Element("expirationdate")?.Value ?? "N/A";
                            
                            DebugLog($"Found GL Policy[{i}] {policyNum} ({polEffDate} - {polExpDate})", "Info");
                            
                            // Check if this policy matches the selected one
                            if (policyNum == selectedGLPolicy.PolicyNumber)
                            {
                                DebugLog($"✅ GL POLICY MATCH FOUND: {policyNum}", "Success");
                                await GLPolicyMatched(policy, i);
                            }
                        }
                        else
                        {
                            DebugLog($"GeneralLiability.policy[{i}]: basicdetails element not found", "Warning");
                        }
                    }
                }
                else
                {
                    DebugLog("No <policy> nodes found within <GeneralLiability>", "Warning");
                }
            }

            DebugLog("GL XML analysis completed", "Success");
        }

        private async Task GLPolicyMatched(XElement matchedPolicy, int policyIndex)
        {
            DebugLog($"=== ANALYZING MATCHED GL POLICY [{policyIndex}] ===", "Success");
            
            // 1. Process GL Coverage Limits
            DebugLog("--- Processing GL Coverage Limits ---", "Info");
            await ProcessGLCoverageLimits(matchedPolicy, policyIndex);
            
            // 2. Process Rating Basis from hazards
            DebugLog("--- Processing GL Rating Basis ---", "Info");
            await ProcessGLRatingBasis(matchedPolicy, policyIndex);
            
            DebugLog($"=== GL POLICY [{policyIndex}] ANALYSIS COMPLETE ===", "Success");
        }

        private async Task ProcessGLCoverageLimits(XElement matchedPolicy, int policyIndex)
        {
            try
            {
                DebugLog($"--- Processing GL Coverage Limits for Policy [{policyIndex}] ---", "Info");
                
                // Extract basic details for policy information
                var basicDetails = matchedPolicy.Element("basicdetails");
                var premium = basicDetails?.Element("premium")?.Value ?? "";
                
                // Extract coverage limits from coverages.limits
                var coverages = matchedPolicy.Element("coverages");
                if (coverages == null)
                {
                    DebugLog("coverages element not found - cannot process coverage limits", "Warning");
                    return;
                }

                var limits = coverages.Element("limits");
                if (limits == null)
                {
                    DebugLog("limits element not found - cannot process coverage limits", "Warning");
                    return;
                }

                // Extract limit values based on the mapping
                var productsAggregateStr = limits.Element("ProdAndCompOpsAggregate")?.Element("covlmtprodcompoperaggr")?.Value ?? "";
                var generalAggregateStr = limits.Element("GenAggAppliesTo")?.Element("covlmtgenaggapplto")?.Value ?? "";
                var personalInjuryStr = limits.Element("PIADV")?.Element("covlmtpersadvinj")?.Value ?? "";
                var eachOccurrenceStr = limits.Element("EAOCC")?.Element("covlmteaoccur")?.Value ?? "";
                var damageToPremisesStr = limits.Element("FIRDM")?.Element("covlmtdamrentedpremeaoccur")?.Value ?? "";
                var medicalExpensesStr = limits.Element("MEDEX")?.Element("covlmtmedexponeper")?.Value ?? "";
                var additionalCoverageName = limits.Element("Other")?.Element("covlmtothrdesc")?.Value ?? "";
                var additionalCoverageLimitStr = limits.Element("Other")?.Element("covlmtothr")?.Value ?? "";

                // Extract Claims Made/Occurrence from coverage
                var coverage = coverages.Element("coverage");
                var claimsMadeOccurrence = coverage?.Element("ClaimsMadeOrOccurrence")?.Element("description")?.Value ?? "";

                DebugLog($"Raw XML Coverage Data:", "Info");
                DebugLog($"  ProductsAggregate: {productsAggregateStr}", "Info");
                DebugLog($"  GeneralAggregate: {generalAggregateStr}", "Info");
                DebugLog($"  PersonalInjury: {personalInjuryStr}", "Info");
                DebugLog($"  EachOccurrence: {eachOccurrenceStr}", "Info");
                DebugLog($"  DamageToPremises: {damageToPremisesStr}", "Info");
                DebugLog($"  MedicalExpenses: {medicalExpensesStr}", "Info");
                DebugLog($"  ClaimsMadeOrOccurrence: {claimsMadeOccurrence}", "Info");

                // Convert values
                var productsAggregate = ConvertLimitToInt(productsAggregateStr, "ProductsAggregate");
                var generalAggregate = ConvertLimitToInt(generalAggregateStr, "GeneralAggregate");
                var personalInjury = ConvertLimitToInt(personalInjuryStr, "PersonalInjury");
                var eachOccurrence = ConvertLimitToInt(eachOccurrenceStr, "EachOccurrence");
                var damageToPremises = ConvertLimitToInt(damageToPremisesStr, "DamageToPremises");
                var medicalExpenses = ConvertLimitToInt(medicalExpensesStr, "MedicalExpenses");
                var additionalCoverageLimit = ConvertLimitToInt(additionalCoverageLimitStr, "AdditionalCoverageLimit");
                var premiumDecimal = ConvertCurrencyToDecimal(premium, "Premium");

                // Determine Claims Made vs Occurrence
                var isClaimsMade = !string.IsNullOrWhiteSpace(claimsMadeOccurrence) && 
                                   claimsMadeOccurrence.ToLower().Contains("laim");
                var isOccurrence = !string.IsNullOrWhiteSpace(claimsMadeOccurrence) && 
                                   claimsMadeOccurrence.ToLower().Contains("curre");

                DebugLog($"Converted Values:", "Info");
                DebugLog($"  ProductsAggregate: {productsAggregate:N0}", "Info");
                DebugLog($"  GeneralAggregate: {generalAggregate:N0}", "Info");
                DebugLog($"  PersonalInjury: {personalInjury:N0}", "Info");
                DebugLog($"  EachOccurrence: {eachOccurrence:N0}", "Info");
                DebugLog($"  DamageToPremises: {damageToPremises:N0}", "Info");
                DebugLog($"  MedicalExpenses: {medicalExpenses:N0}", "Info");
                DebugLog($"  ClaimsMade: {isClaimsMade}, Occurrence: {isOccurrence}", "Info");

                // Create or update GeneralLiabilityCoverage
                var glCoverage = new GeneralLiabilityCoverage
                {
                    PolicyId = selectedGLPolicy.PolicyId,
                    ProductsAggregate = productsAggregate,
                    GeneralAggregate = generalAggregate,
                    PersonalInjury = personalInjury,
                    EachOccurrence = eachOccurrence,
                    DamageToPremises = damageToPremises,
                    MedicalExpenses = medicalExpenses,
                    AdditionalCoverageName = !string.IsNullOrWhiteSpace(additionalCoverageName) ? additionalCoverageName : null,
                    AdditionalCoverageLimit = additionalCoverageLimit,
                    Premium = premiumDecimal,
                    ClaimsMade = isClaimsMade,
                    Occurence = isOccurrence,
                    DateModified = DateTime.UtcNow
                };

                // Upsert the coverage record
                await PolicyService.UpsertGeneralLiabilityCoverageAsync(glCoverage);
                
                DebugLog($"✅ Successfully processed GL Coverage limits", "Success");
            }
            catch (Exception ex)
            {
                DebugLog($"❌ Error processing GL coverage limits: {ex.Message}", "Error");
            }
        }

        private async Task ProcessGLRatingBasis(XElement matchedPolicy, int policyIndex)
        {
            try
            {
                DebugLog($"--- Processing GL Rating Basis for Policy [{policyIndex}] ---", "Info");
                
                // Look for hazards element within the matched policy
                var hazardsElement = matchedPolicy.Element("hazards");
                if (hazardsElement == null)
                {
                    DebugLog("hazards element not found within the policy", "Warning");
                    return;
                }

                var hazardNodes = hazardsElement.Elements("hazard").ToList();
                DebugLog($"Found {hazardNodes.Count} hazard nodes within policy", "Info");

                for (int hazardIndex = 0; hazardIndex < hazardNodes.Count; hazardIndex++)
                {
                    var hazardNode = hazardNodes[hazardIndex];
                    DebugLog($"--- Processing Hazard [{hazardIndex}] ---", "Info");
                    
                    var hazardDetails = hazardNode.Element("hazarddetails");
                    if (hazardDetails == null)
                    {
                        DebugLog($"hazard[{hazardIndex}]: hazarddetails element not found", "Warning");
                        continue;
                    }

                    var classCode = hazardDetails.Element("hazclascode")?.Value ?? "";
                    var classDescription = hazardDetails.Element("hazclass")?.Value ?? "";
                    var exposure = hazardDetails.Element("hazexpos")?.Value ?? "";
                    var baseRateStr = hazardDetails.Element("hazratepremoper")?.Value ?? "";
                    var premiumStr = hazardDetails.Element("hazpremumpremoper")?.Value ?? "";

                    var premBasis = hazardNode.Element("PremBasis");
                    var basis = premBasis?.Element("description")?.Value ?? "";

                    DebugLog($"Hazard Data - ClassCode: {classCode}, Description: {classDescription}, Exposure: {exposure}", "Info");
                    DebugLog($"  BaseRate: {baseRateStr}, Premium: {premiumStr}, Basis: {basis}", "Info");

                    // Convert values
                    var baseRate = ConvertToDecimal(baseRateStr, "BaseRate");
                    var premium = ConvertCurrencyToDecimal(premiumStr, "Premium");

                    // Create or update RatingBasis
                    var ratingBasis = new RatingBasis
                    {
                        PolicyId = selectedGLPolicy.PolicyId,
                        ClassCode = classCode,
                        ClassDescription = classDescription,
                        Exposure = exposure,
                        Basis = basis,
                        BaseRate = baseRate,
                        Premium = premium,
                        DateModified = DateTime.UtcNow
                    };

                    await PolicyService.UpsertRatingBasisAsync(ratingBasis);
                    DebugLog($"✅ Successfully processed Hazard ClassCode: {classCode}", "Success");
                }
            }
            catch (Exception ex)
            {
                DebugLog($"❌ Error processing GL rating basis: {ex.Message}", "Error");
            }
        }

        private async Task ProcessAllMatchingPolicies()
        {
            DebugLog("=== PROCESSING ALL MATCHING POLICIES START ===", "Info");
            
            try
            {
                var xmlDoc = XDocument.Parse(xmlData);
                var policiesProcessed = 0;
                var policiesMatched = 0;

                // Process Work Comp policies
                var workersCompElement = xmlDoc.Descendants("WorkersCompensation").FirstOrDefault();
                if (workersCompElement != null)
                {
                    var wcPolicyNodes = workersCompElement.Elements("Policy").ToList();
                    DebugLog($"Found {wcPolicyNodes.Count} WorkersCompensation policies in XML", "Info");

                    foreach (var policyNode in wcPolicyNodes)
                    {
                        var basicDetails = policyNode.Element("BasicDetails");
                        if (basicDetails != null)
                        {
                            var policyNumber = basicDetails.Element("policynum")?.Value ?? "";
                            var matchingPolicy = allPolicies.FirstOrDefault(p => p.PolicyNumber == policyNumber);
                            
                            if (matchingPolicy != null)
                            {
                                DebugLog($"✅ Found matching Work Comp policy: {policyNumber}", "Success");
                                selectedWorkCompPolicy = matchingPolicy;
                                await WorkCompPolicyMatched(policyNode, policiesMatched);
                                policiesProcessed++;
                                policiesMatched++;
                            }
                            else
                            {
                                DebugLog($"⚠️ Work Comp policy not found in client policies: {policyNumber}", "Warning");
                            }
                        }
                    }
                }

                // Process Auto policies
                var businessAutoElement = xmlDoc.Descendants("businessauto").FirstOrDefault();
                if (businessAutoElement != null)
                {
                    var autoPolicyNodes = businessAutoElement.Elements("policy").ToList();
                    DebugLog($"Found {autoPolicyNodes.Count} businessauto policies in XML", "Info");

                    foreach (var policyNode in autoPolicyNodes)
                    {
                        var basicDetails = policyNode.Element("basicdetails");
                        if (basicDetails != null)
                        {
                            var policyNumber = basicDetails.Element("policynum")?.Value ?? "";
                            var matchingPolicy = allPolicies.FirstOrDefault(p => p.PolicyNumber == policyNumber);
                            
                            if (matchingPolicy != null)
                            {
                                DebugLog($"✅ Found matching Auto policy: {policyNumber}", "Success");
                                selectedAutoPolicy = matchingPolicy;
                                await AutoPolicyMatched(policyNode, policiesMatched);
                                policiesProcessed++;
                                policiesMatched++;
                            }
                            else
                            {
                                DebugLog($"⚠️ Auto policy not found in client policies: {policyNumber}", "Warning");
                            }
                        }
                    }
                }

                // Process GL policies
                var glElement = xmlDoc.Descendants("GeneralLiability").FirstOrDefault();
                if (glElement != null)
                {
                    var glPolicyNodes = glElement.Elements("policy").ToList();
                    DebugLog($"Found {glPolicyNodes.Count} GeneralLiability policies in XML", "Info");

                    foreach (var policyNode in glPolicyNodes)
                    {
                        var basicDetails = policyNode.Element("basicdetails");
                        if (basicDetails != null)
                        {
                            var policyNumber = basicDetails.Element("policynumber")?.Value ?? "";
                            var matchingPolicy = allPolicies.FirstOrDefault(p => p.PolicyNumber == policyNumber);
                            
                            if (matchingPolicy != null)
                            {
                                DebugLog($"✅ Found matching GL policy: {policyNumber}", "Success");
                                selectedGLPolicy = matchingPolicy;
                                await GLPolicyMatched(policyNode, policiesMatched);
                                policiesProcessed++;
                                policiesMatched++;
                            }
                            else
                            {
                                DebugLog($"⚠️ GL policy not found in client policies: {policyNumber}", "Warning");
                            }
                        }
                    }
                }

                DebugLog($"=== ALL MATCHING POLICIES PROCESSING COMPLETE ===", "Success");
                DebugLog($"Processed {policiesProcessed} policies from XML data", "Success");
            }
            catch (Exception ex)
            {
                DebugLog($"❌ Error processing all matching policies: {ex.Message}", "Error");
                throw;
            }
        }

        private async Task SaveXmlAsAttachment()
        {
            try
            {
                DebugLog("=== SAVING XML AS ATTACHMENT START ===", "Info");
                
                if (!ClientId.HasValue)
                {
                    DebugLog("Cannot save attachment: ClientId is null", "Error");
                    return;
                }

                // Create a cleaned and formatted XML content
                var cleanedXml = xmlData;
                
                // Pretty format the XML
                try
                {
                    var xmlDoc = XDocument.Parse(cleanedXml);
                    cleanedXml = xmlDoc.ToString();
                    DebugLog("XML formatted successfully", "Info");
                }
                catch (Exception ex)
                {
                    DebugLog($"Could not format XML, using original: {ex.Message}", "Warning");
                }

                // Generate filename with timestamp
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var originalFileName = $"SyncData_Import_{timestamp}.xml";
                
                // Create the syncdata directory path
                var syncdataPath = Path.Combine("uploads", "clients", ClientId.Value.ToString(), "syncdata");
                var absoluteSyncdataPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", syncdataPath);
                
                if (!Directory.Exists(absoluteSyncdataPath))
                {
                    Directory.CreateDirectory(absoluteSyncdataPath);
                    DebugLog($"Created syncdata directory: {absoluteSyncdataPath}", "Info");
                }

                // Generate hashed filename
                var hash = Surefire.Domain.Shared.Helpers.StringHelper.GenerateFiveCharacterHash(Path.GetFileNameWithoutExtension(originalFileName));
                var hashedFileName = $"{Path.GetFileNameWithoutExtension(originalFileName)}_{hash}.xml";
                
                // Write the XML file
                var filePath = Path.Combine(absoluteSyncdataPath, hashedFileName);
                await File.WriteAllTextAsync(filePath, cleanedXml);
                
                var fileInfo = new FileInfo(filePath);
                DebugLog($"XML file written to: {filePath} (Size: {fileInfo.Length} bytes)", "Info");

                // Create attachment record
                var attachment = new Attachment
                {
                    OriginalFileName = originalFileName,
                    HashedFileName = hashedFileName,
                    LocalPath = syncdataPath.Replace("\\", "/"),
                    FileFormat = ".xml",
                    FileSize = fileInfo.Length,
                    DateCreated = DateTime.UtcNow,
                    ClientId = ClientId.Value,
                    Description = "Processed XML sync data import",
                    IsClientAccessible = false,
                    Status = 1
                };

                // Save attachment to database
                await AttachmentService.SaveAttachmentDirectlyAsync(attachment);
                
                DebugLog($"✅ Successfully saved XML attachment: {originalFileName}", "Success");
                
                MessageService.ShowMessageBar(options =>
                {
                    options.Title = "📎 Attachment Saved";
                    options.Body = $"XML data saved as attachment: {originalFileName}";
                    options.Intent = MessageIntent.Info;
                    options.ClearAfterNavigation = false;
                });
            }
            catch (Exception ex)
            {
                DebugLog($"❌ Error saving XML as attachment: {ex.Message}", "Error");
                MessageService.ShowMessageBar(options =>
                {
                    options.Title = "❌ Attachment Save Failed";
                    options.Body = $"Failed to save XML attachment: {ex.Message}";
                    options.Intent = MessageIntent.Error;
                    options.ClearAfterNavigation = false;
                });
            }
        }
    }
}