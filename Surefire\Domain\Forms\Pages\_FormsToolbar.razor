@using Syncfusion.Blazor.SplitButtons
@using Microsoft.FluentUI.AspNetCore.Components

<div class="page-toolbar">
    <FluentMenuButton ButtonAppearance="Appearance.Accent" Text="Forms" OnMenuChanged="HandleOnMenuChanged">
        <FluentMenuItem Id="Certificate"><FluentIcon Value="@(new Icons.Regular.Size20.Certificate())" Color="Color.Custom" CustomColor="#000" Slot="start" /> Certificate</FluentMenuItem>
        <FluentMenuItem Id="Application"><FluentIcon Value="@(new Icons.Regular.Size20.Clipboard())" Color="Color.Custom" CustomColor="#000" Slot="start" /> Application</FluentMenuItem>
        <FluentMenuItem Id="LossRunsRequest"><FluentIcon Value="@(new Icons.Regular.Size20.DocumentSearch())" Color="Color.Custom" CustomColor="#000" Slot="start" /> Loss Runs Request</FluentMenuItem>
    </FluentMenuButton>

    <span class="sf-verthr"></span>

    <a class="toolbar-link @(IsDisabled("Certificate") ? "toolbar-disabled" : "")" href="/Forms/Certificate">
        <FluentIcon Value="@(new Icons.Regular.Size24.Certificate())" />
        <span class="toolbar-text">Certificates</span>
    </a>

    <a class="toolbar-link @(IsDisabled("Application") ? "toolbar-disabled" : "")" href="/Forms/Application">
        <FluentIcon Value="@(new Icons.Regular.Size24.Clipboard())" />
        <span class="toolbar-text">Applications</span>
    </a>
    
    <a class="toolbar-link @(IsDisabled("LossRunsRequest") ? "toolbar-disabled" : "")" href="/Forms/LossRunsRequest">
        <FluentIcon Value="@(new Icons.Regular.Size24.DocumentSearch())" />
        <span class="toolbar-text">Loss Runs</span>
    </a>
</div>

@code {
    [Parameter] public string PageName { get; set; } = "";
    
    private async Task HandleOnMenuChanged(MenuChangeEventArgs args)
    {
        switch (args.Id)
        {
            case "Certificate":
                Navigation.NavigateTo("/Forms/Certificate");
                break;
            case "Application":
                Navigation.NavigateTo("/Forms/Application");
                break;
            case "LossRunsRequest":
                Navigation.NavigateTo("/Forms/LossRunsRequest");
                break;
        }
    }

    private bool IsDisabled(string toolbarPageName)
    {
        // Return true to disable, false to enable
        return string.Equals(toolbarPageName, PageName, StringComparison.OrdinalIgnoreCase);
    }
} 