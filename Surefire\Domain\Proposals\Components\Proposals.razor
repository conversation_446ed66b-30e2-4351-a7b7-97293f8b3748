﻿@page "/clients/{ClientId:int}/proposals"
@namespace Surefire.Domain.Proposals
@using Microsoft.FluentUI.AspNetCore.Components
@using Surefire.Data
@using System.Linq
@using System.IO
@using Syncfusion.Blazor.SfPdfViewer
@using Syncfusion.Blazor.Inputs
@using Microsoft.JSInterop
@using Surefire.Domain.Attachments.Models
@using Surefire.Domain.Attachments.Services
@using Syncfusion.PdfToImageConverter
@using System.Drawing
@using SixLabors.ImageSharp
@using SixLabors.ImageSharp.Processing
@using Newtonsoft.Json
@using Newtonsoft.Json.Linq
@inject ProposalService ProposalService
@inject AttachmentService AttachmentService
@inject NavigationManager Navigation
@inject IJSRuntime JS
@inject IWebHostEnvironment Environment

@if (!showPdfPrepper && !showPdfUploader && !showProcessing)
{
    <FluentStack>
        <FluentLabel Typo="Typography.H3">Proposals for Client @ClientId</FluentLabel>
        <FluentStack Orientation="Orientation.Horizontal" Gap="10">
            <FluentButton Disabled="@isCreating" OnClick="@CreateNewProposal">
                @if (isCreating)
                {
                    <FluentProgressRing />
                    <span class="ms-2">Creating...</span>
                }
                else
                {
                    <span>Create New Proposal</span>
                }
            </FluentButton>
            <FluentButton Appearance="Appearance.Outline" OnClick="@(() => showPdfUploader = true)">
                <span>Upload PDF</span>
            </FluentButton>
        </FluentStack>
    </FluentStack>

    @if (error != null)
    {
        <FluentMessageBar Intent="MessageIntent.Error" OnDismiss="@(() => error = null)">
            @error
        </FluentMessageBar>
    }

    @if (statusMessage != null)
    {
        <FluentMessageBar Intent="MessageIntent.Success" OnDismiss="@(() => statusMessage = null)">
            @statusMessage
        </FluentMessageBar>
    }

    @if (proposalList == null)
    {
        <FluentStack HorizontalAlignment="HorizontalAlignment.Center" Class="my-5">
            <FluentProgressRing />
            <FluentLabel>Loading proposals...</FluentLabel>
        </FluentStack>
    }
    else if (!proposalList.Any())
    {
        <FluentStack HorizontalAlignment="HorizontalAlignment.Center" Class="my-5">
            <FluentLabel Typo="Typography.Body">No proposals found. Click the "Create New Proposal" button to get started or upload a PDF.</FluentLabel>
        </FluentStack>
    }
    else
    {
        <FluentDataGrid @ref="@proposalDataGrid" Items="@proposalListQueryable" ResizableColumns="true" TGridItem="Proposal">
            <PropertyColumn Property="@(p => p.ProposalId)" Title="ID" Sortable="true" />
            <PropertyColumn Property="@(p => p.IsApproved)" Title="Status" Sortable="true" />
            <PropertyColumn Property="@(p => p.RawJsonAttachmentId)" Title="RawJsonId" Sortable="true" />
            <PropertyColumn Property="@(p => p.CleanedJsonAttachmentId)" Title="CleanJsonId" Sortable="true" />
            <PropertyColumn Property="@(p => p.DateCreated)" Title="Created" Sortable="true" Format="MMM dd, yyyy" />
            <PropertyColumn Property="@(p => p.DateModified)" Title="Modified" Sortable="true" Format="MMM dd, yyyy" />
            <TemplateColumn Title="Actions">
                <FluentStack Orientation="Orientation.Horizontal" Gap="5">
                    <FluentButton Appearance="Appearance.Lightweight" OnClick="@(() => Navigation.NavigateTo($"/Proposals/{context.ClientId}"))">
                        Edit
                    </FluentButton>
                    <FluentButton Appearance="Appearance.Lightweight" OnClick="@(() => ShowFileUploaderForProposal(context.ProposalId))">
                        Upload PDF
                    </FluentButton>
                    @if (context.RawJsonAttachmentId.HasValue)
                    {
                        <FluentButton Appearance="Appearance.Accent" OnClick="@(() => ShowCleaner(ClientId, context.ProposalId))">
                            Cleaner
                        </FluentButton>
                    }
                </FluentStack>
            </TemplateColumn>
        </FluentDataGrid>
    }
}
else if (showPdfUploader)
{
    <FluentStack Orientation="Orientation.Horizontal" VerticalAlignment="VerticalAlignment.Center" HorizontalAlignment="HorizontalAlignment.Stretch">
        <FluentButton Appearance="Appearance.Lightweight" OnClick="@(() => { showPdfUploader = false; showPdfPrepper = false; })">
            <FluentIcon Value="@(new Icons.Regular.Size16.ArrowLeft())" />
            <span class="ms-2">Back to Proposals</span>
        </FluentButton>
        <FluentLabel Typo="Typography.H3">Upload PDF for Proposal</FluentLabel>
    </FluentStack>

    @if (error != null)
    {
        <FluentMessageBar Intent="MessageIntent.Error" OnDismiss="@(() => error = null)">
            @error
        </FluentMessageBar>
    }

    @if (statusMessage != null)
    {
        <FluentMessageBar Intent="MessageIntent.Success" OnDismiss="@(() => statusMessage = null)">
            @statusMessage
        </FluentMessageBar>
    }

    <FluentCard>
        <FluentStack Orientation="Orientation.Horizontal" VerticalAlignment="VerticalAlignment.Center" HorizontalAlignment="HorizontalAlignment.Stretch">
            <FluentLabel>Upload a PDF document to prepare for proposal</FluentLabel>

            @* ------           File Uploader for Uppin Dem Filezz    ------ *@

            <SfUploader @ref="uploader" AllowedExtensions=".pdf" MaxFileSize="10000000" AllowMultiple="false" AutoUpload="true" ShowFileList="true">
                <UploaderEvents OnFailure="OnUploadFailure" ValueChange="OnChange" OnActionComplete="OnCompleted" />
            </SfUploader>
            @* ------  ----------------------------------------------- ------ *@

            <FluentButton Appearance="Appearance.Accent" OnClick="@ProcessUpload" Class="ms-2">
                Continue to PDF Viewer
            </FluentButton>
        </FluentStack>
    </FluentCard>
}
else if (showPdfPrepper)
{
    <FluentStack Orientation="Orientation.Horizontal" VerticalAlignment="VerticalAlignment.Center" HorizontalAlignment="HorizontalAlignment.Stretch">
        <FluentButton Appearance="Appearance.Lightweight" OnClick="@(() => { showPdfUploader = true; showPdfPrepper = false; })">
            <FluentIcon Value="@(new Icons.Regular.Size16.ArrowLeft())" />
            <span class="ms-2">Back to Upload</span>
        </FluentButton>
        <FluentLabel Typo="Typography.H3">Select Pages for Proposal @selectedProposalId</FluentLabel>
        <FluentButton Disabled="@(selectedPages.Count == 0)" OnClick="@ProcessSelectedPages">
            <span>Process Selected Pages</span>
        </FluentButton>
    </FluentStack>

    @if (error != null)
    {
        <FluentMessageBar Intent="MessageIntent.Error" OnDismiss="@(() => error = null)">
            @error
        </FluentMessageBar>
    }

    @if (statusMessage != null)
    {
        <FluentMessageBar Intent="MessageIntent.Success" OnDismiss="@(() => statusMessage = null)">
            @statusMessage
        </FluentMessageBar>
    }

    <FluentDivider></FluentDivider>

    <FluentStack Orientation="Orientation.Horizontal">
        <FluentStack Style="width: 50%;" VerticalAlignment="VerticalAlignment.Top">
            @if (pdfLoaded)
            {
                <div class="control-section" style="height:1000px; width:100%;">

                    @* ------      PDF Viewer for Selecting and Deleting Pages ------ *@

                    <SfPdfViewer2 @ref=pdfViewer DocumentPath="@DocumentPath" Height="1000px" Width="100%">
                        <PdfViewerEvents DocumentLoaded="OnDocumentLoaded" PageChanged="OnPageChanged"></PdfViewerEvents>
                    </SfPdfViewer2>
                    @* ------  ----------------------------------------------- ------ *@

                </div>
            }
            else
            {
                <FluentCard Style="height:1000px; width:100%;">
                    <div class="d-flex justify-content-center align-items-center">
                        <FluentLabel>PDF is loading...</FluentLabel>
                    </div>
                </FluentCard>
            }
        </FluentStack>

        <FluentStack Style="width: 50%;" VerticalAlignment="VerticalAlignment.Top">
            <FluentCard>
                <FluentLabel Typo="Typography.H5">Selected Pages</FluentLabel>
                <FluentDivider></FluentDivider>

                @if (totalPages > 0)
                {
                    <FluentStack Orientation="Orientation.Horizontal" VerticalAlignment="VerticalAlignment.Center" HorizontalAlignment="HorizontalAlignment.Stretch">
                        <FluentLabel>Current Page: @currentPage of @totalPages</FluentLabel>
                        <FluentButton OnClick="@AddCurrentPage" Disabled="@(selectedPages.Contains(currentPage))">
                            Add Current Page
                        </FluentButton>
                    </FluentStack>

                    <FluentDivider></FluentDivider>

                    @if (selectedPages.Count > 0)
                    {
                        <div class="selected-pages-grid">
                            @foreach (var page in selectedPages.OrderBy(p => p))
                            {
                                <div class="page-card @(newlyAddedPage == page ? "new-card" : "") @(isRemoving ? "removing" : "")" @key="page">
                                    <div class="page-preview">
                                        @if (pageThumbnails.ContainsKey(page))
                                        {
                                            <img src="@pageThumbnails[page]" alt="Page @(page) preview" />
                                        }
                                        <div class="page-actions">
                                            <FluentButton Appearance="Appearance.Lightweight" 
                                                         OnClick="@(() => RemovePage(page))"
                                                         Class="delete-button">
                                                <FluentIcon Value="@(new Icons.Regular.Size16.Delete())" />
                                            </FluentButton>
                                            <FluentButton Appearance="Appearance.Lightweight" 
                                                         OnClick="@(() => GoToPage(page))"
                                                         Class="view-button">
                                                <FluentIcon Value="@(new Icons.Regular.Size16.Eye())" />
                                            </FluentButton>
                                        </div>
                                        <div class="page-number">Page @(page)</div>
                                    </div>
                                </div>
                            }
                        </div>

                        <FluentDivider></FluentDivider>

                        <FluentButton OnClick="@ClearSelectedPages" Appearance="Appearance.Outline" Class="clear-button">
                            Clear All Pages
                        </FluentButton>
                    }
                    else
                    {
                        <FluentLabel>No pages selected. Add pages to process.</FluentLabel>
                    }
                }
                else
                {
                    <FluentLabel>PDF is loading...</FluentLabel>
                }
            </FluentCard>
        </FluentStack>
    </FluentStack>
}
else if (showProcessing)
{
    <FluentStack>
        <FluentButton Appearance="Appearance.Lightweight" OnClick="@(() => { showPdfPrepper = true; showProcessing = false; })" Disabled="@isProcessing">
            <FluentIcon Value="@(new Icons.Regular.Size16.ArrowLeft())" />
            <span class="ms-2">Back to Page Selection</span>
        </FluentButton>
        <FluentLabel Typo="Typography.H3">Processing Proposal @selectedProposalId</FluentLabel>
    </FluentStack>

    @if (error != null)
    {
        <FluentMessageBar Intent="MessageIntent.Error" OnDismiss="@(() => error = null)">
            @error
        </FluentMessageBar>
    }

    @if (statusMessage != null)
    {
        <FluentMessageBar Intent="MessageIntent.Success" OnDismiss="@(() => statusMessage = null)">
            @statusMessage
        </FluentMessageBar>
    }

    <FluentDivider></FluentDivider>

    <FluentCard Style="padding: 2rem; max-width: 800px; margin: 2rem auto;">
        <FluentStack VerticalAlignment="VerticalAlignment.Center">
            <FluentLabel Typo="Typography.H5" Style="margin-bottom: 1rem;">Processing @selectedPages.Count Pages</FluentLabel>
            
            <div class="progress-container">
                <div class="progress-bar-wrapper">
                    <div class="progress-bar" style="width: @processingProgress%">
                        <div class="progress-pulse"></div>
                    </div>
                </div>
                <div class="progress-label">@Math.Round(processingProgress)%</div>
            </div>
            
            <div class="processing-status">
                <FluentLabel>@processingStatusMessage</FluentLabel>
            </div>
            
            <div class="page-indicators">
                @for (int i = 0; i < selectedPages.Count; i++)
                {
                    int pageIndex = i;
                    int pageNumber = selectedPages[i];
                    string status = "pending";
                    
                    if (processingProgress >= 80)
                    {
                        status = "completed";
                    }
                    else if (processingProgress >= 20)
                    {
                        double progressPerPage = 60.0 / selectedPages.Count;
                        double pageStartProgress = 20 + (pageIndex * progressPerPage);
                        double pageEndProgress = 20 + ((pageIndex + 1) * progressPerPage);
                        
                        if (processingProgress >= pageEndProgress)
                        {
                            status = "completed";
                        }
                        else if (processingProgress >= pageStartProgress)
                        {
                            status = "in-progress";
                        }
                    }
                    
                    <div class="page-indicator @status" title="Page @pageNumber">
                        <div class="page-number">@pageNumber</div>
                        @if (status == "completed")
                        {
                            <div class="status-icon">
                                <FluentIcon Value="@(new Icons.Regular.Size16.CheckmarkCircle())" />
                            </div>
                        }
                        else if (status == "in-progress")
                        {
                            <div class="status-icon">
                                <FluentProgressRing Size="ProgressRingSize.Tiny" />
                            </div>
                        }
                    </div>
                }
            </div>
            
            @if (processingSteps.Any())
            {
                <div class="processing-steps">
                    @foreach (var step in processingSteps)
                    {
                        <div class="processing-step @(step.IsCompleted ? "completed" : "")">
                            <div class="step-icon">
                                @if (step.IsCompleted)
                                {
                                    <FluentIcon Value="@(new Icons.Regular.Size16.CheckmarkCircle())" />
                                }
                                else if (step.IsInProgress)
                                {
                                    <FluentProgressRing Size="ProgressRingSize.Tiny" />
                                }
                                else
                                {
                                    <FluentIcon Value="@(new Icons.Regular.Size16.Circle())" />
                                }
                            </div>
                            <div class="step-content">
                                <div class="step-title">@step.Title</div>
                                <div class="step-description">@step.Description</div>
                            </div>
                        </div>
                    }
                </div>
            }
            
            @if (!isProcessing && processingComplete)
            {
                <FluentButton Appearance="Appearance.Accent" OnClick="@ReturnToProposalList" Style="margin-top: 2rem;">
                    Return to Proposals
                </FluentButton>
            }
        </FluentStack>
    </FluentCard>
}
<style>
    .e-upload .e-file-select-wrap .e-btn, .e-upload .e-upload-actions .e-btn, .e-bigger.e-small .e-upload .e-file-select-wrap .e-btn, .e-bigger.e-small .e-upload .e-upload-actions .e-btn {
        font-size: .9em;
        padding: 0px 12px !important;
        color: #62a0d6;
        position: relative;
        top: -35px;
        left: 15px;
    }

    .selected-pages-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 1.5rem;
        padding: 1.5rem;
        justify-content: center;
        align-items: flex-start;
        min-height: 250px;
        position: relative;
        background-color: #f9f9f9;
        background-image: radial-gradient(#e0e0e0 1px, transparent 1px);
        background-size: 20px 20px;
        border-radius: 12px;
        margin: 1rem 0;
    }

    .page-card {
        width: 220px;
        position: relative;
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        transform-origin: center;
        margin: 0 0.5rem;
        flex: 0 0 auto;
        perspective: 1000px;
        z-index: 1;
    }

    .page-card.new-card {
        animation: addPageAnimation 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
        z-index: 2;
    }

    .page-preview {
        position: relative;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        background: white;
        transform-style: preserve-3d;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 4px solid white;
    }

    .page-preview::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 50%, rgba(0,0,0,0.1) 100%);
        pointer-events: none;
    }

    .page-preview:hover {
        transform: translateY(-5px) rotateY(5deg);
        box-shadow: 0 12px 24px rgba(0,0,0,0.15);
    }

    .page-preview img {
        width: 100%;
        height: auto;
        display: block;
        transition: filter 0.3s ease;
    }

    .page-preview:hover img {
        filter: brightness(1.05);
    }

    .page-actions {
        position: absolute;
        top: 8px;
        right: 8px;
        display: flex;
        gap: 8px;
        opacity: 0;
        transition: opacity 0.3s ease, transform 0.3s ease;
        transform: translateY(-10px);
    }

    .page-preview:hover .page-actions {
        opacity: 1;
        transform: translateY(0);
    }

    .page-number {
        position: absolute;
        bottom: 12px;
        left: 12px;
        background: rgba(0,0,0,0.7);
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        transition: transform 0.3s ease, background 0.3s ease;
    }

    .page-preview:hover .page-number {
        transform: scale(1.05);
        background: rgba(0,0,0,0.8);
    }

    .delete-button, .view-button {
        background: rgba(255,255,255,0.9) !important;
        border-radius: 50% !important;
        min-width: 36px !important;
        height: 36px !important;
        padding: 0 !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
        transition: transform 0.2s ease, background 0.2s ease, box-shadow 0.2s ease !important;
    }

    .delete-button:hover {
        background: #ffebee !important;
        transform: scale(1.1) !important;
        box-shadow: 0 4px 12px rgba(244,67,54,0.2) !important;
    }

    .view-button:hover {
        background: #e3f2fd !important;
        transform: scale(1.1) !important;
        box-shadow: 0 4px 12px rgba(33,150,243,0.2) !important;
    }

    @@keyframes addPageAnimation {
        0% {
            transform: scale(0) translateX(100px);
            opacity: 0;
        }
        60% {
            transform: scale(1.1) translateX(0);
            opacity: 1;
        }
        100% {
            transform: scale(1) translateX(0);
            opacity: 1;
        }
    }

    .removing {
        animation: scaleOut 0.5s cubic-bezier(0.6, -0.28, 0.735, 0.045) forwards;
    }

    @@keyframes scaleOut {
        0% {
            transform: scale(1) rotateY(0);
            opacity: 1;
        }
        100% {
            transform: scale(0) rotateY(90deg);
            opacity: 0;
        }
    }

    .clear-button {
        margin: 1.5rem;
        transition: all 0.3s ease;
    }

    .clear-button:hover {
        transform: translateY(-2px);
    }

    /* Processing styles */
    .progress-container {
        width: 100%;
        position: relative;
        margin: 1.5rem 0;
    }

    .progress-bar-wrapper {
        width: 100%;
        height: 12px;
        background-color: #f0f0f0;
        border-radius: 6px;
        overflow: hidden;
        box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
    }

    .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #4caf50, #8bc34a);
        border-radius: 6px;
        transition: width 0.5s ease;
        position: relative;
        overflow: hidden;
    }

    .progress-pulse {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, 
            rgba(255,255,255,0) 0%, 
            rgba(255,255,255,0.4) 50%, 
            rgba(255,255,255,0) 100%);
        animation: pulse 1.5s infinite;
        transform: translateX(-100%);
    }

    @@keyframes pulse {
        0% {
            transform: translateX(-100%);
        }
        100% {
            transform: translateX(100%);
        }
    }

    .progress-label {
        position: absolute;
        right: 0;
        top: -25px;
        font-weight: bold;
        color: #4caf50;
    }

    .processing-status {
        margin: 1rem 0;
        text-align: center;
        font-style: italic;
    }

    .processing-steps {
        margin-top: 2rem;
        width: 100%;
    }

    .processing-step {
        display: flex;
        margin-bottom: 1rem;
        padding: 1rem;
        border-radius: 8px;
        background-color: #f5f5f5;
        transition: all 0.3s ease;
    }

    .processing-step.completed {
        background-color: #e8f5e9;
    }

    .step-icon {
        margin-right: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #757575;
    }

    .processing-step.completed .step-icon {
        color: #4caf50;
    }

    .step-content {
        flex: 1;
    }

    .step-title {
        font-weight: bold;
        margin-bottom: 0.25rem;
    }

    .step-description {
        font-size: 0.875rem;
        color: #757575;
    }

    .page-indicators {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin: 1rem 0;
        justify-content: center;
    }

    .page-indicator {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        background-color: #f5f5f5;
        transition: all 0.3s ease;
    }

    .page-indicator.pending {
        border: 2px solid #e0e0e0;
    }

    .page-indicator.in-progress {
        border: 2px solid #2196f3;
        background-color: #e3f2fd;
    }

    .page-indicator.completed {
        border: 2px solid #4caf50;
        background-color: #e8f5e9;
    }

    .page-indicator .page-number {
        font-weight: bold;
        font-size: 14px;
    }

    .page-indicator .status-icon {
        position: absolute;
        bottom: -5px;
        right: -5px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    }

    .page-indicator.completed .status-icon {
        color: #4caf50;
    }

    .page-indicator.in-progress .status-icon {
        color: #2196f3;
    }
</style>

@code {
    [Parameter] public int ClientId { get; set; }
    [Parameter] public EventCallback<(int clientId, int proposalId)>? OnShowCleaner { get; set; }

    // Proposal list properties
    private List<Proposal> proposalList;
    private IQueryable<Proposal> proposalListQueryable;
    private bool isCreating;
    private string statusMessage;
    private string error;
    private FluentDataGrid<Proposal> proposalDataGrid;
    public string DocumentPath { get; set; }

    // PDF viewer properties
    private SfPdfViewer2 pdfViewer;
    private SfUploader uploader;
    private string documentPath;
    private string tempFileName;
    private string finishedFileName = "n/a";
    private bool pdfLoaded = false;
    private bool isProcessing = false;
    private int totalPages = 0;
    private int currentPage = 1;
    private List<int> selectedPages = new List<int>();
    private byte[] pdfBytes;
    private IQueryable<int> SelectedPagesQuery => selectedPages.OrderBy(p => p).AsQueryable();

    // View state properties
    private bool showPdfUploader = false;
    private bool showPdfPrepper = false;
    private bool showProcessing = false;
    private int? selectedProposalId = null;

    // Thumbnail properties
    private Dictionary<int, string> pageThumbnails = new Dictionary<int, string>();

    // New state properties
    private bool isRemoving = false;
    private int? newlyAddedPage = null;

    // Processing properties
    private double processingProgress = 0;
    private string processingStatusMessage = "";
    private bool processingComplete = false;
    private List<ProcessingStep> processingSteps = new List<ProcessingStep>();

    // Processing step class
    private class ProcessingStep
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public bool IsCompleted { get; set; }
        public bool IsInProgress { get; set; }
    }

    protected override async Task OnInitializedAsync()
    {
        try 
        {
            await LoadProposals();
        }
        catch (Exception ex)
        {
            error = $"Failed to load proposals: {ex.Message}";
            Console.WriteLine($"Error in Proposals.razor: {ex}");
        }
    }

    //Step 1: Load the proposal list
    private async Task LoadProposals()
    {
        proposalList = await ProposalService.GetProposalsForClientAsync(ClientId);
        proposalListQueryable = proposalList.AsQueryable();
    }

    //Step 2: Create a new proposal in the database and let's get uploading that quote!
    private async Task CreateNewProposal()
    {
        try
        {
            isCreating = true;
            int newProposalId = await ProposalService.CreateProposalForClientAsync(ClientId);
            statusMessage = $"Proposal #{newProposalId} created successfully!";
            await LoadProposals();
            if (proposalDataGrid != null)
            {
                await proposalDataGrid.RefreshDataAsync();
            }

            // Instead of navigating away, ask if they want to upload a PDF
            ShowFileUploaderForProposal(newProposalId);
        }
        catch (UnauthorizedAccessException)
        {
            error = "You must be logged in to create a proposal.";
        }
        catch (Exception ex)
        {
            error = $"Error creating proposal: {ex.Message}";
            Console.WriteLine($"Error creating proposal: {ex}");
        }
        finally
        {
            isCreating = false;
        }
    }

    //Step 3: Show the file upload button
    private void ShowFileUploaderForProposal(int proposalId)
    {
        selectedProposalId = proposalId;
        showPdfUploader = true;
        showPdfPrepper = false;
        pdfLoaded = false;
        selectedPages.Clear();
        statusMessage = $"Upload a PDF for Proposal #{proposalId}";
    }

    //Step 4: Upload the file to the temp folder
    private async Task OnChange(UploadChangeEventArgs args)
    {
        foreach (var file in args.Files)
        {
            string filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot/uploads/temp", file.FileInfo.Name);
            Console.WriteLine($"Path: {filePath}");

            using (var filestream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                await file.File.OpenReadStream(long.MaxValue).CopyToAsync(filestream);
            }
            finishedFileName = file.FileInfo.Name;
            DocumentPath = "wwwroot/uploads/temp/" + file.FileInfo.Name;
            Console.WriteLine($"OnChangeFilePathDone: {DocumentPath}");
        }
        statusMessage = $"File selected.";
    }
    private void OnUploadFailure(FailureEventArgs args)
    {
        Console.WriteLine("File upload failed: " + args.File.Name);
    }

    //Step 4b: Confirm we got a good status code from the uploader so we can load everything into the UI for the next step
    private async Task OnCompleted(ActionCompleteEventArgs args)
    {
        Console.WriteLine("Status: " + args.FileData[0].StatusCode);
        Console.WriteLine("Filename: " + args.FileData[0].Name);
        Console.WriteLine("Size: " + args.FileData[0].Size);
        Console.WriteLine("Extension: " + Path.GetExtension(args.FileData[0].Name));
        Console.WriteLine("DocumentPath: " + DocumentPath);
        Console.WriteLine("FinishedFileName: " + finishedFileName);
        if(args.FileData[0].StatusCode == "2") {
            await ProcessUpload();
        }else{
            Console.WriteLine("Error!");
        }
    }

    //Step 5: Load as bytes and update the UI
    private async Task ProcessUpload()
    {
        Console.WriteLine($"DocumentPath: {DocumentPath}");
        try
        {
            if (string.IsNullOrEmpty(DocumentPath))
            {
                Console.WriteLine($"DocumentPath is null!");
                error = "No file has been uploaded.";
                return;
            }

            if (!File.Exists(DocumentPath))
            {
                Console.WriteLine($"File not found!");
                error = "Uploaded file not found.";
                return;
            }

            // Read the file into memory
            pdfBytes = await File.ReadAllBytesAsync(DocumentPath);

            // Now we can switch to the PDF prepper view
            pdfLoaded = true;
            selectedPages.Clear();
            showPdfUploader = false;
            showPdfPrepper = true;

            statusMessage = "PDF uploaded successfully. Select pages to include in the proposal.";
            Console.WriteLine($"Switching view. Document path set to: {documentPath}");

            // Force a state update to ensure the PDF viewer is rendered with the correct path
            StateHasChanged();
        }
        catch (Exception ex)
        {
            error = $"Error processing upload: {ex.Message}";
            Console.WriteLine($"Error in ProcessUpload: {ex}");
            pdfLoaded = false;
        }
    }

    //Step 6: The PdfView Loads the file and we're off to selecting the pages
    private async Task OnDocumentLoaded()
    {
        Console.WriteLine("Doc Loaded...");
        try
        {
            if (pdfViewer != null)
            {
                Console.WriteLine("Getting Page Count...");
                totalPages = pdfViewer.PageCount;
                currentPage = 1;
                statusMessage = $"PDF loaded with {totalPages} pages. Select pages to include in the proposal.";
                Console.WriteLine($"PDF loaded successfully with {totalPages} pages");
            }
        }
        catch (Exception ex)
        {
            error = $"Error loading PDF: {ex.Message}";
            Console.WriteLine($"Error loading PDF: {ex}");
        }
    }

    private void OnPageChanged(PageChangeEventArgs args)
    {
        currentPage = args.CurrentPageNumber;
        StateHasChanged();
    }
    
    private async Task AddCurrentPage()
    {
        if (!selectedPages.Contains(currentPage))
        {
            try
            {
                // Get the current page as PDF bytes
                byte[] pageData = await pdfViewer.GetDocumentAsync();
                
                // Create a unique filename for this page's thumbnail
                string fileName = $"page_{currentPage}_{DateTime.UtcNow:yyyyMMddHHmmss}.jpg";
                string tempDirectory = Path.Combine(Environment.WebRootPath, "uploads", "temp");
                if (!Directory.Exists(tempDirectory))
                {
                    Directory.CreateDirectory(tempDirectory);
                }
                
                string thumbnailPath = Path.Combine(tempDirectory, fileName);
                
                // Convert PDF page to image using PdfToImageConverter
                using (var inputStream = new MemoryStream(pageData))
                {
                    var imageConverter = new PdfToImageConverter();
                    imageConverter.Load(inputStream);
                    
                    // Convert the current page (0-based index)
                    using (var outputStream = imageConverter.Convert(currentPage - 1, false, false))
                    using (var memoryStream = outputStream as MemoryStream)
                    {
                        byte[] imageBytes = memoryStream.ToArray();
                        using (var image = System.Drawing.Image.FromStream(new MemoryStream(imageBytes)))
                        {
                            // Resize the image to a 300px width while maintaining aspect ratio
                            int thumbnailWidth = 300;
                            int thumbnailHeight = (int)(image.Height * (300.0 / image.Width));
                            
                            using (var thumbnail = new System.Drawing.Bitmap(image, new System.Drawing.Size(thumbnailWidth, thumbnailHeight)))
                            {
                                thumbnail.Save(thumbnailPath, System.Drawing.Imaging.ImageFormat.Jpeg);
                            }
                        }
                    }
                }
                
                // Store the web-accessible path to the thumbnail
                pageThumbnails[currentPage] = $"/uploads/temp/{fileName}";
                
                // First, set the newly added page
                newlyAddedPage = currentPage;
                
                // Then add the page to the selection
                selectedPages.Add(currentPage);
                
                // Force UI update to show the animation
                StateHasChanged();
                
                // Clear the newly added page flag after animation completes
                await Task.Delay(1000);
                newlyAddedPage = null;
                StateHasChanged();
                
                statusMessage = $"Added page {currentPage} to selection.";
            }
            catch (Exception ex)
            {
                error = $"Error creating thumbnail: {ex.Message}";
                Console.WriteLine($"Error creating thumbnail: {ex}");
            }
        }
    }
    
    private async Task RemovePage(int page)
    {
        if (selectedPages.Contains(page))
        {
            isRemoving = true;
            StateHasChanged();
            await Task.Delay(300); // Wait for animation
            
            selectedPages.Remove(page);
            if (pageThumbnails.ContainsKey(page))
            {
                // Remove the thumbnail file
                try
                {
                    string thumbnailPath = pageThumbnails[page].Replace("/", "\\").TrimStart('\\');
                    string fullPath = Path.Combine(Environment.WebRootPath, thumbnailPath);
                    if (File.Exists(fullPath))
                    {
                        File.Delete(fullPath);
                    }
                    pageThumbnails.Remove(page);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error deleting thumbnail: {ex}");
                }
            }
            isRemoving = false;
            statusMessage = $"Removed page {page} from selection.";
        }
    }
    
    private async Task GoToPage(int page)
    {
        if (pdfViewer != null)
        {
            // Use JS interop to navigate to the page
            await JS.InvokeVoidAsync("eval", $"document.querySelector('.e-pv-current-page-box').value = {page}; document.querySelector('.e-pv-current-page-box').dispatchEvent(new Event('change'));");
        }
    }
    
    private async Task ClearSelectedPages()
    {
        isRemoving = true;
        StateHasChanged();
        await Task.Delay(300); // Wait for animation
        
        // Delete all thumbnail files
        foreach (var page in selectedPages.ToList())
        {
            if (pageThumbnails.ContainsKey(page))
            {
                try
                {
                    string thumbnailPath = pageThumbnails[page].Replace("/", "\\").TrimStart('\\');
                    string fullPath = Path.Combine(Environment.WebRootPath, thumbnailPath);
                    if (File.Exists(fullPath))
                    {
                        File.Delete(fullPath);
                    }
                    pageThumbnails.Remove(page);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error deleting thumbnail: {ex}");
                }
            }
        }
        selectedPages.Clear();
        isRemoving = false;
        statusMessage = "Cleared all selected pages.";
    }
    
    private async Task ProcessSelectedPages()
    {
        if (selectedPages.Count == 0)
        {
            error = "Please select at least one page to process.";
            return;
        }
        
        if (!selectedProposalId.HasValue)
        {
            error = "No proposal selected.";
            return;
        }
        
        try
        {
            // Switch to processing view
            showPdfPrepper = false;
            showProcessing = true;
            isProcessing = true;
            processingProgress = 0;
            processingComplete = false;
            processingStatusMessage = "Initializing...";
            
            // Initialize processing steps
            processingSteps = new List<ProcessingStep>
            {
                new ProcessingStep { 
                    Title = "Extract Selected Pages", 
                    Description = "Creating a new PDF with only the selected pages",
                    IsInProgress = true
                },
                new ProcessingStep { 
                    Title = "Process with Form Recognizer", 
                    Description = "Analyzing pages with Azure AI Form Recognizer"
                },
                new ProcessingStep { 
                    Title = "Save Extracted Data", 
                    Description = "Saving the extracted data to the proposal"
                }
            };
            
            StateHasChanged();
            await Task.Delay(500); // Small delay to ensure UI updates
            
            // Step 1: Extract selected pages
            processingStatusMessage = "Extracting selected pages...";
            processingProgress = 10;
            StateHasChanged();
            
            // Extract selected pages using the ProposalService
            byte[] extractedPdfBytes = ProposalService.ExtractSelectedPages(pdfBytes, selectedPages.ToArray());
            
            // Generate a filename for the extracted PDF
            string fileName = $"proposal_{selectedProposalId}_{DateTime.UtcNow:yyyyMMddHHmmss}.pdf";
            
            // Save the extracted PDF to a temporary file
            string tempDirectory = Path.Combine(Environment.WebRootPath, "uploads", "temp");
            if (!Directory.Exists(tempDirectory))
            {
                Directory.CreateDirectory(tempDirectory);
            }
            
            string tempFilePath = Path.Combine(tempDirectory, fileName);
            await File.WriteAllBytesAsync(tempFilePath, extractedPdfBytes);
            
            // Update progress
            processingProgress = 20;
            processingSteps[0].IsCompleted = true;
            processingSteps[0].IsInProgress = false;
            processingSteps[1].IsInProgress = true;
            processingStatusMessage = "Starting form recognition...";
            StateHasChanged();
            
            // Step 2: Process with Form Recognizer
            // Calculate progress increment per page
            double progressPerPage = 60.0 / selectedPages.Count; // 60% of progress bar for form recognition
            
            // Process each page with Form Recognizer
            var extractedData = new List<object>(); // Replace with your actual data model
            
            // Create a mapping of original page numbers to new page positions (1-indexed)
            Dictionary<int, int> pageMapping = new Dictionary<int, int>();
            for (int i = 0; i < selectedPages.Count; i++)
            {
                pageMapping[selectedPages[i]] = i + 1; // Map original page number to position in new PDF (1-indexed)
            }
            
            for (int i = 0; i < selectedPages.Count; i++)
            {
                int originalPageNumber = selectedPages[i];
                int newPageNumber = i + 1; // 1-indexed position in the extracted PDF
                
                processingStatusMessage = $"Processing page {originalPageNumber} ({i+1} of {selectedPages.Count})...";
                
                try
                {
                    // Call the Form Recognizer service for this page using the new page number
                    var pageData = await ProposalService.ExtractorService.ProcessPageAsync(tempFilePath, newPageNumber);
                    
                    // Add the original page number to the extracted data for reference
                    if (pageData is Newtonsoft.Json.Linq.JObject jObject)
                    {
                        jObject["original_page_number"] = originalPageNumber;
                    }
                    
                    extractedData.Add(pageData);
                    
                    // Update progress
                    processingProgress = 20 + ((i + 1) * progressPerPage);
                    StateHasChanged();
                }
                catch (Exception ex)
                {
                    // Log the error but continue processing other pages
                    Console.WriteLine($"Error processing page {originalPageNumber}: {ex.Message}");
                    error = $"Warning: Error processing page {originalPageNumber}. Continuing with other pages.";
                    StateHasChanged();
                }
            }
            
            // Update progress
            processingProgress = 80;
            processingSteps[1].IsCompleted = true;
            processingSteps[1].IsInProgress = false;
            processingSteps[2].IsInProgress = true;
            processingStatusMessage = "Saving extracted data...";
            StateHasChanged();
            
            // Step 3: Save the extracted data
            // Create a wrapper object to hold all the extracted data
            var completeData = new Newtonsoft.Json.Linq.JObject();
            completeData["proposal_id"] = selectedProposalId.Value;
            completeData["client_id"] = ClientId;
            completeData["extraction_timestamp"] = DateTime.UtcNow.ToString("o");
            completeData["total_pages"] = selectedPages.Count;
            completeData["selected_pages"] = new Newtonsoft.Json.Linq.JArray(selectedPages);
            completeData["pages_data"] = Newtonsoft.Json.Linq.JArray.FromObject(extractedData);
            
            // Convert to JSON string
            string jsonData = completeData.ToString(Newtonsoft.Json.Formatting.Indented);
            
            // Save JSON to file
            string jsonFileName = $"proposal_{selectedProposalId}_data.json";
            string jsonFilePath = Path.Combine(tempDirectory, jsonFileName);
            await File.WriteAllTextAsync(jsonFilePath, jsonData);
            
            // Create an attachment for the extracted PDF
            var pdfAttachment = new Attachment
            {
                OriginalFileName = fileName,
                FileFormat = ".pdf",
                FileSize = extractedPdfBytes.Length,
                Description = $"Proposal PDF with {selectedPages.Count} selected pages",
                IsClientAccessible = true,
                Status = 1,
                IsProposal = true,
                ClientId = ClientId
            };
            
            // Save the attachment using the AttachmentService
            await AttachmentService.SaveDropZoneAttachmentAsync(pdfAttachment);
            
            // Create an attachment for the JSON data
            var jsonAttachment = new Attachment
            {
                OriginalFileName = jsonFileName,
                FileFormat = ".json",
                FileSize = jsonData.Length,
                Description = $"Extracted data from proposal",
                IsClientAccessible = false,
                Status = 1,
                IsProposal = false,
                ClientId = ClientId
            };
            
            // Save the JSON attachment
            await AttachmentService.SaveDropZoneAttachmentAsync(jsonAttachment);
            
            Console.WriteLine($"PDF Attachment ID: {pdfAttachment.AttachmentId}");
            Console.WriteLine($"JSON Attachment ID: {jsonAttachment.AttachmentId}");
            
            // Update the proposal with the attachment IDs and JSON data
            await ProposalService.UpdateProposalWithExtractedDataAsync(
                selectedProposalId.Value, 
                pdfAttachment.AttachmentId,
                jsonAttachment.AttachmentId,
                jsonData
            );
            
            // Complete the process
            processingProgress = 100;
            processingSteps[2].IsCompleted = true;
            processingSteps[2].IsInProgress = false;
            processingStatusMessage = "Processing complete! Moving to data cleaner...";
            processingComplete = true;
            isProcessing = false;
            
            statusMessage = $"Proposal #{selectedProposalId} processed successfully with {selectedPages.Count} pages!";
            StateHasChanged();

            // Switch to the cleaner view
            await Task.Delay(1000); // Give user a moment to see the completion message
            if (OnShowCleaner.HasValue)
            {
                await OnShowCleaner.Value.InvokeAsync((ClientId, selectedProposalId.Value));
            }
        }
        catch (Exception ex)
        {
            error = $"Error processing selected pages: {ex.Message}";
            Console.WriteLine($"Error processing selected pages: {ex}");
            isProcessing = false;
        }
    }
    
    private async Task ReturnToProposalList()
    {
        // Return to the proposal list view
        showPdfUploader = false;
        showPdfPrepper = false;
        showProcessing = false;
        selectedProposalId = null;
        
        // Refresh the proposal list
        await LoadProposals();
        if (proposalDataGrid != null)
        {
            await proposalDataGrid.RefreshDataAsync();
        }
    }

    private async Task ShowCleaner(int clientId, int proposalId)
    {
        if (OnShowCleaner.HasValue)
        {
            await OnShowCleaner.Value.InvokeAsync((clientId, proposalId));
        }
    }

    public static string ConvertPdfToImage(string pdfFilePath, int pageNumber)
    {
        // Define the folder where the thumbnail will be saved.
        string saveFolder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", "temp");
        if (!Directory.Exists(saveFolder))
        {
            Directory.CreateDirectory(saveFolder);
        }

        // Build the file path using the page number (e.g., "1.jpg").
        string thumbnailFilePath = Path.Combine(saveFolder, $"{pageNumber}.jpg");

        // Open the PDF file.
        using (FileStream inputStream = new FileStream(pdfFilePath, FileMode.Open, FileAccess.Read))
        {
            // Initialize the PDF to Image converter.
            PdfToImageConverter imageConverter = new PdfToImageConverter();
            imageConverter.Load(inputStream);

            // Convert the specified page (note: assuming pageNumber is 1-indexed, so subtract 1).
            using (Stream outputStream = imageConverter.Convert(pageNumber - 1, false, false))
            {
                // Cast to MemoryStream to get the byte array.
                using (MemoryStream memoryStream = outputStream as MemoryStream)
                {
                    byte[] imageBytes = memoryStream.ToArray();
                    using (Bitmap image = (Bitmap)System.Drawing.Image.FromStream(new MemoryStream(imageBytes)))
                    {
                        // Resize the image to a fixed width (300px) while keeping the aspect ratio.
                        int thumbnailWidth = 300;
                        int thumbnailHeight = (int)(image.Height * (300.0 / image.Width));

                        using (Bitmap thumbnail = new Bitmap(image, new System.Drawing.Size(thumbnailWidth, thumbnailHeight)))
                        {
                            // Save the thumbnail image as a JPEG.
                            thumbnail.Save(thumbnailFilePath, System.Drawing.Imaging.ImageFormat.Jpeg);
                        }
                    }
                }
            }
        }

        // Return the full file path to the thumbnail image.
        return thumbnailFilePath;
    }

}
