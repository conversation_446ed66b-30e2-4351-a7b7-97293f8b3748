﻿using System.Collections.Generic;

namespace Surefire.Domain.Agents.Utilities
{
    /// <summary>
    /// Provides standardized prompt templates for LLM interactions
    /// </summary>
    public static class PromptTemplates
    {
        private static readonly Dictionary<string, string> _templates = new()
        {
            {
                "IntentClassification",
                @"Classify this user input into one of four categories for an insurance business application:

1.  **AgentAction**: Commands that trigger business workflows
    -   Examples: 'Send loss run for Acme Corp', 'Request certificates for Pacific Security'
    -   Keywords: send, request, generate, create, process

2.  **DatabaseQuery**: Questions about data that require database queries
    -   Examples: 'How many policies does Acme have?', 'What carriers do we work with?'
    -   Keywords: how many, what, show, list, find, count

3.  **Navigation**: Requests to navigate to different parts of the application
    -   Examples: 'Open client screen for TWS', 'Show policies for Baron\'s Heating', 'View transcriptions for Innovative Med'
    -   Keywords: open, view, show, navigate, go to, display

4.  **GeneralAI**: General knowledge or explanatory questions
    -   Examples: 'What is workers compensation?', 'Explain general liability'
    -   Keywords: what is, explain, define, help

Input: ""{{input}}""

Respond with ONLY raw JSON (no markdown, no code blocks, no explanations):
{{
  ""intent"": ""AgentAction|DatabaseQuery|Navigation|GeneralAI"",
  ""confidence"": 0.0-1.0,
  ""reasoning"": ""Brief explanation""
}}"
            },
            {
                "SqlGeneration",
                @"Generate a SQL query for this question: ""{{input}}""

Database Schema:
{{schema}}

Requirements:
1.  Use proper JOINs to connect related tables
2.  Only SELECT queries are allowed
3.  Use appropriate WHERE clauses
4.  Handle NULL values properly
5.  Use SQL Server syntax
6.  When listing several policies, list each policy row in a plain pipe-delimited table (Number | Line | Effective | Expiration | Client | Carrier) with no narrative sentences or numbered lists.
7.  When selecting client columns (e.g., ClientId, Name), join the Clients table on p.ClientId = c.ClientId and alias it as c
8.  Select these columns for policy listings: p.PolicyId, pr.LineName AS Line, c.Name AS ClientName, p.PolicyNumber, ca.CarrierName AS CarrierName, w.CarrierName AS WholesalerName, p.EffectiveDate, p.ExpirationDate
9.  Join the Carriers table twice: alias as ca on p.CarrierId = ca.CarrierId and as w on p.WholesalerId = w.CarrierId
10. Never include numeric client IDs; always select client Name as ClientName and use that instead of ID
11. When filtering by client name, always use Clients.Name; do not use the BusinessDetails table or a non-existent BusinessName column
12. For email address queries, join EmailAddresses on Contacts.PrimaryEmailId = EmailAddresses.EmailId with alias ea and select ea.Email AS EmailAddress; never return ContactId
13. For phone number queries, join PhoneNumbers on Contacts.PrimaryPhoneId = PhoneNumbers.PhoneNumberId with alias pn and select pn.Number AS PhoneNumber; filter using FirstName LIKE without additional role or inactivity conditions

Respond with ONLY raw JSON (no markdown, no code blocks, no explanations):
{{
  ""sql"": ""SELECT ..."",
  ""explanation"": ""What this query does"",
  ""success"": true
}}"
            },
            {
                "ParameterExtraction",
                @"Extract parameters from this business action request: ""{{input}}""

Extract these if present:
-   client_name: Name of the client/company
-   action_type: Type of action (loss_run, certificate, proposal)
-   policy_type: Type of insurance
-   time_period: Time range requested
-   urgency: Any urgency indicators

Respond with ONLY raw JSON (no markdown, no code blocks) containing the parameters found."
            }
        };

        /// <summary>
        /// Gets a prompt template by name and optionally replaces placeholders.
        /// </summary>
        /// <param name="name">The name of the template.</param>
        /// <param name="placeholders">A dictionary of placeholder keys and their values.</param>
        /// <returns>The formatted prompt string.</returns>
        public static string GetPrompt(string name, Dictionary<string, string> placeholders = null)
        {
            if (!_templates.TryGetValue(name, out var template))
            {
                throw new KeyNotFoundException($"Prompt template '{name}' not found.");
            }

            if (placeholders != null)
            {
                foreach (var placeholder in placeholders)
                {
                    template = template.Replace($"{{{{{placeholder.Key}}}}}", placeholder.Value);
                }
            }

            return template;
        }
    }
}