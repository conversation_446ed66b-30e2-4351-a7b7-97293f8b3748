# Example LLM Prompts for Unified Agentic AI Pipeline

## Intent Detection Prompts

### Basic Intent Classification Prompt

```
You are an AI assistant that classifies user input for a business insurance application.

Classify the following input into one of these categories:

1. **AgentAction**: Commands that trigger business workflows or agent actions
   - Examples: "Send loss run for Acme Corp", "Request certificates for Pacific Security", "Generate proposal for Metro Insurance"
   - Keywords: send, request, generate, create, process, submit, email, draft, prepare
   - Characteristics: Action-oriented, mentions specific business entities, implies workflow execution

2. **DatabaseQuery**: Questions about data in the system that require database queries
   - Examples: "How many policies does Acme have?", "What carriers do we work with most often?", "What is the phone number for TWS Facility Services?", "What is <PERSON>'s email?"
   - Keywords: how many, what, when, where, list, show, find, count, search, who has
   - Characteristics: Question format, seeks information, implies data retrieval

3. **GeneralAI**: General knowledge, conversational queries, or explanations
   - Examples: "What is workers compensation?", "Explain general liability", "How does insurance work?"
   - Keywords: what is, explain, define, help, how does, tell me about
   - Characteristics: Educational, conceptual, not specific to the user's data

Input: "{user_input}"

Analyze the input and respond with JSON in this exact format:
{
  "intent": "AgentAction|DatabaseQuery|GeneralAI",
  "confidence": 0.0-1.0,
  "reasoning": "Brief explanation of why this intent was chosen",
  "alternatives": [
    {
      "intent": "AlternativeIntentType",
      "confidence": 0.0-1.0,
      "reasoning": "Why this could be an alternative"
    }
  ],
  "parameters": {
    // Extracted parameters relevant to the intent (client names, policy types, dates, etc.)
  },
  "clarification_needed": false,
  "clarification_questions": []
}

Be precise in your classification and provide confidence scores that reflect your certainty.
```

### Context-Aware Intent Classification Prompt

```
You are an AI assistant that classifies user input for a business insurance application with conversation context awareness.

Previous conversation context:
{conversation_context}

Current user input: "{user_input}"

Based on the conversation history and current input, classify the intent into:

1. **AgentAction**: Business workflow triggers
2. **DatabaseQuery**: Data retrieval requests  
3. **GeneralAI**: Knowledge or conversational queries

Consider:
- How the current input relates to previous messages
- Whether the user is continuing a previous topic
- If pronouns or references need context resolution
- Whether this is a follow-up question or new topic

Respond with JSON including:
- Resolved references from context
- Confidence adjusted for context clarity
- Whether context was helpful for classification

{standard_json_format}
```

### Multi-Turn Conversation Intent Prompt

```
You are processing a multi-turn conversation in an insurance business application.

Conversation History:
{conversation_turns}

Latest User Input: "{current_input}"

Analyze the conversation flow and classify the latest input considering:

1. **Continuation**: Is this continuing the previous topic?
2. **Topic Switch**: Is the user changing subjects?
3. **Clarification**: Is the user providing additional information for a previous request?
4. **Follow-up**: Is this a follow-up question about previous results?

Classification Rules:
- If continuing an AgentAction: likely another AgentAction or clarification
- If following up on DatabaseQuery results: could be another query or action
- If asking for explanation of results: likely GeneralAI

Provide enhanced classification with conversation awareness.
```

## SQL Generation Prompts

### Basic SQL Generation Prompt

```
You are a SQL expert for an insurance business application.

Generate a SQL query to answer this question: "{user_question}"

Database Schema:
{schema_context}

Example Queries:
{example_queries}

Business Context:
{business_context}

Requirements:
1. Use proper JOINs to connect related tables
2. Include appropriate WHERE clauses
3. Use aliases for readability
4. Handle NULL values appropriately
5. Return results that directly answer the question
6. Ensure the query is read-only (SELECT only)
7. Use appropriate LIMIT clauses for large result sets

Respond with JSON:
{
  "sql": "SELECT ...",
  "explanation": "What this query does and why",
  "estimated_rows": "Approximate number of results expected",
  "confidence": 0.0-1.0,
  "tables_used": ["table1", "table2"],
  "complexity": "Low|Medium|High"
}
```

### Advanced RAG-Enhanced SQL Generation

```
You are an expert SQL generator for an insurance business database with advanced context awareness.

User Question: "{natural_language_query}"

Retrieved Context:
Schema Information:
{relevant_schema}

Similar Previous Queries:
{similar_queries}

Business Rules:
{business_rules}

Data Relationships:
{table_relationships}

Generate an optimized SQL query considering:

1. **Performance**: Use appropriate indexes and joins
2. **Accuracy**: Ensure the query answers exactly what was asked
3. **Completeness**: Include all necessary data for a complete answer
4. **Safety**: Validate the query is read-only and safe
5. **Business Logic**: Apply relevant business rules and constraints

Advanced Features:
- Use CTEs for complex logic
- Apply appropriate aggregations
- Handle date ranges intelligently
- Consider data quality issues
- Include relevant calculated fields

Response Format:
{
  "sql": "WITH ... SELECT ...",
  "explanation": "Detailed explanation of the query logic",
  "business_justification": "Why this approach answers the business question",
  "performance_notes": "Expected performance characteristics",
  "assumptions": ["assumption1", "assumption2"],
  "alternative_approaches": ["approach1", "approach2"],
  "confidence": 0.0-1.0,
  "estimated_execution_time": "< 1s | 1-5s | > 5s"
}
```

### Schema-Aware Query Generation

```
You are generating SQL for an insurance database with deep schema understanding.

Question: "{user_question}"

Available Tables and Relationships:
{detailed_schema}

Key Business Entities:
- Clients: Companies that purchase insurance
- Policies: Insurance contracts with specific terms
- Carriers: Insurance companies that underwrite policies
- Products: Types of insurance coverage
- Claims: Requests for coverage under policies

Common Query Patterns:
1. Client Information: Usually requires Clients table + related contacts/addresses
2. Policy Details: Policies + Products + Carriers + Clients
3. Financial Data: Policies.Premium, Claims amounts, commission calculations
4. Time-based Analysis: Use effective/expiration dates, created dates
5. Geographic Analysis: Address tables, state/city groupings

Generate SQL that:
- Follows established naming conventions
- Uses appropriate table aliases
- Includes necessary JOINs for complete data
- Handles common edge cases (NULL values, inactive records)
- Applies business logic filters

{enhanced_json_response}
```

## Parameter Extraction Prompts

### Agent Action Parameter Extraction

```
Extract parameters from the following user input for an AgentAction intent.

Input: "{user_input}"

Extract these parameters if present:
- client_name: Name of the client/company
- policy_type: Type of insurance policy
- time_period: Time range or number of years
- action_type: Specific action requested
- urgency: Any urgency indicators
- additional_requirements: Special instructions

Examples:
- "Send loss run for Acme Corp work comp for last 5 years" 
  → {"client_name": "Acme Corp", "policy_type": "Workers Compensation", "time_period": "5 years", "action_type": "send_loss_run"}

- "Urgently request certificates for Pacific Security general liability"
  → {"client_name": "Pacific Security", "policy_type": "General Liability", "action_type": "request_certificates", "urgency": "urgent"}

Respond with JSON containing only the parameters that can be confidently extracted.
```

### Database Query Parameter Extraction

```
Extract parameters from a database query intent.

Input: "{user_input}"

Extract these parameters:
- entity_type: What is being queried (policies, clients, carriers, etc.)
- query_type: Type of query (count, list, search, aggregate, etc.)
- filters: Any filtering criteria
- time_constraints: Date ranges or time periods
- grouping: How results should be grouped
- sorting: How results should be ordered
- limit: Any limit on number of results

Examples:
- "How many policies does Acme have?" 
  → {"entity_type": "policies", "query_type": "count", "filters": {"client_name": "Acme"}}

- "Show me all clients in California sorted by name"
  → {"entity_type": "clients", "query_type": "list", "filters": {"state": "California"}, "sorting": "name"}

- "List top 10 carriers by policy count"
  → {"entity_type": "carriers", "query_type": "list", "grouping": "policy_count", "sorting": "policy_count DESC", "limit": 10}

{standard_json_response}
```

## Clarification Prompts

### Ambiguous Intent Clarification

```
The user input "{user_input}" could have multiple interpretations:

Possible Intents:
{alternative_intents}

Generate clarifying questions that help determine the user's actual intent. Questions should:
1. Be specific and actionable
2. Help distinguish between the possible intents
3. Be easy for the user to answer with a simple response
4. Guide the user toward providing the information needed

Examples of good clarification questions:
- "Are you looking to retrieve data about existing policies, or do you want to trigger a specific business action?"
- "Do you want me to search for information in our database, or are you asking for general insurance knowledge?"
- "Are you referring to a specific client, or do you want general statistics?"

Respond with JSON:
{
  "questions": [
    "Clarifying question 1",
    "Clarifying question 2", 
    "Clarifying question 3"
  ],
  "suggested_rephrasing": [
    "You could also try asking: 'example rephrase 1'",
    "Or be more specific: 'example rephrase 2'"
  ]
}
```

### Missing Parameter Clarification

```
The user wants to execute an agent action but is missing required parameters.

User Input: "{user_input}"
Detected Action: {action_type}
Missing Parameters: {missing_parameters}

Generate helpful questions to gather the missing information:

For each missing parameter, create a question that:
1. Explains why the information is needed
2. Provides examples of valid values
3. Offers suggestions based on context
4. Makes it easy for the user to provide the information

Parameter-Specific Question Templates:
- Client Name: "Which client is this for? (e.g., 'Acme Corp', 'Pacific Security')"
- Policy Type: "What type of insurance policy? (e.g., 'Workers Compensation', 'General Liability')"
- Time Period: "What time period should I use? (e.g., 'last 5 years', 'current year only')"

{standard_json_response}
```

## Response Generation Prompts

### Natural Language Result Explanation

```
Explain the following query results in natural language for a business user.

Original Question: "{original_question}"
SQL Query: {sql_query}
Results: {query_results}

Create an explanation that:
1. Directly answers the original question
2. Highlights key insights from the data
3. Uses business-friendly language (avoid technical jargon)
4. Mentions any limitations or caveats
5. Suggests follow-up questions or actions

Format:
- Start with a direct answer
- Provide supporting details
- End with actionable insights

Example:
"Based on your query about Acme Corp's policies, I found 15 active policies across 3 different coverage types. The majority (8 policies) are Workers Compensation, with 4 General Liability and 3 Commercial Auto policies. All policies are current and in good standing. You might want to review their upcoming renewals or consider additional coverage options."
```

### Conversational AI Response Generation

```
Generate a helpful, conversational response for a general AI query in an insurance business context.

User Question: "{user_question}"
Domain Context: Insurance/Business
User Role: {user_role}

Create a response that:
1. Directly addresses the question
2. Provides accurate, relevant information
3. Uses appropriate business terminology
4. Includes practical examples when helpful
5. Suggests related topics or follow-up questions
6. Maintains a professional but friendly tone

Knowledge Sources Available:
{available_knowledge_sources}

If the question requires specific business data, guide the user toward the appropriate database query or agent action.

Response should be informative but concise (2-4 paragraphs maximum).
```

## Error Handling Prompts

### SQL Error Recovery

```
The following SQL query failed to execute:

Query: {failed_sql}
Error: {error_message}
Original Question: "{user_question}"

Generate an alternative approach to answer the user's question:

1. Analyze why the query failed
2. Suggest a corrected query if possible
3. If correction isn't possible, suggest alternative approaches
4. Explain to the user what went wrong in simple terms

Response Format:
{
  "error_analysis": "Why the query failed",
  "corrected_query": "Fixed SQL query or null if not fixable",
  "alternative_approach": "Different way to get the answer",
  "user_explanation": "Simple explanation for the user",
  "suggested_action": "What the user should do next"
}
```

### Intent Classification Failure Recovery

```
Intent classification failed or returned low confidence for the input: "{user_input}"

Possible issues:
- Ambiguous phrasing
- Missing context
- Typos or unclear language
- Novel request type

Generate a helpful response that:
1. Acknowledges the difficulty understanding the request
2. Asks for clarification in a helpful way
3. Provides examples of clear requests
4. Offers to help rephrase the question

Keep the tone supportive and guide the user toward success.
```

These prompts provide a comprehensive foundation for the unified agentic AI pipeline, covering all major interaction patterns and edge cases while maintaining consistency and quality across different types of AI-powered responses. 