@using System.Text.Json
@using Syncfusion.Blazor.Spinner
@using Syncfusion.Blazor.Notifications
@using Surefire.Domain.DocuSign
@using Surefire.Domain.Shared.Services
@inject IDocuSignService DocuSignService
@inject StateService StateService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@implements IDisposable

<div class="sectiontitletab">DocuSign History</div>
<div class="leads-box-inner">
    <table id="envelopestable" cellspacing="0" class="ltable">
        <thead class="lbg">
            <tr>
                <th class="mid-a">Subject</th>
                <th class="mid-c">Last Change</th>
                <th class="mid-d">Status</th>
            </tr>
        </thead>
        <tbody class="lbody">
            @if (isLoading)
            {
                @for (var i = 0; i < 8; i++)
                {
                    <tr class="lrow">
                        <td class="mid-a">
                            <SfSkeleton Shape=SkeletonType.Rectangle Width="100%" Height="15px" CssClass="e-customize" Visible="true"></SfSkeleton>
                        </td>
                        <td class="mid-c">
                            <SfSkeleton Shape=SkeletonType.Rectangle Width="100%" Height="15px" CssClass="e-customize" Visible="true"></SfSkeleton>
                        </td>
                        <td class="mid-d">
                            <SfSkeleton Shape=SkeletonType.Rectangle Width="100%" Height="15px" CssClass="e-customize" Visible="true"></SfSkeleton>
                        </td>
                    </tr>
                }
            }
            else if (envelopes == null || !envelopes.Any())
            {
                <tr class="fade-in">
                    <td colspan="3" class="no-taskssub">No envelopes available</td>
                </tr>
            }
            else
            {
                @for (var i = 0; i < Math.Min(envelopes.Count, 15); i++)
                {
                    var envelope = envelopes[i];
                    var isVisible = visibleRows > i;
                    <tr class="lrow main @(isVisible ? "slide-in-row visible" : "slide-in-row")" 
                        style="animation-delay: @(i * 80)ms;">
                        <td class="mid-a mid-name ellipsis" @onclick="() => ShowDetails(envelope)">@envelope.EmailSubject</td>
                       @*  <td class="mid-b mid-color ellipsis">@FormatDate(envelope.CreatedDateTime)</td> *@
                        <td class="mid-c mid-date ellipsis">@FormatDate(envelope.StatusChangedDateTime)</td>
                        <td class="mid-d ellipsis">
                            <span class="dot dot-@GetStatusClass(envelope.Status)"></span>
                            <span class="mid-dotname link-@GetStatusClass(envelope.Status)">@GetStatusName(envelope.Status)</span>
                        </td>
                    </tr>
                }
            }
        </tbody>
    </table>
    <div style="height:7px;"></div>
</div>

@if (showDetailsModal && selectedEnvelope != null)
{
    <div class="modal-backdrop" @onclick="CloseDetailsModal">
        <div class="modal-content" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>Envelope Details</h3>
                <button type="button" class="close-btn" @onclick="CloseDetailsModal">×</button>
            </div>
            <div class="modal-body">
                <table class="details-table">
                    <tr>
                        <td class="detail-label">Subject:</td>
                        <td>@selectedEnvelope.EmailSubject</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Status:</td>
                        <td><span class="status-badge status-@GetStatusClass(selectedEnvelope.Status)">@GetStatusName(selectedEnvelope.Status)</span></td>
                    </tr>
                    <tr>
                        <td class="detail-label">Created:</td>
                        <td>@FormatDate(selectedEnvelope.CreatedDateTime)</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Last Modified:</td>
                        <td>@FormatDate(selectedEnvelope.StatusChangedDateTime)</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Sent:</td>
                        <td>@FormatDate(selectedEnvelope.SentDateTime)</td>
                    </tr>
                </table>
                <div class="modal-actions">
                    <button type="button" class="action-btn download-pdf-btn" @onclick="() => DownloadPdf(selectedEnvelope.EnvelopeId)">
                        📄 Download PDF
                    </button>
                    <button type="button" class="action-btn close-modal-btn" @onclick="CloseDetailsModal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@if (isDownloading)
{
    <div class="download-overlay">
        <div class="download-spinner">
            <SfSpinner Size="50" Label="Downloading PDF..." Type="SpinnerType.Material"></SfSpinner><br />
            Loading...
        </div>
    </div>
}

@code {
    private List<EnvelopeInfo> envelopes = new();
    private bool isLoading = true;
    private bool isDownloading = false;
    private bool showDetailsModal = false;
    private EnvelopeInfo selectedEnvelope;
    private int visibleRows = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadDocuSignData();
    }

        private async Task LoadDocuSignData()
    {
        try
        {
            // Check if data is likely cached (no need for loading animation)
            var isCached = await IsDataCached();
            
            if (!isCached)
            {
                Console.WriteLine("Data is NOT cached - showing loading skeletons");
                isLoading = true;
                visibleRows = 0;
                StateHasChanged();
            }
            else
            {
                Console.WriteLine("Data IS cached - skipping loading skeletons");
                isLoading = false;
                visibleRows = 0; // Start with 0 for animation or set to full count for immediate display
            }

            // Get data from StateService cache (which handles its own refresh logic)
            var allEnvelopes = await StateService.GetDocuSignEnvelopesAsync(CancellationToken.None);
            envelopes = allEnvelopes.Take(15).ToList();

            // Stop loading and either animate rows in (if fresh) or show immediately (if cached)
            isLoading = false;
            if (isCached)
            {
                visibleRows = Math.Min(envelopes.Count, 15);
                StateHasChanged();
            }
            else
            {
                StateHasChanged();
                await AnimateRowsIn();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading DocuSign data: {ex.Message}");
            envelopes = new();
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task<bool> IsDataCached()
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            var testData = await StateService.GetDocuSignEnvelopesAsync(CancellationToken.None);
            stopwatch.Stop();
            Console.WriteLine($"Data load took {stopwatch.ElapsedMilliseconds}ms");
            
            // Temporarily force animation to test - remove this line when satisfied
            return false;
            
            return stopwatch.ElapsedMilliseconds < 100;
        }
        catch
        {
            stopwatch.Stop();
            return false;
        }
    }

    private async Task AnimateRowsIn()
    {
        var totalRows = Math.Min(envelopes.Count, 15);
        for (int i = 0; i < totalRows; i++)
        {
            visibleRows = i + 1;
            StateHasChanged();
            await Task.Delay(40); // Stagger the row animations - 33% faster
        }
    }

    private async Task DownloadPdf(string envelopeId)
    {
        try
        {
            isDownloading = true;
            StateHasChanged();

            // Get access token
            var accessToken = await DocuSignService.GetAccessTokenAsync();

            // Download the PDF document directly using the service
            var pdfBytes = await DocuSignService.DownloadDocumentPdfAsync(accessToken, envelopeId);

            // Use JavaScript interop to download the file in the browser
            await JSRuntime.InvokeVoidAsync("downloadFileFromBytes", 
                Convert.ToBase64String(pdfBytes), 
                "application/pdf", 
                $"docusign-{envelopeId}.pdf");
        }
        catch (Exception ex)
        {
            Console.WriteLine("Error downloading PDF from DocuSign Service.");
            // Could add toast notification here
        }
        finally
        {
            isDownloading = false;
            StateHasChanged();
        }
    }

    private void ShowDetails(EnvelopeInfo envelope)
    {
        selectedEnvelope = envelope;
        showDetailsModal = true;
        StateHasChanged();
    }

    private void CloseDetailsModal()
    {
        showDetailsModal = false;
        StateHasChanged();
    }

    private string FormatDate(DateTime date)
    {
        if (date == DateTime.MinValue)
            return "N/A";

        return date.ToString("MM/dd/yyyy");
    }

    private string GetStatusName(string status)
    {
        return status?.ToLower() switch
        {
            "completed" => "done",
            "voided" => "void",
            "delivered" => "dlv'd",
            _ => status
        };
    }
    
    private int GetStatusClass(string status)
    {
        return status?.ToLower() switch
        {
            "completed" => 2,
            "delivered" => 1,
            "sent" => 1,
            "signed" => 1,
            _ => 0
        };
    }

    public void Dispose()
    {
        // No subscriptions to clean up since DocuSign data has its own refresh cycle
    }
} 