using Surefire.Domain.Agents.Interfaces;
using Surefire.Domain.Agents.Models;
using Surefire.Domain.Clients.Services;
using Surefire.Data;
using Microsoft.EntityFrameworkCore;
using Surefire.Domain.Ember;
using System.Globalization;

namespace Surefire.Domain.Agents.TaskAgents
{
    /// <summary>
    /// Agent for creating payment links
    /// Example: "Send a pay link to Acme Corp for $400 for the Work Comp Broker Fee"
    /// </summary>
    public class PaymentLinkAgent : ITaskAgent
    {
        private readonly ILogger<PaymentLinkAgent> _logger;
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly ClientService _clientService;
        private readonly IEmbeddingService _embeddingService;
        private readonly IVectorStore _vectorStore;
        private readonly EmberService _emberService;

        public PaymentLinkAgent(
            ILogger<PaymentLinkAgent> logger,
            IDbContextFactory<ApplicationDbContext> dbContextFactory,
            ClientService clientService,
            IEmbeddingService embeddingService,
            IVectorStore vectorStore,
            EmberService emberService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dbContextFactory = dbContextFactory ?? throw new ArgumentNullException(nameof(dbContextFactory));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _embeddingService = embeddingService ?? throw new ArgumentNullException(nameof(embeddingService));
            _vectorStore = vectorStore ?? throw new ArgumentNullException(nameof(vectorStore));
            _emberService = emberService ?? throw new ArgumentNullException(nameof(emberService));
            
            _logger.LogInformation("=== PaymentLinkAgent CONSTRUCTOR CALLED ===");
            _logger.LogInformation("All dependencies injected successfully (including vector search)");
        }

        /// <summary>
        /// Agent definition with metadata, parameters, and capabilities
        /// </summary>
        public TaskAgentDefinition Definition => new()
        {
            AgentId = "payment_link",
            Name = "Payment Link Generator",
            Description = "Create and send payment links to clients for invoices, fees, and other charges",
            Category = "Payments",
            TriggerPhrases = new List<string>
            {
                "send pay link",
                "create payment link", 
                "send payment",
                "payment link",
                "pay link",
                "paylink",
                "create paylink",
                "send invoice link"
            },
            Parameters = new List<AgentParameterDefinition>
            {
                new()
                {
                    Name = "client_name",
                    Description = "Name of the client",
                    ParameterType = typeof(string),
                    IsRequired = true,
                    UseEntityExtraction = true,
                    EntityType = "ClientName",
                    ExtractionPrompt = "Extract the client or company name from the user input",
                    ClarificationQuestion = "Which client should receive the payment link?"
                },
                new()
                {
                    Name = "amount",
                    Description = "Payment amount in dollars",
                    ParameterType = typeof(decimal),
                    IsRequired = true,
                    ExtractionPrompt = "Extract the payment amount (look for dollar signs, numbers with decimals)",
                    ClarificationQuestion = "What is the payment amount?"
                },
                new()
                {
                    Name = "description",
                    Description = "Description of what the payment is for",
                    ParameterType = typeof(string),
                    IsRequired = true,
                    ExtractionPrompt = "Extract what the payment is for (broker fee, deductible, premium, etc.)",
                    ClarificationQuestion = "What is this payment for? (e.g., 'Work Comp Broker Fee', 'Deductible Payment')"
                },
                new()
                {
                    Name = "client_email",
                    Description = "Client's email address",
                    ParameterType = typeof(string),
                    IsRequired = false,
                    ExtractionPrompt = "Extract email address if provided",
                    ClarificationQuestion = "What is the client's email address? (leave blank to use default from client record)"
                },
                new()
                {
                    Name = "policy_id",
                    Description = "Related policy ID if applicable",
                    ParameterType = typeof(string),
                    IsRequired = false,
                    ExtractionPrompt = "Extract policy number or ID if mentioned",
                    ClarificationQuestion = "Is this payment related to a specific policy? (optional)"
                },
                new()
                {
                    Name = "due_date",
                    Description = "Payment due date",
                    ParameterType = typeof(DateTime),
                    IsRequired = false,
                    DefaultValue = DateTime.Now.AddDays(30),
                    ExtractionPrompt = "Extract due date if mentioned",
                    ClarificationQuestion = "When is this payment due? (default: 30 days from now)"
                }
            },
            OutcomeType = AgentOutcomeType.Navigation,
            SupportsAIChat = true,
            SupportsActionButton = true,
            EstimatedExecutionSeconds = 15,
            RequiresConfirmation = true,
            NavigationInfo = new AgentNavigationInfo
            {
                NavigationUrl = "/payments/create",
                OpenInNewTab = false
            }
        };

        /// <summary>
        /// Execute the payment link agent
        /// </summary>
        public async Task<TaskAgentResult> ExecuteAsync(TaskAgentRequest request, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;
            
            try
            {
                _logger.LogInformation("=== PAYMENT LINK AGENT EXECUTION STARTED ===");
                _logger.LogInformation("Creating payment link for user {UserId}", request.UserId);
                _logger.LogInformation("Request parameters count: {ParameterCount}", request.Parameters?.Count ?? 0);
                
                // Log all parameters
                if (request.Parameters != null)
                {
                    foreach (var param in request.Parameters)
                    {
                        _logger.LogInformation("Parameter: {Key} = {Value} (Type: {Type})", 
                            param.Key, param.Value, param.Value?.GetType().Name ?? "null");
                    }
                }

                // Extract parameters with JsonElement handling
                _logger.LogInformation("=== EXTRACTING PARAMETERS ===");
                var clientName = GetStringValue(request.Parameters.GetValueOrDefault("client_name"));
                var amount = GetDecimalValue(request.Parameters.GetValueOrDefault("amount"));
                var description = GetStringValue(request.Parameters.GetValueOrDefault("description"));
                var clientEmail = GetStringValue(request.Parameters.GetValueOrDefault("client_email"));
                var policyId = GetStringValue(request.Parameters.GetValueOrDefault("policy_id"));
                var dueDate = GetDateTimeValue(request.Parameters.GetValueOrDefault("due_date")) ?? DateTime.Now.AddDays(30);

                _logger.LogInformation("Extracted parameters:");
                _logger.LogInformation("  clientName: {ClientName}", clientName ?? "null");
                _logger.LogInformation("  amount: {Amount}", amount);
                _logger.LogInformation("  description: {Description}", description ?? "null");
                _logger.LogInformation("  clientEmail: {ClientEmail}", clientEmail ?? "null");
                _logger.LogInformation("  policyId: {PolicyId}", policyId ?? "null");
                _logger.LogInformation("  dueDate: {DueDate}", dueDate);

                // Process the payment link creation with vector search
                _logger.LogInformation("=== STARTING BUSINESS LOGIC PROCESSING ===");
                var result = await CreatePaymentLinkAsync(clientName, amount, description, clientEmail, policyId, dueDate, cancellationToken);

                _logger.LogInformation("Business logic completed. Success: {Success}, Message: {Message}", 
                    result.Success, result.Message);

                var finalResult = new TaskAgentResult
                {
                    Success = result.Success,
                    OutcomeType = result.Success ? AgentOutcomeType.Navigation : AgentOutcomeType.Error,
                    Message = result.Message,
                    Data = result.Data,
                    ErrorMessage = result.Success ? null : result.Message,
                    NavigationInfo = new AgentNavigationInfo
                    {
                        NavigationUrl = "/payments/create",
                        NavigationParameters = new Dictionary<string, object>
                        {
                            ["client_name"] = clientName,
                            ["amount"] = amount,
                            ["description"] = description,
                            ["client_email"] = clientEmail ?? "",
                            ["policy_id"] = policyId ?? "",
                            ["due_date"] = dueDate
                        },
                        PrePopulatedFields = new Dictionary<string, object>
                        {
                            ["ClientName"] = clientName,
                            ["Amount"] = amount,
                            ["Description"] = description,
                            ["ClientEmail"] = clientEmail ?? "",
                            ["PolicyId"] = policyId ?? "",
                            ["DueDate"] = dueDate.ToString("yyyy-MM-dd")
                        }
                    },
                    ExecutionTime = DateTime.UtcNow - startTime,
                    Suggestions = new List<string>
                    {
                        "Review and send the email draft in Outlook",
                        "Create another payment link for a different client",
                        "Set up automatic payment reminders",
                        "Copy the payment link to share via other channels"
                    }
                };

                _logger.LogInformation("=== PAYMENT LINK AGENT EXECUTION COMPLETED ===");
                _logger.LogInformation("Final result - Success: {Success}, ExecutionTime: {ExecutionTime}ms", 
                    finalResult.Success, finalResult.ExecutionTime.TotalMilliseconds);

                return finalResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "=== CRITICAL ERROR IN PAYMENT LINK AGENT EXECUTION ===");
                _logger.LogError("Error details: {ErrorMessage}", ex.Message);
                _logger.LogError("Stack trace: {StackTrace}", ex.StackTrace);
                
                return new TaskAgentResult
                {
                    Success = false,
                    OutcomeType = AgentOutcomeType.Error,
                    ErrorMessage = "Failed to create payment link: " + ex.Message,
                    ExecutionTime = DateTime.UtcNow - startTime
                };
            }
        }

        /// <summary>
        /// Validate parameters for the agent
        /// </summary>
        public async Task<AgentParameterValidation> ValidateParametersAsync(Dictionary<string, object> parameters)
        {
            var validation = new AgentParameterValidation();
            var validatedParameters = new Dictionary<string, object>();

            // Validate client_name
            if (parameters.TryGetValue("client_name", out var clientName) && !string.IsNullOrWhiteSpace(clientName?.ToString()))
            {
                validatedParameters["client_name"] = clientName.ToString().Trim();
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "client_name",
                    Status = ParameterStatus.Valid,
                    Value = clientName
                });
            }
            else
            {
                validation.MissingParameters.Add("client_name");
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "client_name",
                    Status = ParameterStatus.Missing,
                    Message = "Client name is required"
                });
            }

            // Validate amount
            if (parameters.TryGetValue("amount", out var amount) && decimal.TryParse(amount?.ToString(), out var amountValue) && amountValue > 0)
            {
                validatedParameters["amount"] = amountValue;
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "amount",
                    Status = ParameterStatus.Valid,
                    Value = amountValue
                });
            }
            else
            {
                validation.MissingParameters.Add("amount");
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "amount",
                    Status = ParameterStatus.Missing,
                    Message = "Valid payment amount is required"
                });
            }

            // Validate description
            if (parameters.TryGetValue("description", out var description) && !string.IsNullOrWhiteSpace(description?.ToString()))
            {
                validatedParameters["description"] = description.ToString().Trim();
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "description",
                    Status = ParameterStatus.Valid,
                    Value = description
                });
            }
            else
            {
                validation.MissingParameters.Add("description");
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "description",
                    Status = ParameterStatus.Missing,
                    Message = "Payment description is required"
                });
            }

            // Validate client_email (optional)
            if (parameters.TryGetValue("client_email", out var email) && !string.IsNullOrWhiteSpace(email?.ToString()))
            {
                var emailStr = email.ToString().Trim();
                if (IsValidEmail(emailStr))
                {
                    validatedParameters["client_email"] = emailStr;
                    validation.Parameters.Add(new ParameterValidationResult
                    {
                        ParameterName = "client_email",
                        Status = ParameterStatus.Valid,
                        Value = emailStr
                    });
                }
                else
                {
                    validation.Parameters.Add(new ParameterValidationResult
                    {
                        ParameterName = "client_email",
                        Status = ParameterStatus.Invalid,
                        Message = "Invalid email address format"
                    });
                }
            }

            // Validate policy_id (optional)
            if (parameters.TryGetValue("policy_id", out var policyId) && !string.IsNullOrWhiteSpace(policyId?.ToString()))
            {
                validatedParameters["policy_id"] = policyId.ToString().Trim();
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "policy_id",
                    Status = ParameterStatus.Valid,
                    Value = policyId
                });
            }

            // Validate due_date (optional)
            if (parameters.TryGetValue("due_date", out var dueDate))
            {
                if (DateTime.TryParse(dueDate?.ToString(), out var dueDateValue) && dueDateValue > DateTime.Now)
                {
                    validatedParameters["due_date"] = dueDateValue;
                    validation.Parameters.Add(new ParameterValidationResult
                    {
                        ParameterName = "due_date",
                        Status = ParameterStatus.Valid,
                        Value = dueDateValue
                    });
                }
                else if (dueDate != null)
                {
                    validation.Parameters.Add(new ParameterValidationResult
                    {
                        ParameterName = "due_date",
                        Status = ParameterStatus.Invalid,
                        Message = "Due date must be in the future"
                    });
                }
            }

            validation.ValidatedParameters = validatedParameters;
            validation.IsComplete = validation.MissingParameters.Count == 0 && 
                                   !validation.Parameters.Any(p => p.Status == ParameterStatus.Invalid);

            return validation;
        }

        /// <summary>
        /// Get execution preview for confirmation
        /// </summary>
        public async Task<string> GetExecutionPreviewAsync(Dictionary<string, object> parameters)
        {
            var clientName = parameters.GetValueOrDefault("client_name")?.ToString() ?? "Unknown Client";
            var amount = Convert.ToDecimal(parameters.GetValueOrDefault("amount", 0));
            var description = parameters.GetValueOrDefault("description")?.ToString() ?? "Payment";
            var dueDate = parameters.GetValueOrDefault("due_date") as DateTime? ?? DateTime.Now.AddDays(30);

            return $"Create a payment link for {clientName} in the amount of ${amount:F2} for '{description}'. " +
                   $"Due date: {dueDate:MM/dd/yyyy}. This will open the payment creation form with all details pre-filled.";
        }

        /// <summary>
        /// Process the payment link creation with vector search client lookup and Outlook email creation
        /// </summary>
        private async Task<(bool Success, string Message, object Data)> CreatePaymentLinkAsync(
            string clientName,
            decimal? amount,
            string description,
            string clientEmail,
            string policyId,
            DateTime dueDate,
            CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("=== STARTING DATABASE OPERATIONS ===");
                _logger.LogInformation("Creating database context...");
                
                using var context = _dbContextFactory.CreateDbContext();
                _logger.LogInformation("Database context created successfully");

                // 1. Look up client using vector search
                _logger.LogInformation("=== STEP 1: LOOKING UP CLIENT USING VECTOR SEARCH ===");
                var client = await FindClientByVectorSearchAsync(clientName, cancellationToken);

                if (client == null)
                {
                    var errorMsg = $"Client '{clientName}' not found.";
                    _logger.LogWarning("Client lookup failed: {ErrorMessage}", errorMsg);
                    return (false, errorMsg, null);
                }
                
                _logger.LogInformation("Client found: {ClientId} - {ClientName}", client.ClientId, client.Name);

                // 2. Validate amount
                if (!amount.HasValue || amount <= 0)
                {
                    var errorMsg = "Payment amount must be greater than zero.";
                    _logger.LogWarning("Amount validation failed: {ErrorMessage}", errorMsg);
                    return (false, errorMsg, null);
                }

                // 3. Validate description
                if (string.IsNullOrWhiteSpace(description))
                {
                    var errorMsg = "Payment description is required.";
                    _logger.LogWarning("Description validation failed: {ErrorMessage}", errorMsg);
                    return (false, errorMsg, null);
                }

                // 4. Get client email - first try provided email, then client's primary email, then get from contacts
                var finalEmail = await GetClientEmailAsync(client, clientEmail, context, cancellationToken);
                
                if (string.IsNullOrWhiteSpace(finalEmail))
                {
                    var errorMsg = $"No email address found for client '{client.Name}'. Please provide an email address.";
                    _logger.LogWarning("Email validation failed: {ErrorMessage}", errorMsg);
                    return (false, errorMsg, null);
                }

                _logger.LogInformation("=== STEP 2: GENERATING PAYMENT LINK ===");

                // Generate payment link using same logic as Paylink.razor
                var paymentLink = GeneratePaymentLink(client.Name, finalEmail, amount.Value, description);
                _logger.LogInformation("Payment link generated: {PaymentLink}", paymentLink);

                // 5. Create and send Outlook email using EmberService
                _logger.LogInformation("=== STEP 3: CREATING OUTLOOK EMAIL ===");
                var emailSuccess = await CreateOutlookEmailAsync(client.Name, finalEmail, amount.Value, description, paymentLink);

                var message = emailSuccess 
                    ? $"✅ Payment link created for {client.Name} - ${amount:F2} for '{description}'. Email draft opened in Outlook for review."
                    : $"✅ Payment link created for {client.Name} - ${amount:F2} for '{description}'. Note: Email creation failed, but link is ready to use.";

                var data = new
                {
                    ClientId = client.ClientId,
                    ClientName = client.Name,
                    Amount = amount.Value,
                    Description = description,
                    ClientEmail = finalEmail,
                    PolicyId = policyId,
                    DueDate = dueDate,
                    PaymentLink = paymentLink,
                    PaymentLinkId = Guid.NewGuid().ToString(),
                    Status = "Ready",
                    EmailCreated = emailSuccess
                };

                _logger.LogInformation("=== PROCESSING COMPLETED SUCCESSFULLY ===");
                _logger.LogInformation("Payment link created for client: {ClientName}, Amount: ${Amount:F2}, Email: {EmailSuccess}", 
                    client.Name, amount.Value, emailSuccess);

                return (true, message, data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "=== CRITICAL ERROR IN BUSINESS LOGIC ===");
                _logger.LogError("Error processing payment link request: {ErrorMessage}", ex.Message);
                _logger.LogError("Stack trace: {StackTrace}", ex.StackTrace);
                return (false, $"Error processing request: {ex.Message}", null);
            }
        }

        /// <summary>
        /// Simple email validation
        /// </summary>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Find client using vector search for better fuzzy matching
        /// </summary>
        private async Task<Domain.Clients.Models.Client?> FindClientByVectorSearchAsync(string clientName, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(clientName)) return null;

            try
            {
                _logger.LogInformation("🔍 Performing vector search for client: '{ClientName}'", clientName);
                
                // Generate embedding for the client name
                var queryVector = await _embeddingService.GenerateEmbeddingAsync(clientName, cancellationToken);
                
                // Search for similar clients in the vector store
                var searchResults = await _vectorStore.SearchAsync(queryVector, 5, cancellationToken);
                
                _logger.LogInformation("Vector search found {ResultCount} potential client matches", searchResults.Count);
                
                // Filter for Client entities and find the best match
                var clientMatches = searchResults
                    .Where(r => GetMetadataString(r.Metadata, "Entity") == "Client")
                    .Where(r => r.Score > 0.4f) // Same threshold as LossRunRequestAgent
                    .ToList();

                if (!clientMatches.Any())
                {
                    _logger.LogWarning("No client matches found above threshold (0.4) for '{ClientName}'", clientName);
                    
                    // Log all results for debugging
                    _logger.LogInformation("All vector search results for debugging:");
                    foreach (var result in searchResults)
                    {
                        var entityType = GetMetadataString(result.Metadata, "Entity") ?? "Unknown";
                        var entityName = GetMetadataString(result.Metadata, "Name");
                        var entityId = GetMetadataInt(result.Metadata, "EntityId");
                        _logger.LogInformation("  {EntityType} '{EntityName}' (ID: {EntityId}) - Score: {Score:F3}", 
                            entityType, entityName, entityId, result.Score);
                    }
                    
                    return null;
                }

                // Log all matches for debugging
                foreach (var match in clientMatches)
                {
                    var entityId = GetMetadataInt(match.Metadata, "EntityId");
                    var entityName = GetMetadataString(match.Metadata, "Name");
                    _logger.LogInformation("Client match: ID={EntityId}, Name='{EntityName}', Score={Score:F3}", 
                        entityId, entityName, match.Score);
                }

                // Get the best match
                var bestMatch = clientMatches.First();
                var clientId = GetMetadataInt(bestMatch.Metadata, "EntityId");
                
                if (clientId == null)
                {
                    _logger.LogError("Best client match has invalid EntityId");
                    return null;
                }

                _logger.LogInformation("✅ Selected best client match: ID={ClientId}, Score={Score:F3}", 
                    clientId, bestMatch.Score);

                // Fetch the actual client from database
                using var context = _dbContextFactory.CreateDbContext();
                var client = await context.Clients.FirstOrDefaultAsync(c => c.ClientId == clientId.Value, cancellationToken);
                
                if (client != null)
                {
                    _logger.LogInformation("✅ Client retrieved from database: {ClientName}", client.Name);
                }
                else
                {
                    _logger.LogError("❌ Client with ID {ClientId} not found in database", clientId);
                }

                return client;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in vector search for client '{ClientName}'", clientName);
                return null;
            }
        }

        /// <summary>
        /// Helper method to safely extract string values from JsonElement or other object types
        /// </summary>
        private string? GetStringValue(object? value)
        {
            if (value == null) return null;
            
            if (value is System.Text.Json.JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.String)
                {
                    return jsonElement.GetString();
                }
                else if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.Null)
                {
                    return null;
                }
                else
                {
                    return jsonElement.ToString();
                }
            }
            
            return value.ToString();
        }

        /// <summary>
        /// Helper method to safely extract decimal values from JsonElement or other object types
        /// </summary>
        private decimal? GetDecimalValue(object? value)
        {
            if (value == null) return null;
            
            if (value is System.Text.Json.JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.Number)
                {
                    return jsonElement.GetDecimal();
                }
                else if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.String)
                {
                    var stringValue = jsonElement.GetString();
                    if (decimal.TryParse(stringValue?.Replace("$", "").Replace(",", ""), out var decimalValue))
                    {
                        return decimalValue;
                    }
                }
                else if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.Null)
                {
                    return null;
                }
            }
            else if (value is string stringValue)
            {
                if (decimal.TryParse(stringValue.Replace("$", "").Replace(",", ""), out var decimalValue))
                {
                    return decimalValue;
                }
            }
            else if (value is decimal decimalValue)
            {
                return decimalValue;
            }
            else if (value is double doubleValue)
            {
                return (decimal)doubleValue;
            }
            else if (value is float floatValue)
            {
                return (decimal)floatValue;
            }
            
            return null;
        }

        /// <summary>
        /// Helper method to safely extract DateTime values from JsonElement or other object types
        /// </summary>
        private DateTime? GetDateTimeValue(object? value)
        {
            if (value == null) return null;
            
            if (value is System.Text.Json.JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.String)
                {
                    var stringValue = jsonElement.GetString();
                    if (DateTime.TryParse(stringValue, out var dateTimeValue))
                    {
                        return dateTimeValue;
                    }
                }
                else if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.Null)
                {
                    return null;
                }
            }
            else if (value is string stringValue)
            {
                if (DateTime.TryParse(stringValue, out var dateTimeValue))
                {
                    return dateTimeValue;
                }
            }
            else if (value is DateTime dateTimeValue)
            {
                return dateTimeValue;
            }
            
            return null;
        }

        /// <summary>
        /// Helper method to safely get string values from metadata
        /// </summary>
        private string? GetMetadataString(IDictionary<string, object> metadata, string key)
        {
            if (metadata.TryGetValue(key, out var value))
            {
                if (value is System.Text.Json.JsonElement jsonElement)
                {
                    return jsonElement.ValueKind == System.Text.Json.JsonValueKind.String ? jsonElement.GetString() : jsonElement.ToString();
                }
                return value?.ToString();
            }
            return null;
        }

        /// <summary>
        /// Helper method to safely get integer values from metadata
        /// </summary>
        private int? GetMetadataInt(IDictionary<string, object> metadata, string key)
        {
            if (metadata.TryGetValue(key, out var value))
            {
                if (value is System.Text.Json.JsonElement jsonElement)
                {
                    if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.Number)
                    {
                        return jsonElement.GetInt32();
                    }
                    else if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.String)
                    {
                        var stringValue = jsonElement.GetString();
                        if (int.TryParse(stringValue, out var intValue))
                        {
                            return intValue;
                        }
                    }
                }
                else if (value is int intValue)
                {
                    return intValue;
                }
                else if (int.TryParse(value?.ToString(), out var parsedValue))
                {
                    return parsedValue;
                }
            }
            return null;
        }

        /// <summary>
        /// Get client email address - try provided email first, then client's primary email, then contact emails
        /// </summary>
        private async Task<string?> GetClientEmailAsync(
            Domain.Clients.Models.Client client, 
            string? providedEmail, 
            ApplicationDbContext context, 
            CancellationToken cancellationToken)
        {
            // 1. Use provided email if valid
            if (!string.IsNullOrWhiteSpace(providedEmail) && IsValidEmail(providedEmail))
            {
                _logger.LogInformation("Using provided email: {Email}", providedEmail);
                return providedEmail;
            }

            // 2. Use client's primary email if available
            if (!string.IsNullOrWhiteSpace(client.Email) && IsValidEmail(client.Email))
            {
                _logger.LogInformation("Using client primary email: {Email}", client.Email);
                return client.Email;
            }

            // 3. Look for email addresses in client contacts
            try
            {
                var clientContacts = await context.Contacts
                    .Include(c => c.EmailAddresses)
                    .Where(c => c.ClientId == client.ClientId)
                    .ToListAsync(cancellationToken);

                var emailAddress = clientContacts
                    .SelectMany(c => c.EmailAddresses)
                    .FirstOrDefault()?.Email;

                if (!string.IsNullOrWhiteSpace(emailAddress) && IsValidEmail(emailAddress))
                {
                    _logger.LogInformation("Using contact email: {Email}", emailAddress);
                    return emailAddress;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to lookup contact emails for client {ClientId}", client.ClientId);
            }

            _logger.LogWarning("No valid email address found for client {ClientName}", client.Name);
            return null;
        }

        /// <summary>
        /// Generate payment link using same logic as Paylink.razor
        /// </summary>
        private string GeneratePaymentLink(string payerName, string emailAddress, decimal amount, string comments)
        {
            var url = "https://metroinsurance.epaypolicy.com/";
            var queryParams = new List<string>();

            if (!string.IsNullOrEmpty(payerName))
            {
                queryParams.Add($"payer={Uri.EscapeDataString(payerName)}");
            }

            if (!string.IsNullOrEmpty(emailAddress))
            {
                queryParams.Add($"emailAddress={Uri.EscapeDataString(emailAddress)}");
            }

            if (amount > 0)
            {
                queryParams.Add($"amount={amount.ToString("F2", CultureInfo.InvariantCulture)}");
            }

            if (!string.IsNullOrEmpty(comments))
            {
                queryParams.Add($"comments={Uri.EscapeDataString(comments)}");
            }

            if (queryParams.Count > 0)
            {
                return url + "?" + string.Join("&", queryParams);
            }

            return url;
        }

        /// <summary>
        /// Create Outlook email using EmberService (same as Paylink.razor)
        /// </summary>
        private async Task<bool> CreateOutlookEmailAsync(string payerName, string toEmail, decimal amount, string note, string paymentLink)
        {
            try
            {
                var subject = "Payment Link from Metro Insurance";
                
                // Create HTML email body using same format as Paylink.razor
                var outlookButtonHtml = $@"<table role=""presentation"" border=""0"" cellpadding=""0"" cellspacing=""0""><tr><td align=""center"" bgcolor=""#0078d4"" style=""border-radius:4px;""><a href=""{paymentLink}"" target=""_blank"" style=""display:inline-block;padding:10px 20px;font-family:Arial,sans-serif;font-size:16px;color:#ffffff;text-decoration:none;border-radius:4px;font-weight:500;border:1px solid #006cbe;"">Make Payment</a></td></tr></table>";
                
                var emailBody = $@"<div style=""font-family: Arial, sans-serif; line-height: 1.5;"">
<p>Dear {payerName},</p>
<p>Please click the link below to make your payment:</p>
{outlookButtonHtml}
<p>Payment Details:<br>
Amount: {amount:C2}<br>
{note}</p>
<p>If you have any questions, please don't hesitate to contact us.</p>
<p>Best regards,<br>
Metro Insurance</p>
</div>";

                _logger.LogInformation("Creating Outlook email - To: {ToEmail}, Subject: {Subject}", toEmail, subject);

                var emailParams = new List<string> { toEmail, subject, emailBody };
                await _emberService.RunEmberFunction("OutlookEmail_CreateNew", emailParams);

                _logger.LogInformation("✅ Outlook email created successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Failed to create Outlook email");
                return false;
            }
        }
    }
} 