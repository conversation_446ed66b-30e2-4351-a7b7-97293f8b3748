﻿@page "/Renewals/List"
@using Surefire.Domain.Renewals.Models
@using Surefire.Domain.Renewals.Services
@using Surefire.Domain.Renewals.Components
@using Microsoft.EntityFrameworkCore
@inject RenewalService RenewalService

<_toolbar PageName="List" />

<div class="page-content-datagrid">
    <div class="fluent-data-grid">
        <FluentDataGrid Items="@FilteredRenewals" ResizableColumns="true" Pagination="@pagination" ShowHover="true"
                        OnRowDoubleClick="@((FluentDataGridRow<Renewal> row) => HandleRowDoubleClick(row))"
                        OnRowClick="@((FluentDataGridRow<Renewal> row) => HandleRowClick(row))">
            <PropertyColumn Property="@(p => p.RenewalId)" Title="Renewal Id" Width="60px" Sortable="true" />
            <PropertyColumn Property="@(p => p.Client.Name)" Title="Client Name" Sortable="true" />
            <PropertyColumn Property="@(p => p.Product.LineNickname)" Title="Line Nickname" Sortable="true" />
            <PropertyColumn Property="@(p => p.ExpiringPolicyNumber)" Title="Expiring Policy Number" Sortable="true" />
            <PropertyColumn Property="@(p => p.Policy.PolicyNumber)" Title="New Policy Number" />
            <PropertyColumn Property="@(p => p.ExpiringPremium)" Title="Expiring Premium" Sortable="true" />
            <PropertyColumn Property="@(p => p.AssignedTo)" Title="Assigned To" Sortable="true" />
            <PropertyColumn Property="@(p => p.RenewalDate)" Title="Renewal Date" Sortable="true" Format="MM/dd/yyyy" />
            <PropertyColumn Property="@(p => p.Carrier.CarrierName)" Title="Carrier Name" />
            <PropertyColumn Property="@(p => p.Wholesaler.CarrierName)" Title="Wholesaler Name" />
        </FluentDataGrid>
        <div class="fluent-data-grid__bottombar">
            <div class="fluent-data-grid__pagination">
                <FluentPaginator State="@pagination" />
            </div>

            <div class="fluent-data-grid__search">
                <FluentSearch Placeholder="Search..." @bind-Value="SearchTerm" @oninput="HandleSearchInput" />
            </div>
        </div>
    </div>
</div>

@code {
    public IQueryable<Renewal> RenewalData { get; set; }
    public IQueryable<Renewal> FilteredRenewals { get; set; }
    public int RenewalId { get; set; }
    public string SelectedRenewalName { get; set; } = "";
    public string SearchTerm { get; set; } = string.Empty;
    PaginationState pagination = new PaginationState { ItemsPerPage = 18 };
    private System.Timers.Timer debounceTimer;
    bool SelectFromEntireRow = true;


    protected override async Task OnInitializedAsync()
    {
        RenewalData = (await RenewalService.GetAllRenewals()).AsQueryable();
        FilteredRenewals = RenewalData;
        debounceTimer = new System.Timers.Timer(300); // Debounce for 300ms
        debounceTimer.AutoReset = false; // Prevent multiple triggers
        debounceTimer.Elapsed += async (sender, e) =>
        {
            await InvokeAsync(ApplySearchFilter); // Apply the search filter after the debounce period
        };
    }

    private void HandleSearchInput(Microsoft.AspNetCore.Components.ChangeEventArgs e)
    {
        if (e.Value is string searchTerm)
        {
            SearchTerm = searchTerm;
            debounceTimer.Stop();
            debounceTimer.Start(); // Start the debounce timer
        }
    }


    private void ApplySearchFilter()
    {
        FilteredRenewals = string.IsNullOrWhiteSpace(SearchTerm)
        ? RenewalData
        : RenewalData.Where(r =>
              EF.Functions.Like(r.Client.Name.ToLower(), $"%{SearchTerm.ToLower()}%")
           || EF.Functions.Like(r.Product.LineNickname.ToLower(), $"%{SearchTerm.ToLower()}%"));

        InvokeAsync(StateHasChanged); // Trigger UI refresh after filtering
    }

    private void HandleRowDoubleClick(FluentDataGridRow<Renewal> row)
    {
        if (row != null && row.Item != null)
        {
            var selectedRenewal = row.Item;
            RenewalId = selectedRenewal.RenewalId;
            Navigation.NavigateTo($"/Renewals/Details/{RenewalId}");
        }
    }

    private void HandleRowClick(FluentDataGridRow<Renewal> row)
    {
        if (row != null && row.Item != null)
        {
            var selectedRenewal = row.Item;
            row.Class = "selected-row";
            SelectedRenewalName = selectedRenewal.Client.Name + ": ";
            RenewalId = selectedRenewal.RenewalId;
            StateHasChanged();
        }
    }
}
