﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Surefire.Domain.Clients.Models;
using Surefire.Domain.Ember;
using Surefire.Domain.Policies.Models;

namespace Surefire.Domain.Agents.Utilities
{
    /// <summary>
    /// Helper class for Outlook integration via EmberService
    /// </summary>
    public class OutlookIntegrationHelper
    {
        private readonly EmberService _emberService;
        private readonly ILogger<OutlookIntegrationHelper> _logger;

        public OutlookIntegrationHelper(EmberService emberService, ILogger<OutlookIntegrationHelper> logger)
        {
            _emberService = emberService;
            _logger = logger;
        }

        /// <summary>
        /// Creates a new email in Outlook with the specified parameters
        /// </summary>
        /// <param name="toEmail">Recipient email address</param>
        /// <param name="subject">Email subject</param>
        /// <param name="body">Email body (HTML format)</param>
        /// <returns>Task representing the asynchronous operation</returns>
        public async Task CreateNewEmailAsync(string toEmail, string subject, string body)
        {
            try
            {
                if (string.IsNullOrEmpty(toEmail))
                {
                    throw new ArgumentException("Recipient email address is required", nameof(toEmail));
                }

                if (string.IsNullOrEmpty(subject))
                {
                    throw new ArgumentException("Email subject is required", nameof(subject));
                }

                if (string.IsNullOrEmpty(body))
                {
                    throw new ArgumentException("Email body is required", nameof(body));
                }

                var parameters = new List<string> { toEmail, subject, body };
                _logger.LogInformation("Creating new Outlook email to: {ToEmail}, Subject: {Subject}", toEmail, subject);

                await _emberService.RunEmberFunction("OutlookEmail_CreateNew", parameters);
                _logger.LogInformation("Successfully created Outlook email");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Outlook email");
                throw;
            }
        }

        /// <summary>
        /// Creates a loss run request email for a client and carrier
        /// </summary>
        /// <param name="client">Client information</param>
        /// <param name="carrierName">Carrier name</param>
        /// <param name="policies">List of policies to include in the request</param>
        /// <param name="toEmail">Recipient email address</param>
        /// <returns>Task representing the asynchronous operation</returns>
        public async Task CreateLossRunRequestEmailAsync(Client client, string carrierName, List<Policy> policies, string toEmail)
        {
            try
            {
                if (client == null)
                {
                    throw new ArgumentNullException(nameof(client), "Client information is required");
                }

                if (string.IsNullOrEmpty(carrierName))
                {
                    throw new ArgumentException("Carrier name is required", nameof(carrierName));
                }

                if (policies == null || policies.Count == 0)
                {
                    throw new ArgumentException("At least one policy is required", nameof(policies));
                }

                if (string.IsNullOrEmpty(toEmail))
                {
                    throw new ArgumentException("Recipient email address is required", nameof(toEmail));
                }

                // Get a representative policy to use in the subject
                var firstPolicy = policies[0];

                // Get the product line name from the first policy
                var productLine = firstPolicy.Product?.LineName ?? "Insurance";

                // Create subject with client name
                string subject = $"Loss Run Request - {client.Name} - {productLine} ({firstPolicy.PolicyNumber})";

                // Build email body with client name
                string body = $"<p>Please send us the current valued loss runs for <strong>{client.Name}</strong>'s {productLine} policies from {carrierName}:</p>";

                // Add policy details to body
                body += "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
                body += "<tr style='background-color: #f2f2f2;'><th>Policy Number</th><th>Effective Date</th><th>Expiration Date</th><th>Carrier</th><th>Wholesaler</th></tr>";

                foreach (var policy in policies)
                {
                    string carrierValue = policy.Carrier?.CarrierName ?? "N/A";
                    string wholesalerValue = policy.Wholesaler?.CarrierName ?? "N/A";

                    body += $"<tr><td>{policy.PolicyNumber}</td><td>{policy.EffectiveDate.ToShortDateString()}</td><td>{policy.ExpirationDate.ToShortDateString()}</td><td>{carrierValue}</td><td>{wholesalerValue}</td></tr>";
                }

                body += "</table>";
                body += "<p>Thank you for your assistance.</p>";

                // Create the email using Ember service
                await CreateNewEmailAsync(toEmail, subject, body);
                _logger.LogInformation("Successfully created loss run request email for {Client} to {Carrier}", client.Name, carrierName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating loss run request email");
                throw;
            }
        }

        /// <summary>
        /// Searches Outlook for emails related to a policy
        /// </summary>
        /// <param name="policyNumber">Policy number to search for</param>
        /// <returns>Task representing the asynchronous operation</returns>
        public async Task SearchOutlookForPolicyAsync(string policyNumber)
        {
            try
            {
                if (string.IsNullOrEmpty(policyNumber))
                {
                    throw new ArgumentException("Policy number is required", nameof(policyNumber));
                }

                // Generate variations of the policy number for more comprehensive search
                var policySearchList = GeneratePolicyVariations(policyNumber);

                _logger.LogInformation("Searching Outlook for policy: {PolicyNumber}", policyNumber);
                await _emberService.RunEmberFunction("OutlookSearch_Policy", policySearchList);
                _logger.LogInformation("Successfully initiated Outlook search for policy");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching Outlook for policy");
                throw;
            }
        }

        /// <summary>
        /// Searches Outlook for emails related to a list of email addresses
        /// </summary>
        /// <param name="emailAddresses">List of email addresses to search for</param>
        /// <returns>Task representing the asynchronous operation</returns>
        public async Task SearchOutlookBroadAsync(List<string> emailAddresses)
        {
            try
            {
                if (emailAddresses == null || emailAddresses.Count == 0)
                {
                    throw new ArgumentException("At least one email address is required", nameof(emailAddresses));
                }

                _logger.LogInformation("Searching Outlook for {Count} email addresses", emailAddresses.Count);
                await _emberService.RunEmberFunction("OutlookSearch_EmailBroad", emailAddresses);
                _logger.LogInformation("Successfully initiated broad Outlook search");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing broad Outlook search");
                throw;
            }
        }

        /// <summary>
        /// Generates variations of a policy number for more comprehensive searching
        /// </summary>
        /// <param name="policyNumber">Original policy number</param>
        /// <returns>List of policy number variations</returns>
        private List<string> GeneratePolicyVariations(string policyNumber)
        {
            var variations = new List<string> { policyNumber };

            // Add variations without spaces
            if (policyNumber.Contains(" "))
            {
                variations.Add(policyNumber.Replace(" ", ""));
            }

            // Add variations without hyphens
            if (policyNumber.Contains("-"))
            {
                variations.Add(policyNumber.Replace("-", ""));
            }

            // Add variations with different separators
            if (policyNumber.Contains("-"))
            {
                variations.Add(policyNumber.Replace("-", " "));
            }

            if (policyNumber.Contains(" "))
            {
                variations.Add(policyNumber.Replace(" ", "-"));
            }

            return variations;
        }
    }
}