@page "/Forms/CertificateRequests"
@using Surefire.Domain.Forms.Models
@using Surefire.Domain.Forms.Services
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Spinner
@using Microsoft.Extensions.Logging
@inject FormService FormService
@inject ILogger<CertificateRequestsList> Logger
@inject NavigationManager NavigationManager
@inherits AppComponentBase

<PageTitle>Certificate Requests</PageTitle>

<div class="page-container">
    <div class="page-header">
        <div class="title-container">
            <h1 class="page-title">Certificate Requests</h1>
            <div class="action-buttons">
                <SfButton CssClass="e-primary" @onclick='() => NavigationManager.NavigateTo("/Forms/CertificateRequest/New")'>
                    <span class="material-icons">add</span>&amp;nbsp;New Request
                </SfButton>
                <SfButton CssClass="e-info" @onclick="RefreshRequests">
                    <span class="material-icons">refresh</span>&amp;nbsp;Refresh
                </SfButton>
            </div>
        </div>
    </div>

    <div class="filter-controls">
        <SfTextBox Placeholder="Search..." @bind-Value="searchText" @oninput="OnSearchTextChanged"></SfTextBox>
        <div class="filter-status">
            <label>Status:</label>
            <FluentSelect @bind-Value="statusFilter" Class="status-select" @onchange="FilterRequests" TOption="string">
                <FluentOption Value="">All</FluentOption>
                <FluentOption Value="Pending">Pending</FluentOption>
                <FluentOption Value="Approved">Approved</FluentOption>
                <FluentOption Value="Rejected">Rejected</FluentOption>
                <FluentOption Value="Review">Review</FluentOption>
                <FluentOption Value="Completed">Completed</FluentOption>
            </FluentSelect>
        </div>
        <div class="date-range">
            <label>Date Range:</label>
            <FluentSelect @bind="dateRangeFilter" class="form-control date-select" @onchange="FilterRequests" TOption="string">
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="week">Past Week</option>
                <option value="month">Past Month</option>
                <option value="quarter">Past 3 Months</option>
                <option value="year">Past Year</option>
            </FluentSelect>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="loading-container">
            <SfSpinner Size="50" Type="SpinnerType.Material"></SfSpinner>
            <p>Loading certificate requests...</p>
        </div>
    }
    else if (hasError)
    {
        <div class="error-state">
            <span class="material-icons error-icon">error_outline</span>
            <p>Unable to load certificate requests.</p>
            <p class="error-details">@errorMessage</p>
            <SfButton CssClass="e-primary" @onclick="RetryLoading">Retry</SfButton>
        </div>
    }
    else
    {
        <div class="grid-container">
            <SfGrid @ref="Grid" DataSource="@filteredRequests" AllowPaging="true" AllowSorting="true" AllowFiltering="false">
                <GridEvents TValue="CertificateRequest" RowSelected="RowSelected"></GridEvents>
                <GridPageSettings PageSize="15" PageSizes="true"></GridPageSettings>
                <GridColumns>
                    <GridColumn Field=@nameof(CertificateRequest.CertificateRequestId) HeaderText="ID" Width="70"></GridColumn>
                    <GridColumn Field=@nameof(CertificateRequest.HolderName) HeaderText="Certificate Holder" Width="200"></GridColumn>
                    <GridColumn Field=@nameof(CertificateRequest.ClientCompany) HeaderText="Client" Width="170"></GridColumn>
                    <GridColumn Field=@nameof(CertificateRequest.RequestDate) HeaderText="Request Date" Format="g" Type="ColumnType.DateTime" Width="150"></GridColumn>
                    <GridColumn Field=@nameof(CertificateRequest.Status) HeaderText="Status" Width="120">
                        <Template>
                            @{
                                var request = (context as CertificateRequest);
                                <span class="status-badge status-@(request.Status.ToLower())">@request.Status</span>
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn Field=@nameof(CertificateRequest.IsImported) HeaderText="Source" Width="120">
                        <Template>
                            @{
                                var request = (context as CertificateRequest);
                                if (request.IsImported)
                                {
                                    <span class="source-badge imported">External Portal</span>
                                }
                                else
                                {
                                    <span class="source-badge internal">Internal</span>
                                }
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn HeaderText="Actions" Width="150">
                        <Template>
                            @{
                                var request = (context as CertificateRequest);
                                <div class="action-buttons">
                                    <button class="btn-icon" @onclick="() => NavigateToDetail(request.CertificateRequestId)" title="View Details">
                                        <span class="material-icons">visibility</span>
                                    </button>
                                    <button class="btn-icon" title="Print Certificate">
                                        <span class="material-icons">print</span>
                                    </button>
                                    <button class="btn-icon" title="Email Certificate">
                                        <span class="material-icons">email</span>
                                    </button>
                                    <button class="btn-icon" @onclick="() => OpenConfirmDeleteDialog(request)" title="Delete">
                                        <span class="material-icons text-danger">delete</span>
                                    </button>
                                </div>
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    }
</div>

<SfDialog @bind-Visible="@isDeleteDialogVisible" IsModal="true" Width="400px">
    <DialogTemplates>
        <Header>
            <div class="dialog-title">
                <span class="material-icons error-icon">warning</span>
                Confirm Delete
            </div>
        </Header>
        <Content>
            <p>Are you sure you want to delete the certificate request for <strong>@(requestToDelete?.HolderName ?? "")</strong>?</p>
            <p class="warning-text">This action cannot be undone.</p>
        </Content>
        <FooterTemplate>
            <div class="dialog-footer">
                <SfButton CssClass="e-danger" @onclick="DeleteRequest">Delete</SfButton>
                <SfButton @onclick="CloseDeleteDialog">Cancel</SfButton>
            </div>
        </FooterTemplate>
    </DialogTemplates>
</SfDialog>

<style>
    .page-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    }

    .page-header {
    margin-bottom: 20px;
    }

    .title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    }

    .page-title {
    margin: 0;
    font-size: 1.8rem;
    color: var(--text-primary);
    }

    .action-buttons {
    display: flex;
    gap: 10px;
    }

    .filter-controls {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    align-items: center;
    }

    .filter-status, .date-range {
    display: flex;
    align-items: center;
    gap: 8px;
    }

    .status-select, .date-select {
    width: 150px;
    }

    .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    }

    .error-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 300px;
        color: #721c24;
        text-align: center;
        padding: 0 20px;
    }

    .error-icon {
        font-size: 48px;
        margin-bottom: 15px;
        color: #dc3545;
    }

    .error-details {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 20px;
        max-width: 600px;
    }

    .grid-container {
    flex: 1;
    height: calc(100vh - 250px);
    }

    .status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    color: white;
    }

    .status-approved {
    background-color: #036ac4;
    }

    .status-rejected {
    background-color: #d94c43;
    }

    .status-review {
    background-color: #72174a;
    }

    .status-completed {
    background-color: #2ca55e;
    }

    .status-pending {
    background-color: #6c757d;
    }

    .source-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    }

    .source-badge.imported {
    background-color: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
    }

    .source-badge.internal {
    background-color: #e9ecef;
    color: #212529;
    border: 1px solid #ced4da;
    }

    .action-buttons {
    display: flex;
    gap: 5px;
    }

    .btn-icon {
    background: none;
    border: none;
    cursor: pointer;
    color: #495057;
    border-radius: 4px;
    padding: 4px;
    transition: background-color 0.2s;
    }

    .btn-icon:hover {
    background-color: #e9ecef;
    }

    .btn-icon .material-icons {
    font-size: 18px;
    }

    .btn-icon .text-danger {
    color: #dc3545;
    }

    .btn-icon:hover .text-danger {
    color: #c82333;
    }

    .dialog-title {
    display: flex;
    align-items: center;
    gap: 10px;
    }

    .warning-text {
    color: #dc3545;
    font-weight: bold;
    }

    .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    }
</style>

@code {
    private List<CertificateRequest> allRequests = new List<CertificateRequest>();
    private List<CertificateRequest> filteredRequests = new List<CertificateRequest>();
    private bool isLoading = true;
    private string searchText = "";
    private string statusFilter = "";
    private string dateRangeFilter = "all";
    private bool isDeleteDialogVisible = false;
    private CertificateRequest requestToDelete;
    private SfGrid<CertificateRequest> Grid;
    private System.Timers.Timer debounceTimer;
    private bool hasError = false;
    private string errorMessage = "";

    protected override async Task OnInitializedAsync()
    {
        debounceTimer = new System.Timers.Timer(300);
        debounceTimer.Elapsed += async (sender, e) =>
        {
            await InvokeAsync(() =>
            {
                FilterRequests();
                StateHasChanged();
            });
        };
        debounceTimer.AutoReset = false;

        await LoadRequests();
    }

    private async Task LoadRequests()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            allRequests = await FormService.GetAllCertificateRequestsAsync();
            FilterRequests();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading certificate requests");
            hasError = true;
            errorMessage = ex.Message;
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshRequests()
    {
        await LoadRequests();
    }
    private void NewCertGo()
    {
        NavigationManager.NavigateTo("Forms/CertificateRequest/New");
    }
    private void OnSearchTextChanged(ChangeEventArgs args)
    {
        searchText = args.Value?.ToString() ?? "";
        
        // Debounce search to avoid too many refreshes while typing
        debounceTimer.Stop();
        debounceTimer.Start();
    }

    private void FilterRequests()
    {
        filteredRequests = allRequests;

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(searchText))
        {
            var searchLower = searchText.ToLower();
            filteredRequests = filteredRequests.Where(r => 
                r.HolderName?.ToLower().Contains(searchLower) == true || 
                r.ClientCompany?.ToLower().Contains(searchLower) == true ||
                r.CertificateRequestId.ToString().Contains(searchLower) ||
                r.HolderEmail?.ToLower().Contains(searchLower) == true ||
                r.ClientEmail?.ToLower().Contains(searchLower) == true
            ).ToList();
        }

        // Apply status filter
        if (!string.IsNullOrWhiteSpace(statusFilter))
        {
            filteredRequests = filteredRequests.Where(r => r.Status == statusFilter).ToList();
        }

        // Apply date range filter
        if (!string.IsNullOrWhiteSpace(dateRangeFilter))
        {
            var today = DateTime.Today;
            filteredRequests = dateRangeFilter switch
            {
                "today" => filteredRequests.Where(r => r.RequestDate.Date == today).ToList(),
                "week" => filteredRequests.Where(r => r.RequestDate >= today.AddDays(-7)).ToList(),
                "month" => filteredRequests.Where(r => r.RequestDate >= today.AddMonths(-1)).ToList(),
                "quarter" => filteredRequests.Where(r => r.RequestDate >= today.AddMonths(-3)).ToList(),
                "year" => filteredRequests.Where(r => r.RequestDate >= today.AddYears(-1)).ToList(),
                _ => filteredRequests
            };
        }
    }

    private void NavigateToDetail(int requestId)
    {
        NavigationManager.NavigateTo($"/Forms/CertificateRequest/{requestId}");
    }

    private void RowSelected(RowSelectEventArgs<CertificateRequest> args)
    {
        NavigateToDetail(args.Data.CertificateRequestId);
    }

    private void OpenConfirmDeleteDialog(CertificateRequest request)
    {
        requestToDelete = request;
        isDeleteDialogVisible = true;
    }

    private void CloseDeleteDialog()
    {
        isDeleteDialogVisible = false;
        requestToDelete = null;
    }

    private async Task DeleteRequest()
    {
        if (requestToDelete != null)
        {
            try
            {
                var success = await FormService.DeleteCertificateRequestAsync(requestToDelete.CertificateRequestId);
                if (success)
                {
                    allRequests.Remove(requestToDelete);
                    FilterRequests();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"Error deleting certificate request {requestToDelete.CertificateRequestId}");
            }
            
            CloseDeleteDialog();
        }
    }

    private void RetryLoading()
    {
        LoadRequests();
    }
} 