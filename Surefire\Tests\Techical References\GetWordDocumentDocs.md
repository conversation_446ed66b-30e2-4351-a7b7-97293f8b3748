# GetWordDocContents EmberCommand Documentation

## Overview
The `GetWordDocContents` EmberCommand allows you to retrieve the plain text content from the currently active Microsoft Word document via the Surefire Tray application.

## Command Details
- **Command Name**: `GetWordDocContents`
- **Parameters**: None required (empty list can be passed)
- **Response**: Returns the document content as plain text via SignalR

## Prerequisites
1. Microsoft Word must be installed on the target machine
2. A Word document must be currently open and active
3. Surefire Tray application must be running and connected to the SignalR hub

## Sending the Command

### JavaScript/TypeScript Example (SignalR Client)
```javascript
// Send the command to get Word document contents
await connection.invoke("SendEmberCommand", userId, "GetWordDocContents", []);
```

### C# Example (SignalR Hub)
```csharp
// From a SignalR Hub method
await Clients.User(userId).SendAsync("ReceiveEmberCommand", "GetWordDocContents", new List<string>());
```

## Receiving the Response

The response will be sent back via the `SendEmberResponse` SignalR method with the following structure:
- **Command**: `"GetWordDocContents"`
- **Response Data**: List containing a single string with the document content

### JavaScript/TypeScript Response Handler
```javascript
// Set up the response listener
connection.on("ReceiveEmberResponse", (userId, command, responseData) => {
    if (command === "GetWordDocContents") {
        const documentText = responseData[0];
        
        if (documentText.startsWith("ERROR:")) {
            console.error("Error getting Word document:", documentText);
            // Handle error case
        } else {
            console.log("Document content received:", documentText);
            // Process the document text
            processDocumentContent(documentText);
        }
    }
});

function processDocumentContent(text) {
    // Your logic to handle the document text
    console.log(`Document length: ${text.length} characters`);
    
    // Example: Display in a text area
    document.getElementById('documentContent').value = text;
    
    // Example: Send to another API for processing
    // await fetch('/api/process-document', {
    //     method: 'POST',
    //     body: JSON.stringify({ content: text }),
    //     headers: { 'Content-Type': 'application/json' }
    // });
}
```

### C# Response Handler (SignalR Hub)
```csharp
[HubMethodName("SendEmberResponse")]
public async Task ReceiveEmberResponse(string userId, string command, List<string> responseData)
{
    if (command == "GetWordDocContents")
    {
        var documentText = responseData.FirstOrDefault();
        
        if (documentText?.StartsWith("ERROR:") == true)
        {
            // Handle error
            await LogError($"Word document error for user {userId}: {documentText}");
        }
        else
        {
            // Process the document content
            await ProcessDocumentContent(userId, documentText);
        }
    }
}

private async Task ProcessDocumentContent(string userId, string documentText)
{
    // Your logic to handle the document text
    Console.WriteLine($"Received document with {documentText?.Length ?? 0} characters from user {userId}");
    
    // Example: Save to database
    // await _documentService.SaveDocumentContent(userId, documentText);
    
    // Example: Send processed result back to client
    // await Clients.User(userId).SendAsync("DocumentProcessed", processedResult);
}
```

## Error Handling

The command can return the following error messages:
- `"ERROR: No active Word application found"` - Word is not running
- `"ERROR: Word application is null"` - Could not connect to Word
- `"ERROR: No active Word document found"` - No document is currently open
- `"ERROR: [Exception message]"` - Any other unexpected error

### Error Handling Example
```javascript
connection.on("ReceiveEmberResponse", (userId, command, responseData) => {
    if (command === "GetWordDocContents") {
        const response = responseData[0];
        
        if (response.startsWith("ERROR:")) {
            const errorMessage = response.substring(7); // Remove "ERROR: " prefix
            
            switch (true) {
                case errorMessage.includes("No active Word application"):
                    showUserMessage("Please open Microsoft Word first.");
                    break;
                case errorMessage.includes("No active Word document"):
                    showUserMessage("Please open a document in Word.");
                    break;
                default:
                    showUserMessage(`An error occurred: ${errorMessage}`);
            }
        } else {
            // Success - process the document content
            processDocumentContent(response);
        }
    }
});
```

## Complete Usage Example

### Client-Side Implementation
```javascript
class WordDocumentManager {
    constructor(signalRConnection) {
        this.connection = signalRConnection;
        this.setupResponseHandler();
    }
    
    setupResponseHandler() {
        this.connection.on("ReceiveEmberResponse", (userId, command, responseData) => {
            if (command === "GetWordDocContents") {
                this.handleDocumentResponse(responseData[0]);
            }
        });
    }
    
    async requestDocumentContent(userId) {
        try {
            await this.connection.invoke("SendEmberCommand", userId, "GetWordDocContents", []);
            console.log("Document content request sent");
        } catch (error) {
            console.error("Failed to send document request:", error);
        }
    }
    
    handleDocumentResponse(content) {
        if (content.startsWith("ERROR:")) {
            this.handleError(content);
        } else {
            this.processDocument(content);
        }
    }
    
    handleError(errorMessage) {
        const error = errorMessage.substring(7);
        console.error("Word document error:", error);
        
        // Show user-friendly error message
        this.showNotification(this.getErrorMessage(error), 'error');
    }
    
    processDocument(content) {
        console.log(`Processing document with ${content.length} characters`);
        
        // Example processing
        const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
        const lineCount = content.split('\n').length;
        
        this.showNotification(`Document loaded: ${wordCount} words, ${lineCount} lines`, 'success');
        
        // Update UI
        this.updateDocumentDisplay(content, { wordCount, lineCount });
    }
    
    getErrorMessage(error) {
        if (error.includes("No active Word application")) {
            return "Please open Microsoft Word first.";
        } else if (error.includes("No active Word document")) {
            return "Please open a document in Word.";
        } else {
            return `Error accessing Word document: ${error}`;
        }
    }
    
    updateDocumentDisplay(content, stats) {
        // Update your UI with the document content and statistics
        document.getElementById('documentText').value = content;
        document.getElementById('wordCount').textContent = stats.wordCount;
        document.getElementById('lineCount').textContent = stats.lineCount;
    }
    
    showNotification(message, type) {
        // Your notification system
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
}

// Usage
const wordManager = new WordDocumentManager(signalRConnection);
await wordManager.requestDocumentContent(currentUserId);
```

## Technical Notes

1. **Text Format**: The returned text is plain text only, without formatting, images, or other rich content.
2. **Document State**: The command captures the document content at the moment the command is executed.
3. **Performance**: Large documents may take a moment to process and transmit.
4. **Security**: The command only accesses the currently active document and requires Word to be running.
5. **Encoding**: Text is returned as UTF-8 encoded string.

## Troubleshooting

### Common Issues
1. **"No active Word application found"**
   - Ensure Microsoft Word is running
   - Try opening Word and a document, then retry the command

2. **"No active Word document found"**
   - Open a document in Word
   - Make sure the document window is active (clicked on)

3. **No response received**
   - Check that Surefire Tray is running and connected
   - Verify the SignalR connection is active
   - Check the debug logs in Surefire Tray

4. **Empty content returned**
   - The document may be empty
   - Check if the document has any text content

### Debug Information
The Surefire Tray application logs all operations to `C:\SUREFIRE\TrayLog.txt`. Check this file for detailed information about command execution and any errors. 