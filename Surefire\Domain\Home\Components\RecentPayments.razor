@using Surefire.Domain.Shared.Models
@using Surefire.Domain.Shared.Services
@using Syncfusion.Blazor.Notifications
@using Syncfusion.Blazor.Spinner
@inject StateService StateService
@implements IDisposable

<div class="sectiontitletab" @onclick="ToggleCollapse">
    <span>Recent Payments</span>
    <span class="caret-container">
        <FluentIcon class=@(isCollapsed ? "caret collapsed" : "caret") Value="@(new Icons.Regular.Size20.ChevronDown())" />
    </span>
</div>
<div class="leads-box-inner slider @(isCollapsed ? "collapsed" : "")">
    <table id="paymentstable" cellspacing="0" class="ltable">
        <thead class="lbg">
            <tr>
                <th class="mid-a">Client</th>
                <th class="mid-b">Amount</th>
                <th class="mid-c">Sale Date</th>
                <th class="mid-d">Status</th>
            </tr>
        </thead>
        <tbody class="lbody">
            @if (isLoading)
            {
                @for (var i = 0; i < 8; i++)
                {
                    <tr class="lrow" style="animation-delay: @(i * 50)ms;">
                        <td class="mid-a ellipsis">
                            <SfSkeleton Shape=SkeletonType.Rectangle Width="100%" Height="15px" CssClass="skeleton-shimmer" Visible="true"></SfSkeleton>
                        </td>
                        <td class="mid-b ellipsis">
                            <SfSkeleton Shape=SkeletonType.Rectangle Width="100%" Height="15px" CssClass="skeleton-shimmer" Visible="true"></SfSkeleton>
                        </td>
                        <td class="mid-c ellipsis">
                            <SfSkeleton Shape=SkeletonType.Rectangle Width="100%" Height="15px" CssClass="skeleton-shimmer" Visible="true"></SfSkeleton>
                        </td>
                        <td class="mid-d ellipsis">
                            <SfSkeleton Shape=SkeletonType.Rectangle Width="100%" Height="15px" CssClass="skeleton-shimmer" Visible="true"></SfSkeleton>
                        </td>
                    </tr>
                }
            }
            else if (transactions == null || !transactions.Any())
            {
                <tr class="fade-in">
                    <td colspan="4" class="no-taskssub">No recent payments found</td>
                </tr>
            }
            else
            {
                @for (var i = 0; i < Math.Min(transactions.Count, 15); i++)
                {
                    var transaction = transactions[i];
                    var isVisible = visibleRows > i;
                    <tr class="lrow main @(isVisible ? "slide-in-row visible" : "slide-in-row")" 
                        style="animation-delay: @(i * 80)ms;">
                        <td class="mid-a mid-name ellipsis" @onclick="() => ShowDetails(transaction)">@transaction.Payer</td>
                        <td class="mid-b mid-color ellipsis">@FormatCurrency(transaction.Amount - transaction.Fee)</td>
                        <td class="mid-c mid-date ellipsis">@FormatDate(transaction.SaleDate)</td>
                        <td class="mid-d ellipsis">
                            <span class="dot dot-2"></span>
                            <span class="mid-dotname">paid</span>
                        </td>
                    </tr>
                }
            }
        </tbody>
    </table>
    <div style="height:7px;"></div>
</div>

@if (showDetailsModal && selectedTransaction != null)
{
    <div class="modal-backdrop" @onclick="CloseDetailsModal">
        <div class="modal-content" @onclick:stopPropagation="true">
            <div class="modal-header">
                <h3>Payment Details</h3>
                <button type="button" class="close-btn" @onclick="CloseDetailsModal">×</button>
            </div>
            <div class="modal-body">
                <table class="details-table">
                    <tr>
                        <td class="detail-label">Transaction ID:</td>
                        <td>@selectedTransaction.TransactionId</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Client:</td>
                        <td>@selectedTransaction.Payer</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Email:</td>
                        <td>@selectedTransaction.Email</td>
                    </tr>
                    <tr>
                        <td class="detail-label green">Net Amount:</td>
                        <td class="green">@FormatCurrency(selectedTransaction.Amount - selectedTransaction.Fee)</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Fee:</td>
                        <td>@FormatCurrency(selectedTransaction.Fee)</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Charged:</td>
                        <td>@FormatCurrency(selectedTransaction.Amount)</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Sale Date:</td>
                        <td>@FormatDate(selectedTransaction.SaleDate)</td>
                    </tr>
                    <tr>
                        <td class="detail-label">Settle Date:</td>
                        <td>@FormatDate(selectedTransaction.SettleDate)</td>
                    </tr>
                    @if (!string.IsNullOrEmpty(selectedTransaction.Comments))
                    {
                        <tr>
                            <td class="detail-label">Comments:</td>
                            <td>@selectedTransaction.Comments</td>
                        </tr>
                    }
                </table>
                <div class="modal-actions">
                    <button type="button" class="action-btn close-modal-btn" @onclick="CloseDetailsModal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<RecentTransactions> transactions = new();
    private bool isLoading = true;
    private bool showDetailsModal = false;
    private RecentTransactions selectedTransaction;
    private bool isCollapsed = false;
    private int visibleRows = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadRecentPaymentsData();
    }

    private async Task LoadRecentPaymentsData()
    {
        try
        {
            // Check if data is likely cached (no need for loading animation)
            var isCached = await IsDataCached();
            
            if (!isCached)
            {
                isLoading = true;
                visibleRows = 0;
                StateHasChanged();
            }

            var allPayments = await StateService.GetRecentPaymentsAsync(CancellationToken.None);
            transactions = allPayments
                .OrderByDescending(t => t.SaleDate)
                .Take(12)
                .ToList();

            // Stop loading and either animate rows in (if fresh) or show immediately (if cached)
            isLoading = false;
            if (isCached)
            {
                visibleRows = Math.Min(transactions.Count, 12);
                StateHasChanged();
            }
            else
            {
                StateHasChanged();
                await AnimateRowsIn();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading recent payments data: {ex.Message}");
            transactions = new();
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task<bool> IsDataCached()
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        try
        {
            var testData = await StateService.GetRecentPaymentsAsync(CancellationToken.None);
            stopwatch.Stop();
            return stopwatch.ElapsedMilliseconds < 100;
        }
        catch
        {
            stopwatch.Stop();
            return false;
        }
    }

    private async Task AnimateRowsIn()
    {
        var totalRows = Math.Min(transactions.Count, 15);
        for (int i = 0; i < totalRows; i++)
        {
            visibleRows = i + 1;
            StateHasChanged();
            await Task.Delay(40); // Stagger the row animations - 33% faster
        }
    }

    private void ToggleCollapse()
    {
        isCollapsed = !isCollapsed;
    }

    private void ShowDetails(RecentTransactions transaction)
    {
        selectedTransaction = transaction;
        showDetailsModal = true;
    }

    private void CloseDetailsModal()
    {
        showDetailsModal = false;
    }

    private string FormatDate(DateTime? date)
    {
        if (date == null || date == DateTime.MinValue)
            return "N/A";

        return date.Value.ToString("MM/dd/yyyy");
    }

    private string FormatCurrency(decimal? amount)
    {
        if (amount == null)
            return "N/A";

        return amount.Value.ToString("C");
    }

    public void Dispose()
    {
        // Nothing here
    }
} 