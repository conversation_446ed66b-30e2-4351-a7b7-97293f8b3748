.data-import-container {
    max-width: 1400px;
    margin: 0 auto;
    height: 100vh;
    overflow: hidden;
}

.data-import-layout {
    display: flex;
    gap: 20px;
    height: calc(100vh - 120px);
}

.data-import-main {
    flex: 1;
    overflow-y: auto;
}

.message-bar-container {
    margin-bottom: 15px;
}

.data-import-header {
    margin-bottom: 30px;
    border-bottom: 1px solid var(--fill-color);
    padding-bottom: 15px;
}

.data-import-header h3 {
    margin: 0 0 5px 0;
    color: var(--accent-foreground-rest);
}

.data-import-header p {
    margin: 0;
    color: var(--neutral-foreground-hint);
    font-size: 14px;
}

.import-section {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.xml-input-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.import-options {
    background: var(--fill-color);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--stroke-color);
}

.import-options h4 {
    margin: 0 0 15px 0;
    color: var(--neutral-foreground-rest);
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.policy-selection {
    margin-left: 25px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.no-policies-message {
    margin-left: 25px;
    margin-top: 10px;
}

.import-actions {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 20px 0;
    border-top: 1px solid var(--stroke-color);
}

.warning-text {
    color: var(--fill-color-warning);
    font-size: 14px;
    margin: 0;
    font-style: italic;
}

.option-description {
    margin-left: 25px;
    margin-top: 5px;
}

.option-description small {
    color: var(--neutral-foreground-hint);
    font-size: 12px;
}

.debug-panel {
    width: 400px;
    background: var(--fill-color);
    border: 1px solid var(--stroke-color);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.debug-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--stroke-color);
    background: var(--neutral-layer-2);
}

.debug-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--neutral-foreground-rest);
}

.debug-content {
    flex: 1;
    padding: 10px;
    overflow-y: auto;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 12px;
}

.debug-message {
    margin-bottom: 8px;
    padding: 6px 8px;
    border-radius: 4px;
    border-left: 3px solid;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.debug-message.info {
    background: rgba(0, 120, 212, 0.1);
    border-left-color: #0078d4;
}

.debug-message.success {
    background: rgba(16, 124, 16, 0.1);
    border-left-color: #107c10;
}

.debug-message.warning {
    background: rgba(255, 185, 0, 0.1);
    border-left-color: #ffb900;
}

.debug-message.error {
    background: rgba(196, 43, 28, 0.1);
    border-left-color: #c42b1c;
}

.debug-time {
    font-size: 10px;
    color: var(--neutral-foreground-hint);
    font-weight: 600;
}

.debug-text {
    color: var(--neutral-foreground-rest);
    word-wrap: break-word;
    white-space: pre-wrap;
}

.debug-empty {
    color: var(--neutral-foreground-hint);
    font-style: italic;
    text-align: center;
    padding: 20px;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .debug-panel {
        width: 350px;
    }
}

@media (max-width: 768px) {
    .data-import-container {
        padding: 15px;
        height: auto;
    }
    
    .data-import-layout {
        flex-direction: column;
        height: auto;
    }
    
    .data-import-main {
        overflow-y: visible;
    }
    
    .debug-panel {
        width: 100%;
        max-height: 300px;
    }
    
    .import-actions {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .import-actions fluent-button {
        width: 100%;
        margin-left: 0 !important;
    }
} 