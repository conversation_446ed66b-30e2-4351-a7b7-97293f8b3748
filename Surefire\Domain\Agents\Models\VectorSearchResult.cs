using System.Collections.Generic;

namespace Surefire.Domain.Agents.Models
{
    public class VectorSearchResult
    {
        /// <summary>
        /// Identifier of the vector entry (e.g., client or policy line ID)
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Similarity score (cosine similarity)
        /// </summary>
        public float Score { get; set; }

        /// <summary>
        /// Optional metadata for additional context
        /// </summary>
        public IDictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }
}
