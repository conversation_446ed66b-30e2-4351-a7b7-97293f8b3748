﻿.enhanced-chat-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 76px);
    width: 100%;
    margin: 0 auto;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    overflow: hidden;
}

.chat-header {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
}

.header-left h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.tagline {
    margin: 0.25rem 0 0 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.health-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    background: rgba(255,255,255,0.15);
    font-size: 0.9rem;
}

    .health-status.online {
        color: #4caf50;
    }

    .health-status.offline {
        color: #f44336;
    }

.stats .stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    opacity: 0.9;
}
.img-responsive {
    width:35px;
    position:relative;
    top:-1px;
}

.chat-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
    height: calc(100vh - 76px);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    scroll-behavior: smooth;
}

.message-wrapper {
    margin-bottom: 1.5rem;
    display: flex;
}

    .message-wrapper.user {
        justify-content: flex-end;
    }

    .message-wrapper.assistant {
        justify-content: flex-start;
    }

.message-bubble {
    max-width: 80%;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    overflow: hidden;
    animation: slideIn 0.3s ease-out;
}

.message-wrapper.user .message-bubble {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff !important;
}

.message-header {
    padding: 1rem 1.5rem 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sender-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.user-avatar {
    background: rgba(255,255,255,0.2);
}

.ai-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.sender-name {
    font-family: "montserrat", sans-serif;
    font-weight: 300;
    font-size: 1.2em;
    color: #f44336;
    color: #b1b1b1;
}

.message-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.8rem;
    opacity: 0.7;
}

.intent-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.intent-action {
    background: #e3f2fd;
    color: #1976d2;
}

.intent-query {
    background: #f3e5f5;
    color: #7b1fa2;
}

.intent-general {
    background: #e8f5e8;
    color: #388e3c;
}

.message-content {
    padding: 0 1.5rem 0rem;
    line-height: 1.6;
    font-size: .9em;
}

.streaming-text {
    position: relative;
}

.typing-cursor {
    animation: blink 1s infinite;
    color: #667eea;
    font-weight: bold;
}

.welcome-message h4 {
    color: #667eea;
    margin-bottom: 0.75rem;
}

.capabilities {
    margin: 1rem 0;
}

.capability {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
}

    .capability i {
        color: #667eea;
        width: 20px;
    }

.data-results {
    margin: 1rem 1.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.results-header {
    background: #f5f5f5;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 500;
}

.expand-toggle {
    margin-left: auto;
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
}

.results-content {
    padding: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

.json-data pre {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    font-size: 0.85rem;
    overflow-x: auto;
}

.suggestions-container {
    margin: 1rem 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.suggestions-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    color: #667eea;
    font-weight: 500;
    font-size: 0.9rem;
}

.suggestions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
}

.suggestion-chip {
    background: #f0f4ff;
    border: 1px solid #e0e8ff;
    color: #667eea;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

    .suggestion-chip:hover {
        background: #667eea;
        color: white;
        transform: translateY(-1px);
    }

.processing-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.pulse-dots {
    display: flex;
    gap: 0.25rem;
}

    .pulse-dots span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #667eea;
        animation: pulse 1.5s infinite;
    }

        .pulse-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .pulse-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

.chat-input-section {
    background: white;
    border-top: 1px solid #e0e0e0;
    padding: 1.5rem;
}

.error-banner {
    background: #ffebee;
    color: #c62828;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.dismiss-error {
    margin-left: auto;
    background: none;
    border: none;
    color: #c62828;
    cursor: pointer;
    padding: 0.25rem;
}

.quick-actions-bar {
    margin-bottom: 1rem;
}

.quick-actions-label {
    font-weight: 500;
    margin-bottom: 0.75rem;
    color: #666;
    font-size: 0.9rem;
}

.quick-actions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.quick-action {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    padding: 0.75rem 1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
}

    .quick-action:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
        transform: translateY(-1px);
    }

.input-container {
    border: 2px solid #e0e0e0;
    border-radius: 16px;
    overflow: hidden;
    transition: border-color 0.2s;
}

    .input-container:focus-within {
        border-color: #667eea;
    }

.input-wrapper {
    display: flex;
    align-items: flex-end;
}

.chat-input {
    flex: 1;
    border: none;
    padding: 1rem 1.25rem;
    font-size: 1rem;
    resize: none;
    max-height: 120px;
    min-height: 50px;
    font-family: inherit;
}

    .chat-input:focus {
        outline: none;
    }

.input-actions {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    gap: 0.5rem;
}

.action-btn {
    background: none;
    border: none;
    padding: 0.75rem;
    border-radius: 50%;
    cursor: pointer;
    color: #666;
    transition: all 0.2s;
}

    .action-btn:hover:not(:disabled) {
        background: #f0f0f0;
        color: #667eea;
    }

    .action-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

.send-button {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
    min-width: 80px;
}

    .send-button:hover:not(:disabled) {
        background: #5a6fd8;
        transform: translateY(-1px);
    }

    .send-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

.input-footer {
    padding: 0.75rem 1.25rem;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
}

.char-counter.warning {
    color: #ff9800;
}

.char-counter.danger {
    color: #f44336;
}

.right-actions {
    display: flex;
    gap: 1rem;
}

.footer-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    transition: color 0.2s;
}

    .footer-btn:hover {
        color: #667eea;
    }

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }

    51%, 100% {
        opacity: 0;
    }
}

@keyframes pulse {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }

    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .enhanced-chat-container {
        height: calc(100vh - 80px);
        border-radius: 0;
    }

    .chat-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .message-bubble {
        max-width: 95%;
    }

    .quick-actions-list {
        flex-direction: column;
    }

    .suggestions-grid {
        grid-template-columns: 1fr;
    }
}
