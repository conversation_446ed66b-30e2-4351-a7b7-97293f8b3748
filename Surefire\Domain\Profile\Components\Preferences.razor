﻿@page "/Profile/Preferences"
@using Surefire.Domain.Ember
@using Surefire.Domain.Logs

<div class="systemcontainer">
    <div class="system-header">System Interface</div>
    <div class="system-content">
      @*   <FluentTabs Size="TabSize.Large" Style="padding:0;">
            <FluentTab Id="tab-1" Label="Profile" Class="sys-tab">
                <SystemSettings />
            </FluentTab>
            <FluentTab Id="tab-2" Label="Transactions" Class="sys-tab">
                <RecentPaymentsList ShowAll="true" />
            </FluentTab>
            <FluentTab Id="tab-3" Label="Call Log" Class="sys-tab">
                <RecentPhoneCallsAll />
            </FluentTab>
            <FluentTab Id="tab-4" Label="Users" Class="sys-tab">
                <div class="system-subheader">All registered users.</div>
                <Users />
            </FluentTab>
            <FluentTab Id="tab-5" Label="Console" Class="sys-tab">
                <div class="log-console">
                    @if (logs == null)
                    {
                        <FluentProgressRing Width="30px" Color="#1b8ce3" />
                    }
                    else if (!logs.Any())
                    {
                        <span class="log-empty">No log entries available for the current user.</span>
                    }
                    else
                    {
                        <div class="log-content">
                            @foreach (var log in logs)
                            {
                                <div class="log-entry">
                                    <span class="log-timestamp">[@log.Timestamp]</span>
                                    <span class="log-level">@log.LogLevel:</span>
                                    <span class="log-message">@log.Message</span>
                                </div>
                            }
                        </div>
                    }
                </div>
            </FluentTab>
            <FluentTab Id="tab-6" Label="Account Settings" Class="sys-tab">
                <div class="system-subheader">Manage your account settings and security.</div>
                <UserProfileManager />
            </FluentTab>
            <FluentTab Id="tab-7" Label="Email Templates" Class="sys-tab">
                <div class="scroller">
                    <div class="system-subheader">Manage email templates for automated communications.</div>
                    <EmailTemplateManager />
                </div>
                <style>
                    .scroller {
                        padding: 0px;
                        overflow-y: scroll;
                        height: fit-content;
                    }
                </style>
            </FluentTab>
        </FluentTabs> *@
    </div>
</div>

@code {
    [Inject] private ILoggingService LoggingService { get; set; }

    private List<Log> logs = null;

    protected override async Task OnInitializedAsync()
    {
        logs = await GetLogsForCurrentUserAsync();  // Load logs on initialization
    }

    private async Task<List<Log>> GetLogsForCurrentUserAsync()
    {
        try
        {
            return await LoggingService.GetLogsForCurrentUserAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading logs: {ex.Message}");
            return new List<Log>();
        }
    }
}