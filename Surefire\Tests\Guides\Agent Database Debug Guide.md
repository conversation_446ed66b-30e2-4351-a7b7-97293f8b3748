# Database Query Debug Guide

## Overview
Added comprehensive debug logging to troubleshoot the "Failed to generate SQL query" error when asking database questions like "What is TWS Facility's phone number?"

## Debug Configuration Added

### appsettings.json Configuration
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Information",
      "Surefire.Domain.Agents.Handlers.DatabaseQueryHandler": "Debug"
    }
  },
  "DatabaseQuery": {
    "EnableDebugLogging": true,
    "LogSqlGeneration": true,
    "LogSchemaContext": true,
    "LogPrompts": true
  }
}
```

### Debug Features Added

1. **Step-by-step processing logging** with 🔍 emoji markers
2. **SQL generation detailed logging** including:
   - Schema context retrieval
   - Prompt generation and content
   - LLM response parsing
   - SQL validation steps
   - Query execution timing

3. **Debug endpoint** for direct testing:
   ```
   GET /api/debug/database-query?query=What is TWS Facility's phone number?
   ```

## How to Debug

### 1. Check Application Logs
Look for these debug markers in your application logs:

- `🔍 DEBUG: Starting database query handling`
- `🔍 SQL-GEN DEBUG: Starting SQL generation`
- `🔍 SCHEMA DEBUG: Retrieved schema context`
- `🔍 PROMPT DEBUG: Generated prompt`
- `🔍 RESPONSE DEBUG: Raw LLM response`
- `🔍 PARSE DEBUG: Parsing LLM response`
- `🔍 VALIDATION DEBUG: SQL validation`

### 2. Use the Debug Endpoint
Test specific queries directly:
```bash
curl "https://localhost:5001/api/debug/database-query?query=What%20is%20TWS%20Facility's%20phone%20number?"
```

### 3. What to Look For

**Common Issues:**
1. **Empty LLM Response**: Check if OpenAI API is working
2. **Parse Failures**: LLM not returning valid JSON
3. **Schema Context Issues**: Check if database schema info is loading correctly
4. **SQL Validation Failures**: Generated SQL not passing safety checks

**Debug Output Examples:**
```
🔍 DEBUG: Starting database query handling for input: 'What is TWS Facility's phone number?'
🔍 DEBUG: Step 1 - Starting SQL generation...
🔍 SQL-GEN DEBUG: Getting schema context...
🔍 SCHEMA DEBUG: Retrieved schema context - Tables: 8, Business Context: 'Insurance business application...'
🔍 PROMPT DEBUG: Generated prompt:
You are a SQL expert for an insurance business application...
🔍 SQL-GEN DEBUG: Calling LLM with gpt-4o-mini model...
🔍 SQL-GEN DEBUG: LLM response received. Response length: 245, IsEmpty: False
🔍 RESPONSE DEBUG: Raw LLM response:
{"sql": "SELECT c.PhoneNumber FROM Clients c WHERE c.Name LIKE '%TWS Facility%'", "explanation": "...", ...}
🔍 PARSE DEBUG: Successfully parsed - SQL: 'SELECT c.PhoneNumber...', Confidence: 0.85
🔍 DEBUG: Step 2 - Starting SQL validation...
🔍 VALIDATION DEBUG: Final validation result - Valid: True, ReadOnly: True, Complexity: Low
```

## Services Re-enabled for Debug

- `IDatabaseQueryHandler` and `DatabaseQueryHandler` - Main query processing with debug logging
- `IOpenAISimpleService` and `OpenAISimpleService` - Required for LLM communication
- `IUnifiedInputHandler` and `UnifiedInputHandlerAdapter` - Adapter to bridge old components with new OpenAI architecture

## Architecture Bridge

The `UnifiedInputHandlerAdapter` solves the dependency injection issue by:
- Implementing `IUnifiedInputHandler` interface that existing components expect
- Delegating all operations to the new `IOpenAIAgent` service
- Maintaining compatibility while using the improved OpenAI architecture
- Adding debug logging with 🔄 ADAPTER markers for easy identification

## Next Steps

1. **Run the application** and check logs when asking database questions
2. **Use the debug endpoint** to test specific queries
3. **Review the debug output** to identify where the process is failing
4. **Check configuration** if no debug output appears

The debug logging will show exactly where the SQL generation is failing and help identify whether it's:
- Schema loading issues
- LLM communication problems  
- Response parsing failures
- SQL validation issues
- Database execution problems 