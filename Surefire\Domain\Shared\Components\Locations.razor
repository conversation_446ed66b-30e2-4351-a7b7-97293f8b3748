﻿@page "/Locations"
@using Microsoft.EntityFrameworkCore
@inject SharedService SharedService

<div class="page-toolbar">
    <FluentMenuButton Text="Create New" OnMenuChanged="HandleOnMenuChanged">
        <FluentMenuItem><FluentIcon Value="@(new Icons.Regular.Size20.Briefcase())" Color="Color.Custom" CustomColor="#000" Slot="start" /> Client</FluentMenuItem>
        <FluentMenuItem><FluentIcon Value="@(new Icons.Regular.Size20.Person())" Color="Color.Custom" CustomColor="#000" Slot="start" /> Contact</FluentMenuItem>
        <FluentMenuItem><FluentIcon Value="@(new Icons.Regular.Size20.Certificate())" Color="Color.Custom" CustomColor="#000" Slot="start" /> Certificate</FluentMenuItem>
        <FluentMenuItem><FluentIcon Value="@(new Icons.Regular.Size20.Document())" Color="Color.Custom" CustomColor="#000" Slot="start" /> Policy</FluentMenuItem>
    </FluentMenuButton>
</div>

<div class="page-content-datagrid">
    <div class="fluent-data-grid">
        <FluentDataGrid Items="@FilteredAddresses" ResizableColumns="true" Pagination="@pagination" ShowHover="true">
            <PropertyColumn Property="@(p => p.AddressId)" Title="Address Id" Width="60px" Sortable="true" />
            <PropertyColumn Property="@(p => p.AddressLine1)" Title="Address Line 1" Width="350px" Sortable="true" />
            <PropertyColumn Property="@(p => p.AddressLine2)" Title="Address Line 2" Sortable="true" />
            <PropertyColumn Property="@(p => p.City)" Title="City" Sortable="true" />
            <PropertyColumn Property="@(p => p.State)" Title="State" Sortable="true" />
            <PropertyColumn Property="@(p => p.PostalCode)" Title="Postal Code" Sortable="true" />
        </FluentDataGrid>
        <div class="fluent-data-grid__bottombar">
            <div class="fluent-data-grid__pagination">
                <FluentPaginator State="@pagination" />
            </div>

            <div class="fluent-data-grid__search">
                <FluentSearch Placeholder="Search..." @bind-Value="SearchTerm" @oninput="HandleSearchInput" />
            </div>
        </div>
    </div>
</div>

@code {
    public IQueryable<Address> AddressData { get; set; }
    public IQueryable<Address> FilteredAddresses { get; set; }
    public int AddressId { get; set; }
    public string SelectedAddressLine1 { get; set; } = "";
    public string SearchTerm { get; set; } = string.Empty;
    PaginationState pagination = new PaginationState { ItemsPerPage = 18 };
    private System.Timers.Timer debounceTimer;
    bool SelectFromEntireRow = true;
    [CascadingParameter] public Action<string> UpdateHeader { get; set; }

    protected override async Task OnInitializedAsync()
    {
        UpdateHeader?.Invoke("Locations");
        AddressData = SharedService.GetAllAddresses();
        FilteredAddresses = AddressData;
        debounceTimer = new System.Timers.Timer(300); // Debounce for 300ms
        debounceTimer.AutoReset = false; // Prevent multiple triggers
        debounceTimer.Elapsed += async (sender, e) =>
        {
            await InvokeAsync(ApplySearchFilter); // Apply the search filter after the debounce period
        };
    }

    private void HandleSearchInput(Microsoft.AspNetCore.Components.ChangeEventArgs e)
    {
        if (e.Value is string searchTerm)
        {
            SearchTerm = searchTerm;
            debounceTimer.Stop();
            debounceTimer.Start(); // Start the debounce timer
        }
    }

    private void ApplySearchFilter()
    {
        FilteredAddresses = string.IsNullOrWhiteSpace(SearchTerm)
        ? AddressData
        : AddressData.Where(a =>
              EF.Functions.Like(a.AddressLine1.ToLower(), $"%{SearchTerm.ToLower()}%")
           || EF.Functions.Like(a.City.ToLower(), $"%{SearchTerm.ToLower()}%")
           || EF.Functions.Like(a.State.ToLower(), $"%{SearchTerm.ToLower()}%")
           || EF.Functions.Like(a.PostalCode.ToLower(), $"%{SearchTerm.ToLower()}%"));

        InvokeAsync(StateHasChanged); // Trigger UI refresh after filtering
    }

    private void HandleRowDoubleClick(FluentDataGridRow<Address> row)
    {
        if (row != null && row.Item != null)
        {
            var selectedAddress = row.Item;
            AddressId = selectedAddress.AddressId;
            Navigation.NavigateTo($"/Locations/Details/{AddressId}");
        }
    }

    private void HandleRowClick(FluentDataGridRow<Address> row)
    {
        if (row != null && row.Item != null)
        {
            var selectedAddress = row.Item;
            row.Class = "selected-row";
            SelectedAddressLine1 = selectedAddress.AddressLine1 + ": ";
            AddressId = selectedAddress.AddressId;
            StateHasChanged();
        }
    }
    private async Task HandleOnMenuChanged(MenuChangeEventArgs args)
    {
        switch (args.Id)
        {
            case "Client":
                Navigation.NavigateTo($"/Clients/Create");
                break;
            case "Contact":
                Navigation.NavigateTo($"/Clients/Create");
                break;
            case "Certificate":
                Navigation.NavigateTo($"/Clients");
                break;
            case "Policy":
                Navigation.NavigateTo($"/Policies");
                break;
        }
    }
}
