using Microsoft.JSInterop;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Surefire.Domain.Clients.Models;
using Surefire.Domain.Graph;
using System.Linq;
using System.Collections.Generic;
using Surefire.Domain.Forms.Models;

namespace Surefire.Domain.Clients.Services
{
    public class ClientStateService
    {
        private readonly IJSRuntime _jsRuntime;
        private const string StateKey = "ClientsPageState";

        // State properties to persist
        public int? SelectedClientId { get; set; }
        public string? SearchTerm { get; set; }
        public string? ActiveTab { get; set; }
        public bool? DisablePlugins { get; set; }
        public List<ClientListItem>? FilteredClients { get; set; }

        // Email cache (keeping this one)
        public Dictionary<string, List<EmailMessage>> RecentClientEmailsCache { get; set; } = new();

        // New cached data for rarely changing items
        public List<FormPdf>? AllFormPdfs { get; set; }
        public DateTime? AllFormPdfsLastLoaded { get; set; }
        public List<ClientListItem>? AllClients { get; set; }
        public DateTime? AllClientsLastLoaded { get; set; }

        // Cache duration for rarely changing data (default: 30 minutes)
        private readonly TimeSpan CacheDuration = TimeSpan.FromMinutes(30);

        public ClientStateService(IJSRuntime jsRuntime)
        {
            _jsRuntime = jsRuntime;
            RecentClientEmailsCache = new Dictionary<string, List<EmailMessage>>();
        }

        /// <summary>
        /// Check if cached FormPdfs are still valid (not expired)
        /// </summary>
        public bool IsFormPdfsCacheValid()
        {
            return AllFormPdfs != null && 
                   AllFormPdfsLastLoaded.HasValue && 
                   DateTime.UtcNow - AllFormPdfsLastLoaded.Value < CacheDuration;
        }

        /// <summary>
        /// Check if cached Clients are still valid (not expired)
        /// </summary>
        public bool IsClientsCacheValid()
        {
            return AllClients != null && 
                   AllClientsLastLoaded.HasValue && 
                   DateTime.UtcNow - AllClientsLastLoaded.Value < CacheDuration;
        }

        /// <summary>
        /// Set cached FormPdfs with timestamp
        /// </summary>
        public void SetFormPdfsCache(List<FormPdf> formPdfs)
        {
            AllFormPdfs = formPdfs;
            AllFormPdfsLastLoaded = DateTime.UtcNow;
        }

        /// <summary>
        /// Set cached Clients with timestamp
        /// </summary>
        public void SetClientsCache(List<ClientListItem> clients)
        {
            AllClients = clients;
            AllClientsLastLoaded = DateTime.UtcNow;
        }

        /// <summary>
        /// Clear the FormPdfs cache (useful when forms are added/modified)
        /// </summary>
        public async Task InvalidateFormPdfsCacheAsync()
        {
            AllFormPdfs = null;
            AllFormPdfsLastLoaded = null;
            await SaveStateAsync();
        }

        /// <summary>
        /// Clear the Clients cache (useful when clients are added/modified)
        /// </summary>
        public async Task InvalidateClientsCacheAsync()
        {
            AllClients = null;
            AllClientsLastLoaded = null;
            FilteredClients = null; // Also clear filtered clients since they depend on the main list
            await SaveStateAsync();
        }

        /// <summary>
        /// Get cached emails for a client key. Returns null if not found.
        /// </summary>
        public List<EmailMessage>? GetCachedEmails(string cacheKey)
        {
            if (RecentClientEmailsCache != null && RecentClientEmailsCache.TryGetValue(cacheKey, out var emails))
            {
                return emails;
            }
            return null;
        }

        /// <summary>
        /// Set cached emails for a client key, enforcing cache size limits.
        /// </summary>
        public void SetCachedEmails(string cacheKey, List<EmailMessage> emails)
        {
            if (RecentClientEmailsCache == null)
                RecentClientEmailsCache = new Dictionary<string, List<EmailMessage>>();

            // Limit emails per client
            if (emails.Count > 50)
                emails = emails.Take(50).ToList();

            RecentClientEmailsCache[cacheKey] = emails;

            // Limit to 10 clients in cache
            if (RecentClientEmailsCache.Count > 10)
            {
                // Remove oldest entry
                var keyToRemove = RecentClientEmailsCache.Keys.FirstOrDefault();
                if (keyToRemove != null)
                    RecentClientEmailsCache.Remove(keyToRemove);
            }
        }

        public async Task SaveStateAsync()
        {
            var state = new PersistedState
            {
                SelectedClientId = SelectedClientId,
                SearchTerm = SearchTerm,
                ActiveTab = ActiveTab,
                DisablePlugins = DisablePlugins,
                FilteredClients = FilteredClients,
                RecentClientEmailsCache = RecentClientEmailsCache,
                AllFormPdfs = AllFormPdfs,
                AllFormPdfsLastLoaded = AllFormPdfsLastLoaded,
                AllClients = AllClients,
                AllClientsLastLoaded = AllClientsLastLoaded
            };
            var options = new JsonSerializerOptions
            {
                ReferenceHandler = ReferenceHandler.Preserve,
                WriteIndented = false
            };
            var json = JsonSerializer.Serialize(state, options);
            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", StateKey, json);
        }

        public async Task LoadStateAsync()
        {
            var json = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", StateKey);
            if (!string.IsNullOrEmpty(json))
            {
                var options = new JsonSerializerOptions
                {
                    ReferenceHandler = ReferenceHandler.Preserve,
                    WriteIndented = false
                };
                var state = JsonSerializer.Deserialize<PersistedState>(json, options);
                if (state != null)
                {
                    SelectedClientId = state.SelectedClientId;
                    SearchTerm = state.SearchTerm;
                    ActiveTab = state.ActiveTab;
                    DisablePlugins = state.DisablePlugins;
                    FilteredClients = state.FilteredClients;
                    RecentClientEmailsCache = state.RecentClientEmailsCache ?? new Dictionary<string, List<EmailMessage>>();
                    AllFormPdfs = state.AllFormPdfs;
                    AllFormPdfsLastLoaded = state.AllFormPdfsLastLoaded;
                    AllClients = state.AllClients;
                    AllClientsLastLoaded = state.AllClientsLastLoaded;
                }
            }
        }

        public async Task ClearStateAsync()
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", StateKey);
            SelectedClientId = null;
            SearchTerm = null;
            ActiveTab = null;
            DisablePlugins = null;
            FilteredClients = null;
            RecentClientEmailsCache = new Dictionary<string, List<EmailMessage>>();
            AllFormPdfs = null;
            AllFormPdfsLastLoaded = null;
            AllClients = null;
            AllClientsLastLoaded = null;
        }

        /// <summary>
        /// Invalidates the cached client data for a specific client
        /// </summary>
        public async Task InvalidateClientCacheAsync(int clientId)
        {
            // Clear email cache for this client
            var emailCacheKeysToRemove = RecentClientEmailsCache.Keys
                .Where(key => key.Contains(clientId.ToString()))
                .ToList();
            
            foreach (var key in emailCacheKeysToRemove)
            {
                RecentClientEmailsCache.Remove(key);
            }
            
            // Update the filtered clients list if it exists
            if (FilteredClients != null)
            {
                var clientToUpdate = FilteredClients.FirstOrDefault(c => c.ClientId == clientId);
                if (clientToUpdate != null)
                {
                    // Remove it so it gets refreshed on next load
                    FilteredClients.Remove(clientToUpdate);
                }
            }

            // Update the main clients cache if it exists
            if (AllClients != null)
            {
                var clientToUpdate = AllClients.FirstOrDefault(c => c.ClientId == clientId);
                if (clientToUpdate != null)
                {
                    // Remove it so it gets refreshed on next load
                    AllClients.Remove(clientToUpdate);
                }
            }
            
            // Save the updated state
            await SaveStateAsync();
        }

        /// <summary>
        /// Refreshes the currently selected client data
        /// </summary>
        public async Task RefreshSelectedClientAsync()
        {
            if (SelectedClientId.HasValue)
            {
                await InvalidateClientCacheAsync(SelectedClientId.Value);
            }
        }

        /// <summary>
        /// Clears all client-related caches
        /// </summary>
        public async Task ClearAllClientCachesAsync()
        {
            FilteredClients = null;
            AllClients = null;
            AllClientsLastLoaded = null;
            RecentClientEmailsCache.Clear();
            
            await SaveStateAsync();
        }

        /// <summary>
        /// Clears all caches including forms
        /// </summary>
        public async Task ClearAllCachesAsync()
        {
            await ClearAllClientCachesAsync();
            AllFormPdfs = null;
            AllFormPdfsLastLoaded = null;
            await SaveStateAsync();
        }

        private class PersistedState
        {
            public int? SelectedClientId { get; set; }
            public string? SearchTerm { get; set; }
            public string? ActiveTab { get; set; }
            public bool? DisablePlugins { get; set; }
            public List<ClientListItem>? FilteredClients { get; set; }
            public Dictionary<string, List<EmailMessage>>? RecentClientEmailsCache { get; set; }
            public List<FormPdf>? AllFormPdfs { get; set; }
            public DateTime? AllFormPdfsLastLoaded { get; set; }
            public List<ClientListItem>? AllClients { get; set; }
            public DateTime? AllClientsLastLoaded { get; set; }
        }
    }
}
