# Server/Database Admin Implementation Checklist

## Environment Variables & Configuration

### Required Environment Variables
- [ ] **OPENAI_API_KEY** - OpenAI API key for GPT models
- [ ] **OLLAMA_ENDPOINT** - Ollama service endpoint (default: http://localhost:11434/)
- [ ] **VECTOR_DB_CONNECTION** - Vector database connection string (if using external vector DB)

### Optional Environment Variables
- [ ] **AZURE_OPENAI_ENDPOINT** - Azure OpenAI endpoint (if using Azure OpenAI)
- [ ] **AZURE_OPENAI_KEY** - Azure OpenAI API key
- [ ] **SEMANTIC_KERNEL_LOG_LEVEL** - Logging level for Semantic Kernel (Debug, Info, Warning, Error)

## Local AI Infrastructure (Ollama)

### Ollama Installation & Setup
- [ ] Install Ollama on the server: `curl -fsSL https://ollama.ai/install.sh | sh`
- [ ] Start Ollama service: `systemctl start ollama` (Linux) or start as service (Windows)
- [ ] Pull required models:
  ```bash
  ollama pull llama3.2:3b          # For general completions
  ollama pull nomic-embed-text     # For embeddings
  ollama pull codellama:7b         # For SQL generation (optional)
  ```
- [ ] Verify Ollama is running: `curl http://localhost:11434/api/tags`
- [ ] Configure Ollama to start on boot: `systemctl enable ollama`

### Network Configuration
- [ ] Ensure port 11434 is accessible from the application server
- [ ] Configure firewall rules if needed
- [ ] Set up reverse proxy if exposing Ollama externally (not recommended for production)

## Database Schema Enhancements

### Semantic Memory Tables
- [ ] Create `SemanticMemoryCollections` table:
  ```sql
  CREATE TABLE SemanticMemoryCollections (
      Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
      Name NVARCHAR(255) NOT NULL UNIQUE,
      Description NVARCHAR(MAX),
      CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
      UpdatedAt DATETIME2 DEFAULT GETUTCDATE()
  );
  ```

- [ ] Create `SemanticMemoryItems` table:
  ```sql
  CREATE TABLE SemanticMemoryItems (
      Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
      CollectionId UNIQUEIDENTIFIER NOT NULL,
      ExternalId NVARCHAR(255) NOT NULL,
      Text NVARCHAR(MAX) NOT NULL,
      Description NVARCHAR(MAX),
      Metadata NVARCHAR(MAX), -- JSON
      Embedding VARBINARY(MAX), -- Serialized float array
      CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
      UpdatedAt DATETIME2 DEFAULT GETUTCDATE(),
      FOREIGN KEY (CollectionId) REFERENCES SemanticMemoryCollections(Id),
      UNIQUE(CollectionId, ExternalId)
  );
  ```

### Agent Execution Tracking
- [ ] Create `AgentExecutions` table:
  ```sql
  CREATE TABLE AgentExecutions (
      Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
      UserId NVARCHAR(450) NOT NULL,
      InputText NVARCHAR(MAX) NOT NULL,
      IntentType NVARCHAR(50) NOT NULL, -- AgentAction, DatabaseQuery, GeneralAI
      AgentName NVARCHAR(255),
      ExecutionTimeMs INT,
      Success BIT NOT NULL,
      ErrorMessage NVARCHAR(MAX),
      Response NVARCHAR(MAX),
      CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
      FOREIGN KEY (UserId) REFERENCES AspNetUsers(Id)
  );
  ```

### Database Schema Documentation
- [ ] Create `DatabaseSchemaDocumentation` table:
  ```sql
  CREATE TABLE DatabaseSchemaDocumentation (
      Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
      TableName NVARCHAR(255) NOT NULL,
      ColumnName NVARCHAR(255),
      Description NVARCHAR(MAX) NOT NULL,
      ExampleQueries NVARCHAR(MAX), -- JSON array of example queries
      BusinessContext NVARCHAR(MAX),
      CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
      UpdatedAt DATETIME2 DEFAULT GETUTCDATE()
  );
  ```

### Indexes for Performance
- [ ] Create indexes on semantic memory:
  ```sql
  CREATE INDEX IX_SemanticMemoryItems_CollectionId ON SemanticMemoryItems(CollectionId);
  CREATE INDEX IX_SemanticMemoryItems_ExternalId ON SemanticMemoryItems(ExternalId);
  CREATE INDEX IX_AgentExecutions_UserId_CreatedAt ON AgentExecutions(UserId, CreatedAt);
  CREATE INDEX IX_AgentExecutions_IntentType ON AgentExecutions(IntentType);
  ```

## Vector Database (Optional - for Production Scale)

### If Using Azure Cognitive Search
- [ ] Create Azure Cognitive Search service
- [ ] Configure search indexes for semantic memory
- [ ] Set up API keys and connection strings

### If Using Pinecone
- [ ] Create Pinecone account and project
- [ ] Set up indexes with appropriate dimensions (1536 for OpenAI embeddings)
- [ ] Configure API keys

### If Using Qdrant (Self-hosted)
- [ ] Install Qdrant: `docker run -p 6333:6333 qdrant/qdrant`
- [ ] Create collections for different memory types
- [ ] Configure persistence and backup

## Application Configuration

### appsettings.json Updates
- [ ] Add AI service configuration:
  ```json
  {
    "AI": {
      "DefaultProvider": "OpenAI", // or "Ollama"
      "OpenAI": {
        "ApiKey": "", // Set via environment variable
        "DefaultModel": "gpt-4o-mini",
        "MaxTokens": 4000,
        "Temperature": 0.7
      },
      "Ollama": {
        "Endpoint": "http://localhost:11434",
        "DefaultModel": "llama3.2:3b",
        "EmbeddingModel": "nomic-embed-text"
      },
      "SemanticMemory": {
        "Provider": "InMemory", // or "Database", "AzureCognitiveSearch", "Pinecone"
        "DefaultCollection": "general",
        "MaxResults": 10
      }
    }
  }
  ```

### Logging Configuration
- [ ] Configure structured logging for AI operations
- [ ] Set up log retention policies
- [ ] Configure log levels for different AI components

## Security & Compliance

### API Key Management
- [ ] Store API keys in secure key vault (Azure Key Vault, AWS Secrets Manager, etc.)
- [ ] Implement key rotation policies
- [ ] Set up monitoring for API key usage

### Data Privacy
- [ ] Implement data retention policies for AI interactions
- [ ] Set up data anonymization for sensitive information
- [ ] Configure audit logging for AI operations

### Rate Limiting
- [ ] Implement rate limiting for AI API calls
- [ ] Set up monitoring and alerting for API usage
- [ ] Configure fallback mechanisms for API failures

## Monitoring & Observability

### Application Insights / Monitoring
- [ ] Set up AI operation tracking
- [ ] Configure performance counters for AI services
- [ ] Set up alerts for AI service failures

### Health Checks
- [ ] Implement health checks for Ollama service
- [ ] Add health checks for vector database connections
- [ ] Configure health check endpoints

## Backup & Recovery

### Data Backup
- [ ] Include semantic memory data in backup procedures
- [ ] Set up backup for vector database (if external)
- [ ] Test restore procedures

### Disaster Recovery
- [ ] Document AI service dependencies
- [ ] Create runbooks for AI service recovery
- [ ] Test failover procedures

## Performance Optimization

### Caching
- [ ] Implement Redis cache for frequent AI responses
- [ ] Set up response caching for database queries
- [ ] Configure embedding caching

### Resource Allocation
- [ ] Monitor CPU/Memory usage for AI operations
- [ ] Configure appropriate timeouts for AI calls
- [ ] Set up auto-scaling if using cloud services

## Testing & Validation

### Integration Testing
- [ ] Test AI service connectivity
- [ ] Validate semantic memory operations
- [ ] Test agent execution workflows

### Load Testing
- [ ] Test AI service performance under load
- [ ] Validate response times for different query types
- [ ] Test concurrent user scenarios

## Documentation

### Operational Documentation
- [ ] Document AI service architecture
- [ ] Create troubleshooting guides
- [ ] Document configuration procedures

### User Documentation
- [ ] Create user guides for AI features
- [ ] Document example queries and commands
- [ ] Create FAQ for common issues

---

## Verification Commands

After completing the setup, verify everything is working:

```bash
# Test Ollama
curl http://localhost:11434/api/tags

# Test database connectivity
# Run from application: dotnet ef database update

# Test AI services
# Use the application's health check endpoint
curl http://localhost:5000/health
```

## Notes

- This checklist assumes a Windows/SQL Server environment based on the existing codebase
- Adjust database scripts for other database providers (PostgreSQL, MySQL, etc.)
- Consider using Docker containers for easier deployment and scaling
- Implement gradual rollout for production environments 