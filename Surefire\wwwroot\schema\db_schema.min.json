[{"table": "Address", "desc": "Street addresses for clients, contacts, carriers or businesses.", "col": [{"n": "AddressId", "t": "int", "desc": "Primary key, auto-incrementing ID."}, {"n": "AddressLine1"}, {"n": "AddressLine2"}, {"n": "City"}, {"n": "State"}, {"n": "PostalCode"}], "pk": ["AddressId"]}, {"table": "AspNetUsers", "desc": "Stores user account and identity information.", "col": [{"n": "Id", "t": "<PERSON><PERSON><PERSON><PERSON>(450)"}, {"n": "UserName", "t": "<PERSON><PERSON><PERSON><PERSON>(256)"}, {"n": "Email", "t": "<PERSON><PERSON><PERSON><PERSON>(256)"}, {"n": "FirstName", "t": "<PERSON><PERSON><PERSON><PERSON>(50)"}, {"n": "LastName", "t": "<PERSON><PERSON><PERSON><PERSON>(50)"}], "pk": ["Id"]}, {"table": "BusinessDetails", "desc": "Stores detailed business information for a client, including financials, operations, and licensing.", "col": [{"n": "BusinessDetailsId", "t": "int"}, {"n": "ClientId", "t": "int"}, {"n": "FEIN"}, {"n": "ShortDescription"}, {"n": "LongDescription"}, {"n": "BusinessIndustry", "desc": "Industry sector of the business."}, {"n": "BusinessType", "t": "int", "desc": "Code for type of business (contractor, retailer, etc.)."}, {"n": "DateStarted", "t": "datetime2", "desc": "Business start date."}, {"n": "YearsExperience", "t": "int", "desc": "Years of industry experience."}, {"n": "<PERSON>um<PERSON><PERSON><PERSON>", "t": "int"}, {"n": "LicenseType", "t": "int", "desc": "Type of professional/business license."}, {"n": "LicenseNumber"}, {"n": "EstimatedSubcontractingExpenses", "t": "decimal"}, {"n": "NumPartTimeEmployees", "t": "int"}, {"n": "NumFullTimeEmployees", "t": "int"}, {"n": "EstimatedAnnualPayroll0–4", "t": "decimal", "desc": "Estimated payroll amounts (multiple categories or years)."}, {"n": "AnnualGrossSalesRevenueReceipts", "t": "decimal", "desc": "Total annual revenue from sales/receipts."}, {"n": "AnnualPayrollHazardExposure", "t": "decimal", "desc": "Portion of payroll exposed to hazard."}, {"n": "BusinessPersonalPropertyBPP", "t": "decimal", "desc": "Value of Business Personal Property."}], "pk": ["BusinessDetailsId"], "fk": [{"column": "ClientId", "references": "Clients(ClientId)"}]}, {"tbl": "CallTranscriptions", "desc": "Stores metadata and transcribed text of phone calls.", "pk": ["Id"], "fk": [{"column": "ClientId", "references": {"table": "Clients", "column": "ClientId"}}], "col": [{"n": "Id", "t": "int", "desc": "Unique identifier for the call transcription"}, {"n": "CallId", "desc": "Identifier for the call"}, {"n": "RecordingId", "desc": "Identifier for the call recording"}, {"n": "PhoneNumber"}, {"n": "TranscriptionText"}, {"n": "Notes"}, {"n": "ClientId", "t": "int", "desc": "Associated client ID"}, {"n": "FromPhoneNumber"}, {"n": "ToPhoneNumber"}]}, {"tbl": "Carriers", "desc": "Stores insurance carrier details and metadata.", "pk": ["CarrierId"], "fk": [{"column": "AddressId", "references": {"table": "Address", "column": "AddressId"}}, {"column": "CreatedById", "references": {"table": "AspNetUsers", "column": "Id"}}], "col": [{"n": "CarrierId", "t": "int", "desc": "Unique identifier for the carrier"}, {"n": "LookupCode", "desc": "Code used to look up the carrier"}, {"n": "CarrierName"}, {"n": "City"}, {"n": "State"}, {"n": "Zip"}, {"n": "Phone"}, {"n": "Website"}, {"n": "IssuingCarrier", "t": "bit", "desc": "Indicates if this is an issuing carrier"}, {"n": "Wholesaler", "t": "bit", "desc": "Indicates if the carrier is a wholesaler"}, {"n": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "desc": "JSON structure for risk appetite"}, {"n": "<PERSON>uo<PERSON><PERSON><PERSON><PERSON>", "desc": "JSON data related to quote lines"}, {"n": "Notes", "desc": "Notes about the carrier"}, {"n": "AddressId", "t": "int", "desc": "Reference to the address table"}, {"n": "QuotingWebsite"}, {"n": "StreetAddress"}, {"n": "CarrierNickname"}, {"n": "NewSubmissionEmail"}, {"n": "ServicingEmail"}, {"n": "ServicingWebsite", "desc": "Broker portal or service website URL"}, {"n": "LossRunsEmail", "desc": "Email for requesting loss runs"}, {"n": "LossRunsNote", "desc": "Instructions for special handling of requesting loss runs"}, {"n": "LossRunsURL", "desc": "URL for requesting or downloading loss runs from the carrier or wholesaler"}]}, {"tbl": "Clients", "desc": "Stores primary client records, including contact, address, and metadata.", "pk": ["ClientId"], "fk": [{"column": "AddressId", "references": {"table": "Address", "column": "AddressId"}}, {"column": "PrimaryContactId", "references": {"table": "Contacts", "column": "ContactId"}}, {"column": "ProducerId", "references": {"table": "AspNetUsers", "column": "Id"}}, {"column": "CSRId", "references": {"table": "AspNetUsers", "column": "Id"}}], "col": [{"n": "ClientId", "t": "int", "desc": "Unique identifier for the client"}, {"n": "LookupCode", "desc": "Short code for quick client reference"}, {"n": "Name"}, {"n": "PhoneNumber"}, {"n": "Email"}, {"n": "Website"}, {"n": "AddressId", "t": "int", "desc": "Reference to address details"}, {"n": "PrimaryContactId", "t": "int", "desc": "ID of the client's main point of contact"}, {"n": "ProducerId", "t": "<PERSON><PERSON><PERSON><PERSON>(450)", "desc": "User ID of the producer responsible for this client"}, {"n": "CSRId", "t": "<PERSON><PERSON><PERSON><PERSON>(450)", "desc": "User ID of the CSR handling this client"}, {"n": "DateOpened", "t": "datetime2(7)", "desc": "Date the client account was opened"}]}, {"tbl": "Contacts", "desc": "Stores individual contact records associated with clients or carriers.", "pk": ["ContactId"], "fk": [{"column": "AddressId", "references": {"table": "Address", "column": "AddressId"}}, {"column": "CarrierId", "references": {"table": "Carriers", "column": "CarrierId"}, "onDelete": "SET NULL"}, {"column": "ClientId", "references": {"table": "Clients", "column": "ClientId"}}, {"column": "PrimaryEmailEmailAddressId", "references": {"table": "EmailAddresses", "column": "EmailAddressId"}}, {"column": "PrimaryPhonePhoneNumberId", "references": {"table": "PhoneNumbers", "column": "PhoneNumberId"}}], "col": [{"n": "ContactId", "t": "int", "desc": "Unique identifier for the contact"}, {"n": "FirstName"}, {"n": "MiddleName"}, {"n": "LastName"}, {"n": "Title"}, {"n": "Notes", "desc": "Additional notes about the contact"}, {"n": "AddressId", "t": "int", "desc": "Address reference"}, {"n": "CarrierId", "t": "int", "desc": "Linked carrier, if any"}, {"n": "Billing", "t": "bit", "desc": "Whether the contact is for billing"}, {"n": "IsInactive", "t": "bit", "desc": "Flag indicating inactive status"}, {"n": "Representative", "t": "bit"}, {"n": "Service", "t": "bit"}, {"n": "Underwriter", "t": "bit"}, {"n": "Owner", "t": "bit"}, {"n": "PrimaryEmailEmailAddressId", "t": "int", "desc": "Primary email reference"}, {"n": "PrimaryEmailId", "t": "int", "desc": "Deprecated or unused reference to email ID"}, {"n": "PrimaryPhoneId", "t": "int", "desc": "Deprecated or unused reference to phone ID"}, {"n": "PrimaryPhonePhoneNumberId", "t": "int", "desc": "Primary phone number reference"}]}, {"tbl": "EmailAddresses", "desc": "Stores email addresses related to contacts.", "pk": ["EmailAddressId"], "fk": [{"column": "ContactId", "references": {"table": "Contacts", "column": "ContactId"}}], "col": [{"n": "EmailAddressId", "t": "int", "desc": "Unique ID for the email address"}, {"n": "Email"}, {"n": "Label"}, {"n": "IsPrimary", "t": "bit", "desc": "Indicates if this is the primary email for the contact"}, {"n": "ContactId", "t": "int", "desc": "Reference to the related contact"}]}, {"tbl": "Policies", "desc": "Stores insurance policy information including dates, premium, and associations.", "pk": ["PolicyId"], "fk": [{"column": "ApplicationId", "references": {"table": "Application", "column": "ApplicationId"}}, {"column": "CarrierId", "references": {"table": "Carriers", "column": "CarrierId"}}, {"column": "WholesalerId", "references": {"table": "Carriers", "column": "CarrierId"}}, {"column": "ProductId", "references": {"table": "Products", "column": "ProductId"}}, {"column": "ClientId", "references": {"table": "Clients", "column": "ClientId"}}, {"column": "CSRId", "references": {"table": "AspNetUsers", "column": "Id"}}, {"column": "ProducerId", "references": {"table": "AspNetUsers", "column": "Id"}}], "col": [{"n": "PolicyId", "t": "int", "desc": "Unique policy identifier"}, {"n": "PolicyNumber", "desc": "Policy number"}, {"n": "EffectiveDate", "t": "datetime2(7)", "desc": "Date policy becomes effective"}, {"n": "ExpirationDate", "t": "datetime2(7)", "desc": "Date policy expires"}, {"n": "Premium", "t": "decimal(18, 2)", "desc": "Total premium amount"}, {"n": "ApplicationId", "t": "int", "desc": "Related application ID"}, {"n": "CarrierId", "t": "int", "desc": "Carrier providing the policy"}, {"n": "WholesalerId", "t": "int", "desc": "Wholesaler involved"}, {"n": "ProductId", "t": "int", "desc": "Product line for the policy"}, {"n": "ClientId", "t": "int", "desc": "Client holding the policy"}, {"n": "Notes"}, {"n": "Status"}, {"n": "CSRId", "t": "<PERSON><PERSON><PERSON><PERSON>(450)", "desc": "CSR assigned to the policy"}]}, {"tbl": "Products", "desc": "Defines lines of insurance coverages such as general liability and work comp.", "pk": ["ProductId"], "col": [{"n": "ProductId", "t": "int", "desc": "Unique product ID"}, {"n": "LineName", "desc": "Full name of the line"}, {"n": "desc", "desc": "Description of the product"}, {"n": "LineCode", "desc": "Short nickname used for the line, i.e. WC or CGL"}, {"n": "LineNickname", "desc": "Nickname or short label for the line, i.e. work comp, or gl"}]}, {"tbl": "Renewals", "desc": "Contains records of policy renewals including related entities and premium details.", "pk": ["RenewalId"], "fk": [{"column": "AssignedToId", "references": {"table": "AspNetUsers", "column": "Id"}}, {"column": "CarrierId", "references": {"table": "Carriers", "column": "CarrierId"}}, {"column": "WholesalerId", "references": {"table": "Carriers", "column": "CarrierId"}}, {"column": "PolicyId", "references": {"table": "Policies", "column": "PolicyId"}}, {"column": "ClientId", "references": {"table": "Clients", "column": "ClientId"}}, {"column": "ProductId", "references": {"tbl": "Products", "column": "ProductId"}}], "col": [{"n": "RenewalId", "t": "int", "desc": "Unique renewal ID"}, {"n": "RenewalDate", "t": "datetime2(7)"}, {"n": "CarrierId", "t": "int"}, {"n": "WholesalerId", "t": "int"}, {"n": "PolicyId", "t": "int", "desc": "Related policy ID"}, {"n": "AssignedToId", "t": "<PERSON><PERSON><PERSON><PERSON>(450)", "desc": "User assigned to this renewal"}, {"n": "ClientId", "t": "int"}, {"n": "ProductId", "t": "int", "desc": "Product line associated"}, {"n": "ExpiringPolicyNumber", "desc": "Policy number of the expiring policy"}, {"n": "ExpiringPremium", "t": "decimal(18, 2)"}, {"n": "Notes", "desc": "Additional notes"}, {"n": "BillType", "desc": "Billing type for renewal"}, {"n": "RenewalStatus", "desc": "Status of the renewal"}]}]