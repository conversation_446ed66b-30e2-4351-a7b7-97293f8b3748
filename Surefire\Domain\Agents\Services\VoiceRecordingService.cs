using Microsoft.Extensions.Logging;
using Surefire.Domain.Chat.Services;
using Surefire.Domain.Agents.Interfaces;

namespace Surefire.Domain.Agents.Services
{
    /// <summary>
    /// Service for handling voice recording and transcription in the Enhanced AI Chat
    /// </summary>
    public class VoiceRecordingService : IVoiceRecordingService
    {
        private readonly TranscriptionService _transcriptionService;
        private readonly ILogger<VoiceRecordingService> _logger;

        // Maximum file size for audio uploads (25MB - OpenAI limit)
        private const double MAX_FILE_SIZE_MB = 25.0;
        
        // Supported audio formats by OpenAI Whisper
        private readonly string[] _supportedFormats = { "mp3", "mp4", "mpeg", "mpga", "m4a", "wav", "webm" };

        // Specialized prompt for push-to-talk transcriptions
        private const string PUSH_TO_TALK_PROMPT = @"You are transcribing voice input for a business insurance application. This transcription will be used for AI chat interactions about clients, policies, and business operations.

Pay special attention to:
- Business names (e.g., 'TWS Facility Services', 'RT Specialty', 'Quad-B Systems')
- Contact names and proper nouns
- Insurance carrier names (e.g., 'Travelers', 'Liberty Mutual', 'The Hartford')
- Acronyms and initial-lettered company names are very common

Common misheard business names to watch for:
- 'TWS' in 'TWS Facility Services' (not 'TW' or 'T-W-S')
- 'RT Specialty' (not 'R-T' or 'Art Specialty')
- 'Quad-B Systems' (often misheard as 'Quad Be Systems' or 'Quad Bee')
- 'SB Industrial' (sometimes misinterpreted as 'Let's Be Industrial' or 'S-B Industrial')
- 'JLT' (not 'J-L-T' or 'Jolt')
- 'CRC' (not 'C-R-C' or 'Creek')

Transcribe accurately, preserving all business terminology, proper names, and technical insurance terms. Keep the transcription conversational and natural as it will be used for AI chat interactions.";

        public VoiceRecordingService(
            TranscriptionService transcriptionService,
            ILogger<VoiceRecordingService> logger)
        {
            _transcriptionService = transcriptionService;
            _logger = logger;
        }

        public async Task<string> TranscribeAudioAsync(byte[] audioData, string language = "en")
        {
            try
            {
                _logger.LogInformation("[VoiceChat] Starting audio transcription. Size: {SizeKB} KB", audioData.Length / 1024);

                // Validate audio data first
                var validation = ValidateAudioData(audioData);
                if (!validation.IsValid)
                {
                    _logger.LogWarning("[VoiceChat] Audio validation failed: {Error}", validation.ErrorMessage);
                    return $"Audio validation failed: {validation.ErrorMessage}";
                }

                // Use the transcription service with specialized push-to-talk prompt
                var transcriptionText = await _transcriptionService.TranscribeAudioDataAsync(audioData, language, PUSH_TO_TALK_PROMPT);
                
                _logger.LogInformation("[VoiceChat] Transcription completed successfully. Length: {Length} characters", transcriptionText.Length);
                
                return transcriptionText;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[VoiceChat] Error during audio transcription");
                return "Sorry, I couldn't process your voice message. Please try again or type your message.";
            }
        }

        public VoiceValidationResult ValidateAudioData(byte[] audioData)
        {
            var result = new VoiceValidationResult();

            try
            {
                // Check if audio data exists
                if (audioData == null || audioData.Length == 0)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "No audio data received";
                    return result;
                }

                // Calculate file size
                result.FileSizeMB = audioData.Length / (1024.0 * 1024.0);

                // Check file size limits
                if (result.FileSizeMB > MAX_FILE_SIZE_MB)
                {
                    result.IsValid = false;
                    result.ErrorMessage = $"Audio file too large ({result.FileSizeMB:F1} MB). Maximum size is {MAX_FILE_SIZE_MB} MB.";
                    return result;
                }

                // Check minimum size (at least 1KB)
                if (audioData.Length < 1024)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "Audio file too small. Please record a longer message.";
                    return result;
                }

                // Basic format validation by checking file headers
                var isValidFormat = IsValidAudioFormat(audioData);
                if (!isValidFormat)
                {
                    result.IsValid = false;
                    result.ErrorMessage = "Unsupported audio format. Please use MP3, WAV, or WebM format.";
                    return result;
                }

                result.IsValid = true;
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "[VoiceChat] Error validating audio data");
                result.IsValid = false;
                result.ErrorMessage = "Error validating audio data";
                return result;
            }
        }

        private bool IsValidAudioFormat(byte[] audioData)
        {
            if (audioData.Length < 12)
                return false;

            try
            {
                // Check for common audio file signatures
                var header = audioData.Take(12).ToArray();
                
                // MP3 - Check for ID3 tag or MPEG frame sync
                if (header[0] == 0xFF && (header[1] & 0xE0) == 0xE0) // MPEG frame sync
                    return true;
                if (header[0] == 0x49 && header[1] == 0x44 && header[2] == 0x33) // ID3v2 tag
                    return true;

                // WAV - Check for RIFF header
                if (header[0] == 0x52 && header[1] == 0x49 && header[2] == 0x46 && header[3] == 0x46 && // "RIFF"
                    header[8] == 0x57 && header[9] == 0x41 && header[10] == 0x56 && header[11] == 0x45) // "WAVE"
                    return true;

                // WebM - Check for EBML header
                if (header[0] == 0x1A && header[1] == 0x45 && header[2] == 0xDF && header[3] == 0xA3)
                    return true;

                // M4A/MP4 - Check for ftyp box
                if (header[4] == 0x66 && header[5] == 0x74 && header[6] == 0x79 && header[7] == 0x70) // "ftyp"
                    return true;

                // For other formats, assume valid (OpenAI will validate on their end)
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
} 