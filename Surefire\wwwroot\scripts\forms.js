﻿function openPdfInNewWindow(base64Pdf) {
    const binaryString = window.atob(base64Pdf);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }

    const blob = new Blob([bytes.buffer], { type: 'application/pdf' });
    const url = URL.createObjectURL(blob);
    const printWindow = window.open(url, '_blank');
    printWindow.onload = function () {
        printWindow.focus();
        printWindow.print();
    };
}
function downloadPdf(base64String, fileName) {
    const linkSource = `data:application/pdf;base64,${base64String}`;
    const downloadLink = document.createElement("a");
    downloadLink.href = linkSource;
    downloadLink.download = fileName;
    downloadLink.click();
}

function openPdfInNewWindow(base64Pdf) {
    const binaryString = window.atob(base64Pdf);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }

    const blob = new Blob([bytes.buffer], { type: 'application/pdf' });
    const url = URL.createObjectURL(blob);
    window.open(url, '_blank');
}
function downloadFileFromBytes(base64Data, contentType, fileName) {
    // Create a blob from the base64 data
    const byteCharacters = atob(base64Data);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += 512) {
        const slice = byteCharacters.slice(offset, offset + 512);

        const byteNumbers = new Array(slice.length);
        for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
    }

    const blob = new Blob(byteArrays, { type: contentType });

    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;

    // Append to body, click to download, then clean up
    document.body.appendChild(link);
    link.click();

    // Cleanup
    setTimeout(() => {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    }, 100);
}
function updateUrl(newPathAndQuery) {
    const newUrl = `${window.location.origin}${newPathAndQuery}`;
    window.history.pushState({}, '', newUrl);
}
function blurField(elementId) {
    document.getElementById(elementId).blur();
}
function topbarStartDrag() {
    isDragging = true;
    window.chrome.webview.postMessage({ action: "drag_start" });
}
function topbarStopDrag() {
    isDragging = false;
    window.chrome.webview.postMessage({ action: "drag_stop" });
}
function topbarDrag() {
    window.chrome.webview.postMessage({ action: "drag_move" });
}

/**
 * Listeners
 */

window.addEventListener('visibilitychange', function () {
    // Focuses on the FireSearch field when Surefire is brought back into focus
    var fluentTextField = document.getElementById('fSearcher');
    document.getElementById('fSearcher').focus();

    if (fluentTextField && fluentTextField.shadowRoot) {
        var input = fluentTextField.shadowRoot.querySelector('input');

        if (input) {
            input.focus();
        }

    } else {
        fluentTextField.focus();
    }
});

window.scrollMessagesToBottom = function () {
    // Find the messages list container
    Console.Log("Scrolling messages to bottom");
    const messagesContainer = document.querySelector('.messages-list');
    if (messagesContainer) {
        // Scroll to the bottom
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
};

/**
 * Logo Animation System
 */
let logoImagesPreloaded = false;
let logoImages = [];
let logoAnimationInterval = null;
let logoLoadingInterval = null;
let isLogoLoading = false;

function preloadLogoImages() {
    if (logoImagesPreloaded) return;

    logoImages = [];
    let loadedCount = 0;
    const totalImages = 51; // 0-50

    for (let i = 0; i <= 50; i++) {
        const img = new Image();
        const frameNumber = i.toString().padStart(5, '0');
        img.src = `/img/home/<USER>/sflogo_${frameNumber}.png`;

        img.onload = () => {
            loadedCount++;
            if (loadedCount === totalImages) {
                logoImagesPreloaded = true;
                console.log('Logo animation images preloaded successfully');
            }
        };

        logoImages.push(img);
    }
}

function playLogoSequenceWithTopBar() {
    const logoImg = document.querySelector('.app-logo');
    if (!logoImg) return;

    // Stop any existing logo animations
    stopLogoAnimation();
    stopLogoLoading();

    let currentFrame = 0;
    const totalFrames = 51; // 0-50
    const frameDuration = 33; // 30 FPS to match fire animation

    logoAnimationInterval = setInterval(() => {
        const frameNumber = currentFrame.toString().padStart(5, '0');
        const imageUrl = `/img/home/<USER>/sflogo_${frameNumber}.png`;
        logoImg.src = imageUrl;

        currentFrame++;

        if (currentFrame >= totalFrames) {
            clearInterval(logoAnimationInterval);
            logoAnimationInterval = null;
            // Return to first frame
            logoImg.src = '/img/home/<USER>/sflogo_00000.png';
        }
    }, frameDuration);
}

function startLogoLoading() {
    if (isLogoLoading) return;

    const logoImg = document.querySelector('.app-logo');
    if (!logoImg) return;

    // Stop any existing animations
    stopLogoAnimation();
    
    isLogoLoading = true;
    let currentFrame = 0;
    const totalFrames = 51; // 0-50
    const frameDuration = 50; // Slightly slower for loading state

    logoLoadingInterval = setInterval(() => {
        const frameNumber = currentFrame.toString().padStart(5, '0');
        const imageUrl = `/img/home/<USER>/sflogo_${frameNumber}.png`;
        logoImg.src = imageUrl;

        currentFrame++;

        // Loop continuously
        if (currentFrame >= totalFrames) {
            currentFrame = 0;
        }
    }, frameDuration);
}

function stopLogoLoading() {
    if (!isLogoLoading) return;

    isLogoLoading = false;
    
    if (logoLoadingInterval) {
        clearInterval(logoLoadingInterval);
        logoLoadingInterval = null;
    }

    // Always end at first frame
    const logoImg = document.querySelector('.app-logo');
    if (logoImg) {
        logoImg.src = '/img/home/<USER>/sflogo_00000.png';
    }
}

function stopLogoAnimation() {
    if (logoAnimationInterval) {
        clearInterval(logoAnimationInterval);
        logoAnimationInterval = null;
    }
}

/**
 * Top Bar Fire Animation System
 */
let isLoadingMode = false;
let currentGradientPosition = 0; // 0=Home, 1=Clients, 2=Renewals
let fireImagesPreloaded = false;
let fireImages = [];

function preloadFireImages() {
    if (fireImagesPreloaded) return;

    fireImages = [];
    let loadedCount = 0;
    const totalImages = 50;

    for (let i = 1; i <= totalImages; i++) {
        const img = new Image();
        const frameNumber = i.toString().padStart(5, '0');
        img.src = `/img/home/<USER>/fire_${frameNumber}.png`;

        img.onload = () => {
            loadedCount++;
            if (loadedCount === totalImages) {
                fireImagesPreloaded = true;
                console.log('Fire animation images preloaded successfully');
            }
        };

        fireImages.push(img);
    }
}

function PlayTopBarVideoAsync(section) {
    // Map section names to positions
    const positions = {
        'Home': 0,
        'Clients': 1,
        'Renewals': 2,
        'Other': 3  // Fourth position for all other nav items
    };

    const position = positions[section];
    if (position === undefined) return;

    // Stop loading mode if active
    if (isLoadingMode) {
        stopLoadingMode();
    }

    // Stop logo loading if active
    if (isLogoLoading) {
        stopLogoLoading();
    }

    // Animate gradient to target position
    animateGradientToPosition(position);

    // Play fire animation sequence immediately
    playFireSequence();

    // Play logo animation sequence in sync
    playLogoSequenceWithTopBar();

    currentGradientPosition = position;
}

// New function specifically for other nav items that go to the fourth position
function PlayTopBarVideoAsyncOther() {
    PlayTopBarVideoAsync('Other');
}

function animateGradientToPosition(targetPosition) {
    const gradientLayer = document.getElementById('topbar-gradient-layer');
    if (!gradientLayer) return;

    const targetLeft = -(targetPosition * 1920); // Each position is 1920px wide

    gradientLayer.style.transition = 'left 0.8s cubic-bezier(0.7, 0, 0.2, 1)';
    gradientLayer.style.left = `${targetLeft}px`;
}

function playFireSequence() {
    const fireLayer = document.getElementById('topbar-fire-layer');
    if (!fireLayer) return;

    let currentFrame = 1; // Start from frame 1
    const totalFrames = 50; // Play 50 frames (1-50)

    // No fade in - PNG sequence handles transparency

    const frameInterval = setInterval(() => {
        const frameNumber = currentFrame.toString().padStart(5, '0');
        const imageUrl = `/img/home/<USER>/fire_${frameNumber}.png`;
        fireLayer.style.backgroundImage = `url('${imageUrl}')`;

        currentFrame++;

        if (currentFrame > totalFrames) {
            clearInterval(frameInterval);
            // No fade out - PNG sequence ends as transparent
            // Clear the background image to ensure clean end state
            fireLayer.style.backgroundImage = '';
        }
    }, 33); // 30 FPS (33ms per frame)
}

function startLoadingMode() {
    if (isLoadingMode) return;

    isLoadingMode = true;
    const gradientLayer = document.getElementById('topbar-gradient-layer');
    if (!gradientLayer) return;

    // Create seamless infinite scroll effect
    gradientLayer.style.transition = 'none';
    gradientLayer.style.backgroundImage = `url('/img/home/<USER>'), url('/img/home/<USER>')`;
    gradientLayer.style.backgroundPosition = '0 0, 5760px 0';
    gradientLayer.style.backgroundSize = '5760px 50px, 5760px 50px';
    gradientLayer.style.backgroundRepeat = 'no-repeat, no-repeat';

    // Start infinite animation
    gradientLayer.style.animation = 'topbar-infinite-scroll 10s linear infinite';
}

function stopLoadingMode() {
    if (!isLoadingMode) return;

    isLoadingMode = false;
    const gradientLayer = document.getElementById('topbar-gradient-layer');
    if (!gradientLayer) return;

    // Stop animation and reset to single background
    gradientLayer.style.animation = 'none';
    gradientLayer.style.backgroundImage = `url('/img/home/<USER>')`;
    gradientLayer.style.backgroundPosition = '0 0';
    gradientLayer.style.backgroundSize = '5760px 50px';
    gradientLayer.style.backgroundRepeat = 'no-repeat';

    // Return to current position
    animateGradientToPosition(currentGradientPosition);
}

// Initialize the animation layers on page load
window.addEventListener('DOMContentLoaded', function () {
    initializeTopBarAnimations();
    // Start preloading fire images
    preloadFireImages();
    // Start preloading logo images
    preloadLogoImages();
    // Start preloading mic spinner images
    preloadMicSpinnerImages();
});

function initializeTopBarAnimations() {
    const topBar = document.getElementById('sf-top-bar');
    if (!topBar) return;

    // Create gradient layer (bottom layer)
    const gradientLayer = document.createElement('div');
    gradientLayer.id = 'topbar-gradient-layer';
    gradientLayer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 5760px;
        height: 50px;
        background-image: url('/img/home/<USER>');
        background-repeat: no-repeat;
        z-index: -2;
        pointer-events: none;
    `;

    // Create fire layer (top layer)
    const fireLayer = document.createElement('div');
    fireLayer.id = 'topbar-fire-layer';
    fireLayer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 50px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        z-index: -1;
        pointer-events: none;
    `;

    // Make top bar relative positioned to contain the absolute layers
    //topBar.style.position = 'relative';
    //topBar.style.overflow = 'hidden';

    // Insert layers at the beginning
    topBar.insertBefore(gradientLayer, topBar.firstChild);
    topBar.insertBefore(fireLayer, topBar.firstChild);
}

// Clipboard: Copy HTML as rich text for Outlook/Email
window.copyHtmlToClipboard = function(html) {
    if (navigator.clipboard && window.ClipboardItem) {
        const type = "text/html";
        const blob = new Blob([html], { type });
        const data = [new window.ClipboardItem({ [type]: blob })];
        navigator.clipboard.write(data);
    } else {
        // fallback: copy as plain text
        navigator.clipboard.writeText(html);
    }
}

// Set checkbox indeterminate state
window.setCheckboxIndeterminate = function(element, isIndeterminate) {
    if (element) {
        element.indeterminate = isIndeterminate;
    }
}

// Push-to-talk functionality for main logo
let pushToTalkMediaRecorder;
let pushToTalkAudioChunks = [];
let pushToTalkAudioStream;
let micSpinnerImages = [];
let micSpinnerPreloaded = false;
let micSpinnerInterval = null;
let micSpinnerCurrentFrame = 0;

// Preload mic spinner images
function preloadMicSpinnerImages() {
    if (micSpinnerPreloaded) return;

    micSpinnerImages = [];
    let loadedCount = 0;
    const totalImages = 75; // 0-74

    for (let i = 0; i <= 74; i++) {
        const img = new Image();
        const frameNumber = i.toString().padStart(5, '0');
        img.src = `/img/home/<USER>/micspinner_${frameNumber}.png`;

        img.onload = () => {
            loadedCount++;
            if (loadedCount === totalImages) {
                micSpinnerPreloaded = true;
                console.log('Mic spinner images preloaded successfully');
            }
        };

        img.onerror = () => {
            console.warn(`Failed to load mic spinner image: micspinner_${frameNumber}.png`);
            loadedCount++;
            if (loadedCount === totalImages) {
                micSpinnerPreloaded = true;
                console.log('Mic spinner images preloaded (some failed)');
            }
        };

        micSpinnerImages[i] = img;
    }
}

// Start mic spinner animation
function startMicSpinnerAnimation() {
    if (micSpinnerInterval) {
        clearInterval(micSpinnerInterval);
    }

    const logoImg = document.querySelector('.app-logo');
    if (!logoImg) return;

    // Start with frame 0
    micSpinnerCurrentFrame = 0;
    const frameNumber = micSpinnerCurrentFrame.toString().padStart(5, '0');
    logoImg.src = `/img/home/<USER>/micspinner_${frameNumber}.png`;

    micSpinnerInterval = setInterval(() => {
        micSpinnerCurrentFrame++;
        
        // Play frames 0-39, then loop 29-39
        if (micSpinnerCurrentFrame > 39) {
            micSpinnerCurrentFrame = 29; // Loop back to frame 29
        }

        const frameNumber = micSpinnerCurrentFrame.toString().padStart(5, '0');
        logoImg.src = `/img/home/<USER>/micspinner_${frameNumber}.png`;
    }, 33); // ~30 FPS
}

// Stop mic spinner animation and play ending sequence
function stopMicSpinnerAnimation() {
    return new Promise((resolve) => {
        if (micSpinnerInterval) {
            clearInterval(micSpinnerInterval);
            micSpinnerInterval = null;
        }

        const logoImg = document.querySelector('.app-logo');
        if (!logoImg) {
            resolve();
            return;
        }

        // Continue from current frame to 74
        let endingFrame = Math.max(micSpinnerCurrentFrame, 40); // Start from at least frame 40
        
        const endingInterval = setInterval(() => {
            const frameNumber = endingFrame.toString().padStart(5, '0');
            logoImg.src = `/img/home/<USER>/micspinner_${frameNumber}.png`;
            
            endingFrame++;
            
            if (endingFrame > 74) {
                clearInterval(endingInterval);
                // Reset to original logo
                logoImg.src = '/img/home/<USER>/sflogo_00000.png';
                resolve();
            }
        }, 33); // ~30 FPS
    });
}

window.startPushToTalkRecording = async function() {
    try {
        // Start the mic spinner animation
        startMicSpinnerAnimation();
        
        // Request microphone permission
        pushToTalkAudioStream = await navigator.mediaDevices.getUserMedia({ 
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
            } 
        });
        
        // Reset audio chunks for new recording
        pushToTalkAudioChunks = [];
        
        // Create MediaRecorder with WebM format (widely supported)
        const options = { mimeType: 'audio/webm' };
        if (!MediaRecorder.isTypeSupported(options.mimeType)) {
            // Fallback to default format
            pushToTalkMediaRecorder = new MediaRecorder(pushToTalkAudioStream);
        } else {
            pushToTalkMediaRecorder = new MediaRecorder(pushToTalkAudioStream, options);
        }
        
        pushToTalkMediaRecorder.ondataavailable = function(event) {
            if (event.data.size > 0) {
                pushToTalkAudioChunks.push(event.data);
            }
        };
        
        pushToTalkMediaRecorder.start(1000); // Collect data every second
        return true;
    } catch (error) {
        console.error('Error starting push-to-talk recording:', error);
        // Stop animation on error
        await stopMicSpinnerAnimation();
        return false;
    }
};

window.stopPushToTalkRecording = function() {
    return new Promise(async (resolve, reject) => {
        // Stop the mic spinner animation and play ending sequence
        await stopMicSpinnerAnimation();
        
        if (pushToTalkMediaRecorder && pushToTalkMediaRecorder.state !== 'inactive') {
            pushToTalkMediaRecorder.onstop = async function() {
                try {
                    // Stop all audio tracks
                    if (pushToTalkAudioStream) {
                        pushToTalkAudioStream.getTracks().forEach(track => track.stop());
                    }
                    
                    // Create blob from recorded chunks
                    const audioBlob = new Blob(pushToTalkAudioChunks, { type: 'audio/webm' });
                    
                    // Convert blob to base64 string for safer transfer
                    const reader = new FileReader();
                    reader.onloadend = function() {
                        const base64String = reader.result.split(',')[1]; // Remove data:audio/webm;base64,
                        resolve(base64String);
                    };
                    reader.onerror = function() {
                        reject(new Error('Failed to convert audio to base64'));
                    };
                    reader.readAsDataURL(audioBlob);
                } catch (error) {
                    console.error('Error processing push-to-talk audio:', error);
                    reject(error);
                }
            };
            
            pushToTalkMediaRecorder.stop();
        } else {
            resolve('');
        }
    });
};

// Function to send transcription to Enhanced AI Chat
window.sendTranscriptionToChat = function(transcription) {
    // This will be called after navigation to /agents
    // We'll store the transcription and let the Enhanced AI Chat page pick it up
    sessionStorage.setItem('pendingVoiceTranscription', transcription);
    
    // Trigger a custom event that the Enhanced AI Chat page can listen for
    window.dispatchEvent(new CustomEvent('voiceTranscriptionReady', { 
        detail: { transcription: transcription } 
    }));
};

// Function to show transcription errors
window.showTranscriptionError = function(errorMessage) {
    // Store error message for Enhanced AI Chat page to display
    sessionStorage.setItem('voiceTranscriptionError', errorMessage);
    
    // Trigger error event
    window.dispatchEvent(new CustomEvent('voiceTranscriptionError', { 
        detail: { error: errorMessage } 
    }));
};

// Function to clear pending voice transcriptions
window.clearPendingVoiceTranscriptions = function() {
    // Clear any pending transcriptions and errors
    sessionStorage.removeItem('pendingVoiceTranscription');
    sessionStorage.removeItem('voiceTranscriptionError');
    
    console.log('[PushToTalk] Cleared pending voice transcriptions');
};

// Global audio instance for staff notifications
let staffNotificationAudio = null;
let audioContextUnlocked = false;

// Initialize audio context after user interaction
window.initializeAudioContext = function() {
    if (audioContextUnlocked) return;
    
    try {
        // Create and preload the audio
        staffNotificationAudio = new Audio('/img/StaffAlert.mp3');
        staffNotificationAudio.volume = 0.5;
        staffNotificationAudio.preload = 'auto';
        
        // Play silently to unlock audio context
        staffNotificationAudio.play().then(() => {
            staffNotificationAudio.pause();
            staffNotificationAudio.currentTime = 0;
            audioContextUnlocked = true;
            console.log('Audio context unlocked for notifications');
        }).catch(error => {
            console.warn('Failed to unlock audio context:', error);
        });
    } catch (error) {
        console.warn('Error initializing audio context:', error);
    }
};

// Add event listeners to unlock audio on first user interaction
document.addEventListener('click', window.initializeAudioContext, { once: true });
document.addEventListener('keydown', window.initializeAudioContext, { once: true });
document.addEventListener('touchstart', window.initializeAudioContext, { once: true });

// Function to play staff message notification sound
window.playStaffMessageSound = function() {
    try {
        // Use pre-initialized audio if available, otherwise create new
        const audio = staffNotificationAudio || new Audio('/img/StaffAlert.mp3');
        
        if (!staffNotificationAudio) {
            audio.volume = 0.5;
        } else {
            // Reset audio to beginning
            audio.currentTime = 0;
        }
        
        const playPromise = audio.play();
        
        if (playPromise !== undefined) {
            playPromise.then(() => {
                console.log('Staff message sound played successfully (audio context unlocked: ' + audioContextUnlocked + ')');
            }).catch(error => {
                if (error.name === 'NotAllowedError') {
                    console.warn('Staff message sound blocked by browser - user interaction required first');
                } else {
                    console.warn('Failed to play staff message sound:', error);
                }
            });
        }
    } catch (error) {
        console.warn('Error playing staff message sound:', error);
    }
};
// Function to play staff message reminder sound (every 5 minutes for unread messages)
window.playStaffMessageReminder = function () {
    try {
        const audio = document.getElementById('staffMessageReminderAudio');
        if (audio) {
            // Reset audio to beginning
            audio.currentTime = 0;

            // Play the audio
            const playPromise = audio.play();

            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('Staff message reminder played successfully');
                }).catch(error => {
                    if (error.name === 'NotAllowedError') {
                        console.warn('Staff message reminder blocked by browser - user interaction required first');
                    } else {
                        console.warn('Failed to play staff message reminder:', error);
                    }
                });
            }
        } else {
            console.warn('Staff message reminder audio element not found');
        }
    } catch (error) {
        console.error('Error playing staff message reminder:', error);
    }
};