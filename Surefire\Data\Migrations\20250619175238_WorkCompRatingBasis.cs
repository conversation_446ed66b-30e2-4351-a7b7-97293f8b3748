﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Surefire.Migrations
{
    /// <inheritdoc />
    public partial class WorkCompRatingBasis : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "WorkCompRatingBases",
                columns: table => new
                {
                    WorkCompRatingBasisId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ClassCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ClassDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BaseRate = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    NetRate = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    Premium = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Payroll = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    FullTimeEmployees = table.Column<int>(type: "int", nullable: true),
                    PartTimeEmployees = table.Column<int>(type: "int", nullable: true),
                    LocationNumberNote = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DateCreated = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateModified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UserModifiedId = table.Column<string>(type: "nvarchar(450)", nullable: true),
                    PolicyId = table.Column<int>(type: "int", nullable: false),
                    LocationId = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkCompRatingBases", x => x.WorkCompRatingBasisId);
                    table.ForeignKey(
                        name: "FK_WorkCompRatingBases_AspNetUsers_UserModifiedId",
                        column: x => x.UserModifiedId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_WorkCompRatingBases_Locations_LocationId",
                        column: x => x.LocationId,
                        principalTable: "Locations",
                        principalColumn: "LocationId");
                    table.ForeignKey(
                        name: "FK_WorkCompRatingBases_Policies_PolicyId",
                        column: x => x.PolicyId,
                        principalTable: "Policies",
                        principalColumn: "PolicyId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_WorkCompRatingBases_LocationId",
                table: "WorkCompRatingBases",
                column: "LocationId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkCompRatingBases_PolicyId",
                table: "WorkCompRatingBases",
                column: "PolicyId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkCompRatingBases_UserModifiedId",
                table: "WorkCompRatingBases",
                column: "UserModifiedId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "WorkCompRatingBases");
        }
    }
}
