# Dialog Implementation Guide for Surefire

This guide outlines the recommended approach for implementing modal dialogs in Surefire using a pattern based on `BaseDialog`, parent component state management, and `@bind-Hidden`.

## Core Philosophy

*   **Parent Owns Visibility:** The parent component that invokes the dialog is the single source of truth for whether the dialog is visible or hidden.
*   **Child Reacts to State:** The dialog component reacts to state passed down as parameters (e.g., data to edit) and notifies the parent of events (save, cancel, close).
*   **`@bind-Hidden` is Key:** This Blazor directive is the primary mechanism for controlling visibility between parent and child.
*   **Service Isolation:** Services (like `SurefireDialogService`) should NOT interfere with the `@bind-Hidden` visibility mechanism. Use services only for registration if needed.
*   **Data Layer Isolation:** UI components interact with services, which handle data access using `IDbContextFactory` to prevent stale data and concurrency issues.
*   **Lazy Initialization:** Dialog state should only be initialized when the dialog becomes visible, preventing blank dialogs from flashing.

## Common Issues Fixed

### 1. Blank Dialog Flashing
**Problem:** Dialogs flash blank content before the page fully loads.
**Root Cause:** Dialog state was being initialized in `OnInitialized` regardless of visibility.
**Solution:** Initialize dialog state only in `OnParametersSet` when `!Hidden`.

### 2. Dialogs Opening Unexpectedly
**Problem:** Dialogs sometimes start as opened but blank.
**Root Cause:** Conflicting state management between `SurefireDialogService` and `@bind-Hidden`.
**Solution:** Remove all service-based state management from dialog components using `@bind-Hidden`.

### 3. Mixed Dialog Patterns
**Problem:** Some dialogs use `@ref` + `ShowDialog()` while others use `@bind-Hidden`.
**Solution:** Standardize on `@bind-Hidden` pattern for all dialogs.

## Core Components

1.  **`BaseDialog.razor`**: A wrapper around `FluentDialog` that provides structure but **does not** manage visibility state via services.
2.  **`SurefireDialogService`**: Used only for dialog registration (`RegisterDialog`) if needed. **Never** use it to manage `Hidden` state.
3.  **Specific Dialog Components**: Inherit structure from `BaseDialog` and contain specific UI and logic.
4.  **Parent Component**: Controls dialog visibility and data context.

## Implementation Steps

### 1. Create the Dialog Component

```csharp
@* Domain/YourFeature/Components/Dialogs/YourDialog.razor *@
@namespace Surefire.Domain.YourFeature.Components.Dialogs
@using Surefire.Domain.Shared.Components
@using Microsoft.FluentUI.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Forms

<BaseDialog DialogId="@DialogId"
            Title="@GetTitle()"
            @bind-Hidden="Hidden">
    <ChildContent>
        @if (EditContext != null)
        {
            <EditForm EditContext="@EditContext">
                <DataAnnotationsValidator />
                <ValidationSummary />
                <FluentTextField @bind-Value="CurrentItem.Name" Label="Name" />
                @* Other fields... *@
            </EditForm>
        }
        else
        {
            <p>Loading...</p>
        }
    </ChildContent>
    <FooterContent>
        <FluentButton Appearance="Appearance.Accent" OnClick="SaveChanges">Save</FluentButton>
        <FluentButton Appearance="Appearance.Outline" OnClick="Cancel">Cancel</FluentButton>
    </FooterContent>
</BaseDialog>

@code {
    [Parameter] public string DialogId { get; set; } = "your-dialog-id";
    [Parameter] public YourModel? ItemToEdit { get; set; } // Data passed from parent

    // --- Visibility Binding ---
    [Parameter] public bool Hidden { get; set; } = true;
    [Parameter] public EventCallback<bool> HiddenChanged { get; set; }

    // --- Action Callbacks ---
    [Parameter] public EventCallback<YourModel> OnSave { get; set; }

    // --- Internal State ---
    private YourModel CurrentItem { get; set; } = new();
    private EditContext? EditContext { get; set; }

    // *** CRITICAL: Only initialize when dialog becomes visible ***
    protected override void OnParametersSet()
    {
        if (!Hidden && (ItemToEdit != CurrentItem || EditContext == null))
        {
            InitializeDialogState();
        }
    }

    private void InitializeDialogState()
    {
        if (ItemToEdit != null)
        {
            CurrentItem = CreateModelCopy(ItemToEdit);
        }
        else
        {
            CurrentItem = new YourModel();
        }
        EditContext = new EditContext(CurrentItem);
        StateHasChanged();
    }

    private string GetTitle() => ItemToEdit?.Id > 0 ? "Edit Item" : "Add Item";

    private async Task SaveChanges()
    {
        if (EditContext?.Validate() ?? false)
        {
            try
            {
                await OnSave.InvokeAsync(CurrentItem);
                await CloseDialogAsync();
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Error saving: {ex.Message}");
            }
        }
    }

    private async Task Cancel()
    {
        await CloseDialogAsync();
    }

    private async Task CloseDialogAsync()
    {
        Hidden = true;
        await HiddenChanged.InvokeAsync(Hidden);
    }

    private YourModel CreateModelCopy(YourModel source) => new() { /* copy properties */ };
}
```

### 2. Parent Component Implementation

```csharp
@* YourParentComponent.razor *@
@using Surefire.Domain.YourFeature.Components.Dialogs

<!-- Dialog Component -->
<YourDialog DialogId="your-feature-dialog"
            @bind-Hidden="isDialogHidden"
            ItemToEdit="itemToEdit"
            OnSave="HandleItemSaved" />

@code {
    // Parent state variables
    private bool isDialogHidden = true;
    private YourModel? itemToEdit = null;

    private void ShowAddDialog()
    {
        itemToEdit = null; // Signal Add mode
        isDialogHidden = false; // Show dialog
    }

    private void ShowEditDialog(YourModel item)
    {
        itemToEdit = item; // Set data context
        isDialogHidden = false; // Show dialog
    }

    private async Task HandleItemSaved(YourModel savedItem)
    {
        try
        {
            if (savedItem.Id > 0)
            {
                await YourService.UpdateItemAsync(savedItem);
            }
            else
            {
                await YourService.AddItemAsync(savedItem);
            }

            // Hide dialog first
            isDialogHidden = true;
            itemToEdit = null;

            // Then reload data
            await LoadDataAsync();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Error saving: {ex.Message}");
        }
    }
}
```

## Best Practices

1. **Never use `ShowDialog()` methods** - Control visibility via `@bind-Hidden` only
2. **Initialize state lazily** - Only in `OnParametersSet` when `!Hidden`
3. **Parent handles all data operations** - Dialog just passes data back via callbacks
4. **Use `IDbContextFactory`** - Every service method gets a fresh context
5. **Hide dialog before reload** - Better visual feedback
6. **Copy data objects** - Prevent direct modification of passed parameters

## Anti-Patterns to Avoid

❌ **DON'T:** Initialize state in `OnInitialized`
```csharp
protected override void OnInitialized()
{
    EditContext = new EditContext(CurrentItem); // WRONG - causes flashing
}
```

✅ **DO:** Initialize state conditionally in `OnParametersSet`
```csharp
protected override void OnParametersSet()
{
    if (!Hidden && (ItemToEdit != CurrentItem || EditContext == null))
    {
        InitializeDialogState();
    }
}
```

❌ **DON'T:** Mix service state with `@bind-Hidden`
```csharp
// WRONG - creates conflicting state
DialogService.SetDialogState(DialogId, Hidden);
Hidden = false;
HiddenChanged.InvokeAsync(Hidden);
```

✅ **DO:** Use only `@bind-Hidden`
```csharp
// CORRECT - single source of truth
Hidden = true;
await HiddenChanged.InvokeAsync(Hidden);
```

❌ **DON'T:** Use `@ref` for dialog control
```csharp
<YourDialog @ref="dialogRef" />
// Then: dialogRef.ShowDialog()
```

✅ **DO:** Use `@bind-Hidden` state variables
```csharp
<YourDialog @bind-Hidden="isDialogHidden" />
// Then: isDialogHidden = false;
```

## Troubleshooting

### Dialog Flashes Blank Content
- Check if dialog state is initialized in `OnInitialized` instead of `OnParametersSet`
- Ensure initialization only happens when `!Hidden`

### Dialog Opens Unexpectedly
- Look for conflicting service calls (`DialogService.ShowDialog`, etc.)
- Remove all `SurefireDialogService` state management from dialog components
- Ensure `Hidden` parameter defaults to `true`

### Dialog Doesn't Close
- Verify `CloseDialogAsync` calls `HiddenChanged.InvokeAsync(true)`
- Check parent component receives the binding change
- Ensure no service is overriding the Hidden state

### Data Doesn't Update After Save
- Check if parent's save handler calls data reload after hiding dialog
- Verify service methods use `IDbContextFactory` for fresh data
- Ensure dialog is hidden before data reload for better UX 