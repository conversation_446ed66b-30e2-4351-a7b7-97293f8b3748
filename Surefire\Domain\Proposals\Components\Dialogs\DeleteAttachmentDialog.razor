@using Microsoft.FluentUI.AspNetCore.Components

<BaseDialog DialogId="@DialogId" Title="Delete Attachment" @bind-Hidden="Hidden">
    <ChildContent>
        <div>
            Are you sure you want to remove <b>@FileName</b>?
            <br />
            <span style="color: #b71c1c;" hidden="@(!ShowFileWarning)">
                Deleting the file will remove it from the filesystem and cannot be undone.
            </span>
        </div>
    </ChildContent>
    <FooterContent>
        <FluentButton Appearance="Appearance.Outline" OnClick="Cancel">Cancel</FluentButton>
        <FluentButton Appearance="Appearance.Accent" Style="background-color: #43a047; color: white;" OnClick="Remove">Remove</FluentButton>
        <FluentButton Appearance="Appearance.Accent" Style="background-color: #b71c1c; color: white;" OnClick="DeleteFile">Delete File</FluentButton>
    </FooterContent>
</BaseDialog>

@code {
    [Parameter] public string DialogId { get; set; } = "delete-attachment-dialog";
    [Parameter] public bool Hidden { get; set; }
    [Parameter] public EventCallback<bool> HiddenChanged { get; set; }
    [Parameter] public string FileName { get; set; }
    [Parameter] public bool ShowFileWarning { get; set; } = true;
    [Parameter] public EventCallback<string> OnAction { get; set; } // "remove", "deletefile", "cancel"

    private async Task Cancel() => await OnAction.InvokeAsync("cancel");
    private async Task Remove() => await OnAction.InvokeAsync("remove");
    private async Task DeleteFile() => await OnAction.InvokeAsync("deletefile");
}
