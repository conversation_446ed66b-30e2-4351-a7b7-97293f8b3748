using Surefire.Domain.Agents.Interfaces;
using Surefire.Domain.Agents.Models;
using Surefire.Domain.Clients.Services;
using Surefire.Domain.Policies.Services;
using Surefire.Domain.Policies.Models;
using Surefire.Domain.Shared.Models;
using Surefire.Domain.Ember;
using Surefire.Data;
using Microsoft.EntityFrameworkCore;
using Surefire.Domain.Carriers.Models;
using Surefire.Domain.Agents.Utilities;

namespace Surefire.Domain.Agents.TaskAgents
{
    /// <summary>
    /// Agent for handling loss run requests
    /// Example: "Send loss run requests for Acme Corp work comp for last 5 years"
    /// </summary>
    public class LossRunRequestAgent : ITaskAgent
    {
        private readonly ILogger<LossRunRequestAgent> _logger;
        private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
        private readonly EmberService _emberService;
        private readonly ClientService _clientService;
        private readonly PolicyService _policyService;
        private readonly IEmbeddingService _embeddingService;
        private readonly IVectorStore _vectorStore;

        public LossRunRequestAgent(
            ILogger<LossRunRequestAgent> logger,
            IDbContextFactory<ApplicationDbContext> dbContextFactory,
            EmberService emberService,
            ClientService clientService,
            PolicyService policyService,
            IEmbeddingService embeddingService,
            IVectorStore vectorStore)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dbContextFactory = dbContextFactory ?? throw new ArgumentNullException(nameof(dbContextFactory));
            _emberService = emberService ?? throw new ArgumentNullException(nameof(emberService));
            _clientService = clientService ?? throw new ArgumentNullException(nameof(clientService));
            _policyService = policyService ?? throw new ArgumentNullException(nameof(policyService));
            _embeddingService = embeddingService ?? throw new ArgumentNullException(nameof(embeddingService));
            _vectorStore = vectorStore ?? throw new ArgumentNullException(nameof(vectorStore));
            
            _logger.LogInformation("=== LossRunRequestAgent CONSTRUCTOR CALLED ===");
            _logger.LogInformation("All dependencies injected successfully (including vector search)");
        }

        /// <summary>
        /// Agent definition with metadata, parameters, and capabilities
        /// </summary>
        public TaskAgentDefinition Definition => new()
        {
            AgentId = "loss_run_request",
            Name = "Loss Run Request",
            Description = "Send loss run requests to carriers for specified clients and policies",
            Category = "Loss Runs",
            TriggerPhrases = new List<string>
            {
                "send loss run",
                "request loss run",
                "get loss run",
                "loss run request",
                "need loss runs"
            },
            Parameters = new List<AgentParameterDefinition>
            {
                new()
                {
                    Name = "client_name",
                    Description = "Name of the client",
                    ParameterType = typeof(string),
                    IsRequired = false,
                    UseEntityExtraction = true,
                    EntityType = "ClientName",
                    ExtractionPrompt = "Extract the client or company name from the user input",
                    ClarificationQuestion = "Which client would you like to request loss runs for?"
                },
                new()
                {
                    Name = "policy_type",
                    Description = "Type of insurance policy",
                    ParameterType = typeof(string),
                    IsRequired = false,
                    UseEntityExtraction = true,
                    EntityType = "PolicyType",
                    ValidValues = new List<string> { "Workers Compensation", "General Liability", "Commercial Auto", "Property" },
                    ExtractionPrompt = "Extract the policy type or line of business (workers comp, general liability, auto, property, etc.)",
                    ClarificationQuestion = "What type of policy do you need loss runs for? (Workers Compensation, General Liability, Commercial Auto, Property)"
                },
                new()
                {
                    Name = "time_period",
                    Description = "Time period for loss runs in years",
                    ParameterType = typeof(int),
                    IsRequired = false,
                    DefaultValue = 5,
                    ExtractionPrompt = "Extract the number of years for the loss run period (e.g., '5 years', 'last 3 years')",
                    ClarificationQuestion = "How many years of loss run data do you need? (default: 5 years)"
                },
                new()
                {
                    Name = "carrier_name",
                    Description = "Name of the insurance carrier",
                    ParameterType = typeof(string),
                    IsRequired = false,
                    UseEntityExtraction = true,
                    EntityType = "CarrierName",
                    ExtractionPrompt = "Extract the insurance carrier name if specified",
                    ClarificationQuestion = "Which carrier should receive the loss run request? (leave blank for all carriers)"
                },
                new()
                {
                    Name = "client_id",
                    Description = "Client ID (for direct lookup)",
                    ParameterType = typeof(int),
                    IsRequired = false,
                    ExtractionPrompt = "Extract the client ID if provided",
                    ClarificationQuestion = "What is the client ID? (optional if client name is provided)"
                },
                new()
                {
                    Name = "product_id",
                    Description = "Product ID for policy line",
                    ParameterType = typeof(int),
                    IsRequired = false,
                    ExtractionPrompt = "Extract the product ID if provided",
                    ClarificationQuestion = "What is the product ID? (optional if policy type is provided)"
                }
            },
            OutcomeType = AgentOutcomeType.Message,
            SupportsAIChat = true,
            SupportsActionButton = true,
            EstimatedExecutionSeconds = 30,
            RequiresConfirmation = true
        };

        /// <summary>
        /// Execute the loss run request agent
        /// </summary>
        public async Task<TaskAgentResult> ExecuteAsync(TaskAgentRequest request, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;
            
            try
            {
                _logger.LogInformation("=== LOSS RUN AGENT EXECUTION STARTED ===");
                _logger.LogInformation("Executing loss run request for user {UserId}", request.UserId);
                _logger.LogInformation("Request parameters count: {ParameterCount}", request.Parameters?.Count ?? 0);
                
                // Log all parameters
                if (request.Parameters != null)
                {
                    foreach (var param in request.Parameters)
                    {
                        _logger.LogInformation("Parameter: {Key} = {Value} (Type: {Type})", 
                            param.Key, param.Value, param.Value?.GetType().Name ?? "null");
                    }
                }

                // Extract parameters with detailed logging
                _logger.LogInformation("=== EXTRACTING PARAMETERS ===");
                var clientName = GetStringValue(request.Parameters.GetValueOrDefault("client_name"));
                var policyType = GetStringValue(request.Parameters.GetValueOrDefault("policy_type"));
                var timePeriod = ExtractTimePeriod(request.Parameters.GetValueOrDefault("time_period"), 5);
                var carrierName = GetStringValue(request.Parameters.GetValueOrDefault("carrier_name"));
                var clientId = GetIntValue(request.Parameters.GetValueOrDefault("client_id"));
                var productId = GetIntValue(request.Parameters.GetValueOrDefault("product_id"));

                _logger.LogInformation("Extracted parameters:");
                _logger.LogInformation("  clientName: {ClientName}", clientName ?? "null");
                _logger.LogInformation("  policyType: {PolicyType}", policyType ?? "null");
                _logger.LogInformation("  timePeriod: {TimePeriod}", timePeriod);
                _logger.LogInformation("  carrierName: {CarrierName}", carrierName ?? "null");
                _logger.LogInformation("  clientId: {ClientId}", clientId?.ToString() ?? "null");
                _logger.LogInformation("  productId: {ProductId}", productId?.ToString() ?? "null");

                // Process the loss run request with real business logic
                _logger.LogInformation("=== STARTING BUSINESS LOGIC PROCESSING ===");
                var result = await ProcessLossRunRequestAsync(
                    clientName, policyType, timePeriod, carrierName, clientId, productId, cancellationToken);

                _logger.LogInformation("Business logic completed. Success: {Success}, Message: {Message}", 
                    result.Success, result.Message);

                // Generate dynamic suggestions based on results
                var suggestions = new List<string>();
                if (result.Success && result.Data is { })
                {
                    _logger.LogInformation("=== GENERATING SUGGESTIONS ===");
                    var data = result.Data as dynamic;
                    var report = data?.DetailedReport as LossRunReport;
                    
                    if (report?.WebsiteCarriers?.Any() == true)
                    {
                        suggestions.Add("🌐 Click the website links above to access carrier portals for loss runs");
                        _logger.LogInformation("Added website suggestion");
                    }
                    
                    if (report?.PhoneCarriers?.Any() == true)
                    {
                        suggestions.Add("📞 Contact the carriers above by phone for loss run requests");
                        _logger.LogInformation("Added phone suggestion");
                    }
                    
                    if (report?.EmailResults?.Any() == true)
                    {
                        suggestions.Add("📧 Review and send the email drafts in Outlook");
                        _logger.LogInformation("Added email suggestion");
                    }
                    
                    suggestions.Add("📋 Would you like to request certificates as well?");
                    suggestions.Add("🔄 Do you need loss runs for any other policy types?");
                    suggestions.Add("⏰ Should I set a reminder to follow up on these requests?");
                    
                    _logger.LogInformation("Generated {SuggestionCount} suggestions", suggestions.Count);
                }

                var finalResult = new TaskAgentResult
                {
                    Success = result.Success,
                    OutcomeType = result.Success ? AgentOutcomeType.Message : AgentOutcomeType.Error,
                    Message = result.Message,
                    Data = result.Data,
                    ErrorMessage = result.Success ? null : result.Message,
                    ExecutionTime = DateTime.UtcNow - startTime,
                    Suggestions = suggestions
                };

                _logger.LogInformation("=== LOSS RUN AGENT EXECUTION COMPLETED ===");
                _logger.LogInformation("Final result - Success: {Success}, ExecutionTime: {ExecutionTime}ms", 
                    finalResult.Success, finalResult.ExecutionTime.TotalMilliseconds);

                return finalResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "=== CRITICAL ERROR IN LOSS RUN AGENT EXECUTION ===");
                _logger.LogError("Error details: {ErrorMessage}", ex.Message);
                _logger.LogError("Stack trace: {StackTrace}", ex.StackTrace);
                
                return new TaskAgentResult
                {
                    Success = false,
                    OutcomeType = AgentOutcomeType.Error,
                    ErrorMessage = "Failed to process loss run request: " + ex.Message,
                    ExecutionTime = DateTime.UtcNow - startTime
                };
            }
        }

        /// <summary>
        /// Validate parameters for the agent
        /// </summary>
        public async Task<AgentParameterValidation> ValidateParametersAsync(Dictionary<string, object> parameters)
        {
            _logger.LogInformation("=== VALIDATING PARAMETERS ===");
            _logger.LogInformation("Parameter count: {Count}", parameters?.Count ?? 0);
            
            var validation = new AgentParameterValidation();
            var validatedParameters = new Dictionary<string, object>();

            try
            {
                // Validate client identification (either client_name or client_id)
                var hasClientName = parameters.TryGetValue("client_name", out var clientName) && 
                                   !string.IsNullOrWhiteSpace(clientName?.ToString());
                
                int clientIdValue = 0;
                var hasClientId = parameters.TryGetValue("client_id", out var clientId) && 
                                 int.TryParse(clientId?.ToString(), out clientIdValue);

                _logger.LogInformation("Client validation - hasClientName: {HasClientName}, hasClientId: {HasClientId}", 
                    hasClientName, hasClientId);

                if (hasClientName)
                {
                    validatedParameters["client_name"] = clientName.ToString().Trim();
                    validation.Parameters.Add(new ParameterValidationResult
                    {
                        ParameterName = "client_name",
                        Status = ParameterStatus.Valid,
                        Value = clientName
                    });
                    _logger.LogInformation("Client name validated: {ClientName}", clientName);
                }
                else if (hasClientId && clientIdValue > 0)
                {
                    validatedParameters["client_id"] = clientIdValue;
                    validation.Parameters.Add(new ParameterValidationResult
                    {
                        ParameterName = "client_id",
                        Status = ParameterStatus.Valid,
                        Value = clientIdValue
                    });
                    _logger.LogInformation("Client ID validated: {ClientId}", clientIdValue);
                }
                else
                {
                    validation.MissingParameters.Add("client_name");
                    validation.Parameters.Add(new ParameterValidationResult
                    {
                        ParameterName = "client_name",
                        Status = ParameterStatus.Missing,
                        Message = "Either client name or client ID is required"
                    });
                    _logger.LogWarning("Client identification missing");
                }

                // Validate policy type identification (either policy_type or product_id)
                var hasPolicyType = parameters.TryGetValue("policy_type", out var policyType) && 
                                   !string.IsNullOrWhiteSpace(policyType?.ToString());
                
                int productIdValue = 0;
                var hasProductId = parameters.TryGetValue("product_id", out var productId) && 
                                  int.TryParse(productId?.ToString(), out productIdValue);

                _logger.LogInformation("Policy validation - hasPolicyType: {HasPolicyType}, hasProductId: {HasProductId}", 
                    hasPolicyType, hasProductId);

                if (hasPolicyType)
                {
                    var validPolicyTypes = Definition.Parameters.First(p => p.Name == "policy_type").ValidValues;
                    var policyTypeStr = policyType.ToString().Trim();
                    
                    if (validPolicyTypes.Contains(policyTypeStr, StringComparer.OrdinalIgnoreCase))
                    {
                        validatedParameters["policy_type"] = policyTypeStr;
                        validation.Parameters.Add(new ParameterValidationResult
                        {
                            ParameterName = "policy_type",
                            Status = ParameterStatus.Valid,
                            Value = policyTypeStr
                        });
                        _logger.LogInformation("Policy type validated: {PolicyType}", policyTypeStr);
                    }
                    else
                    {
                        validation.Parameters.Add(new ParameterValidationResult
                        {
                            ParameterName = "policy_type",
                            Status = ParameterStatus.Invalid,
                            Message = $"Policy type must be one of: {string.Join(", ", validPolicyTypes)}",
                            SuggestedValues = validPolicyTypes
                        });
                        _logger.LogWarning("Invalid policy type: {PolicyType}", policyTypeStr);
                    }
                }
                else if (hasProductId && productIdValue > 0)
                {
                    validatedParameters["product_id"] = productIdValue;
                    validation.Parameters.Add(new ParameterValidationResult
                    {
                        ParameterName = "product_id",
                        Status = ParameterStatus.Valid,
                        Value = productIdValue
                    });
                    _logger.LogInformation("Product ID validated: {ProductId}", productIdValue);
                }
                else
                {
                    validation.MissingParameters.Add("policy_type");
                    validation.Parameters.Add(new ParameterValidationResult
                    {
                        ParameterName = "policy_type",
                        Status = ParameterStatus.Missing,
                        Message = "Either policy type or product ID is required"
                    });
                    _logger.LogWarning("Policy type identification missing");
                }

                // Validate time_period (optional with default)
                var timePeriod = parameters.GetValueOrDefault("time_period", 5);
                if (int.TryParse(timePeriod?.ToString(), out var years) && years > 0 && years <= 10)
                {
                    validatedParameters["time_period"] = years;
                    validation.Parameters.Add(new ParameterValidationResult
                    {
                        ParameterName = "time_period",
                        Status = ParameterStatus.Valid,
                        Value = years
                    });
                    _logger.LogInformation("Time period validated: {Years} years", years);
                }
                else if (timePeriod != null)
                {
                    validation.Parameters.Add(new ParameterValidationResult
                    {
                        ParameterName = "time_period",
                        Status = ParameterStatus.Invalid,
                        Message = "Time period must be between 1 and 10 years"
                    });
                    _logger.LogWarning("Invalid time period: {TimePeriod}", timePeriod);
                }

                // Validate carrier_name (optional)
                if (parameters.TryGetValue("carrier_name", out var carrierName) && !string.IsNullOrWhiteSpace(carrierName?.ToString()))
                {
                    validatedParameters["carrier_name"] = carrierName.ToString().Trim();
                    validation.Parameters.Add(new ParameterValidationResult
                    {
                        ParameterName = "carrier_name",
                        Status = ParameterStatus.Valid,
                        Value = carrierName
                    });
                    _logger.LogInformation("Carrier name validated: {CarrierName}", carrierName);
                }

                validation.ValidatedParameters = validatedParameters;
                validation.IsComplete = validation.MissingParameters.Count == 0 && 
                                       !validation.Parameters.Any(p => p.Status == ParameterStatus.Invalid);

                _logger.LogInformation("Parameter validation completed. IsComplete: {IsComplete}, MissingCount: {MissingCount}", 
                    validation.IsComplete, validation.MissingParameters.Count);

                return validation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during parameter validation");
                validation.Parameters.Add(new ParameterValidationResult
                {
                    ParameterName = "validation_error",
                    Status = ParameterStatus.Invalid,
                    Message = $"Validation error: {ex.Message}"
                });
                return validation;
            }
        }

        /// <summary>
        /// Get execution preview for confirmation
        /// </summary>
        public async Task<string> GetExecutionPreviewAsync(Dictionary<string, object> parameters)
        {
            _logger.LogInformation("=== GENERATING EXECUTION PREVIEW ===");
            
            try
            {
                var clientName = parameters.GetValueOrDefault("client_name")?.ToString() ?? 
                                $"Client ID {parameters.GetValueOrDefault("client_id", "Unknown")}";
                var policyType = parameters.GetValueOrDefault("policy_type")?.ToString() ?? 
                                $"Product ID {parameters.GetValueOrDefault("product_id", "Unknown")}";
                var timePeriod = Convert.ToInt32(parameters.GetValueOrDefault("time_period", 5));
                var carrierName = parameters.GetValueOrDefault("carrier_name")?.ToString();

                var preview = $"Send loss run requests for {clientName}'s {policyType} policies for the last {timePeriod} years";
                
                if (!string.IsNullOrWhiteSpace(carrierName))
                {
                    preview += $" from {carrierName}";
                }
                else
                {
                    preview += " from all carriers";
                }

                preview += ". This will create email requests and open them in Outlook for review before sending.";

                _logger.LogInformation("Generated preview: {Preview}", preview);
                return preview;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating execution preview");
                return "Error generating preview: " + ex.Message;
            }
        }

        /// <summary>
        /// Process the actual loss run request using real business logic
        /// </summary>
        private async Task<(bool Success, string Message, object Data)> ProcessLossRunRequestAsync(
            string clientName, 
            string policyType, 
            int timePeriod, 
            string carrierName,
            int? clientId,
            int? productId,
            CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("=== STARTING DATABASE OPERATIONS ===");
                _logger.LogInformation("Creating database context...");
                
                using var context = _dbContextFactory.CreateDbContext();
                _logger.LogInformation("Database context created successfully");

                // 1. Look up client using vector search
                _logger.LogInformation("=== STEP 1: LOOKING UP CLIENT USING VECTOR SEARCH ===");
                var client = clientId.HasValue 
                    ? await context.Clients.FirstOrDefaultAsync(c => c.ClientId == clientId.Value, cancellationToken)
                    : await FindClientByVectorSearchAsync(clientName, cancellationToken);

                if (client == null)
                {
                    var errorMsg = $"Client '{clientName ?? clientId?.ToString()}' not found.";
                    _logger.LogWarning("Client lookup failed: {ErrorMessage}", errorMsg);
                    return (false, errorMsg, null);
                }
                
                _logger.LogInformation("Client found: {ClientId} - {ClientName}", client.ClientId, client.Name);

                // 2. Look up product/line using vector search
                _logger.LogInformation("=== STEP 2: LOOKING UP PRODUCT/LINE USING VECTOR SEARCH ===");
                var product = productId.HasValue
                    ? await context.Products.FirstOrDefaultAsync(p => p.ProductId == productId.Value, cancellationToken)
                    : await FindProductByVectorSearchAsync(policyType, cancellationToken);

                if (product == null)
                {
                    var errorMsg = $"Product/Line '{policyType ?? productId?.ToString()}' not found.";
                    _logger.LogWarning("Product lookup failed: {ErrorMessage}", errorMsg);
                    return (false, errorMsg, null);
                }
                
                _logger.LogInformation("Product found: {ProductId} - {LineName}", product.ProductId, product.LineName);

                // 3. Get policies for the specified client and line for the last N years
                _logger.LogInformation("=== STEP 3: QUERYING POLICIES ===");
                var cutoffDate = DateTime.Now.AddYears(-timePeriod);
                _logger.LogInformation("Cutoff date: {CutoffDate}", cutoffDate);
                
                var policies = await context.Policies
                    .Include(p => p.Client)
                    .Include(p => p.Product)
                    .Include(p => p.Carrier)
                    .Include(p => p.Wholesaler)
                    .Where(p => p.ClientId == client.ClientId && 
                               p.ProductId == product.ProductId &&
                               p.EffectiveDate >= cutoffDate)
                    .ToListAsync(cancellationToken);

                _logger.LogInformation("Found {PolicyCount} policies matching criteria", policies.Count);

                if (!policies.Any())
                {
                    var errorMsg = $"No {product.LineName} policies found for {client.Name} in the last {timePeriod} years.";
                    _logger.LogWarning("No policies found: {ErrorMessage}", errorMsg);
                    return (false, errorMsg, null);
                }

                // Log policy details
                foreach (var policy in policies)
                {
                    _logger.LogInformation("Policy: {PolicyNumber}, Effective: {EffectiveDate}, Carrier: {CarrierName}, Wholesaler: {WholesalerName}",
                        policy.PolicyNumber,
                        policy.EffectiveDate,
                        policy.Carrier?.CarrierName ?? "None",
                        policy.Wholesaler?.CarrierName ?? "None");
                }

                // 4. Group policies by carrier name (same logic as LossRunsRequester.razor)
                _logger.LogInformation("=== STEP 4: GROUPING POLICIES BY CARRIER ===");
                var groupedPolicies = policies
                    .GroupBy(p => GetCarrierName(p))
                    .ToDictionary(g => g.Key, g => g.ToList());

                _logger.LogInformation("Grouped policies into {GroupCount} carrier groups", groupedPolicies.Count);
                foreach (var group in groupedPolicies)
                {
                    _logger.LogInformation("Carrier group: {CarrierName} - {PolicyCount} policies", 
                        group.Key, group.Value.Count);
                }

                // 5. Send emails for each carrier group
                _logger.LogInformation("=== STEP 5: PROCESSING CARRIER GROUPS ===");
                var emailResults = new List<EmailResult>();
                
                foreach (var carrierGroup in groupedPolicies)
                {
                    var currentCarrierName = carrierGroup.Key;
                    var carrierPolicies = carrierGroup.Value;
                    
                    _logger.LogInformation("Processing carrier: {CarrierName} with {PolicyCount} policies", 
                        currentCarrierName, carrierPolicies.Count);
                    
                    // Skip if specific carrier requested and this isn't it
                    if (!string.IsNullOrEmpty(carrierName) && 
                        !currentCarrierName.Contains(carrierName, StringComparison.OrdinalIgnoreCase))
                    {
                        _logger.LogInformation("Skipping carrier {CarrierName} - doesn't match filter {CarrierFilter}", 
                            currentCarrierName, carrierName);
                        continue;
                    }
                    
                    var emailResult = await CreateEmailResultForCarrier(client, currentCarrierName, carrierPolicies, product.LineName);
                    emailResults.Add(emailResult);
                    
                    _logger.LogInformation("Email result for {CarrierName}: Success={Success}, ContactMethod={ContactMethod}, Error={Error}",
                        currentCarrierName, emailResult.Sent, emailResult.ContactMethod, emailResult.Error ?? "None");
                }

                if (!emailResults.Any())
                {
                    var errorMsg = "No carriers found matching the specified criteria.";
                    _logger.LogWarning("No email results: {ErrorMessage}", errorMsg);
                    return (false, errorMsg, null);
                }

                _logger.LogInformation("=== STEP 6: GENERATING REPORTS ===");
                // Generate detailed report
                var report = GenerateDetailedReport(client.Name, product.LineName, timePeriod, emailResults);
                _logger.LogInformation("Generated detailed report with {EmailCount} email results", emailResults.Count);

                // Create structured data for MessageBar consumption
                var messageBarData = CreateMessageBarData(client.Name, product.LineName, timePeriod, emailResults);
                _logger.LogInformation("Generated message bar data with {GroupCount} carrier groups", 
                    messageBarData.CarrierGroups.Count);

                var data = new
                {
                    ClientName = client.Name,
                    ClientId = client.ClientId,
                    ProductId = product.ProductId,
                    PolicyType = product.LineName,
                    TimePeriod = timePeriod,
                    CarrierName = carrierName,
                    PoliciesFound = policies.Count,
                    EmailResults = emailResults,
                    EmailsSent = emailResults.Count(r => r.Sent),
                    EmailsTotal = emailResults.Count,
                    WebsiteCarriers = emailResults.Where(r => r.ContactMethod == ContactMethod.Website).ToList(),
                    PhoneCarriers = emailResults.Where(r => r.ContactMethod == ContactMethod.Phone).ToList(),
                    Status = emailResults.Any(r => r.Sent) ? "Success" : "Partial Success",
                    DetailedReport = report,
                    MessageBarData = messageBarData // Structured data for UI
                };

                _logger.LogInformation("=== PROCESSING COMPLETED SUCCESSFULLY ===");
                _logger.LogInformation("Final stats - Policies: {PolicyCount}, Emails: {EmailCount}, Sent: {SentCount}",
                    policies.Count, emailResults.Count, emailResults.Count(r => r.Sent));

                return (true, report.Summary, data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "=== CRITICAL ERROR IN BUSINESS LOGIC ===");
                _logger.LogError("Error processing loss run request: {ErrorMessage}", ex.Message);
                _logger.LogError("Stack trace: {StackTrace}", ex.StackTrace);
                return (false, $"Error processing request: {ex.Message}", null);
            }
        }

        /// <summary>
        /// Get carrier name using same logic as LossRunsRequester.razor
        /// </summary>
        private string GetCarrierName(Policy policy)
        {
            const string unknownCarrierName = "Unknown Carrier";

            try
            {
                // Check if Wholesaler exists and is marked as Wholesaler
                if (policy.Wholesaler != null && policy.Wholesaler.Wholesaler)
                {
                    return policy.Wholesaler.CarrierName;
                }
                
                // Check if Carrier exists and is marked as Carrier (or IssuingCarrier)
                if (policy.Carrier != null && (policy.Carrier.IssuingCarrier || !policy.Carrier.Wholesaler))
                {
                    return policy.Carrier.CarrierName;
                }
                
                return unknownCarrierName;
            }
            catch
            {
                return unknownCarrierName;
            }
        }

        /// <summary>
        /// Get loss runs email using same logic as LossRunsRequester.razor
        /// </summary>
        private string GetLossRunsEmail(List<Policy> policies)
        {
            foreach (var policy in policies)
            {
                // Check wholesaler first if it exists and was used for grouping
                if (policy.Wholesaler != null && 
                    policy.Wholesaler.Wholesaler && 
                    !string.IsNullOrEmpty(policy.Wholesaler.LossRunsEmail))
                {
                    return policy.Wholesaler.LossRunsEmail;
                }
                
                // Then check carrier if it exists and was used for grouping
                if (policy.Carrier != null && 
                    (policy.Carrier.IssuingCarrier || !policy.Carrier.Wholesaler) &&
                    !string.IsNullOrEmpty(policy.Carrier.LossRunsEmail))
                {
                    return policy.Carrier.LossRunsEmail;
                }
            }
            
            return string.Empty;
        }

        /// <summary>
        /// Create detailed email result for a carrier including all contact methods
        /// </summary>
        private async Task<EmailResult> CreateEmailResultForCarrier(
            Domain.Clients.Models.Client client,
            string carrierName,
            List<Policy> policies,
            string productLine)
        {
            var result = new EmailResult
            {
                CarrierName = carrierName,
                Email = GetLossRunsEmail(policies)
            };

            // Get carrier information for additional contact methods
            var carrierInfo = GetCarrierInfo(policies);
            result.LossRunsURL = carrierInfo?.LossRunsURL;
            result.SpecialInstructions = carrierInfo?.LossRunsSpecialInstruction;

            // Determine contact method and attempt communication
            if (!string.IsNullOrEmpty(result.Email))
            {
                result.ContactMethod = ContactMethod.Email;
                try
                {
                    await SendEmailForCarrier(client, carrierName, policies, result.Email, productLine);
                    result.Sent = true;
                    _logger.LogInformation("Successfully sent loss run email for {CarrierName}", carrierName);
                }
                catch (Exception ex)
                {
                    result.Sent = false;
                    result.Error = ex.Message;
                    _logger.LogError(ex, "Failed to send loss run email for {CarrierName}", carrierName);
                }
            }
            else if (!string.IsNullOrEmpty(result.LossRunsURL))
            {
                result.ContactMethod = ContactMethod.Website;
                result.Sent = false; // No email sent, but alternative available
                result.Error = "No email available - website option provided";
            }
            else
            {
                result.ContactMethod = ContactMethod.Phone;
                result.Sent = false;
                result.Error = "No email or website available - phone contact required";
            }

            return result;
        }

        /// <summary>
        /// Get carrier information from policies (placeholder - extend based on your carrier data model)
        /// </summary>
        private dynamic? GetCarrierInfo(List<Policy> policies)
        {
            var firstPolicy = policies.FirstOrDefault();
            var carrier = firstPolicy?.Carrier ?? firstPolicy?.Wholesaler;
            
            if (carrier == null) return null;

            // This assumes your Carrier model has LossRunsURL and LossRunsSpecialInstruction
            // Adjust based on your actual data model
            return new
            {
                LossRunsURL = GetCarrierProperty(carrier, "LossRunsURL"),
                LossRunsSpecialInstruction = GetCarrierProperty(carrier, "LossRunsSpecialInstruction")
            };
        }

        /// <summary>
        /// Helper to safely get carrier properties via reflection (adjust based on your model)
        /// </summary>
        private string? GetCarrierProperty(object carrier, string propertyName)
        {
            try
            {
                var property = carrier.GetType().GetProperty(propertyName);
                return property?.GetValue(carrier)?.ToString();
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// Generate a detailed HTML-formatted report for the user
        /// </summary>
        private LossRunReport GenerateDetailedReport(string clientName, string productLine, int timePeriod, List<EmailResult> emailResults)
        {
            var emailCarriers = emailResults.Where(r => r.ContactMethod == ContactMethod.Email && r.Sent).ToList();
            var websiteCarriers = emailResults.Where(r => r.ContactMethod == ContactMethod.Website).ToList();
            var phoneCarriers = emailResults.Where(r => r.ContactMethod == ContactMethod.Phone).ToList();

            var report = new LossRunReport();
            
            // Build HTML-formatted summary message
            var summaryParts = new List<string>
            {
                $"<strong>✅ Loss run requests processed for {clientName} ({productLine}) for the last {timePeriod} years.</strong>"
            };

            // Email results
            if (emailCarriers.Any())
            {
                var carrierList = string.Join(", ", emailCarriers.Select(c => $"<em>{c.CarrierName}</em>"));
                summaryParts.Add($"📧 <strong>Created {emailCarriers.Count} email(s)</strong> and opened drafts in Outlook. Ready to send to: {carrierList}");
            }

            // Website carriers
            foreach (var carrier in websiteCarriers)
            {
                var instruction = !string.IsNullOrEmpty(carrier.SpecialInstructions) 
                    ? $"<br/>&nbsp;&nbsp;&nbsp;&nbsp;<small><em>Instructions: {carrier.SpecialInstructions}</em></small>" 
                    : "";
                
                var linkText = !string.IsNullOrEmpty(carrier.LossRunsURL) 
                    ? $"<a href=\"{carrier.LossRunsURL}\" target=\"_blank\" style=\"color: #0066cc; text-decoration: underline;\">Click here to access {carrier.CarrierName} loss runs</a>"
                    : $"{carrier.CarrierName} website";
                    
                summaryParts.Add($"🌐 <strong>{carrier.CarrierName}</strong> has loss runs on their website: {linkText}{instruction}");
            }

            // Phone carriers
            if (phoneCarriers.Any())
            {
                var carrierList = string.Join(", ", phoneCarriers.Select(c => $"<strong>{c.CarrierName}</strong>"));
                var plural = phoneCarriers.Count > 1 ? "carriers" : "carrier";
                summaryParts.Add($"📞 <strong>{phoneCarriers.Count} {plural} require phone contact:</strong> {carrierList}.<br/>&nbsp;&nbsp;&nbsp;&nbsp;<small><em>You will need to call these carriers directly for loss run requests.</em></small>");
            }

            // Join with HTML line breaks
            report.Summary = string.Join("<br/><br/>", summaryParts);
            report.EmailResults = emailCarriers;
            report.WebsiteCarriers = websiteCarriers;
            report.PhoneCarriers = phoneCarriers;

            return report;
        }

        /// <summary>
        /// Create structured data for MessageBar consumption
        /// </summary>
        private LossRunMessageBarData CreateMessageBarData(string clientName, string productLine, int timePeriod, List<EmailResult> emailResults)
        {
            var messageBarData = new LossRunMessageBarData
            {
                SummaryMessage = $"Loss run requests processed for {clientName} ({productLine}) for the last {timePeriod} years."
            };

            var emailCarriers = emailResults.Where(r => r.ContactMethod == ContactMethod.Email && r.Sent).ToList();
            var websiteCarriers = emailResults.Where(r => r.ContactMethod == ContactMethod.Website).ToList();
            var phoneCarriers = emailResults.Where(r => r.ContactMethod == ContactMethod.Phone).ToList();

            // Email group
            if (emailCarriers.Any())
            {
                messageBarData.CarrierGroups.Add(new CarrierMessageGroup
                {
                    GroupType = "Email",
                    Title = $"📧 {emailCarriers.Count} Email(s) Created",
                    Body = $"Email drafts have been created in Outlook for: {string.Join(", ", emailCarriers.Select(c => c.CarrierName))}",
                    Intent = "Success",
                    CarrierNames = emailCarriers.Select(c => c.CarrierName).ToList(),
                    Actions = new List<CarrierMessageAction>
                    {
                        new() 
                        { 
                            Text = "Open Outlook", 
                            ActionType = "OpenOutlook",
                            Parameters = new Dictionary<string, object> { { "carriers", emailCarriers.Select(c => c.CarrierName).ToList() } }
                        }
                    }
                });
            }

            // Website group
            foreach (var carrier in websiteCarriers)
            {
                messageBarData.CarrierGroups.Add(new CarrierMessageGroup
                {
                    GroupType = "Website",
                    Title = $"🌐 {carrier.CarrierName} - Website Available",
                    Body = !string.IsNullOrEmpty(carrier.SpecialInstructions) 
                        ? $"Access loss runs online. Instructions: {carrier.SpecialInstructions}"
                        : "Access loss runs through carrier website.",
                    Intent = "Info",
                    CarrierNames = new List<string> { carrier.CarrierName },
                    Links = new List<CarrierMessageLink>
                    {
                        new() 
                        { 
                            Text = $"Open {carrier.CarrierName} Portal", 
                            Href = carrier.LossRunsURL ?? "#",
                            CarrierName = carrier.CarrierName
                        }
                    }
                });
            }

            // Phone group
            if (phoneCarriers.Any())
            {
                messageBarData.CarrierGroups.Add(new CarrierMessageGroup
                {
                    GroupType = "Phone",
                    Title = $"📞 {phoneCarriers.Count} Carrier(s) Require Phone Contact",
                    Body = $"No email or website available. Call directly: {string.Join(", ", phoneCarriers.Select(c => c.CarrierName))}",
                    Intent = "Warning",
                    CarrierNames = phoneCarriers.Select(c => c.CarrierName).ToList(),
                    Actions = new List<CarrierMessageAction>
                    {
                        new() 
                        { 
                            Text = "View Contact Info", 
                            ActionType = "ViewCarrierContacts",
                            Parameters = new Dictionary<string, object> { { "carriers", phoneCarriers.Select(c => c.CarrierName).ToList() } }
                        }
                    }
                });
            }

            return messageBarData;
        }

        /// <summary>
        /// Send email for carrier using same logic as LossRunsRequester.razor
        /// </summary>
        private async Task SendEmailForCarrier(
            Domain.Clients.Models.Client client, 
            string carrierName, 
            List<Policy> policies, 
            string toEmail, 
            string productLine)
        {
            if (policies == null || !policies.Any())
            {
                throw new ArgumentException("No policies provided for email generation");
            }

            if (string.IsNullOrEmpty(toEmail))
            {
                throw new ArgumentException("No email address provided");
            }

            try
            {
                var firstPolicy = policies.First();
                
                // Create subject with client name
                string subject = $"Loss Run Request - {client.Name} - {productLine} ({firstPolicy.PolicyNumber})";
                
                // Build email body with client name
                string body = $"<p>Please send us the current valued loss runs for <strong>{client.Name}</strong>'s {productLine} policies from {carrierName}:</p>";
                
                // Add policy details to body
                body += "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
                body += "<tr style='background-color: #f2f2f2;'><th>Policy Number</th><th>Effective Date</th><th>Expiration Date</th><th>Carrier</th><th>Wholesaler</th></tr>";
                
                foreach (var policy in policies)
                {
                    string carrierValue = policy.Carrier?.CarrierName ?? "N/A";
                    string wholesalerValue = policy.Wholesaler?.CarrierName ?? "N/A";
                    
                    body += $"<tr><td>{policy.PolicyNumber}</td><td>{policy.EffectiveDate.ToShortDateString()}</td><td>{policy.ExpirationDate.ToShortDateString()}</td><td>{carrierValue}</td><td>{wholesalerValue}</td></tr>";
                }
                
                body += "</table>";
                body += "<p>Thank you for your assistance.</p>";
                
                // Create the email using Ember service
                var parameters = new List<string> { toEmail, subject, body };
                
                _logger.LogInformation("Creating email for {CarrierName} to {Email}", carrierName, toEmail);
                
                await _emberService.RunEmberFunction("OutlookEmail_CreateNew", parameters);
                
                _logger.LogInformation("Successfully created email for {CarrierName}", carrierName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SendEmailForCarrier for {CarrierName}", carrierName);
                throw;
            }
        }

        /// <summary>
        /// Result of email creation for a carrier
        /// </summary>
        public class EmailResult
        {
            public string CarrierName { get; set; } = string.Empty;
            public string Email { get; set; } = string.Empty;
            public bool Sent { get; set; }
            public string? Error { get; set; }
            
            // Enhanced reporting properties
            public string? LossRunsURL { get; set; }
            public string? SpecialInstructions { get; set; }
            public ContactMethod ContactMethod { get; set; } = ContactMethod.None;
        }
        
        /// <summary>
        /// How to contact the carrier for loss runs
        /// </summary>
        public enum ContactMethod
        {
            None,
            Email,
            Website,
            Phone
        }

        /// <summary>
        /// Detailed report of loss run request results
        /// </summary>
        public class LossRunReport
        {
            public string Summary { get; set; } = string.Empty;
            public List<EmailResult> EmailResults { get; set; } = new();
            public List<EmailResult> WebsiteCarriers { get; set; } = new();
            public List<EmailResult> PhoneCarriers { get; set; } = new();
        }

        /// <summary>
        /// Structured data for MessageBar display
        /// </summary>
        public class LossRunMessageBarData
        {
            public string SummaryMessage { get; set; } = string.Empty;
            public List<CarrierMessageGroup> CarrierGroups { get; set; } = new();
        }

        /// <summary>
        /// Carrier group for MessageBar display
        /// </summary>
        public class CarrierMessageGroup
        {
            public string GroupType { get; set; } = string.Empty; // "Email", "Website", "Phone"
            public string Title { get; set; } = string.Empty;
            public string Body { get; set; } = string.Empty;
            public string Intent { get; set; } = "Info"; // "Success", "Warning", "Error", "Info"
            public List<CarrierMessageAction> Actions { get; set; } = new();
            public List<CarrierMessageLink> Links { get; set; } = new();
            public List<string> CarrierNames { get; set; } = new();
        }

        /// <summary>
        /// Action button for MessageBar
        /// </summary>
        public class CarrierMessageAction
        {
            public string Text { get; set; } = string.Empty;
            public string ActionType { get; set; } = string.Empty; // "OpenOutlook", "CallCarrier", etc.
            public Dictionary<string, object> Parameters { get; set; } = new();
        }

        /// <summary>
        /// Link for MessageBar
        /// </summary>
        public class CarrierMessageLink
        {
            public string Text { get; set; } = string.Empty;
            public string Href { get; set; } = string.Empty;
            public string CarrierName { get; set; } = string.Empty;
        }

        /// <summary>
        /// Helper method to safely extract string values from JsonElement or other object types
        /// </summary>
        private string? GetStringValue(object? value)
        {
            if (value == null) return null;
            
            if (value is System.Text.Json.JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.String)
                {
                    return jsonElement.GetString();
                }
                else if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.Null)
                {
                    return null;
                }
                else
                {
                    return jsonElement.ToString();
                }
            }
            
            return value.ToString();
        }

        /// <summary>
        /// Helper method to safely extract integer values from JsonElement or other object types
        /// </summary>
        private int? GetIntValue(object? value)
        {
            if (value == null) return null;
            
            if (value is System.Text.Json.JsonElement jsonElement)
            {
                if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.Number)
                {
                    return jsonElement.GetInt32();
                }
                else if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.String)
                {
                    var stringValue = jsonElement.GetString();
                    if (int.TryParse(stringValue, out var intValue))
                    {
                        return intValue;
                    }
                }
                else if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.Null)
                {
                    return null;
                }
            }
            else if (value is string stringValue)
            {
                if (int.TryParse(stringValue, out var intValue))
                {
                    return intValue;
                }
            }
            else if (value is int intValue)
            {
                return intValue;
            }
            
            return null;
        }

        /// <summary>
        /// Helper method to extract time period from text like "last 5 years", "5 years", "10", etc.
        /// </summary>
        private int ExtractTimePeriod(object? value, int defaultValue = 5)
        {
            if (value == null) return defaultValue;
            
            var stringValue = GetStringValue(value);
            if (string.IsNullOrWhiteSpace(stringValue)) return defaultValue;
            
            _logger.LogInformation("Extracting time period from: '{TimePeriodText}'", stringValue);
            
            // Try to extract number from text like "last 5 years", "5 years", "10 years", etc.
            var match = System.Text.RegularExpressions.Regex.Match(stringValue, @"(\d+)");
            if (match.Success && int.TryParse(match.Groups[1].Value, out var years))
            {
                _logger.LogInformation("Extracted {Years} years from time period text", years);
                return years;
            }
            
            // Try direct integer conversion as fallback
            if (int.TryParse(stringValue, out var directValue))
            {
                _logger.LogInformation("Direct conversion to {Years} years", directValue);
                return directValue;
            }
            
            _logger.LogWarning("Could not extract time period from '{TimePeriodText}', using default {DefaultValue}", 
                stringValue, defaultValue);
            return defaultValue;
        }

        /// <summary>
        /// Find client using vector search for better fuzzy matching
        /// </summary>
        private async Task<Domain.Clients.Models.Client?> FindClientByVectorSearchAsync(string clientName, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(clientName)) return null;

            try
            {
                _logger.LogInformation("🔍 Performing vector search for client: '{ClientName}'", clientName);
                
                // Generate embedding for the client name
                var queryVector = await _embeddingService.GenerateEmbeddingAsync(clientName, cancellationToken);
                
                // Search for similar clients in the vector store
                var searchResults = await _vectorStore.SearchAsync(queryVector, 5, cancellationToken);
                
                _logger.LogInformation("Vector search found {ResultCount} potential client matches", searchResults.Count);
                
                // Filter for Client entities and find the best match
                var clientMatches = searchResults
                    .Where(r => GetMetadataString(r.Metadata, "Entity") == "Client")
                    .Where(r => r.Score > 0.4f) // Lowered threshold for better fuzzy matching
                    .ToList();

                if (!clientMatches.Any())
                {
                    _logger.LogWarning("No client matches found above threshold (0.4) for '{ClientName}'", clientName);
                    
                    // Log all results for debugging
                    _logger.LogInformation("All vector search results for debugging:");
                    foreach (var result in searchResults)
                    {
                        var entityType = GetMetadataString(result.Metadata, "Entity") ?? "Unknown";
                        var entityName = GetMetadataString(result.Metadata, "Name");
                        var entityId = GetMetadataInt(result.Metadata, "EntityId");
                        _logger.LogInformation("  {EntityType} '{EntityName}' (ID: {EntityId}) - Score: {Score:F3}", 
                            entityType, entityName, entityId, result.Score);
                    }
                    
                    return null;
                }

                // Log all matches for debugging
                foreach (var match in clientMatches)
                {
                    var entityId = GetMetadataInt(match.Metadata, "EntityId");
                    var entityName = GetMetadataString(match.Metadata, "Name");
                    _logger.LogInformation("Client match: ID={EntityId}, Name='{EntityName}', Score={Score:F3}", 
                        entityId, entityName, match.Score);
                }

                // Get the best match
                var bestMatch = clientMatches.First();
                var clientId = GetMetadataInt(bestMatch.Metadata, "EntityId");
                
                if (clientId == null)
                {
                    _logger.LogError("Best client match has invalid EntityId");
                    return null;
                }

                _logger.LogInformation("✅ Selected best client match: ID={ClientId}, Score={Score:F3}", 
                    clientId, bestMatch.Score);

                // Fetch the actual client from database
                using var context = _dbContextFactory.CreateDbContext();
                var client = await context.Clients.FirstOrDefaultAsync(c => c.ClientId == clientId.Value, cancellationToken);
                
                if (client != null)
                {
                    _logger.LogInformation("✅ Client retrieved from database: {ClientName}", client.Name);
                }
                else
                {
                    _logger.LogError("❌ Client with ID {ClientId} not found in database", clientId);
                }

                return client;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in vector search for client '{ClientName}'", clientName);
                return null;
            }
        }

        /// <summary>
        /// Find product/policy line using vector search for better fuzzy matching
        /// </summary>
        private async Task<Product?> FindProductByVectorSearchAsync(string policyType, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(policyType)) return null;

            try
            {
                _logger.LogInformation("🔍 Performing vector search for policy type: '{PolicyType}'", policyType);
                
                // Generate embedding for the policy type
                var queryVector = await _embeddingService.GenerateEmbeddingAsync(policyType, cancellationToken);
                
                // Search for similar policy lines in the vector store
                var searchResults = await _vectorStore.SearchAsync(queryVector, 5, cancellationToken);
                
                _logger.LogInformation("Vector search found {ResultCount} potential policy type matches", searchResults.Count);
                
                // Filter for Product entities and find the best match
                var productMatches = searchResults
                    .Where(r => GetMetadataString(r.Metadata, "Entity") == "Product")
                    .Where(r => r.Score > 0.3f) // Lower threshold for better policy type matching
                    .ToList();

                if (!productMatches.Any())
                {
                    _logger.LogWarning("No product matches found above threshold (0.3) for '{PolicyType}'", policyType);
                    
                    // Log all results for debugging
                    _logger.LogInformation("All vector search results for debugging:");
                    foreach (var result in searchResults)
                    {
                        var entityType = GetMetadataString(result.Metadata, "Entity") ?? "Unknown";
                        var entityName = GetMetadataString(result.Metadata, "Name");
                        var entityId = GetMetadataInt(result.Metadata, "EntityId");
                        _logger.LogInformation("  {EntityType} '{EntityName}' (ID: {EntityId}) - Score: {Score:F3}", 
                            entityType, entityName, entityId, result.Score);
                    }
                    
                    return null;
                }

                // Log all matches for debugging
                foreach (var match in productMatches)
                {
                    var entityId = GetMetadataInt(match.Metadata, "EntityId");
                    var entityName = GetMetadataString(match.Metadata, "Name");
                    _logger.LogInformation("Product match: ID={EntityId}, Name='{EntityName}', Score={Score:F3}", 
                        entityId, entityName, match.Score);
                }

                // Get the best match
                var bestMatch = productMatches.First();
                var productId = GetMetadataInt(bestMatch.Metadata, "EntityId");
                
                if (productId == null)
                {
                    _logger.LogError("Best product match has invalid EntityId");
                    return null;
                }

                _logger.LogInformation("✅ Selected best product match: ID={ProductId}, Score={Score:F3}", 
                    productId, bestMatch.Score);

                // Fetch the actual product from database
                using var context = _dbContextFactory.CreateDbContext();
                var product = await context.Products.FirstOrDefaultAsync(p => p.ProductId == productId.Value, cancellationToken);
                
                if (product != null)
                {
                    _logger.LogInformation("✅ Product retrieved from database: {LineName}", product.LineName);
                }
                else
                {
                    _logger.LogError("❌ Product with ID {ProductId} not found in database", productId);
                }

                return product;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in vector search for policy type '{PolicyType}'", policyType);
                return null;
            }
        }

        /// <summary>
        /// Helper method to safely get string values from metadata
        /// </summary>
        private string? GetMetadataString(IDictionary<string, object> metadata, string key)
        {
            if (metadata.TryGetValue(key, out var value))
            {
                if (value is System.Text.Json.JsonElement jsonElement)
                {
                    return jsonElement.ValueKind == System.Text.Json.JsonValueKind.String ? jsonElement.GetString() : jsonElement.ToString();
                }
                return value?.ToString();
            }
            return null;
        }

        /// <summary>
        /// Helper method to safely get integer values from metadata
        /// </summary>
        private int? GetMetadataInt(IDictionary<string, object> metadata, string key)
        {
            if (metadata.TryGetValue(key, out var value))
            {
                if (value is System.Text.Json.JsonElement jsonElement)
                {
                    if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.Number)
                    {
                        return jsonElement.GetInt32();
                    }
                    else if (jsonElement.ValueKind == System.Text.Json.JsonValueKind.String)
                    {
                        var stringValue = jsonElement.GetString();
                        if (int.TryParse(stringValue, out var intValue))
                        {
                            return intValue;
                        }
                    }
                }
                else if (value is int intValue)
                {
                    return intValue;
                }
                else if (int.TryParse(value?.ToString(), out var parsedValue))
                {
                    return parsedValue;
                }
            }
            return null;
        }
    }
} 