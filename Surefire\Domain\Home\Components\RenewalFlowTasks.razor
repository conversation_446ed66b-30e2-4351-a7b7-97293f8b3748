﻿@using Surefire.Domain.Renewals.ViewModels
@using Surefire.Domain.Renewals.Services
@using Surefire.Domain.Shared.Helpers
@using Surefire.Domain.Logs
@using Syncfusion.Blazor.Notifications
@using Microsoft.EntityFrameworkCore
@inject Surefire.Domain.Renewals.Services.TaskService TaskService
@inject Surefire.Domain.Renewals.Services.RenewalService RenewalService
@inject Surefire.Domain.Clients.Services.ClientStateService ClientStateService
@inject Surefire.Domain.Shared.Services.StateService StateService
@inject HomeService HomeService
@inject ILoggingService _log
@inherits AppComponentBase
@implements IDisposable

<div class="rentask-container">
    <div class="sectiontitletab background-@bgColor">@tabTitle</div>
    <div class="home-box background-@bgColor">
        <table id="tasktable" cellspacing="0" class="ttable">
            <thead class="tbg background-@bgColor">
                <tr>
                    <th class="policycoly">Policy</th>
                    <th class="clientcoly">Client</th>
                    <th class="taskcoly">Task</th>
                    <th>Priority</th>
                </tr>
            </thead>
            <tbody class="tbody">
                @if (tasks == null || isLoading)
                {
            
                    for (var i = 0; i < 8; i++)
                    {
                    <tr class="trow">
                        <td colspan="4"><SfSkeleton Shape=SkeletonType.Rectangle Width="100%" Height="20px" CssClass="e-customize" Visible="true"></SfSkeleton></td>
                    </tr>
                    }

                }
                else if (!tasks.Any())
                {
                    <tr>
                        <td colspan="4"><div class="nothingfound">Nothing found here.</div></td>
                    </tr>
                }
                else
                {
                    @foreach (var task in tasks)
                    {
                        <tr class="trow @(task.Highlighted ? "highlighted" : "") @(IsTaskCheckedOffToday(task) ? "blurred" : "")">
                            <td class="policycoly"><span class="hprod hprodbg-@bgColor">@StringHelper.GetSafeSubstring(task.PolicyProduct, 0, 3)</span><span class="hexp">@task.RenewalDate.ToString("MM/dd")</span></td>
                            <td class="clientcoly tcname ellipsis"><a class="sf-link" @onclick="() => HandleClientClick(task.ClientId)" @onclick:preventDefault="true" style="cursor: pointer;">@task.ClientName</a> </td>
                            <td class="taskcoly ttname ellipsis">
                                <a class="rentask-link" @onclick="() => HandleRenewalClick(task.RenewalId)">
                                    @if (!string.IsNullOrEmpty(task.ParentTaskName))
                                    {
                                        <span class="parenttaskname">@task.ParentTaskName</span>
                            
                                        <span class="parentcaret"><FluentIcon Value="@(new Icons.Regular.Size16.ChevronRight())" Slot="end" Color="Color.Custom" CustomColor="#687176" /></span>
                                    }
                                    <span class="thetaskname">@task.TaskName</span>
                                </a>
                            </td>
                            <td class="ttpri ellipsis">
                                <span class="priority-toggle" @onclick="() => ToggleDailyCheckOff(task)">@StringHelper.FormatDuePhrase(task.Priority?.ToString())</span>
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
    </div>
</div>
@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger alert-sm mt-2">
        <small><strong>Error:</strong> @errorMessage</small>
        <button type="button" class="btn btn-xs btn-outline-danger ms-2" @onclick="RefreshData">Retry</button>
    </div>
}

@code {
    private List<HomePageRenFlowTasksViewModel>? tasks = null;
    private bool isLoading = true;
    private string errorMessage = string.Empty;
    private bool _isInitialized = false;

    [Parameter]
    public string tabTitle { get; set; } = "Tasks";

    [Parameter]
    public string bgColor { get; set; } = "default";

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        
        // Subscribe to state changes for automatic refresh
        StateService.OnHomepageDataUpdated += HandleDataUpdated;
        
        await LoadData();
        _isInitialized = true;
    }

    protected override async Task OnParametersSetAsync()
    {
        // Reload data when parameters change (if already initialized)
        if (_isInitialized)
        {
            await LoadData();
        }
    }

    private async Task LoadData()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            StateHasChanged();

            // Try to get data from StateService cache first
            if (StateService.CachedRenFlowTasks?.Any() == true)
            {
                tasks = StateService.CachedRenFlowTasks.ToList();
            }
            else
            {
                // Load fresh data if cache is empty
                tasks = await HomeService.GetHomePageRenFlowTasksAsync();
            }
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error loading RenFlow tasks: {ex.Message}", "RenFlowTasks");
            errorMessage = "Failed to load tasks.";
            tasks = new List<HomePageRenFlowTasksViewModel>();
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshData()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            StateHasChanged();

            // Force fresh data load
            tasks = await HomeService.GetHomePageRenFlowTasksAsync();
            
            // Update cache in StateService
            StateService.UpdateCachedRenFlowTasks(tasks);
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error refreshing RenFlow tasks: {ex.Message}", "RenFlowTasks");
            errorMessage = "Failed to refresh tasks.";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void HandleDataUpdated()
    {
        InvokeAsync(async () =>
        {
            if (StateService.CachedRenFlowTasks?.Any() == true)
            {
                tasks = StateService.CachedRenFlowTasks.ToList();
                StateHasChanged();
            }
        });
    }

    private bool IsTaskCheckedOffToday(HomePageRenFlowTasksViewModel task)
    {
        return task.DailyCheckOff?.Date == DateTime.Today;
    }

    private async Task ToggleDailyCheckOff(HomePageRenFlowTasksViewModel task)
    {
        try
        {
            var isToday = IsTaskCheckedOffToday(task);
            var newValue = isToday ? (DateTime?)null : DateTime.Today;
            task.DailyCheckOff = newValue;
            
            await TaskService.UpdateTrackTaskDailyCheckOffAsync(task.TrackTaskId, newValue);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error toggling daily check off for task {task.TrackTaskId}: {ex.Message}", "RenFlowTasks");
            // Revert the UI change on error
            await LoadData();
        }
    }

    private async Task HandleClientClick(int clientId)
    {
        try
        {
            // Set the client state service to remember which client was selected
            ClientStateService.SelectedClientId = clientId;
            ClientStateService.ActiveTab = "tab-1";
            await ClientStateService.SaveStateAsync();
            
            Navigation.NavigateTo($"/Clients/{clientId}");
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error navigating to client {clientId}: {ex.Message}", "RenFlowTasks");
        }
    }

    protected async Task HandleRenewalClick(int renewalId)
    {
        try
        {
            // Set the state service to remember which renewal was selected
            StateService.HtmlRenewalId = renewalId;
            StateService.HtmlView = "details";
            StateService.HtmlTab = "tab-1";
            
            var ren = await RenewalService.GetRenewalByIdAsync(renewalId);
            Navigation.NavigateTo($"/Renewals/Details/{renewalId}");
        }
        catch (Exception ex)
        {
            await _log.LogAsync(LogLevel.Error, $"Error navigating to renewal {renewalId}: {ex.Message}", "RenFlowTasks");
        }
    }

    public void Dispose()
    {
        StateService.OnHomepageDataUpdated -= HandleDataUpdated;
    }
}