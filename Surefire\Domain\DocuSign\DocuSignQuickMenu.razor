﻿@namespace Surefire.Domain.DocuSign.Components
@using Microsoft.AspNetCore.Components.Web
@using Surefire.Domain.DocuSign
@page "/docusign-status"
@inject IDocuSignService DocuSignService

<h3>DocuSign</h3>

<div class="card mb-4">
    <div class="card-header bg-light">
        <h4>DocuSign Tools</h4>
    </div>
    <div class="card-body">
        <div class="list-group">
            <a href="/docusign-jwt-test" class="list-group-item list-group-item-action">
                JWT Authentication Test
            </a>
            <button class="list-group-item list-group-item-action text-start" @onclick="LoadEnvelopes">
                View Recent Envelopes
            </button>
        </div>
    </div>
</div>

@if (isLoading)
{
    <div class="card">
        <div class="card-body">
            <p>Loading envelopes...</p>
        </div>
    </div>
}
else if (!string.IsNullOrEmpty(error))
{
    <div class="card">
        <div class="card-body">
            <div class="alert alert-danger">
                <h5>Error</h5>
                <p>@error</p>
            </div>
        </div>
    </div>
}
else if (!string.IsNullOrEmpty(envelopesJson))
{
    <div class="card">
        <div class="card-header bg-light">
            <h4>Recent Envelopes</h4>
        </div>
        <div class="card-body">
            <pre style="max-height: 500px; overflow-y: auto;">@envelopesJson</pre>
        </div>
    </div>
}

@code {
    private bool isLoading = false;
    private string envelopesJson = string.Empty;
    private string error;

    private async Task LoadEnvelopes()
    {
        try
        {
            isLoading = true;
            envelopesJson = string.Empty;
            error = string.Empty;
            StateHasChanged();
            
            // Get the access token using the JWT flow
            var accessToken = await DocuSignService.GetAccessTokenAsync();
            // Retrieve recent envelopes
            envelopesJson = await DocuSignService.GetRecentEnvelopesAsync(accessToken);
        }
        catch (Exception ex)
        {
            error = ex.Message;
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
