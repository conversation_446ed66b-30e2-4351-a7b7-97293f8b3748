﻿@namespace Surefire.Components.Account.Pages
@attribute [ExcludeFromInteractiveRouting]
@layout EmptyLayout
@page "/Account/Login"

@using Surefire.Components.Account.Shared
@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity
@using Surefire.Data

@inject SignInManager<ApplicationUser> SignInManager
@inject ILogger<Login> Logger
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager

<PageTitle>Surefire success from relentless focus.</PageTitle>

<div class="page-content">
    <div class="staticlogin">
        <section>
            <div class="txt-alert-main">Login</div>
            <div class="txt-alert-sub"><StatusMessage Message="@errorMessage" /></div>
            <EditForm Model="Input" method="post" OnValidSubmit="LoginUser" FormName="login" name="login">
                <DataAnnotationsValidator />

                <ValidationSummary class="text-danger" role="alert" />
                <span class="sf-thead">Email</span>
                <div class="e-input-group e-control-container e-control-wrapper e-float-input" style="padding-top:0px !important;">
                    <InputText @bind-Value="Input.Email" id="Input.Email" class="e-control e-textbox e-lib" autocomplete="username" aria-required="true" placeholder="<EMAIL>" />
                    <ValidationMessage For="() => Input.Email" class="text-danger" />
                </div>
                <div style="height:20px;"></div>
                <span class="sf-thead">Password</span>
                <div class="e-input-group e-control-container e-control-wrapper e-float-input" style="padding-top:0px !important;">
                    <InputText type="password" @bind-Value="Input.Password" id="Input.Password" class="e-control e-textbox e-lib" autocomplete="current-password" aria-required="true" placeholder="password" />
                    <ValidationMessage For="() => Input.Password" class="text-danger" />
                </div>
                <div style="height:10px;"></div>
                <div class="checkbox mb-3" style="font-size:12px;">
                    <label class="form-label">
                        <InputCheckbox @bind-Value="Input.RememberMe" class="darker-border-checkbox form-check-input" />
                        Remember me
                    </label>
                </div>

                <div>
                    <button type="submit" class="submitbtn">Log in</button>
                </div>
            </EditForm>
        </section>
    </div>
</div>

<style>
    :root .e-float-input {
    padding-top: 0px !important;
    margin-top: 0px !important;
    }
</style>
@code {
    private string? errorMessage = "Let's get you logged back in...";

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        // Redirect authenticated users away from login page
        if (HttpContext.User.Identity?.IsAuthenticated == true)
        {
           NavigationManager.NavigateTo("/"); // Change to your dashboard if needed
           return;
        }
        //Input.Email = "<EMAIL>";
        //Input.Password = "Password123!";
        if (HttpMethods.IsGet(HttpContext.Request.Method))
        {
            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);
        }
    }

    public async Task LoginUser()
    {
        Console.WriteLine($"Login for user: {Input.Email}");
        var result = await SignInManager.PasswordSignInAsync(Input.Email, Input.Password, Input.RememberMe, lockoutOnFailure: false);
        if (result.Succeeded)
        {
            Logger.LogInformation("User logged in.");
            RedirectManager.RedirectTo("loading");
        }
        else if (result.RequiresTwoFactor)
        {
            RedirectManager.RedirectTo("Account/LoginWith2fa", new() { ["returnUrl"] = ReturnUrl, ["rememberMe"] = Input.RememberMe });
        }
        else if (result.IsLockedOut)
        {
            Logger.LogWarning("User account locked out.");
            RedirectManager.RedirectTo("Account/Lockout");
        }
        else
        {
            errorMessage = "Error: Invalid login attempt.";
        }
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";

        [Required]
        [DataType(DataType.Password)]
        public string Password { get; set; } = "";

        [Display(Name = "Remember me?")]
        public bool RememberMe { get; set; }
    }
}

<script>
  document.addEventListener('DOMContentLoaded', function () {
    var form = document.querySelector('form');
    if (form) {
      form.addEventListener('submit', function (e) {
        var btn = form.querySelector('button[type="submit"]');
        if (btn) {
          btn.disabled = true;
          btn.innerText = "Logging in...";
        }
      });
    }
  });
</script>
