﻿.note-card {
    /*background-color: white;
    border-radius: 6px;
    padding: 12px 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 12px;
    border-left: 3px solid #ccc;
    transition: all 0.2s ease;
    max-width: 300px;*/
    border: 1px solid #fff;
    border-radius: 5px;
    background-color: #eeeeee;
    width: 22%;
    min-width: 300px;
    max-width: 400px;
    height: 181px;
    overflow: hidden;
    margin: 8px 2px 8px 2px;
    text-align: center;
}
    .note-card.pinned {
        border-left-color: #0078d4;
        background-color: #f0f7ff;
    }
.note-btn {
    opacity:.7;

}
.note-btn:hover {
    opacity:1;
    cursor: pointer;
}
.note-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    background-color: #d8d8d8;
    padding-top: 3px;
    padding-left: 3px;
    padding-right: 3px;
}

.note-metadata {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    font-size: 12px;
    color: #666;
}

.author-info, .date-info, .reminder-info {
    display: flex;
    align-items: center;
    gap: 4px;
}

.reminder-info {
    color: #9d5d00;
}

.note-actions {
    display: flex;
    gap: 2px;
}

.note-content {  
   text-align: left;  
   font-size: .8em;  
   color: #333;  
   overflow-wrap: break-word;  
   max-height: 150px;  
   overflow-y: auto;  
   padding: 5px;  
   opacity: .7;  
   font-family: "montserrat", sans-serif;  
   line-height: 1.2;  
   transition: all 0.3s ease;  
}
    .note-content p, markdown-content p, .text-content p {
        margin: 0;
        padding: 0;
    }
    .note-content:hover {
        opacity:1;
    }
.markdown-content {
    
}

    .markdown-content ::deep h1,
    .markdown-content ::deep h2,
    .markdown-content ::deep h3,
    .markdown-content ::deep h4 {
        margin-top: 0.5em;
        margin-bottom: 0.3em;
    }

    .markdown-content ::deep p {
        margin: 0;
        padding: 0;
    }

    .markdown-content ::deep ul,
    .markdown-content ::deep ol {
        margin: 0.5em 0;
        padding-left: 1.5em;
    }

.note-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 10px;
}

.tag {
    background-color: #f0f0f0;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    color: #555;
}
