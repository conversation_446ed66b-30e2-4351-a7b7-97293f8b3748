﻿namespace Surefire.Domain.DocuSign
{
    /// <summary>
    /// Interface for accessing DocuSign configuration settings
    /// </summary>
    public interface IDocuSignConfigService
    {
        /// <summary>
        /// Gets the current DocuSign configuration settings
        /// </summary>
        /// <returns>The current DocuSign configuration</returns>
        DocuSignConfig GetDocuSignConfig();

        /// <summary>
        /// Updates the DocuSign configuration settings
        /// </summary>
        /// <param name="config">The new configuration settings</param>
        void UpdateDocuSignConfig(DocuSignConfig config);

        /// <summary>
        /// Determines if the current configuration is for a production environment
        /// </summary>
        /// <returns>True if the current configuration is for production, false for demo/test</returns>
        bool IsProduction();
    }
}